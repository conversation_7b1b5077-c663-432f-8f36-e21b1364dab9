import {
  Component,
  EventEmitter,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { combineLatest, Subject, switchMap, takeUntil } from 'rxjs';

import {
  PAGE_SIZE,
  REPORT_FILTERS_KEY_LABEL,
  SHOW_ENTRIES,
  USER_VISIBILITY,
} from 'src/app/app.constants';
import { DataDateFilters } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { ReportsFilter } from 'src/app/core/interfaces/reports.interface';
import {
  assignToSort,
  changeCalendar,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  getTotalCountForReports,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { FetchAgencyNameList } from 'src/app/reducers/Integration/integration.actions';
import {
  getAgencyNameList,
  getAgencyNameListIsLoading,
} from 'src/app/reducers/Integration/integration.reducer';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchDataCallExportSuccess,
  FetchDataReportsCall,
  UpdateDataCallFilterPayload,
} from 'src/app/reducers/data-reports/data-reports.action';
import {
  getDataCallFiltersPayload,
  getDataReportsCallList,
  getReportsDataCallListIsLoading,
} from 'src/app/reducers/data-reports/data-reports.reducers';
import { FetchDataSourceList, FetchDataSubSourceList } from 'src/app/reducers/data/data-management.actions';
import {
  getDataSourceList,
  getDataSourceListIsLoading,
  getDataSubSourceList,
  getDataSubSourceListIsLoading,
} from 'src/app/reducers/data/data-management.reducer';
import {
  FetchProjectList,
} from 'src/app/reducers/lead/lead.actions';
import {
  getProjectList,
  getProjectListIsLoading,
  getSubSourceListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchOnlyReporteesWithInactive,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getOnlyReporteesWithInactive,
  getOnlyReporteesWithInactiveIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'data-call-report',
  templateUrl: './data-call-report.component.html',
})
export class DataCallReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  gridOptions: any;
  columnDropDown: { field: string; hide: boolean }[] = [];
  rowData: Array<any> = [];
  gridApi: any;
  gridColumnApi: any;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  getPages = getPages;
  appliedFilter: any;
  filtersPayload: ReportsFilter;
  canExportAllUsers: boolean = false;
  canViewAllUsers: boolean = false;
  canViewReportees: boolean = false;
  canExportReportees: boolean = false;
  projectList: any;
  isDateFilter: string;
  subSourceList: any;
  agencyNameList: any;
  dateTypeList: Array<any> = Object.values(DataDateFilters).slice(0, 4);
  visibilityList: Array<Object> = USER_VISIBILITY.slice(0, 3);
  allUsers: Array<any> = [];
  onlyReportees: Array<any> = [];
  users: Array<any> = [];
  reportees: Array<any> = [];
  showLeftNav: boolean = true;
  selectedSection: string = 'All';
  isCallReportLoading: boolean = true;
  isAllUsersLoading: boolean = true;
  isOnlyReporteesLoading: boolean = true;
  isSubSourceListLoading: boolean = true;
  isProjectListLoading: boolean = true;
  agencyNameListIsLoading: boolean = true;
  sourceList: any[] = [];
  isSourcesLoading: boolean = false;
  sourceIdMap: any = {};


  dataCallTotalCount: number;
  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;
  showFilters: boolean = false;
  isSourceListLoading: boolean;
  userData: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;
  toDateForCall: any = new Date();
  fromDateForCall: any = new Date();
  toDate: any = new Date();
  fromDate: any = new Date();
  s3BucketUrl: string = environment.s3ImageBucketURL;
  allDataSubSourceList: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    public router: Router,
    private modalService: BsModalService,
    private shareDataService: ShareDataService
  ) {
    this.headerTitle.setTitle('Data - Call Report');
    this.metaTitle.setTitle('CRM | Reports');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });
    this._store
      .select(getDataCallFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = data;
        this.pageSize = this.filtersPayload?.pageSize;
        const userStatus =
          this.filtersPayload?.userStatus === undefined
            ? 1
            : this.filtersPayload?.userStatus;
        this.appliedFilter = {
          ...this.appliedFilter,
          userStatus: userStatus,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          withTeam: this.filtersPayload?.IsWithTeam,
          visibility: this.filtersPayload?.userStatus,
          users: this.filtersPayload?.UserIds,
          agencyNames: this.filtersPayload?.AgencyNames,
          search: this.filtersPayload?.SearchText,
          sourceIds: this.filtersPayload?.ProspectSourceIds,
          subSources: this.filtersPayload?.SubSources,
          projects: this.filtersPayload?.Projects,
          dateType: DataDateFilters[Number(this.filtersPayload?.dateType)],
          date: [
            patchTimeZoneDate(
              this.filtersPayload?.fromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              this.filtersPayload?.toDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ],
          dateForCall: [
            patchTimeZoneDate(
              this.filtersPayload?.callLogFromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              this.filtersPayload?.callLogToDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ],
        };
        this.activeDate();
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.users = usersData;
          this.allUsers = usersData;
          this.allUsers = assignToSort(this.allUsers, '');
          return this._store
            .select(getUsersListForReassignmentIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
        if (!isLoading) {
          this.currentVisibility(1);
        }
      });

    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.reportees = usersData;
          this.onlyReportees = usersData;
          this.onlyReportees = assignToSort(this.onlyReportees, '');
          return this._store
            .select(getOnlyReporteesWithInactiveIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isOnlyReporteesLoading = isLoading;
        if (!isLoading) {
          this.currentVisibility(1);
        }
      });
    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProjectListLoading = isLoading;
      });
    this._store
      .select(getDataSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isSourceListLoading = isLoading;
      });
    this._store
      .select(getDataReportsCallList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = getTotalCountForReports(data.items);
        this.dataCallTotalCount = data.totalCount;
      });
    this._store
      .select(getReportsDataCallListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isCallReportLoading = isLoading;
      });
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExportAllUsers = permissionsSet.has(
          'Permissions.Reports.ExportAllUsers'
        );
        this.canViewAllUsers = permissionsSet.has(
          'Permissions.Reports.ViewAllUsers'
        );
        this.canExportReportees = permissionsSet.has(
          'Permissions.Reports.ExportReportees'
        );
        this.canViewReportees = permissionsSet.has(
          'Permissions.Reports.ViewReportees'
        );
        if (this.canViewAllUsers) {
          this._store.dispatch(new FetchUsersListForReassignment());
        }
        if (this.canViewReportees) {
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
      });
    this._store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agencyNameList = item
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getAgencyNameListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.agencyNameListIsLoading = isLoading;
      });
    this._store
      .select(getDataSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allDataSubSourceList = data
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.updateSubSource()
      });
    this._store
      .select(getDataSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isSubSourceListLoading = isLoading;
      });
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
    this.initializeGridSettings();
    this.filterFunction();
    this.activeDate();
  }

  ngOnInit() {
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });

    const allSources$ = this._store.select(getAllSources);
    const dataSourceList$ = this._store.select(getDataSourceList);

    combineLatest([allSources$, dataSourceList$])
      .pipe(takeUntil(this.stopper))
      .subscribe(([leadSource, dataSource]) => {
        if (leadSource && dataSource) {
          const matched = dataSource
            .filter((data: any) =>
              leadSource.some((lead: any) => lead?.value === data?.value && lead?.isEnabled))
            .map((data: any) => {
              const matchedLead = leadSource.find((lead: any) => lead?.value === data?.value);
              return {
                ...data,
                isEnabled: matchedLead?.isEnabled ?? false,
                isDefault: matchedLead?.isDefault ?? false
              };
            });
          const sorted = matched.sort((a: any, b: any) => {
            if (a.isEnabled === b.isEnabled) {
              return a.displayName.localeCompare(b.displayName);
            }
            return a.isEnabled ? -1 : 1;
          });
          this.sourceList = sorted;
          this.sourceIdMap = {};
          sorted.forEach((source: any) => {
            if (source.id && source.displayName) {
              this.sourceIdMap[source.id] = source.displayName;
            }
          });
        }
        this.updateSubSource()
      });

    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'User Name',
        field: 'User Name',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [
          params.data?.firstName,
          params.data?.lastName,
        ],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<p class="py-16 text-truncate">${params.value[0]} ${params.value[1]}</p>`;
        },
      },
      {
        headerName: 'Incoming',
        children: [
          {
            headerName: 'Incoming Answered',
            field: 'Incoming Answered',
            filter: false,
            valueGetter: (params: any) => [params.data?.incomingAnswered],
            minWidth: 140,
            cellRenderer: (params: any) => {
              return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
            },
          },
          {
            headerName: 'Incoming Missed',
            field: 'Incoming Missed',
            filter: false,
            valueGetter: (params: any) => [params.data?.incomingMissed],
            minWidth: 140,
            cellRenderer: (params: any) => {
              return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
            },
          },
          {
            headerName: 'Total',
            field: 'Total',
            filter: false,
            valueGetter: (params: any) => [params.data?.totalIncomingCalls],
            minWidth: 80,
            cellRenderer: (params: any) => {
              return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
            },
          },
        ],
      },
      {
        headerName: 'Outgoing',
        children: [
          {
            headerName: 'Outgoing Answered',
            field: 'Outgoing Answered',
            filter: false,
            valueGetter: (params: any) => [params.data?.outgoingAnswered],
            minWidth: 140,
            cellRenderer: (params: any) => {
              return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
            },
          },
          {
            headerName: 'Outgoing Not Connected',
            field: 'Outgoing Not Connected',
            filter: false,
            valueGetter: (params: any) => [params.data?.outgoingNotConnected],
            minWidth: 170,
            cellRenderer: (params: any) => {
              return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
            },
          },
          {
            headerName: 'Total',
            field: 'Total',
            filter: false,
            valueGetter: (params: any) => [params.data?.totalOutgoingCalls],
            minWidth: 80,
            cellRenderer: (params: any) => {
              return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
            },
          },
        ],
      },
      {
        headerName: 'Total TalkTime',
        field: 'Total TalkTime',
        valueGetter: (params: any) => [params.data?.totalTalkTime],
        minWidth: 125,
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Avg TalkTime',
        field: 'Avg TalkTime',
        valueGetter: (params: any) => [params.data?.averageTalkTime],
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Min TalkTime',
        field: 'Min TalkTime',
        valueGetter: (params: any) => [params.data?.minTalkTime],
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Max TalkTime',
        field: 'Max TalkTime',
        valueGetter: (params: any) => [params.data?.maxTalkTime],
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Total Calls',
        field: 'Total Calls',
        valueGetter: (params: any) => [params.data?.totalCalls],
        minWidth: 110,
        cellRenderer: (params: any) => {
          return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
        },
      },
    ];
    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this._store.dispatch(new UpdateDataCallFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchDataReportsCall());
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(new UpdateDataCallFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchDataReportsCall());
    this.currOffset = 0;
  }

  currentVisibility(visibility: any) {
    this.appliedFilter.userStatus = visibility;
    this.appliedFilter.pageNumber = 1;
    this.appliedFilter.users = null;
    this.filterFunction();

    if (this.canViewAllUsers) {
      switch (visibility) {
        case 1:
          this.allUsers = this.users?.filter((user: any) => user.isActive);
          break;
        case 2:
          this.allUsers = this.users?.filter((user: any) => !user.isActive);
          break;
        case null:
          this.allUsers = this.users;
          break;
      }
      this.allUsers = assignToSort(this.allUsers, '');
    } else {
      switch (visibility) {
        case 1:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => user.isActive
          );
          break;
        case 2:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => !user.isActive
          );
          break;
        case null:
          this.onlyReportees = this.reportees;
          break;
      }
      this.onlyReportees = assignToSort(this.onlyReportees, '');
    }
  }

  filterFunction() {
    this.appliedFilter.pageNumber = 1;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      IsWithTeam: this.appliedFilter.withTeam,
      userStatus: this.appliedFilter.userStatus,
      UserIds: this.appliedFilter.users,
      ProspectSourceIds: this.appliedFilter.sourceIds,
      SubSources: this.appliedFilter.subSources,
      Projects: this.appliedFilter.projects,
      SearchText: this.searchTerm,
      AgencyNames: this.appliedFilter.agencyNames,
      dateType: DataDateFilters[this.appliedFilter.dateType],
      ReportPermission: this.canViewAllUsers ? 0 : 1,
      ExportPermission: this.canExportAllUsers ? 0 : 1,

      fromDate: setTimeZoneDate(
        this.appliedFilter?.date?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      toDate: setTimeZoneDate(
        this.appliedFilter.date?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      callLogFromDate: setTimeZoneDate(
        this.appliedFilter?.dateForCall?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      callLogToDate: setTimeZoneDate(
        this.appliedFilter?.dateForCall?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
    };
    this._store.dispatch(new UpdateDataCallFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchDataReportsCall());
    this.currOffset = 0;
    if (
      this.appliedFilter?.dateType?.length ||
      this.appliedFilter?.date?.[0]?.length ||
      this.appliedFilter.users?.length ||
      this.appliedFilter.projects?.length ||
      this.appliedFilter.subSources?.length ||
      this.appliedFilter.sourceIds?.length ||
      this.appliedFilter.dateForCall?.[0] ||
      this.appliedFilter?.agencyNames?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
  }

  onResetDateFilter() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateType: null,
      date: '',
    };
    this.filterFunction();
  }

  resetDate() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateForCall: [null, null],
    };
    this.isDateFilter = '';
    this.filterFunction();
  }

  reset() {
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.pageSize,
      dateForCall: [null, null],
    };
    this.isDateFilter = '';

    this.filterFunction();
  }

  filterByDate(type?: string) {
    // let newDate = new Date();
    let date = new Date(this.currentDate.setHours(0, 0, 0, 0));

    switch (type) {
      case 'today':
        this.isDateFilter = 'today';
        this.appliedFilter.dateForCall[0] = new Date(date);
        this.appliedFilter.dateForCall[1] = new Date(date);
        break;
      case 'yesterday':
        this.isDateFilter = 'yesterday';
        this.appliedFilter.dateForCall[0] = new Date(date).setDate(
          new Date(date).getDate() - 1
        );
        this.appliedFilter.dateForCall[1] = new Date(date).setDate(
          new Date(date).getDate() - 1
        );
        break;
      case 'sevenDays':
        this.isDateFilter = 'sevenDays';
        this.appliedFilter.dateForCall[0] = new Date(date).setDate(
          new Date(date).getDate() - 6
        );
        this.appliedFilter.dateForCall[1] = new Date(date);
        break;
      case 'custom':
        this.isDateFilter = 'custom';
        this.appliedFilter.dateForCall[0] = null;
        this.appliedFilter.dateForCall[1] = null;
        break;
    }
  }

  activeDate() {
    const fromDate = new Date(this.appliedFilter.dateForCall[0]);
    const toDate = new Date(this.appliedFilter.dateForCall[1]);

    const today = new Date(this.currentDate);
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 6);

    if (
      fromDate.toDateString() === today.toDateString() &&
      toDate.toDateString() === today.toDateString()
    ) {
      this.isDateFilter = 'today';
    } else if (
      fromDate.toDateString() === yesterday.toDateString() &&
      toDate.toDateString() === yesterday.toDateString()
    ) {
      this.isDateFilter = 'yesterday';
    } else if (
      fromDate.toDateString() === sevenDaysAgo.toDateString() &&
      toDate.toDateString() === today.toDateString()
    ) {
      this.isDateFilter = 'sevenDays';
    } else if (this.appliedFilter.dateForCall[0]) {
      this.isDateFilter = 'custom';
    }
  }

  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = ['subSources', 'agencyNames', 'projects'];
    if (
      [
        'pageSize',
        'pageNumber',
        'visibility',
        'withTeam',
        'userStatus',
        'search',
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'dateForCall' && values.length === 2) {
      if (key === 'dateForCall' && values[0] !== null) {
        this.toDateForCall = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDateForCall = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDateForCall = getTimeZoneDate(
          this.toDateForCall,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDateForCall = getTimeZoneDate(
          this.fromDateForCall,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );

        const dateString = `${formattedToDateForCall} to ${formattedFromDateForCall}`;
        return [dateString];
      } else {
        return null;
      }
    } else if (key === 'date' && values.length === 2) {
      if (key === 'date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDate = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDate = getTimeZoneDate(
          this.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDate = getTimeZoneDate(
          this.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    } else if (allowedKeys.includes(key)) {
      return values;
    }
    return values?.toString()?.split(',');
  }

  applyAdvancedFilter() {
    this.filterFunction();
    this.modalService.hide();
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.fullName}`;
    });
    return userName;
  }

  onRemoveFilter(key: string, value: string) {
    if (key === 'date' || key === 'dateType') {
      delete this.appliedFilter[key];
      const dependentKey = key === 'date' ? 'dateType' : 'date';
      if (this.appliedFilter[dependentKey]) {
        delete this.appliedFilter[dependentKey];
      }
    } else if (key === 'dateForCall') {
      delete this.appliedFilter[key];
      this.isDateFilter = '';
    } else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter(
        (_: any, index: number) => {
          const matchIndex = this.appliedFilter[key]?.indexOf(value);
          return index !== matchIndex;
        }
      );
    }
    this.filterFunction();
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchAgencyNameList());
    this._store.dispatch(new FetchDataSubSourceList());
    this._store.dispatch(new FetchDataSourceList());
    this._store.dispatch(new FetchAllSources());
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }

  exportCallReport() {
    this._store.dispatch(new FetchDataCallExportSuccess(''));
    this.filterFunction();

    let initialState: any = {
      payload: {
        ...this.filtersPayload,
        timeZoneId:
          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset:
          this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  updateSubSource($event?: any) {
    if ($event?.length) {
      this.subSourceList = [];
      $event.forEach((i: any) => {
        if (i.displayName === '99 Acres') {
          const selectedSubSource = this.allDataSubSourceList['NinetyNineAcres'] || [];
          this.subSourceList = [...this.subSourceList, ...selectedSubSource];
        } else {
          const formattedKey = i.displayName?.replace(/\s+/g, '');
          let selectedSubSource = this.allDataSubSourceList[formattedKey];
          if (!Array.isArray(selectedSubSource)) {
            selectedSubSource = this.allDataSubSourceList[i.displayName] || [];
          }
          this.subSourceList = [...this.subSourceList, ...selectedSubSource];
        }
      });
    } else {
      let subSourceList: string[] = this.sourceList?.flatMap((lead: any): string[] => {
        if (lead?.displayName === '99 Acres') {
          return this.allDataSubSourceList['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        let match = this.allDataSubSourceList[formattedKey];
        if (!match) {
          match = this.allDataSubSourceList[lead?.displayName];
        }
        if (!match && formattedKey?.toLowerCase() === '99acres') {
          match = this.allDataSubSourceList['NinetyNineAcres'];
        }
        return Array.isArray(match) ? match : [];
      }) || [];
      this.subSourceList = subSourceList
    }
  }

  onSelectSource(source: any) {
    if (source) {
      this.updateSubSources(source.displayName);
    } else {
      this.updateSubSources(null);
    }
  }

  updateSubSources(sourceName: string | null) {
    if (sourceName) {
      if (sourceName === '99 Acres') {
        this.subSourceList = this.allDataSubSourceList['NinetyNineAcres'] || [];
      } else {
        const formattedKey = sourceName.replace(/\s+/g, '');
        if (Array.isArray(this.allDataSubSourceList[formattedKey])) {
          this.subSourceList = this.allDataSubSourceList[formattedKey] || [];
        } else {
          this.subSourceList = this.allDataSubSourceList[sourceName] || [];
        }
      }
    } else {
      let subSourceList: string[] = this.sourceList?.flatMap((lead: any): string[] => {
        if (lead?.displayName === '99 Acres') {
          return this.allDataSubSourceList['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        let match = this.allDataSubSourceList[formattedKey];
        if (!match) {
          match = this.allDataSubSourceList[lead?.displayName];
        }
        if (!match && formattedKey?.toLowerCase() === '99acres') {
          match = this.allDataSubSourceList['NinetyNineAcres'];
        }
        return Array.isArray(match) ? match : [];
      }) || [];
      this.subSourceList = subSourceList;
    }
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput(_: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }


  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
