import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { of, EMPTY } from 'rxjs';
import {
  catchError,
  map,
  mergeMap,
  switchMap,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import { AppState } from 'src/app/app.reducer';
import { IndexedDBService } from 'src/app/services/shared/indexeddb.service';
import { NotificationsService } from 'angular2-notifications';
import {
  CheckForUpdates,
  CheckForUpdatesFailure,
  CheckForUpdatesNoChange,
  CheckForUpdatesSuccess,
  ClearIndexedDB,
  ClearIndexedDBFailure,
  ClearIndexedDBSuccess,
  FetchInitialProperties,
  FetchInitialPropertiesFailure,
  FetchInitialPropertiesSuccess,
  FetchModifiedProperties,
  FetchModifiedPropertiesFailure,
  FetchModifiedPropertiesSuccess,
  FetchRemainingProperties,
  FetchRemainingPropertiesFailure,
  FetchRemainingPropertiesSuccess,
  GetBatchProcessingStatus,
  GetBatchProcessingStatusFailure,
  GetBatchProcessingStatusSuccess,
  GetPropertiesFromIndexedDB,
  GetPropertiesFromIndexedDBEmpty,
  GetPropertiesFromIndexedDBFailure,
  GetPropertiesFromIndexedDBSuccess,
  IndexedDBActionTypes,
  InitIndexedDB,
  InitIndexedDBFailure,
  InitIndexedDBSuccess,
  ResetBatchProcessing,
  ResetBatchProcessingFailure,
  ResetBatchProcessingSuccess,
  ResetIndexedDB,
  ResetIndexedDBFailure,
  ResetIndexedDBSuccess,
  ResumeBatchProcessing,
  ResumeBatchProcessingFailure,
  ResumeBatchProcessingSuccess,
  StoreProperties,
  StorePropertiesFailure,
  StorePropertiesSuccess,
} from './indexeddb.actions';
import {
  FetchPropertyList,
  FetchPropertyListSuccess,
  PropertyActionTypes,
} from '../property/property.actions';
import { getFiltersPayload } from '../property/property.reducer';
import { getUseIndexedDB } from './indexeddb.reducer';
import { PropertyService } from 'src/app/services/controllers/properties.service';

@Injectable()
export class IndexedDBEffects {
  constructor(
    private actions$: Actions,
    private store: Store<AppState>,
    private indexedDBService: IndexedDBService,
    private notificationService: NotificationsService,
    private propertyservice: PropertyService
  ) { }

  // Initialize IndexedDB
  initIndexedDB$ = createEffect(() =>
    this.actions$.pipe(
      ofType<InitIndexedDB>(IndexedDBActionTypes.INIT_INDEXED_DB),
      mergeMap(() => {
        // The PropertyService initializes IndexedDB in its constructor,
        // so we just need to check if it's initialized
        return of(new InitIndexedDBSuccess());
      }),
      catchError((error) => {
        console.error('Error initializing IndexedDB:', error);
        return of(new InitIndexedDBFailure(error));
      })
    )
  );

  // Check for updates
  checkForUpdates$ = createEffect(() =>
    this.actions$.pipe(
      ofType<CheckForUpdates>(IndexedDBActionTypes.CHECK_FOR_UPDATES),
      mergeMap(() => {
        return this.indexedDBService.checkForUpdates().pipe(
          map((response) => {
            if (response && response.succeeded) {
              // If the API lastModifiedDate is different from the stored one,
              // we need to fetch the modified properties
              if (response.apiLastModifiedDate !== response.lastModifiedDate) {
                return new CheckForUpdatesSuccess(
                  response.apiLastModifiedDate,
                  response.lastModifiedDate
                );
              } else {
                return new CheckForUpdatesNoChange();
              }
            } else {
              return new CheckForUpdatesNoChange();
            }
          }),
          catchError((error) => {
            console.error('Error checking for updates:', error);
            return of(new CheckForUpdatesFailure(error));
          })
        );
      })
    )
  );

  // After successful check for updates with changes, fetch modified properties
  fetchModifiedProperties$ = createEffect(() =>
    this.actions$.pipe(
      ofType<CheckForUpdatesSuccess>(
        IndexedDBActionTypes.CHECK_FOR_UPDATES_SUCCESS
      ),
      map((action) => {
        // Check if storedLastModifiedDate is defined
        if (!action.storedLastModifiedDate) {
          console.warn(
            'storedLastModifiedDate is undefined in CheckForUpdatesSuccess, using empty string'
          );
          // Use empty string instead of undefined
          return new FetchModifiedProperties('');
        }
        return new FetchModifiedProperties(action.storedLastModifiedDate);
      })
    )
  );

  // Fetch modified properties
  fetchModifiedPropertiesAction$ = createEffect(() =>
    this.actions$.pipe(
      ofType<FetchModifiedProperties>(
        IndexedDBActionTypes.FETCH_MODIFIED_PROPERTIES
      ),
      switchMap((action) => {
        // Check if lastModifiedDate is undefined or empty
        if (!action.lastModifiedDate) {
          console.warn(
            'lastModifiedDate is undefined in fetchModifiedPropertiesAction$, skipping API call'
          );
          // Return a success action with empty data
          return of(new FetchModifiedPropertiesSuccess([], 0));
        }

        return this.propertyservice
          .getModifiedProperties({
            lastModifiedDate: action.lastModifiedDate,
            pageNumber: action.pageNumber,
            pageSize: action.pageSize,
          })
          .pipe(
            map((response: any) => {
              if (response && response.succeeded && response.items) {
                // Filter out archived properties
                const nonArchivedProperties = response.items.filter(
                  (property: any) => property.isArchived !== true
                );

                const archivedProperties = response.items.filter(
                  (property: any) => property?.isArchived === true
                );

                // If there are archived properties, remove them from IndexedDB
                if (archivedProperties.length > 0) {
                  const archivedIds = archivedProperties.map((property: any) => property.id);
                  this.indexedDBService.removePropertiesFromIndexedDB(archivedIds).subscribe({
                    next: () => {
                      console.log(`Removed ${archivedIds.length} archived properties from IndexedDB`);
                    },
                    error: (error) => {
                      console.error('Error removing archived properties from IndexedDB:', error);
                    }
                  });
                }

                // Store the non-archived properties in IndexedDB
                this.store.dispatch(new StoreProperties(nonArchivedProperties));

                // If there are more properties, fetch them in batches
                if (response.totalCount > action.pageSize) {
                  const numBatches = Math.ceil(
                    response.totalCount / action.pageSize
                  );
                  for (let i = 1; i < numBatches; i++) {
                    const pageNumber = i + 1;
                    setTimeout(() => {
                      this.store.dispatch(
                        new FetchModifiedProperties(
                          action.lastModifiedDate,
                          pageNumber,
                          action.pageSize
                        )
                      );
                    }, i * 1000); // 1 second delay between batches
                  }
                }

                return new FetchModifiedPropertiesSuccess(
                  nonArchivedProperties,
                  response.totalCount
                );
              } else {
                return new FetchModifiedPropertiesFailure(
                  'Invalid response from API'
                );
              }
            }),
            catchError((error) => {
              // Log the error
              console.error('Error fetching modified properties:', error);

              // Show notification
              this.notificationService.error(
                'Error',
                'Failed to fetch modified properties'
              );

              // Return error action
              return of(new FetchModifiedPropertiesFailure(error));
            })
          );
      })
    )
  );

  // Fetch initial properties
  fetchInitialPropertiesAction$ = createEffect(() =>
    this.actions$.pipe(
      ofType<FetchInitialProperties>(
        IndexedDBActionTypes.FETCH_INITIAL_PROPERTIES
      ),
      switchMap(() => {
        return this.indexedDBService.fetchInitialProperties().pipe(
          map((response: any) => {
            if (response && response.succeeded && response.items) {
              // Filter out archived properties
              const nonArchivedProperties = response.items.filter(
                (property: any) => property.isArchived !== true
              );

              // Get archived properties
              const archivedProperties = response.items.filter(
                (property: any) => property.isArchived === true
              );

              // If there are archived properties, remove them from IndexedDB
              if (archivedProperties.length > 0) {
                const archivedIds = archivedProperties.map((property: any) => property.id);
                this.indexedDBService.removePropertiesFromIndexedDB(archivedIds).subscribe({
                  next: () => {
                    console.log(`Removed ${archivedIds.length} archived properties from IndexedDB`);
                  },
                  error: (error) => {
                    console.error('Error removing archived properties from IndexedDB:', error);
                  }
                });
              }

              // Store the non-archived properties in IndexedDB
              this.store.dispatch(new StoreProperties(nonArchivedProperties));

              // If there are more records, fetch them in the background
              if (response.totalCount > 10) {
                this.store.dispatch(
                  new FetchRemainingProperties(response.totalCount)
                );
              }

              // Also dispatch FetchPropertyListSuccess to update the UI
              this.store.dispatch(
                new FetchPropertyListSuccess({
                  succeeded: true,
                  items: nonArchivedProperties,
                  totalCount: nonArchivedProperties.length,
                  pageNumber: 1,
                  pageSize: 10,
                  totalPages: Math.ceil(nonArchivedProperties.length / 10),
                } as any)
              );

              return new FetchInitialPropertiesSuccess(
                nonArchivedProperties,
                response.totalCount
              );
            } else {
              return new FetchInitialPropertiesFailure(
                'Invalid response from API'
              );
            }
          }),
          catchError((error) => {
            // Log the error
            console.error('Error fetching initial properties:', error);

            // Show notification
            this.notificationService.error(
              'Error',
              'Failed to fetch initial properties'
            );

            // Return error action
            return of(new FetchInitialPropertiesFailure(error));
          })
        );
      })
    )
  );

  // Store properties in IndexedDB
  storeProperties$ = createEffect(() =>
    this.actions$.pipe(
      ofType<StoreProperties>(IndexedDBActionTypes.STORE_PROPERTIES),
      mergeMap(async (action) => {
        try {
          await this.indexedDBService.storePropertiesInIndexedDBAsync(action.properties);
          return new StorePropertiesSuccess(action.properties.length);
        } catch (error) {
          console.error('Error storing properties in IndexedDB:', error);
          return new StorePropertiesFailure(error);
        }
      })
    )
  );

  // Get properties from IndexedDB
  getPropertiesFromIndexedDB$ = createEffect(() =>
    this.actions$.pipe(
      ofType<GetPropertiesFromIndexedDB>(
        IndexedDBActionTypes.GET_PROPERTIES_FROM_INDEXED_DB
      ),
      withLatestFrom(this.store.select(getUseIndexedDB)),
      mergeMap(async ([action, useIndexedDB]) => {
        // If useIndexedDB is false, skip IndexedDB and use the API directly
        if (!useIndexedDB) {
          console.log('useIndexedDB is false, using API directly');
          return new FetchPropertyList(true);
        }

        try {
          const response = await this.indexedDBService.getPropertiesFromIndexedDBAsync(action.payload);

          if (response && response.succeeded) {
            if (response.items && response.items.length > 0) {
              console.log(
                `Got ${response.items.length} properties from IndexedDB`
              );
              return new GetPropertiesFromIndexedDBSuccess(response);
            } else {
              console.log(
                'No data in IndexedDB, will trigger fetchInitialProperties'
              );
              // If no data in IndexedDB, return empty action which will trigger fetchInitialProperties
              return new GetPropertiesFromIndexedDBEmpty();
            }
          } else {
            console.log(
              'Error or no data in IndexedDB, will trigger fetchInitialProperties'
            );
            // If error or no data, return empty action which will trigger fetchInitialProperties
            return new GetPropertiesFromIndexedDBEmpty();
          }
        } catch (error) {
          // If error, fetch from API
          console.error('Error getting properties from IndexedDB:', error);
          console.log(
            'Error getting properties from IndexedDB, using API directly'
          );
          this.store.dispatch(new FetchPropertyList(true));
          return new GetPropertiesFromIndexedDBFailure(error);
        }
      })
    )
  );

  // After successful retrieval from IndexedDB, dispatch FetchPropertyListSuccess
  getPropertiesFromIndexedDBSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType<GetPropertiesFromIndexedDBSuccess>(
        IndexedDBActionTypes.GET_PROPERTIES_FROM_INDEXED_DB_SUCCESS
      ),
      map((action) => new FetchPropertyListSuccess(action.response))
    )
  );

  // When IndexedDB is empty, fetch initial properties
  getPropertiesFromIndexedDBEmpty$ = createEffect(() =>
    this.actions$.pipe(
      ofType<GetPropertiesFromIndexedDBEmpty>(
        IndexedDBActionTypes.GET_PROPERTIES_FROM_INDEXED_DB_EMPTY
      ),
      map(() => {
        console.log(
          'IndexedDB is empty, dispatching FetchPropertyList with canFetchLeadsCount=true'
        );
        // Instead of fetching initial properties, dispatch FetchPropertyList with canFetchLeadsCount=true
        // This will bypass the interceptor and use the API directly
        this.store.dispatch(new FetchPropertyList(true));
        return new FetchInitialProperties();
      })
    )
  );

  // Fetch remaining properties in the background
  fetchRemainingProperties$ = createEffect(() =>
    this.actions$.pipe(
      ofType<FetchRemainingProperties>(
        IndexedDBActionTypes.FETCH_REMAINING_PROPERTIES
      ),
      tap(async (action: FetchRemainingProperties) => {
        // This is handled by the IndexedDBService directly
        await this.indexedDBService.fetchPropertiesInBackgroundAsync(action.totalCount);
      }),
      map(() => new FetchRemainingPropertiesSuccess()),
      catchError((error) => {
        console.error('Error fetching remaining properties:', error);
        return of(new FetchRemainingPropertiesFailure(error));
      })
    )
  );

  // Clear IndexedDB
  clearIndexedDBAction$ = createEffect(() =>
    this.actions$.pipe(
      ofType<ClearIndexedDB>(IndexedDBActionTypes.CLEAR_INDEXED_DB),
      mergeMap(async (action) => {
        try {
          await this.indexedDBService.clearPropertyDataFromIndexedDBAsync(action.payload);
          this.notificationService.success(
            'Success',
            'Cached data cleared successfully'
          );
          return new ClearIndexedDBSuccess();
        } catch (error) {
          console.error('Error clearing IndexedDB:', error);
          this.notificationService.error(
            'Error',
            'Failed to clear cached data'
          );
          return new ClearIndexedDBFailure(error);
        }
      })
    )
  );

  // Reset IndexedDB
  resetIndexedDBAction$ = createEffect(() =>
    this.actions$.pipe(
      ofType<ResetIndexedDB>(IndexedDBActionTypes.RESET_INDEXED_DB),
      mergeMap(async () => {
        try {
          // First clear all data
          await this.indexedDBService.clearPropertyDataFromIndexedDBAsync();
          // Then fetch initial data
          await this.indexedDBService.fetchInitialPropertiesAsync();

          this.notificationService.success(
            'Success',
            'IndexedDB reset successfully'
          );
          return new ResetIndexedDBSuccess();
        } catch (error) {
          console.error('Error resetting IndexedDB:', error);
          this.notificationService.error(
            'Error',
            'Failed to reset IndexedDB'
          );
          return new ResetIndexedDBFailure(error);
        }
      })
    )
  );

  // Get Batch Processing Status
  getBatchProcessingStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType<GetBatchProcessingStatus>(
        IndexedDBActionTypes.GET_BATCH_PROCESSING_STATUS
      ),
      mergeMap(async () => {
        try {
          const status = await this.indexedDBService.getBatchProcessingStatusAsync();
          return new GetBatchProcessingStatusSuccess(status);
        } catch (error) {
          console.error('Error getting batch processing status:', error);
          return new GetBatchProcessingStatusFailure(error);
        }
      })
    )
  );

  // Resume Batch Processing
  resumeBatchProcessing$ = createEffect(() =>
    this.actions$.pipe(
      ofType<ResumeBatchProcessing>(
        IndexedDBActionTypes.RESUME_BATCH_PROCESSING
      ),
      mergeMap(async () => {
        try {
          // For now, just return a success message since we don't have resumeBatchProcessingAsync
          const result = { resumed: false, message: 'Batch processing resume not implemented in async version' };
          this.notificationService.info('Info', result.message);
          return new ResumeBatchProcessingSuccess(result);
        } catch (error) {
          console.error('Error resuming batch processing:', error);
          this.notificationService.error(
            'Error',
            'Failed to resume batch processing'
          );
          return new ResumeBatchProcessingFailure(error);
        }
      })
    )
  );

  // Reset Batch Processing
  resetBatchProcessing$ = createEffect(() =>
    this.actions$.pipe(
      ofType<ResetBatchProcessing>(IndexedDBActionTypes.RESET_BATCH_PROCESSING),
      mergeMap(async () => {
        try {
          // Reset batch processing by clearing the metadata
          await this.indexedDBService.storeMetadataInIndexedDBAsync('property_batch_progress', null);

          this.notificationService.success(
            'Success',
            'Batch processing status has been reset'
          );
          return new ResetBatchProcessingSuccess({});
        } catch (error) {
          console.error('Error resetting batch processing:', error);
          this.notificationService.error(
            'Error',
            'Failed to reset batch processing status'
          );
          return new ResetBatchProcessingFailure(error);
        }
      })
    )
  );

  // Intercept FetchPropertyList action to use IndexedDB when appropriate
  fetchPropertyList$ = createEffect(() =>
    this.actions$.pipe(
      ofType<FetchPropertyList>(PropertyActionTypes.FETCH_PROPERTY_LIST),
      withLatestFrom(
        this.store.select(getUseIndexedDB),
        this.store.select(getFiltersPayload)
      ),
      mergeMap(([action, useIndexedDB, filtersPayload]) => {
        // If forceUseAPI is true or useIndexedDB is false, let the original effect handle it
        if (action.canFetchLeadsCount || !useIndexedDB) {
          console.log(
            'Using API directly because canFetchLeadsCount is true or useIndexedDB is false'
          );
          return EMPTY; // Return empty observable to not dispatch any action
        }

        // Check if we should use IndexedDB for this query
        return this.indexedDBService.shouldUseIndexedDb(filtersPayload).pipe(
          mergeMap((shouldUseIndexedDb) => {
            if (shouldUseIndexedDb) {
              console.log('Using IndexedDB for this query');
              return of(new GetPropertiesFromIndexedDB(filtersPayload));
            } else {
              console.log(
                'Using API directly because shouldUseIndexedDb returned false'
              );
              return EMPTY; // Let the original effect handle it
            }
          }),
          catchError((error) => {
            console.error('Error checking shouldUseIndexedDb:', error);
            return EMPTY; // Let the original effect handle it
          })
        );
      })
    )
  );
}
