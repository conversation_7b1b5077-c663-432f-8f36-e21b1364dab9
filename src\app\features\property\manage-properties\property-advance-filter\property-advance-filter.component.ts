import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { select, Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import {
  BHK_NO_ALL,
  BHK_TYPE,
  FACING,
  FLOOR_OPTIONS,
  FURNISH_STATUS,
  lockInPeriodList,
  noticePeriodList,
  NUMBER_5,
  POSSESSION_DATE_FILTER_LIST,
  PROP_DATE_TYPE,
  securityDepositDates,
  TRANSACTION_TYPE_LIST,
} from 'src/app/app.constants';
import { PossessionType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  formatBudget,
  getBHKDisplayString,
  onlyNumbers,
  onlyNumbersWithDecimal,
  onPickerOpened
} from 'src/app/core/utils/common.util';
import { FetchAllAmenities } from 'src/app/reducers/amenities-attributes/amenities-attributes.action';
import { getAllAmenities, getAmenitiesLoading } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getProjectList, getProjectListIsLoading } from 'src/app/reducers/lead/lead.reducer';
import { getAreaUnitIsLoading, getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchLocationList, FetchOwnerNames, FetchPropertyCurrency } from 'src/app/reducers/property/property.actions';
import { getLocationList, getLocationListIsLoading, getOwnerNames, getPropertyCurrencyList } from 'src/app/reducers/property/property.reducer';
import { getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'property-advance-filter',
  templateUrl: './property-advance-filter.component.html'
})
export class PropertyAdvanceFilterComponent implements OnInit, OnDestroy {
  @ViewChild('possessionFilter') possessionFilter: any;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  applyAdvancedFilter: () => void;
  onClearAllFilters: (data: any) => void;
  onlyNumbers = onlyNumbers;
  appliedFilter: any = {};
  filterForm: FormGroup;
  private destroy$ = new Subject<void>();
  defaultCurrency: string = 'INR';

  // Loading states
  isProjectsListLoading = false;
  isAmenitiesLoading = false;
  isLocationListLoading = false;
  isGlobalSettingsLoading = false;
  isAreaUnitsLoading = false;
  allUserListIsLoading = false;
  budgetValidation = true;
  minMaxLeadValidation: boolean = true;
  minMaxDataValidation: boolean = true;

  userData: any;
  // Permissions
  canView = false;
  canViewOwner = false;

  // Constants
  dateTypeList = PROP_DATE_TYPE;
  readonly enquiredFor = TRANSACTION_TYPE_LIST.slice(1, 3)
  readonly bhkType = BHK_TYPE;
  readonly noOfBhk = BHK_NO_ALL;
  readonly furnishStatus = FURNISH_STATUS;
  readonly numbers = NUMBER_5;
  readonly floorOptions = FLOOR_OPTIONS;
  readonly facing = FACING;
  readonly securityDepositDates = securityDepositDates;
  readonly lockInPeriodList = lockInPeriodList;
  readonly noticePeriodList = noticePeriodList;
  readonly currentDate = new Date();

  // Data lists
  propertyCurrency: any[] = [];
  amenities: any[] = [];
  propertySubTypes: any[] = [];
  areaSizeUnits: any[] = [];
  projectList: string[] = [];
  ownerNames: string[] = [];
  localityList: string[] = [];
  cityList: string[] = [];
  stateList: string[] = [];
  allUserList: any[] = [];
  allPropertySubTypes: Array<Object> = [];
  carpetAreaValidations: boolean = true;
  buildUpAreaValidations: boolean = true;
  saleableValidation: boolean = true;
  areaValidation: boolean = true;
  // Form validation

  // Property types from localStorage
  propertyTypeList = JSON.parse(localStorage.getItem('propertyType') || '[]');

  // Utility functions
  formatBudget = formatBudget;
  getBHKDisplayString = getBHKDisplayString;
  onlyNumbersWithDecimal = onlyNumbersWithDecimal;
  onPickerOpened = onPickerOpened;
  propertyStatus = [
    { name: 'Active', value: 0 },
    { name: 'Sold', value: 1 },
  ];

  constructor(
    public fb: FormBuilder,
    public store: Store<AppState>,
    public modalRef: BsModalRef
  ) { }

  ngOnInit(): void {
    this.initializeData();
    this.setupSubscriptions();
    this.initializeForm();
    this.dateTypeList = this.dateTypeList?.filter((item: any) => item !== 'Possession Date');
  }

  private initializeForm(): void {
    this.filterForm = this.fb.group({});
  }

  private initializeData(): void {
    this.store.dispatch(new FetchAllAmenities());
    this.store.dispatch(new FetchLocationList());
    this.store.dispatch(new FetchOwnerNames());
    this.store.dispatch(new FetchPropertyCurrency());
    this.initializePropertySubTypes();

    if (!this.allPropertySubTypes?.length) {
      this.propertyTypeList?.map((type: any) => {
        this.allPropertySubTypes = this.allPropertySubTypes.concat(
          type.childTypes
        );
      });
    }
    this.propertySubTypes = [...this.allPropertySubTypes];
  }

  private setupSubscriptions(): void {
    this.store.pipe(
      select(getPermissions),
      takeUntil(this.destroy$)
    ).subscribe(this.handlePermissions.bind(this));

    this.handleAreaUnits()

    this.store.pipe(
      select(getLocationList),
      takeUntil(this.destroy$)
    ).subscribe(this.handleLocationList.bind(this));

    this.setupLoadingStateSubscriptions();

    this.store.pipe(
      select(getUsersListForReassignment),
      takeUntil(this.destroy$)
    ).subscribe(this.handleUsersList.bind(this));

    this.store.pipe(
      select(getAllAmenities),
      takeUntil(this.destroy$)
    ).subscribe(this.handleAmenities.bind(this));

    this.store.pipe(
      select(getProjectList),
      takeUntil(this.destroy$)
    ).subscribe(this.handleProjectList.bind(this));

    this.store.pipe(
      select(getOwnerNames),
      takeUntil(this.destroy$)
    ).subscribe(this.handleOwnerNames.bind(this));

    this.store
      .select(getPropertyCurrencyList)
      .pipe(takeUntil(this.destroy$))
      .subscribe((data: any) => {
        this.propertyCurrency = data;
      });
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : 'INR';
      });
  }

  private setupLoadingStateSubscriptions(): void {
    this.store.pipe(
      select(getLocationListIsLoading),
      takeUntil(this.destroy$)
    ).subscribe(isLoading => this.isLocationListLoading = isLoading);

    this.store.pipe(
      select(getProjectListIsLoading),
      takeUntil(this.destroy$)
    ).subscribe(isLoading => this.isProjectsListLoading = isLoading);

    this.store.pipe(
      select(getAmenitiesLoading),
      takeUntil(this.destroy$)
    ).subscribe(isLoading => this.isAmenitiesLoading = isLoading);
  }

  private handlePermissions(permissions: any): void {
    const permissionsSet = new Set(permissions);
    this.canView = permissionsSet.has('Permissions.Properties.View');
    this.canViewOwner = permissionsSet.has('Permissions.Properties.ViewOwnerInfo');
  }

  private handleAreaUnits(): void {
    this.store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((areaUnits: any) => {
        this.areaSizeUnits = areaUnits;
      });

    this.store
      .select(getAreaUnitIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAreaUnitsLoading = isLoading;
      });
  }

  private handleLocationList(data: any): void {
    if (!data) return;

    data.forEach((location: any) => {
      this.localityList = this.sortAndFilterList(location.locality);
      this.cityList = this.sortAndFilterList(location.city);
      this.stateList = this.sortAndFilterList(location.state);
    });
  }

  private handleUsersList(data: any): void {
    this.allUserList = data?.map((user: any) => ({
      ...user,
      fullName: `${user.firstName} ${user.lastName}`
    }));
    this.allUserList = assignToSort(this.allUserList, '');
  }

  private handleAmenities(data: any): void {
    const allAmenities = data.flatMap((category: any) => category.amenities);
    this.amenities = allAmenities
    this.amenities?.sort((a: any, b: any) =>
      a.amenityDisplayName.localeCompare(b.amenityDisplayName)
    );
  }
  private handleProjectList(data: any): void {
    this.projectList = data?.slice().sort((a: any, b: any) => a.localeCompare(b)) || [];
  }

  private handleOwnerNames(data: any): void {
    this.ownerNames = this.sortAndFilterList(data);
  }

  private sortAndFilterList(list: any[]): string[] {
    return list
      ?.filter(item => item)
      ?.slice()
      ?.sort((a, b) => a.localeCompare(b)) || [];
  }

  initializePropertySubTypes(): void {
    this.propertySubTypes = this.propertyTypeList
      ?.reduce((acc: any[], type: any) => [...acc, ...type.childTypes], [])
      || [];
  }

  updatePropertySubType() {
    if (this.appliedFilter?.propertyType?.length) {
      this.propertySubTypes = this.allPropertySubTypes.filter((subTypes: any) =>
        this.appliedFilter.propertyType.includes(subTypes.baseId)
      );
    } else {
      this.propertySubTypes = [];
      this.propertySubTypes = [...this.allPropertySubTypes];
    }
  }

  validateCarpetArea(): void {
    const minArea = this.appliedFilter.MinCarpetArea;
    const maxArea = this.appliedFilter.MaxCarpetArea;

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.carpetAreaValidations = false;
    } else {
      this.carpetAreaValidations = true;
    }
  }

  validateBuildUpArea(): void {
    const minArea = this.appliedFilter.MinBuildUpArea;
    const maxArea = this.appliedFilter.MaxBuildUpArea;

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.buildUpAreaValidations = false;
    } else {
      this.buildUpAreaValidations = true;
    }
  }

  saleableAreaValidation(): void {
    const mBudget = this.appliedFilter.MinSaleableArea;
    const maxBudget = this.appliedFilter.MaxSaleableArea;

    if (mBudget && maxBudget && maxBudget < mBudget) {
      this.saleableValidation = false;
    } else {
      this.saleableValidation = true;
    }
  }

  propertyAreaValidation(): void {
    const minArea = this.appliedFilter.MinPropertyArea;
    const maxArea = this.appliedFilter.MaxPropertyArea;

    if (minArea && maxArea && maxArea < minArea) {
      this.areaValidation = false;
    } else {
      this.areaValidation = true;
    }
  }

  reset() {
    this.appliedFilter = {}
    this.appliedFilter.Currency = this.defaultCurrency;

    this.appliedFilter.PossesionType = null;
    this.appliedFilter.FromPossesionDate = null;
    this.appliedFilter.ToPossesionDate = null;
    if (this.possessionFilter) {
      this.possessionFilter.reset();
    }

    this.onClearAllFilters('')
  }

  minMaxLeadCheck(): void {
    this.minMaxLeadValidation = !(
      this.appliedFilter.MinLeadCount &&
      this.appliedFilter.MaxLeadCount &&
      this.appliedFilter.MaxLeadCount < this.appliedFilter.MinLeadCount
    );
  }

  minMaxDataCheck() {
    this.minMaxDataValidation = !(
      this.appliedFilter.MinProspectCount &&
      this.appliedFilter.MaxProspectCount &&
      this.appliedFilter.MaxProspectCount < this.appliedFilter.MinProspectCount
    );
  }

  budgetCheck() {
    if (
      this.appliedFilter.minPrice &&
      this.appliedFilter.maxPrice &&
      this.appliedFilter.maxPrice < this.appliedFilter.minPrice
    ) {
      this.budgetValidation = false;
    } else {
      this.budgetValidation = true;
    }
  }

  validateAndApplyFilter() {
    if (!this.budgetValidation) return;
    this.applyAdvancedFilter();
  }

  getFormValue(controlName: string) {
    if (this.filterForm && this.filterForm.get(controlName)) {
      return this.filterForm.get(controlName)?.value;
    }
    return this.appliedFilter?.[controlName] || null;
  }

  onPossessionFilterChange(event: { possessionType: number | null; fromDate: string | null; toDate: string | null }): void {
    this.appliedFilter.PossesionType = event.possessionType;
    this.appliedFilter.FromPossesionDate = event.fromDate;
    this.appliedFilter.ToPossesionDate = event.toDate;
  }

  onResetDateFilter() {
    this.appliedFilter.dateType = 'All'
    this.appliedFilter.date = ''
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}