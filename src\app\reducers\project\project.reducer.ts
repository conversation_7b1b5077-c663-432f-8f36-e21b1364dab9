import { Action, createSelector } from '@ngrx/store';

import { AppState } from 'src/app/app.reducer';
import { Project } from 'src/app/core/interfaces/leads.interface';
import { UnitInfoFilter } from 'src/app/core/interfaces/project.interface';
import {
  FetchAssignedProjectsListSuccess,
  FetchBlockByIdSuccess,
  FetchBuilderDetailsSuccess,
  fetchCurrOffSetSuccess,
  FetchExportProjectStatusSuccess,
  fetchImageDropDownSuccess,
  FetchMicrositeAmenitiesSuccess,
  FetchMicrositeProjectSuccess,
  FetchMicrositeUnitSuccess,
  FetchProjectAmenitiesByIdsSuccess,
  FetchProjectAssignmentsSuccess,
  FetchProjectBasicDetailsByIdSuccess,
  FetchProjectByIdSuccess,
  FetchProjectCountSuccess,
  FetchProjectCurrencySuccess,
  FetchProjectDataByIdSuccess,
  FetchProjectExcelUploadedSuccess,
  FetchProjectGalleryByIdSuccess,
  FetchProjectIDWithNameSuccess,
  FetchProjectLeadsCountByIdsSuccess,
  FetchProjectListSuccess,
  FetchProjectLocationsSuccess,
  FetchProjectUnitExcelUploadedSuccess,
  FetchProjectUnitSuccess,
  FetchProjectWithGoogleLocationSuccess,
  FetchUnitByIdSuccess,
  FetchUnitInfoByIdSuccess,
  FetchUnitInfoSuccess,
  HasProjectNameSuccess,
  ProjectActionTypes,
  ProjectExcelUploadSuccess,
  ProjectUnitExcelUploadSuccess,
  UpdateProjectsFiltersPayload,
  UpdateUnitInfoFilterPayload,
} from 'src/app/reducers/project/project.action';

export type ProjectState = {
  projectUnit: any;
  micrositeProject: any;
  micrositeAmentities: any;
  micrositeUnit: any;
  project?: Array<Project>;
  isProjectLoading: boolean;
  isProjectByIdLoading: boolean
  selectedProject?: Project;
  filtersPayload: any;
  totalCount?: number;
  itemsCount?: number;
  projectCount?: any;
  assignedUserIds?: Array<string>;
  allProjects: any[];
  leadsCount: any[];
  assignedProjects: any[]
  projectTypeCount: any;
  unitInfoData: any;
  projectType: any;
  unitTypeData: any;
  builderDetails: any;
  blockData: any;
  locationsIsLoading: boolean;
  currencyListLoading: boolean;
  locations?: string[];
  projectAmenitiesData: any;
  projectName: any;
  projectCurrency: any;
  blockDeleteSuccess: any;
  unitInfo: any[];
  dropDown: any;
  projectsIdWithName?: Array<Project>;
  isProjectsIdWithNameLoading: boolean;
  excelUploadLoader: boolean;
  isProjectExcelUploadedListLoading: boolean;
  isProjectExportStatusLoading: boolean;
  excelColumnHeading?: any;
  excelUploadedList?: any;
  unitInfoFiltersPayload?: UnitInfoFilter;
  basicDetailsData: any;
  galleryData: any;
  unitData: any;
  projectExcelColumnHeading?: any;
  projectExcelUploadedList?: any;
  projectExportData?: any;
  projectWithGoogleLocation?: any[];
  isProjectWithGoogleLocationLoading: boolean;
  projectAssignments: any[];
};

const initialState: ProjectState = {
  totalCount: 0,
  itemsCount: 0,
  project: [],
  isProjectLoading: true,
  isProjectByIdLoading: true,
  assignedUserIds: [],
  filtersPayload: {
    pageNumber: 1,
    pageSize: 10,
    path: 'project',
    SearchText: null,
    properties: null,
  },
  allProjects: [],
  leadsCount: [],
  assignedProjects: [],
  projectTypeCount: [],
  unitInfoData: [],
  projectType: [],
  micrositeProject: [],
  micrositeAmentities: [],
  micrositeUnit: [],
  projectUnit: [],
  unitTypeData: [],
  builderDetails: [],
  blockData: [],
  locationsIsLoading: true,
  currencyListLoading: true,
  locations: [],
  projectAmenitiesData: [],
  projectName: [],
  projectCurrency: [],
  blockDeleteSuccess: [],
  unitInfo: [],
  dropDown: [],
  projectsIdWithName: [],
  isProjectsIdWithNameLoading: true,
  excelColumnHeading: {},
  projectExcelColumnHeading: {},
  excelUploadedList: [],
  basicDetailsData: [],
  galleryData: [],
  unitData: [],
  projectExcelUploadedList: [],
  excelUploadLoader: true,
  isProjectExcelUploadedListLoading: true,
  isProjectExportStatusLoading: true,
  projectExportData: [],
  projectWithGoogleLocation: [],
  isProjectWithGoogleLocationLoading: true,

  projectAssignments: [],
  unitInfoFiltersPayload: {
    ProjectId: null,
    Search: null,
    Area: null,
    AreaUnitId: null,
    pageNumber: 1,
    PageSize: 10,
    userSearch: null,
    path: 'project/unitinfos',
    CarpetArea: null,
    CarpetAreaUnitId: null,
    BuiltupArea: null,
    BuiltupAreaUnitId: null,
    SuperBuiltupArea: null,
    SuperBuiltupAreaUnitId: null,
    MaintenanceCost: null,
    PricePerUnit: null,
    TotalPrice: null,
    Currency: null,
    UnitType: [],
    UnitSubType: [],
    BHKs: [],
    BHKTypes: [],
    Facings: [],
    FurnishingStatuses: [],
    NoOfBalconies: [],
    NoOfBathrooms: [],
    NoOfLivingrooms: [],
    NoOfBedrooms: [],
    NoOfUtilites: [],
    NoOfKitchens: [],
    NoOfMaximumOccupants: [],
    OrderBy: [],
    maxPrice: null
  },
};

export function projectReducer(
  state: ProjectState = initialState,
  action: Action
): ProjectState {
  switch (action.type) {
    case ProjectActionTypes.FETCH_TEMP_PROJECT_LIST:
      return {
        ...state,
        isProjectLoading: true,
        leadsCount: [],
      };
    case ProjectActionTypes.FETCH_TEMP_PROJECT_LIST_SUCCESS:
      const response = (action as FetchProjectListSuccess).response;
      return {
        ...state,
        project: response.items,
        itemsCount: response.itemsCount,
        totalCount: response.totalCount,
        isProjectLoading: false,
      };
    case ProjectActionTypes.FETCH_TEMP_PROJECT_BY_ID:
      return {
        ...state,
        isProjectByIdLoading: true,
      };
    case ProjectActionTypes.FETCH_TEMP_PROJECT_BY_ID_SUCCESS:
      return {
        ...state,
        selectedProject: (action as FetchProjectByIdSuccess).selectedProject,
        isProjectByIdLoading: false,
      };
    case ProjectActionTypes.FETCH_ALL_ASSIGNED_PROJECTS_SUCCESS:
      return {
        ...state,
        assignedProjects: (action as FetchAssignedProjectsListSuccess).response
      };
    case ProjectActionTypes.FETCH_PROJECT_ASSIGNMENT_DETAILS_SUCCESS:

      return {
        ...state,
        assignedUserIds: (action as FetchAssignedProjectsListSuccess)
          .response,
      };

    case ProjectActionTypes.UPDATE_FILTERS_PAYLOAD:
      return {
        ...state,
        filtersPayload: {
          ...state.filtersPayload,
          ...(action as UpdateProjectsFiltersPayload).payload,
        },
      };
    case ProjectActionTypes.CLEAR_PROJECTS_FILTERSPAYLOAD:
      return {
        ...state,
        unitInfoData: null,
      };
    case ProjectActionTypes.FETCH_PROJECT_LEADS_COUNT_BY_IDS_SUCCESS:
      return {
        ...state,
        leadsCount: [
          ...state?.leadsCount,
          ...(action as FetchProjectLeadsCountByIdsSuccess).counts,
        ],
      };
    case ProjectActionTypes.FETCH_UNIT_INFO_BY_IDS_SUCCESS:
      const dataById = (action as FetchProjectDataByIdSuccess).response;
      return {
        ...state,
        unitInfoData: [dataById.data],
      };
    case ProjectActionTypes.FETCH_PROJECT_COUNT_SUCCESS:
      const countResp = (action as FetchProjectCountSuccess).response;
      return {
        ...state,
        projectTypeCount: countResp,
      };
    case ProjectActionTypes.FETCH_UNIT_TYPE_BY_ID_SUCCESS:
      const UnitdataById = (action as FetchUnitInfoSuccess).response;
      return {
        ...state,
        unitTypeData: UnitdataById,
      };
    case ProjectActionTypes.FETCH_BLOCK_BY_ID_SUCCESS:
      const blockDataById = (action as FetchBlockByIdSuccess).response;
      return {
        ...state,
        blockData: [blockDataById],
      };
    case ProjectActionTypes.FETCH_MICROSITE_PROJECT_SUCCESS:
      return {
        ...state,
        micrositeProject: (action as FetchMicrositeProjectSuccess).project,
      };
    case ProjectActionTypes.FETCH_MICROSITE_UNIT_SUCCESS:
      return {
        ...state,
        micrositeUnit: (action as FetchMicrositeUnitSuccess).project,
      };
    case ProjectActionTypes.FETCH_MICROSITE_AMENITIES_SUCCESS:
      return {
        ...state,
        micrositeAmentities: (action as FetchMicrositeAmenitiesSuccess).project,
      };
    case ProjectActionTypes.FETCH_PROJECT_UNIT_SUCCESS:
      return {
        ...state,
        projectUnit: (action as FetchProjectUnitSuccess).response,
      };
    case ProjectActionTypes.FETCH_BUILDER_DETAILS_SUCCESS:
      const builderDetails = (action as FetchBuilderDetailsSuccess).response;
      return {
        ...state,
        builderDetails: [builderDetails],
      };
    case ProjectActionTypes.FETCH_LOCATIONS:
      return {
        ...state,
        locationsIsLoading: true,
      };
    case ProjectActionTypes.FETCH_LOCATIONS_SUCCESS:
      return {
        ...state,
        locations: (action as FetchProjectLocationsSuccess).response,
        locationsIsLoading: false,
      };
    case ProjectActionTypes.FETCH_PROJECT_AMENITIES_BY_IDS_SUCCESS:
      const amenitiesById = (action as FetchProjectAmenitiesByIdsSuccess)
        .response;
      return {
        ...state,
        projectAmenitiesData: [amenitiesById.data],
      };
    case ProjectActionTypes.HAS_PROJECT_NAME_SUCCESS:
      return {
        ...state,
        projectName: (action as HasProjectNameSuccess).resp,
      };
    case ProjectActionTypes.FETCH_PROJECT_CURRENCY_LIST:
      return {
        ...state,
        currencyListLoading: true,
      };
    case ProjectActionTypes.FETCH_PROJECT_CURRENCY_LIST_SUCCESS:
      return {
        ...state,
        projectCurrency: (action as FetchProjectCurrencySuccess).response,
        currencyListLoading: false,
      };
    case ProjectActionTypes.UNIT_INFO_DELETE_SUCCESS:
      return {
        ...state,
        blockDeleteSuccess: (action as fetchCurrOffSetSuccess).response,
      };
    case ProjectActionTypes.FETCH_UNIT_INFO_BY_ID_SUCCESS:
      return {
        ...state,
        unitInfo: (action as FetchUnitInfoByIdSuccess).unitInf,
      };
    case ProjectActionTypes.FETCH_IMAGE_DROPDOWN_SUCCESS:
      return {
        ...state,
        dropDown: (action as fetchImageDropDownSuccess).response,
      };
    case ProjectActionTypes.FETCH_PROJECT_ID_WITH_NAME:
      return {
        ...state,
        isProjectsIdWithNameLoading: true,
      };
    case ProjectActionTypes.FETCH_PROJECT_ID_WITH_NAME_SUCCESS:
      return {
        ...state,
        projectsIdWithName: (action as FetchProjectIDWithNameSuccess).projectList,
        isProjectsIdWithNameLoading: false,
      };
    case ProjectActionTypes.PROJECT_UNIT_EXCEL_UPLOAD_SUCCESS:
      return {
        ...state,
        excelColumnHeading: (action as ProjectUnitExcelUploadSuccess).resp,
      };
    case ProjectActionTypes.FETCH_EXCEL_UPLOADED_LIST:
      return {
        ...state,
        excelUploadLoader: true,
      };
    case ProjectActionTypes.FETCH_EXCEL_UPLOADED_LIST_SUCCESS:
      return {
        ...state,
        excelUploadedList: (action as FetchProjectUnitExcelUploadedSuccess).response,
        excelUploadLoader: false,
      };
    case ProjectActionTypes.UPDATE_UNITINFO_FILTER_PAYLOAD:
      return {
        ...state,
        unitInfoFiltersPayload: (action as UpdateUnitInfoFilterPayload).filter,
      };
    case ProjectActionTypes.FETCH_PROJECT_BASICDETAILS_BY_IDS_SUCCESS:
      const basicById = (action as FetchProjectBasicDetailsByIdSuccess).response;
      return {
        ...state,
        basicDetailsData: [basicById.data],
      };
    case ProjectActionTypes.FETCH_PROJECT_GALLERY_BY_IDS_SUCCESS:
      const galleryById = (action as FetchProjectGalleryByIdSuccess).response;
      return {
        ...state,
        galleryData: [galleryById.data],
      };
    case ProjectActionTypes.FETCH_UNIT_BY_IDS_SUCCESS:
      const unitById = (action as FetchUnitByIdSuccess).response;
      return {
        ...state,
        unitData: [unitById.data],
      };
    case ProjectActionTypes.PROJECT_EXCEL_UPLOAD_SUCCESS:
      return {
        ...state,
        projectExcelColumnHeading: (action as ProjectExcelUploadSuccess).resp,
      };
    case ProjectActionTypes.FETCH_PROJECT_EXCEL_UPLOADED_LIST:
      return {
        ...state,
        isProjectExcelUploadedListLoading: true,
      };
    case ProjectActionTypes.FETCH_PROJECT_EXCEL_UPLOADED_LIST_SUCCESS:
      return {
        ...state,
        projectExcelUploadedList: (action as FetchProjectExcelUploadedSuccess)
          .response,
        isProjectExcelUploadedListLoading: false,
      };

    case ProjectActionTypes.FETCH_EXPORT_PROJECT_STATUS:
      return {
        ...state,
        isProjectExportStatusLoading: true,
      };
    case ProjectActionTypes.FETCH_EXPORT_PROJECT_STATUS_SUCCESS:
      return {
        ...state,
        projectExportData: (action as FetchExportProjectStatusSuccess)
          .response,
        isProjectExportStatusLoading: false,
      };
    case ProjectActionTypes.FETCH_PROJECT_WITH_GOOGLE_LOCATION:
      return {
        ...state,
        isProjectWithGoogleLocationLoading: true,
      };
    case ProjectActionTypes.FETCH_PROJECT_WITH_GOOGLE_LOCATION_SUCCESS:
      return {
        ...state,
        projectWithGoogleLocation: (action as FetchProjectWithGoogleLocationSuccess)
          .responce,
        isProjectWithGoogleLocationLoading: false,
      };
    case ProjectActionTypes.FETCH_PROJECT_ASSIGNMENTS_SUCCESS:
      return {
        ...state,
        projectAssignments: (action as FetchProjectAssignmentsSuccess).response,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.project;

export const getProject = createSelector(
  selectFeature,
  (state: ProjectState) => state.project
);

export const getProjectIsLoading = createSelector(
  selectFeature,
  (state: ProjectState) => state.isProjectLoading
);

export const getProjectTotalCount = createSelector(
  selectFeature,
  (state: ProjectState) => {
    return {
      totalCount: state.totalCount,
      itemsCount: state.itemsCount,
    };
  }
);

export const getFiltersPayload = createSelector(
  selectFeature,
  (state: ProjectState) => {
    return state.filtersPayload;
  }
);

export const getProjectCount = createSelector(
  selectFeature,
  (state: ProjectState) => {
    return state.projectCount;
  }
);

export const getAssignedProjects = createSelector(
  selectFeature,
  (state: ProjectState) => state.assignedProjects
);

export const getAssignmentDetails = createSelector(
  selectFeature,
  (state: ProjectState) => state.assignedUserIds
);

export const getProjectLeadsCount = createSelector(
  selectFeature,
  (state: ProjectState) => state.leadsCount
);

export const getProjectAllCount = createSelector(
  selectFeature,
  (state: ProjectState) => state.projectTypeCount
);

export const getProjectDataById = createSelector(
  selectFeature,
  (state: ProjectState) => state.unitInfoData
);

export const getUnitInfoData = createSelector(
  selectFeature,
  (state: ProjectState) => state.unitTypeData
);

export const getMicrositeProject = createSelector(
  selectFeature,
  (state: ProjectState) => state.micrositeProject
);

export const getMicrositeUnit = createSelector(
  selectFeature,
  (state: ProjectState) => state.micrositeUnit
);

export const getMicrositeAmentities = createSelector(
  selectFeature,
  (state: ProjectState) => state.micrositeAmentities
);

export const getProjectUnit = createSelector(
  selectFeature,
  (state: ProjectState) => state.projectUnit
  // (state: ProjectState) => state.unitTypeData
);

export const getBuilderDetails = createSelector(
  selectFeature,
  (state: ProjectState) => state.builderDetails
);

export const getBlockData = createSelector(
  selectFeature,
  (state: ProjectState) => state.blockData
);

export const getLocationsIsLoading = createSelector(
  selectFeature,
  (state: ProjectState) => state.locationsIsLoading
);

export const getProjectsLocations = createSelector(
  selectFeature,
  (state: ProjectState) => state.locations
);

export const getProjectAmenitiesData = createSelector(
  selectFeature,
  (state: ProjectState) => state.projectAmenitiesData
);

export const doesProjectExists = createSelector(
  selectFeature,
  (state: ProjectState) => state.projectName
);

export const getCurrOffSet = createSelector(
  selectFeature,
  (state: ProjectState) => state.blockDeleteSuccess
);

export const getProjectCurrencyList = createSelector(
  selectFeature,
  (state: ProjectState) => state.projectCurrency
);

export const getProjectCurrencyListIsLoading = createSelector(
  selectFeature,
  (state: ProjectState) => state.currencyListLoading
);
export const getUnitInfo = createSelector(
  selectFeature,
  (state: ProjectState) => state.unitInfo
);

export const getSelectedProjectById = createSelector(
  selectFeature,
  (state: ProjectState) => state.selectedProject
);

export const getIsProjectByIdLoading = createSelector(
  selectFeature,
  (state: ProjectState) => state.isProjectByIdLoading
);


export const getProjectGalleryDropDown = createSelector(
  selectFeature,
  (state: ProjectState) => state.dropDown
);

export const getProjectsIDWithName = createSelector(
  selectFeature,
  (state: ProjectState) => state.projectsIdWithName
);

export const getProjectsIDWithNameIsLoading = createSelector(
  selectFeature,
  (state: ProjectState) => state.isProjectsIdWithNameLoading
);

export const getColumnHeadings = createSelector(
  selectFeature,
  (state: ProjectState) => {
    return state.excelColumnHeading;
  }
);

export const getProjectUnitExcelUploadedList = createSelector(
  selectFeature,
  (state: ProjectState) => state.excelUploadedList
);
export const getProjectUnitExcelUploadedLoader = createSelector(
  selectFeature,
  (state: ProjectState) => state.excelUploadLoader
);
export const getUnitInfoFiltersPayload = createSelector(
  selectFeature,
  (state: ProjectState) => {
    state.unitInfoFiltersPayload;
  }
);
export const getBasicDetailsById = createSelector(
  selectFeature,
  (state: ProjectState) => state.basicDetailsData
);
export const getProjectGalleryById = createSelector(
  selectFeature,
  (state: ProjectState) => state.galleryData
);
export const getUnitById = createSelector(
  selectFeature,
  (state: ProjectState) => state.unitData
);

export const getProjectColumnHeadings = createSelector(
  selectFeature,
  (state: ProjectState) => {
    return state.projectExcelColumnHeading;
  }
);

export const getProjectExcelUploadedList = createSelector(
  selectFeature,
  (state: ProjectState) => state.projectExcelUploadedList
);

export const getProjectExcelUploadedListIsLoading = createSelector(
  selectFeature,
  (state: ProjectState) => state.isProjectExcelUploadedListLoading
);

export const getProjectExportData = createSelector(
  selectFeature,
  (state: ProjectState) => state.projectExportData
);

export const getProjectExportDataLoading = createSelector(
  selectFeature,
  (state: ProjectState) => state.isProjectExportStatusLoading
);

export const getProjectWithGoogleLocation = createSelector(
  selectFeature,
  (state: ProjectState) => state.projectWithGoogleLocation
);

export const getProjectWithGoogleLocationIsLoading = createSelector(
  selectFeature,
  (state: ProjectState) => state.isProjectWithGoogleLocationLoading
);

export const getProjectAssignments = createSelector(
  selectFeature,
  (state: ProjectState) => state.projectAssignments
)
