import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { of, throwError } from 'rxjs';
import { catchError, map, mergeMap, switchMap } from 'rxjs/operators';

import { Router } from '@angular/router';
import { CloseModal, OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import {
  AddMicrositeLead,
  AddMicrositeLeadSuccess,
  AddProperty,
  AddPropertySuccess,
  AddWaterMark,
  AddWaterMarkSuccess,
  ArchiveProperty,
  BulkReassignProperty,
  BulkReassignPropertySuccess,
  CloneProperty,
  ClonePropertySuccess,
  DeleteProperty,
  ExportProperty,
  ExportPropertySuccess,
  FetchActivePropertyId,
  FetchArchivedPropertyList,
  FetchArchivedPropertyListSuccess,
  FetchBrochureList,
  FetchBrochureListSuccess,
  FetchExportPropertyStatus,
  FetchExportPropertyStatusSuccess,
  FetchGalleryDropdownData,
  FetchGalleryDropdownDataSuccess,
  FetchLocationList,
  FetchLocationListSuccess,
  FetchMSSimilarProperties,
  FetchMSSimilarPropertiesSuccess,
  FetchMatchingLeadsList,
  FetchMatchingLeadsListSuccess,
  FetchMicrositeProperty,
  FetchMicrositePropertySuccess,
  FetchMicrositeUserDetails,
  FetchMicrositeUserDetailsSuccess,
  FetchOwnerNames,
  FetchOwnerNamesSuccess,
  FetchPropertyAssignments,
  FetchPropertyAssignmentsSuccess,
  FetchPropertyById,
  FetchPropertyByIdSuccess,
  FetchPropertyCount,
  FetchPropertyCountSuccess,
  FetchPropertyCurrency,
  FetchPropertyCurrencySuccess,
  FetchPropertyExcelUploadedList,
  FetchPropertyExcelUploadedSuccess,
  FetchPropertyExport,
  FetchPropertyExportSuccess,
  FetchPropertyLeadsCountByIds,
  FetchPropertyLeadsCountByIdsSuccess,
  FetchPropertyList,
  FetchPropertyListSuccess,
  FetchPropertyModifiedOn,
  FetchPropertyModifiedOnSuccess,
  FetchPropertyWithGoogleLocation,
  FetchPropertyWithGoogleLocationSuccess,
  FetchPropertyWithIdNameList,
  FetchPropertyWithIdNameListSuccess,
  IncreaseShareCount,
  PropertyActionTypes,
  PropertyExcelUpload,
  PropertyExcelUploadSuccess,
  UpdateFilterPayload,
  UpdateGallery,
  UpdateProperty,
  UpdatePropertyStatus,
  UpdatePropertySuccess,
  UpdateUserAssignment,
  UploadBrochure,
  UploadBrochureSuccess,
  UploadMappedColumns
} from 'src/app/reducers/property/property.actions';
import { PropertyService } from 'src/app/services/controllers/properties.service';
import { CommonService } from 'src/app/services/shared/common.service';
import { FetchAllListing } from '../listing-site/listing-site.actions';
import { getFiltersPayload } from './property.reducer';

@Injectable()
export class PropertyEffects {
  getPropertyList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_PROPERTY_LIST),
      map((action: FetchPropertyList) => action),
      switchMap((data: any) => {
        if (this.router.url.includes('manage-properties') || this.router.url.includes('property')) {
          let filterPayload: any;
          this._store.select(getFiltersPayload).subscribe((payload: any) => {
            filterPayload = payload;
          });
          this._store.dispatch(new FetchPropertyCount(filterPayload));
          return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
            switchMap((resp: any) => {
              if (resp.succeeded && resp?.items?.length > 0) {
                if (data?.canFetchLeadsCount) {
                  const propertyIds: string[] | undefined = resp?.items?.map(
                    (property: any) => property?.id
                  );
                  if (propertyIds && propertyIds.length > 0) {
                    this._store.dispatch(new FetchPropertyLeadsCountByIds(propertyIds));
                  }
                }
                return of(new FetchPropertyListSuccess(resp));
              } else {
                const previousPageNumber = filterPayload?.pageNumber || 1;
                const retryPayload = {
                  ...filterPayload,
                  pageNumber: previousPageNumber > 1 ? previousPageNumber - 1 : 1,
                  pageSize: filterPayload?.pageSize || 10,
                };
                this._store.dispatch(new UpdateFilterPayload(retryPayload));
                return this.commonService.getModuleListByAdvFilter(retryPayload).pipe(
                  map((retryResp: any) => {
                    if (retryResp.succeeded && data?.canFetchLeadsCount) {
                      const propertyIds: string[] | undefined =
                        retryResp?.items?.map((property: any) => property?.id);
                      if (propertyIds && propertyIds.length > 0) {
                        this._store.dispatch(
                          new FetchPropertyLeadsCountByIds(propertyIds)
                        );
                      }
                    }
                    return new FetchPropertyListSuccess(retryResp);
                  }),
                  catchError((err) => of(new OnError(err)))
                );
              }
            }),
            catchError((err) => of(new OnError(err)))
          );
        } else {
          return of(new FetchAllListing());
        }
      })
    )
  );

  getPropertyLeadsCountById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_PROPERTY_LEADS_COUNT_BY_IDS),
      switchMap((action: FetchPropertyLeadsCountByIds) => {
        let filterPayload = { ids: action?.ids, path: 'property/lead-count' };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp?.succeeded) {
              return new FetchPropertyLeadsCountByIdsSuccess(resp?.data);
            }
            return new FetchPropertyLeadsCountByIdsSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getPropertyById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_PROPERTY_BY_ID),
      map((action: FetchPropertyById) => action),
      switchMap((action: FetchPropertyById) => {
        return this.api.get(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchPropertyByIdSuccess(resp.data);
            }
            return new FetchPropertyByIdSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addProperty$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.ADD_PROPERTY),
      map((action: AddProperty) => action.payload),
      switchMap((payload) =>
        this.api.add(payload).pipe(
          mergeMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Property added successfully.');
              return [
                new FetchPropertyList(true),
                new FetchActivePropertyId(resp.data),
                new FetchPropertyById(resp.data?.id),
                new AddPropertySuccess(resp.data)
              ];
            }
            return [new FetchPropertyListSuccess()];
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  updateProperty$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.UPDATE_PROPERTY),
      map((action: UpdateProperty) => action),
      switchMap((action: UpdateProperty) =>
        this.api.update(action.payload, action.id).pipe(
          switchMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Property updated successfully.`);
              return of(
                new FetchPropertyList(true),
                new UpdatePropertySuccess(resp.data)
              );
            }
            return of(new FetchPropertyListSuccess());
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  updatePropertyStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.UPDATE_PROPERTY_STATUS),
      map((action: UpdatePropertyStatus) => action),
      switchMap((action: UpdatePropertyStatus) => {
        return this.api.updateStatus(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Assignment details updated successfully.`
              );
              return new FetchPropertyList(true);
            }
            return new FetchPropertyListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  ReassignProperty$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.UPDATE_USER_ASSIGNMENT),
      map((action: UpdateUserAssignment) => action),
      switchMap((action: UpdateUserAssignment) => {
        return this.api.reassignProperty(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Assignment details updated successfully.`
              );
              return new FetchPropertyList(true);
            }
            return new FetchPropertyListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkReassignProperty$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.BULK_REASSIGN_PROPERTY),
      map((action: BulkReassignProperty) => action),
      switchMap((action: BulkReassignProperty) => {
        return this.api.bulkReassignProperty(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._store.dispatch(new FetchPropertyList());
              this._notificationService.success(
                `Properties Assigned successfully.`
              );
              return new BulkReassignPropertySuccess(resp);
            }
            return new BulkReassignPropertySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  archiveProperty$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.ARCHIVE_PROPERTY),
      map((action: ArchiveProperty) => action),
      switchMap((action: ArchiveProperty) => {
        return this.api.archive(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (this.router.url.includes('manage-properties')) {
                this._store.dispatch(new FetchArchivedPropertyList());
              }
              return new FetchPropertyList(true);
            }
            return new FetchPropertyListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  increasePropertyShareCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.INCREASE_SHARE_COUNT),
      map((action: IncreaseShareCount) => action),
      switchMap((action: IncreaseShareCount) => {
        return this.api.increaseShareCount(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Property shared successfully.`
              );
              return new FetchPropertyList(true);
            }
            return new FetchPropertyListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteProperty$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.DELETE_PROPERTY),
      map((action: DeleteProperty) => action),
      switchMap((action: DeleteProperty) => {
        return this.api.delete(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Property deleted successfully.`
              );
              return new FetchPropertyList(true);
            }
            return new FetchPropertyListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLocationList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_LOCATION_LIST),
      map((action: FetchLocationList) => action),
      switchMap((data: any) => {
        return this.api.getLocationList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLocationListSuccess(resp.items);
            }
            return new FetchLocationListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  uploadPropertyBrochures$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.UPLOAD_BROCHURE),
      switchMap((action: UploadBrochure) => {
        return this.api.uploadPropertyBrochures(action.id, action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Property Brochure Updated Successfully'
              );
              // this._store.dispatch(new FetchPropertyById(action.id));
              this._store.dispatch(new FetchBrochureList(action.id));
              this._store.dispatch(new FetchPropertyList());
              return new UploadBrochureSuccess(action.payload.documents);
            }
            return new UploadBrochureSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getPropertyBrochures$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_BROCHURE_LIST),
      map((action: FetchBrochureList) => action),
      switchMap((data: any) => {
        return this.api.getPropertyBrochures(data.id).pipe(
          map((resp: any) => {
            return new FetchBrochureListSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getGalleryDropdownData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_GALLERY_DROPDOWN_DATA),
      map((action: FetchGalleryDropdownData) => action),
      switchMap((action: FetchGalleryDropdownData) => {
        return this.api.getGalleryDropdownData().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchGalleryDropdownDataSuccess(resp.items);
            }
            return new FetchGalleryDropdownDataSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateGallery$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.UPDATE_GALLERY),
      map((action: UpdateGallery) => action),
      switchMap((action: UpdateGallery) => {
        return this.api.updateGallery(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchPropertyListSuccess();
            }
            return new FetchPropertyListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getMatchingLeadList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_MATCHING_LEADS),
      map((action: FetchMatchingLeadsList) => action),
      switchMap((data: any) =>
        this.commonService.getModuleList(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchMatchingLeadsListSuccess(resp);
            }
            return new FetchMatchingLeadsListSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getArchivedPropertyList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_ARCHIVED_PROPERTY_LIST),
      map((action: FetchArchivedPropertyList) => action),
      switchMap((data: any) =>
        this.commonService.getModuleList(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              const propertyIds: string[] | undefined = resp?.items?.map(
                (property: any) => property?.id
              );
              if (propertyIds && propertyIds.length > 0) {
                this._store.dispatch(new FetchPropertyLeadsCountByIds(propertyIds));
              }
              return new FetchArchivedPropertyListSuccess(resp);
            }
            return new FetchArchivedPropertyListSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  addBulkLeadExcel$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.PROPERTY_EXCEL_UPLOAD),
      switchMap((action: PropertyExcelUpload) => {
        return this.api.uploadExcel(action.file).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Property Excel uploaded Successfully'
              );
              return new PropertyExcelUploadSuccess(resp.data);
            } else {
              this._store.dispatch(new CloseModal());
              this._notificationService.warn(`${resp.message}`);
              return new FetchPropertyList(true);
            }
          }),
          catchError((err: any) => {
            throwError(err);
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  uploadMappedColumns$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.UPLOAD_MAPPED_COLUMNS),
      switchMap((action: UploadMappedColumns) => {
        return this.api.uploadMappedColumns(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (resp.data) {
                if (resp.data?.excelUrl) {
                  const dataCount = resp.message?.DataCount || '';
                  this._notificationService.success(
                    `${dataCount} Invalid Data Not Uploaded`
                  );
                  return new PropertyExcelUploadSuccess(resp.data);
                } else {
                  this._notificationService.success(
                    'Excel Uploaded Successfully'
                  );
                  return new PropertyExcelUploadSuccess(resp.data);
                }
              } else {
                this._notificationService.error(resp.message);
              }
              return new FetchPropertyList(true);
            }
            return new FetchPropertyListSuccess();
          }),
          catchError((err: any) => {
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getExcelUploadedList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_EXCEL_UPLOADED_LIST),
      map((action: FetchPropertyExcelUploadedList) => action),
      switchMap((data: any) => {
        return this.api
          .getExcelUploadedList(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchPropertyExcelUploadedSuccess(resp);
              }
              return new FetchPropertyExcelUploadedSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  getPropertyExport$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_PROPERTY_EXPORT),
      map((action: FetchPropertyExport) => action.payload),
      switchMap((data: any) => {
        return this.commonService.getModuleList(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchPropertyExportSuccess(resp.data);
            }
            return new FetchPropertyExportSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  exportProperty$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.EXPORT_PROPERTY),
      switchMap((action: ExportProperty) => {
        return this.api.exportProperty(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Property are being exported in excel format`
              );
              return new ExportPropertySuccess(resp);
            }
            return new ExportPropertySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getExportPropertyStatusList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_EXPORT_PROPERTY_STATUS),
      map((action: FetchExportPropertyStatus) => action),
      switchMap((data: any) => {
        return this.api
          .getExportPropertyStatus(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchExportPropertyStatusSuccess(resp);
              }
              return new FetchExportPropertyStatusSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  getOwnerNames$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_OWNER_NAMES),
      map((action: FetchOwnerNames) => action),
      switchMap((data: any) => {
        return this.api.getOwnerNames().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchOwnerNamesSuccess(resp.data);
            }
            return new FetchOwnerNamesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getMicrositeProperty$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_MICROSITE_PROPERTY),
      switchMap((action: FetchMicrositeProperty) => {
        const apiCall = window.location.pathname.includes('listing')
          ? this.api.getMicrositeListing(action.payload)
          : this.api.getMicrositeProperty(action.payload);
        return apiCall.pipe(
          map((resp: any) =>
            resp.succeeded
              ? new FetchMicrositePropertySuccess(resp.data)
              : new FetchMicrositePropertySuccess()
          ),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );
  getMicrositeSimilarProperties$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_MICROSITE_SIMILAR_PROPERTIES),
      switchMap((action: FetchMSSimilarProperties) => {
        return this.api.getMicrositeSimilarProperties(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchMSSimilarPropertiesSuccess(resp.items);
            }
            return new FetchMSSimilarPropertiesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addMicrositeLead$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(PropertyActionTypes.ADD_MS_LEAD),
        map((action: AddMicrositeLead) => action),
        switchMap((action: AddMicrositeLead) => {
          return this.api.addMicrositeLead(action.payload).pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                this._notificationService.success('Lead Added Successfully');
                this._store.dispatch(
                  new AddMicrositeLeadSuccess(resp.succeeded)
                );
              }
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  getMicrositeUser$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_MICROSITE_USER_DETAILS),
      switchMap((action: FetchMicrositeUserDetails) => {
        return this.api.getMicrositeUser(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchMicrositeUserDetailsSuccess(resp.data);
            }
            return new FetchMicrositeUserDetailsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getPropertyCurrency$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_PROPERTY_CURRENCY_LIST),
      map((action: FetchPropertyCurrency) => action),
      switchMap((data: any) => {
        return this.api.getCurrency().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchPropertyCurrencySuccess(resp.data);
            }
            return new FetchPropertyCurrencySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getPropertyWithIdNameList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_PROPERTY_WITH_ID_NAME_LIST),
      map((action: FetchPropertyWithIdNameList) => action),
      switchMap((data: any) => {
        return this.api.getPropertyWithIdNameList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchPropertyWithIdNameListSuccess(resp.data);
            }
            return new FetchPropertyWithIdNameListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addWaterMark$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.ADD_WATER_MARK),
      map((action: AddWaterMark) => action),
      switchMap((action: AddWaterMark) => {
        return this.api.addWaterMark(action.payload).pipe(
          map((resp: any) => {
            if (resp.imageUrl?.length) {
              return new AddWaterMarkSuccess(resp.imageUrl);
            }
            return new AddWaterMarkSuccess(null);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  fetchPropertyCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_PROPERTY_COUNT),
      map((action: FetchPropertyCount) => action),
      switchMap((action: FetchPropertyCount) => {
        return this.api.fetchPropertyCount(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchPropertyCountSuccess(resp?.data);
            }
            return new FetchPropertyCountSuccess();

          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  fetchPropertyAssignments$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_PROPERTY_ASSIGNMENTS),
      map((action: FetchPropertyAssignments) => action),
      switchMap((action: FetchPropertyAssignments) => {
        return this.api.getPropertyAssignments(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchPropertyAssignmentsSuccess(resp.data);
            }
            return new FetchPropertyAssignmentsSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  cloneProperty$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.CLONE_PROPERTY),
      map((action: CloneProperty) => action),
      switchMap((action: CloneProperty) => {
        return this.api.cloneProperty(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Property Cloned Successfully');
              return new ClonePropertySuccess(resp?.data), new FetchAllListing();
            }
            return new ClonePropertySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getPropertyModifiedOn$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_PROPERTY_MODIFIED_ON),
      switchMap((action: FetchPropertyModifiedOn) => {
        let filterPayload = { ...action.payload, path: 'property/modifiedon' };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp?.succeeded) {
              return new FetchPropertyModifiedOnSuccess(resp?.data);
            }
            return new FetchPropertyModifiedOnSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getPropertyWithGoogleLocation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PropertyActionTypes.FETCH_PROPERTY_WITH_GOOGLE_LOCATION),
      switchMap((action: FetchPropertyWithGoogleLocation) => {
        return this.api.getGoogleLocationProperties().pipe(
          map((resp: any) => {
            if (resp?.succeeded) {
              return new FetchPropertyWithGoogleLocationSuccess(resp?.data);
            }
            return new FetchPropertyWithGoogleLocationSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private api: PropertyService,
    private _notificationService: NotificationsService,
    private _store: Store<AppState>,
    private commonService: CommonService,
    public router: Router,
  ) { }
}
