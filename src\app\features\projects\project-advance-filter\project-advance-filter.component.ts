import { Component, EventEmitter, OnInit, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { FACING, PROJECT_CURRENT_STATUS, PROJECT_STATUS } from 'src/app/app.constants';
import { Facing } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { changeCalendar, formatBudget, getTimeZoneDate, onlyNumbers, onlyNumbersWithDecimal, onPickerOpened, setTimeZoneDate } from 'src/app/core/utils/common.util';
import { FetchAllAmenities } from 'src/app/reducers/amenities-attributes/amenities-attributes.action';
import { getAllAmenities, getAmenitiesLoading } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchBuilderDetails, FetchProjectCurrency, FetchProjectLocations, UpdateProjectsFiltersPayload } from 'src/app/reducers/project/project.action';
import { getBuilderDetails, getLocationsIsLoading, getProjectCurrencyList, getProjectsLocations } from 'src/app/reducers/project/project.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

export interface PossessionFilter {
  displayName: string;
  value: string;
}

@Component({
  selector: 'project-advance-filter',
  templateUrl: './project-advance-filter.component.html',
})
export class ProjectAdvanceFilterComponent implements OnInit {
  @ViewChild('possessionFilter') possessionFilter: any;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  applyAdvancedFilter: (data: any) => void;
  onClearAllFilters: (data: any) => void;
  formatBudget = formatBudget;
  onlyNumbers = onlyNumbers;
  onlyNumbersWithDecimal = onlyNumbersWithDecimal;
  onPickerOpened = onPickerOpened;
  appliedFilter: any = {};
  filterPayload: any = {};
  AmenityList: any;
  allAmenityList: any;
  isAllAmenityListLoading: boolean = true;
  localityList: any = [];
  isLocalityListLoading: boolean = true;
  areaSizeUnits: any[] = [];
  currentDate: Date = new Date();
  projectCurrency: any[] = [];
  facing: any = [];
  date: any = new Date();
  userData: any;
  projectSubType: any;
  builderDetails: any;
  defaultCurrency: string = 'INR';
  isProjectCurrencyLoading: boolean = false;
  minMaxLeadValidation: boolean = true;
  minMaxDataValidation: boolean = true;
  selectedStatuses: number[] = [];
  projectTypeList: Array<any> = JSON.parse(
    localStorage.getItem('projectType') || '[]'
  );
  projectCurrentStatusList: Array<string> = Object.values(
    PROJECT_CURRENT_STATUS
  ).slice(0, 2);
  projectStatusList: Array<string> = Object.values(PROJECT_STATUS).slice(1, 8);
  carpetAreaValidations: boolean = true;
  minBudgetValidation: boolean = true;
  maxBudgetValidation: boolean = true;
  constructor(public _store: Store<AppState>, public modalRef: BsModalRef) { }

  ngOnInit(): void {
    this.appliedFilter = { ...this.filterPayload };
    if (this.appliedFilter?.currentStatus == 0) {
      this.appliedFilter.currentStatus = 'Available';
    }
    if (this.appliedFilter?.currentStatus == 1) {
      this.appliedFilter.currentStatus = 'Unavailable';
    }
    this.appliedFilter.facings = this.appliedFilter?.facings?.map(
      (item: any) => Facing[item]
    );
    this.initializeFacing();
    this.initializeProjectSubType();
    this.setupSubscriptions();
    this.fetchInitialData();
    this.initializeFilters();
  }

  private initializeFilters(): void {
    if (this.appliedFilter?.ProjectStatuses) {
      this.selectedStatuses = [...this.appliedFilter.ProjectStatuses];
    }

    // Make sure possession filter values are properly set
    if (this.appliedFilter.Possesion !== undefined && this.appliedFilter.PossesionType === undefined) {
      this.appliedFilter.PossesionType = this.appliedFilter.Possesion;
      delete this.appliedFilter.Possesion;
    }
  }

  private initializeFacing(): void {
    this.facing = FACING?.map((fac: any) => fac.displayName);
  }

  private initializeProjectSubType(): void {
    this.projectSubType = [];
    this.projectTypeList?.forEach((item: any) => {
      item?.childTypes?.forEach((subType: any) => {
        this.projectSubType.push({
          ...subType,
          projectType: item?.displayName,
        });
      });
    });
    this.projectSubType?.sort((a: any, b: any) =>
      a.displayName.localeCompare(b.displayName)
    );
  }

  private setupSubscriptions(): void {
    this.setupAmenitiesSubscription();
    this.setupLocationsSubscription();
    this.setupCurrencySubscription();
    this.setupUserSubscription();
    this.setupBuilderSubscription();
    this.setupGlobalSettingsSubscription();
  }

  private setupAmenitiesSubscription(): void {
    this._store.select(getAllAmenities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const allAmenities = data.flatMap((category: any) => category.amenities);
        this.allAmenityList = allAmenities
        this.allAmenityList?.sort((a: any, b: any) =>
          a.amenityDisplayName.localeCompare(b.amenityDisplayName)
        );
      });

    this._store.select(getAmenitiesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAllAmenityListLoading = isLoading;
      });
  }

  private setupLocationsSubscription(): void {
    this._store
      .select(getProjectsLocations)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.localityList = data
          .filter(Boolean)
          .sort((a: any, b: any) => a.localeCompare(b))
          .filter(
            (item: any, index: any, self: any) =>
              self.findIndex(
                (t: any) => t.toLowerCase() === item.toLowerCase()
              ) === index
          );
      });

    this._store
      .select(getLocationsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isLocalityListLoading = isLoading;
      });
  }

  private setupCurrencySubscription(): void {
    this._store
      .select(getProjectCurrencyList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectCurrency = data;
      });
  }

  private setupUserSubscription(): void {
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });
  }

  private setupBuilderSubscription(): void {
    this._store
      .select(getBuilderDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.builderDetails = data?.[0]?.data || [];
        this.builderDetails = [
          ...new Set(
            this.builderDetails
              .sort((a: any, b: any) => a.name.localeCompare(b.name))
              .map((builder: any) => builder.name)
          ),
        ].map((name) =>
          this.builderDetails.find((builder: any) => builder.name === name)
        );
      });
  }

  private setupGlobalSettingsSubscription(): void {
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : 'INR';
      });
  }

  private fetchInitialData(): void {
    this._store.dispatch(new FetchAllAmenities());
    this._store.dispatch(new FetchProjectLocations());
    this._store.dispatch(new FetchProjectCurrency());
    this._store.dispatch(new FetchBuilderDetails());
  }

  DateFormat(value: Date): string {
    this.date = setTimeZoneDate(
      value,
      this.userData?.timeZoneInfo?.baseUTcOffset
    );
    return getTimeZoneDate(
      this.date,
      this.userData?.timeZoneInfo?.baseUTcOffset,
      'dayMonthYear'
    );
  }

  projectTypeData(data: any): void {
    if (!data) {
      this.resetProjectSubType();
      this.appliedFilter.ProjectType = 'All';
      this.initializeProjectSubType()
      return;
    }
    this.projectSubType = this.projectTypeList.find((item: any) => item.id === data.id)?.childTypes?.map((type: any) => ({ ...type, projectType: data.displayName }));
    const newProjectType = data.displayName;
    this.appliedFilter.ProjectType = newProjectType;
  }

  statusFilterApply(index: number): void {
    const adjustedIndex = index + 1;
    const selectedIndex = this.selectedStatuses.indexOf(adjustedIndex);
    if (selectedIndex === -1) {
      this.selectedStatuses.push(adjustedIndex);
    } else {
      this.selectedStatuses.splice(selectedIndex, 1);
    }
    this.appliedFilter.ProjectStatuses = [...this.selectedStatuses];
  }

  changeStatusColor(status: string, index: number): { [key: string]: string } {
    const adjustedIndex = index + 1;
    const colorMap: { [key: string]: string } = {
      Ongoing: '#30bbda',
      'Ready to move': '#50bea7',
      Upcoming: '#fca400',
      New: '#57D2FF',
      Resale: '#DCD427',
      PreLaunch: '#FB9A64',
      Launch: '#769933',
    };

    if (this.selectedStatuses.includes(adjustedIndex)) {
      return {
        'background-color': colorMap[status] || '#ccc',
        border: `1px solid ${colorMap[status] || '#ccc'}`,
        color: '#FFF',
      };
    }
    return {
      'background-color': '#fff',
      border: '1px solid #ccc',
      color: '#999',
    };
  }

  onPossessionFilterChange(event: { possessionType: number | null; fromDate: string | null; toDate: string | null }): void {
    this.appliedFilter.PossesionType = event.possessionType;
    this.appliedFilter.FromPossesionDate = event.fromDate;
    this.appliedFilter.ToPossesionDate = event.toDate;
  }

  reset(): void {
    this.appliedFilter = {};
    this.selectedStatuses = [];
    this.appliedFilter.Currency = this.defaultCurrency;

    this.appliedFilter.PossesionType = null;
    this.appliedFilter.FromPossesionDate = null;
    this.appliedFilter.ToPossesionDate = null;
    if (this.possessionFilter) {
      this.possessionFilter.reset();
    }

    this.initializeProjectSubType();
    this.onClearAllFilters('');
  }

  applyFilter(): void {
    if (!this.minBudgetValidation || !this.maxBudgetValidation || !this.carpetAreaValidations ||
      !this.minMaxLeadValidation || !this.minMaxDataValidation) {
      return;
    }

    // Make sure we're using the correct property names before dispatching
    if (this.appliedFilter.Possesion !== undefined && this.appliedFilter.PossesionType === undefined) {
      this.appliedFilter.PossesionType = this.appliedFilter.Possesion;
      delete this.appliedFilter.Possesion;
    }

    this._store.dispatch(new UpdateProjectsFiltersPayload(this.appliedFilter));
    this.applyAdvancedFilter(this.appliedFilter);
    this.modalRef.hide();
  }

  private resetProjectSubType(): void {
    this.projectSubType = [];
    this.projectTypeList?.forEach((item: any) => {
      item?.childTypes?.forEach((subType: any) => {
        this.projectSubType.push(subType);
      });
    });
    this.projectSubType?.sort((a: any, b: any) =>
      a.displayName.localeCompare(b.displayName)
    );
  }

  validateCarpetArea(): void {
    const minArea = this.appliedFilter.MinCarpetArea;
    const maxArea = this.appliedFilter.MaxCarpetArea;

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.carpetAreaValidations = false;
    } else {
      this.carpetAreaValidations = true;
    }
  }

  minBudgetCheck(): void {
    const minBudget = this.appliedFilter.FromMinPrice;
    const maxBudget = this.appliedFilter.ToMinPrice;

    if (minBudget !== null && maxBudget !== null && maxBudget < minBudget) {
      this.minBudgetValidation = false;
    } else {
      this.minBudgetValidation = true;
    }
  }

  maxBudgetCheck(): void {
    const minBudget = this.appliedFilter.FromMaxPrice;
    const maxBudget = this.appliedFilter.ToMaxPrice;

    if (minBudget && maxBudget && maxBudget < minBudget) {
      this.maxBudgetValidation = false;
    } else {
      this.maxBudgetValidation = true;
    }
  }

  minMaxLeadCheck(): void {
    this.minMaxLeadValidation = !(
      this.appliedFilter.MinLeadCount &&
      this.appliedFilter.MaxLeadCount &&
      this.appliedFilter.MaxLeadCount < this.appliedFilter.MinLeadCount
    );
  }

  minMaxDataCheck() {
    this.minMaxDataValidation = !(
      this.appliedFilter.MinProspectCount &&
      this.appliedFilter.MaxProspectCount &&
      this.appliedFilter.MaxProspectCount < this.appliedFilter.MinProspectCount
    );
  }

  getFormValue(controlName: string): any {
    return this.appliedFilter[controlName];
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
