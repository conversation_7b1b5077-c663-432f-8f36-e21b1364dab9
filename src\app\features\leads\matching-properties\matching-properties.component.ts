import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, TemplateRef } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, takeUntil } from 'rxjs';

import { BHK_TYPE, MATCHING_RADIUS_LIST, PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { BHKType, EnquiryType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  formatBudget,
  getAssignedToDetails,
  getBRDisplayString,
  getLocationDetailsByObj,
  getPages,
  onFilterChanged,
} from 'src/app/core/utils/common.util';
import { PropertiesShareDataComponent } from 'src/app/features/leads/matching-properties/properties-share-data/properties-share-data.component';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchMatchingPropertyOrProjectList } from 'src/app/reducers/lead/lead.actions';
import { getIsMatchingLeadLoading, getMatchingPropertiesOrProjects } from 'src/app/reducers/lead/lead.reducer';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import { getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { BaseGridComponent } from 'src/app/shared/components/base-grid/base-grid.component';
import { ShareExternalComponent } from 'src/app/shared/components/share-external/share-external.component';

@Component({
  selector: 'matching-properties',
  templateUrl: './matching-properties.component.html',
})
export class MatchingPropertiesComponent
  extends BaseGridComponent
  implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  params: any;
  gridOptions: any;
  rowData: any[] = [];
  pageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  private filtered_list: Array<any> = [];
  defaultCurrency: string = 'INR';
  currOffset: number = 0;
  totalMatchCount: number;
  selectedPageSize: number;
  searchTerm: string;
  filtersPayload: any = {
    path: 'lead/matchingproperties',
    id: null,
    search: null,
  };
  getPages = getPages;
  userList: any;
  @Input() closeShareExternalComponent: any;
  columns: any;
  defaultColumns: any;
  onFilterChanged = onFilterChanged;
  getIsMatchingLeadLoading: boolean;
  areaSizeUnits: any;
  paramsValue: any;
  matchingRadiusList: any[] = MATCHING_RADIUS_LIST;
  matchingRadius: number;
  globalSettingsData: any;

  get sortOrder(): 'asc' | 'desc' {
    return this.filtersPayload?.['SortingCriteria.IsAscending'] == true
      ? 'asc'
      : this.filtersPayload?.['SortingCriteria.IsAscending'] == false
        ? 'desc'
        : undefined;
  }

  constructor(
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    private _store: Store<AppState>,
    private gridOptionsService: GridOptionsService
  ) {

    super();
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowData = this.rowData;

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data;
        this.defaultCurrency = data.countries && data.countries.length > 0 ? data.countries[0].defaultCurrency : null;
      });
    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
      });
  }

  agInit(params: any): void {
    this.params = params;
    this.paramsValue = params;
    this.params = {
      id: this.params?.data?.id,
      name: this.params?.data?.name,
      contactNo: this.params?.data?.contactNo,
      alternateNo: this.params?.data?.alternateContactNo,
      email: this.params?.data?.email,
      assignTo: getAssignedToDetails(params.data.assignTo, this.userList, true) || '',
      enquiredFor:
        !this.params?.data?.enquiry?.enquiredFor
          ? null
          : EnquiryType[this.params?.data?.enquiry?.enquiredFor],
      lowerBudget:
        !this.params?.data?.enquiry?.lowerBudget
          ? null
          : formatBudget(this.params?.data?.enquiry?.lowerBudget, this.params?.data?.enquiry?.currency || this.defaultCurrency),
      upperBudget:
        !this.params?.data?.enquiry?.upperBudget
          ? null
          : formatBudget(this.params?.data?.enquiry?.upperBudget, this.params?.data?.enquiry?.currency || this.defaultCurrency),
      location:
        this.params?.data?.enquiry?.addresses
          ? this.params?.data?.enquiry?.addresses
            ?.map((address: any) => getLocationDetailsByObj(address))
            .join('; ')
          : null,
      propertyType: this.params?.data?.enquiry?.propertyType?.displayName,
      propertySubType:
        this.params?.data?.enquiry?.propertyTypes?.map((property: any) => property?.childType?.displayName),
      noOfBHK: this.params?.data?.enquiry?.noOfBHK,
      bhkType: this.params?.data?.enquiry?.bhkTypes?.map((type: any) => BHKType[type])?.join(', '),
    };

    this.initializeGridSettings();
    this.searchTermSubject.subscribe(() => {
      this.filterFunction();
    });
  }

  ngOnInit() {
    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        filter: false,
        maxWidth: 50,
      },
      {
        headerName: this.paramsValue?.value === "MatchingProperties" ? 'Property Title' : 'Project Title',
        field: this.paramsValue?.value === "MatchingProperties" ? 'Property Title' : 'Project Title',
        valueGetter: (params: any) => [this.paramsValue?.value === "MatchingProperties" ? params.data?.property?.title : params.data?.projectName],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: this.paramsValue?.value === "MatchingProperties" ? 'Property Type' : 'Unit Name',
        field: this.paramsValue?.value === "MatchingProperties" ? 'Property Type' : 'Unit Name',
        minWidth: 120,
        valueGetter: (params: any) => {
          if (this.paramsValue?.value === "MatchingProperties") {
            return [
              params.data?.property?.propertyType?.displayName || '',
              params.data?.property?.propertyType?.childType?.displayName || '',
              params.data?.property?.bhkType || '',
              params.data?.property?.noOfBHK || ''
            ];
          } else {
            return [params.data?.unitInfo?.name || ''];
          }
        },
        cellRenderer: (params: any) => {
          if (this.paramsValue?.value === "MatchingProperties") {
            const isCustomLeadForm = this.globalSettingsData?.isCustomLeadFormEnabled;
            if (isCustomLeadForm) {
              const bhkDisplay = params.value[3] ? getBRDisplayString(params.value[3]) : '';
              return `<p class="text-truncate-1 break-all">${params.value[0]}${params.value[0] && params.value[1] ? ',' : ''} ${params.value[1]}</p>
                      <p class="text-truncate-1 break-all">${bhkDisplay}</p>`;
            } else {
              const bhkTypeDisplay = params.value[2] > 0 ? BHK_TYPE[params.value[2] - 1] : '';
              const bhkDisplay = params.value[3] ? params.value[3] + ' BHK' : '';
              return `<p class="text-truncate-1 break-all">${params.value[0]}${params.value[0] && params.value[1] ? ',' : ''} ${params.value[1]}</p>
                      <p class="text-truncate-1 break-all">${bhkTypeDisplay}${params.value[2] && params.value[3] ? ',' : ''} ${bhkDisplay}</p>`;
            }
          } else {
            return `<p class="text-truncate-1">${params.value[0]}</p>`;
          }
        },
      },
      {
        headerName: this.paramsValue?.value === "MatchingProperties" ? 'Property Details' : 'Pricing',
        field: this.paramsValue?.value === "MatchingProperties" ? 'Property Details' : 'Pricing',
        minWidth: 140,
        valueGetter: (params: any) => {
          if (this.paramsValue?.value === "MatchingProperties") {
            return [
              params.data?.monetaryInfo?.expectedPrice
                ? formatBudget(params.data?.monetaryInfo?.expectedPrice, params.data?.monetaryInfo?.currency || this.defaultCurrency)
                : params.data?.property?.monetaryInfo?.expectedPrice
                  ? formatBudget(params.data?.property?.monetaryInfo?.expectedPrice, params.data?.property?.monetaryInfo?.currency || this.defaultCurrency)
                  : '',
              params.data?.property?.dimension?.area,
              params.data?.property?.dimension?.unit,
            ];
          } else {
            return [
              params.data?.unitInfo?.currency || '',
              params.data?.unitInfo?.price || '--',
            ];
          }
        },
        cellRenderer: (params: any) => {
          if (this.paramsValue?.value === "MatchingProperties") {
            return `<p class="text-sm">${params.value[0] ? 'Budget: ' + params.value[0] : ''}</p>
                    <p class="text-sm">${params.value[1] ? params.value[1] : ''} ${params.value[2] ? params.value[2] : ''}</p>`;
          } else {
            return `<p class="text-sm">${params.value[0] !== '--' ? params.value[0] + ' ' + params.value[1] : '--'}</p>`;
          }
        },
      },
      {
        headerName: 'Location',
        minWidth: 90,
        field: 'Location',
        valueGetter: (params: any) => [this.paramsValue?.value === "MatchingProperties" ? getLocationDetailsByObj(params.data?.property?.address) : getLocationDetailsByObj(params.data?.location)],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'No. Of Match Fields',
        field: 'numberOfMatchField',
        hide: true,
        valueGetter: (params: any) => {
          return `${params.data.noOfFieldsMatched}/${params.data.totalNoOfFields}`;
        },
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate">${params.value}</p>`;
        },
      },
      {
        headerName: 'Percentage Of Match Fields',
        field: 'percentageOfMatchField',
        hide: true,
        valueGetter: (params: any) => params.data.percentageOfFieldsMatched || '',
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate">${params.value}</p>`;
        },
      },
      {
        headerName: 'Actions',
        menuTabs: [],
        maxWidth: 90,
        minWidth: 90,
        filter: false,
        valueGetter: (params: any) => [this.params.contactNo, this.params.email, () => { this.modalRef?.hide(); }, this.params.alternateNo, this.params.id, this.paramsValue?.value === "MatchingProperties" ? "MatchingProperties" : "MatchingProjects"],
        cellRenderer: PropertiesShareDataComponent,
      },
    ];
    if (this.paramsValue?.value === "MatchingProperties") {
      const locationColumnIndex = this.gridOptions.columnDefs.findIndex(
        (col: any) => col.headerName === 'Location'
      );

      const newColumnDef1 = {
        headerName: 'Looking to',
        field: 'Looking to',
        minWidth: 105,
        valueGetter: (params: any) => [EnquiryType[params.data?.property?.enquiredFor]],
        cellRenderer: (params: any) => {
          return `<p>${params.value == 'None' ? '' : [params.value]}</p>`;
        },
      };

      const newColumnDef2 = {
        headerName: 'Notes',
        field: 'Notes',
        minWidth: 80,
        valueGetter: (params: any) => params.data.property?.notes || '--',
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate-2 break-all">${params.value}</p>`;
        },
      };

      if (locationColumnIndex !== -1) {
        this.gridOptions.columnDefs.splice(locationColumnIndex + 1, 0, newColumnDef1);
        this.gridOptions.columnDefs.splice(locationColumnIndex + 2, 0, newColumnDef2);
      } else {
        this.gridOptions.columnDefs.push(newColumnDef1);
        this.gridOptions.columnDefs.push(newColumnDef2);
      }
    }

    if (this.paramsValue?.value === "MatchingProjects") {
      const locationColumnIndex = this.gridOptions.columnDefs.findIndex((col: any) => col.headerName === 'Location');
      const newColumnDef = {
        headerName: 'Carpet Area',
        field: 'Carpet Area',
        minWidth: 105,
        valueGetter: (params: any) => [params.data?.unitInfo?.carpetArea > 0 ? params.data?.unitInfo?.carpetArea + ' ' + this.getUnitById(params.data?.unitInfo?.carpetAreaUnitId) : '--'],
        cellRenderer: (params: any) => {
          return `<p>${params.value == 'None' ? '' : [params.value]}</p>`;
        },
      };
      if (locationColumnIndex !== -1) {
        this.gridOptions.columnDefs.splice(locationColumnIndex + 1, 0, newColumnDef);
      } else {
        this.gridOptions.columnDefs.push(newColumnDef);
      }
    }
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.columns = this.gridColumnApi.getColumns();
    this.columns = this.columns.map((column: any) => {
      return {
        label: column.getColDef().headerName,
        value: column,
      };
    });
    this.columns = this.columns
      .slice(2, this.columns.length - 1)
      .sort((a: any, b: any) => a?.label.localeCompare(b?.label));
    this.defaultColumns = this.columns.filter(
      (col: any) => col.value.getColDef().hide !== true
    );

    let columnData = localStorage.getItem('matchingLeadColumn')?.split(',');
    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
    this.customizeHeader();
  }

  customizeHeader() {
    const columnDefs = this.gridApi?.getColumnDefs();

    const columnToModify: any = columnDefs.find(
      (column: any) =>
        column.colId === this.filtersPayload?.['SortingCriteria.ColumnName']
    );
    if (columnToModify) {
      columnToModify.sort = this.sortOrder;
      this.gridApi.setColumnDefs(columnDefs);
    }
  }

  onColumnsSelected(columns: any[]) {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('matchingLeadColumn', colData?.toString());
    if (!columns) {
      columns = this.defaultColumns;
    }
    const cols = columns?.map((col) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col: any) => col.value),
      false
    );
  }

  openMatchPropertyModal(matchPropModal: TemplateRef<any>) {
    this.filtersPayload = {
      id: this.params?.id,
      path: this.paramsValue?.value === "MatchingProperties" ? 'lead/matchingproperties' : 'lead/matchingprojects',
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(new FetchMatchingPropertyOrProjectList(this.filtersPayload));
    this._store.dispatch(new FetchAreaUnitList());

    this._store
      .select(getIsMatchingLeadLoading).subscribe((isLoading: boolean) => {
        this.getIsMatchingLeadLoading = isLoading;
      });

    this._store
      .select(getMatchingPropertiesOrProjects)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data.items || [];
        this.totalMatchCount = data.totalCount;
      });

    this.modalRef = this.modalService?.show(matchPropModal, {
      class: 'right-modal modal-900 tb-modal-unset',
    });
  }

  filterFunction() {
    this.filtersPayload = {
      ...this.filtersPayload,
      search: this.searchTerm,
      RadiusInKms: this.matchingRadius ? this.matchingRadius : null,
    };
    this._store.dispatch(new FetchMatchingPropertyOrProjectList(this.filtersPayload));
    this._store
      .select(getMatchingPropertiesOrProjects)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data.items || [];
        this.totalMatchCount = data.totalCount;
      });

    this.currOffset = 0;
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(new FetchMatchingPropertyOrProjectList(this.filtersPayload));
    this.gridOptions.paginationPageSize = this.pageSize;
    this.gridOptions.api?.paginationSetPageSize(this.selectedPageSize);
    this.gridApi.setRowData([]);
    if (this.filtered_list.length > 0) {
      this.gridApi.applyTransaction({ add: this.filtered_list });
    } else {
      this.gridApi.applyTransaction({ add: this.rowData });
    }
    this.currOffset = 0;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this._store.dispatch(new FetchMatchingPropertyOrProjectList(this.filtersPayload));
  }

  openShareExternalModal() {
    let selectedNodes: any;
    if (this.gridApi) {
      selectedNodes = this.gridApi?.getSelectedNodes();
    }
    let selectedData = selectedNodes.map((node: any) => {
      if (this.paramsValue?.value === "MatchingProperties") {
        return node?.data?.property;
      }
      return node?.data;
    });
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState = {
      data: selectedData,
      key: this.paramsValue?.value === "MatchingProperties" ? 'bulk-share-property' : 'share-matching-projects',
      type: 'modal',
      contactPhone: this.params.contactNo,
      contactMail: this.params.email,
      alternatePhone: this.params.alternateNo,
      leadId: this.params.id,
      closeShareExternalComponent: () => {
        shareExternalComponentRef.hide();
      },
    };

    const shareExternalComponentRef = this.modalService.show(ShareExternalComponent, {
      class: 'modal-350 top-modal',
      ignoreBackdropClick: true,
      keyboard: false,
      initialState
    });
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns.filter(
      (col: any) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
  }

  getUnitById(id: any): string {
    const unitObject = this.areaSizeUnits.find((unit: any) => unit.id === id);
    return unitObject ? unitObject.unit : '--';
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
