import { Component, EventEmitter, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { filter, take, takeUntil } from 'rxjs';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';

import { POSSESSION_DATE_FILTER_LIST } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  isEmptyObject,
  onPickerOpened,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { FetchSelectedFields } from 'src/app/reducers/fields/fields.action';
import { getSelectedFields } from 'src/app/reducers/fields/fields.reducer';
import {
  cancelAddLead,
  FetchLeadById,
  FetchLeadIdSuccess,
} from 'src/app/reducers/lead/lead.actions';
import { getActiveLead, getLeads } from 'src/app/reducers/lead/lead.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'custom-lead-form',
  templateUrl: './custom-lead-form.component.html',
})
export class CustomLeadFormComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  currentActive: any = 0;

  selectedLeadId: any;
  selectedFieldsInfo: any = [];
  selectedFieldsIsLoading: boolean;
  leadForm!: FormGroup;
  receivedCurrentPath: any;
  isPostingData: boolean;
  selectedLeadInfo: {};
  activeLeadIsLoading: any;
  currentDate: Date = new Date();

  onPickerOpened = onPickerOpened;
  userBasicDetails: any;

  dateFilterList = POSSESSION_DATE_FILTER_LIST;
  isOpenPossessionModal: boolean = false;
  selectedPossession: any;
  selectedMonthAndYear: Date;
  isValidPossDate: boolean;
  selectedMonth: any;
  selectedYear: any;
  @ViewChild('dt5') dt5: OwlDateTimeComponent<any>;

  constructor(
    private headerTitle: HeaderTitleService,
    private store: Store<AppState>,
    public router: Router,
    private activatedRoute: ActivatedRoute,
    private shareDataService: ShareDataService,
    private fb: FormBuilder
  ) {
    this.headerTitle.setLangTitle(
      this.selectedLeadId ? 'BUTTONS.edit-lead' : 'BUTTONS.add-lead'
    );

    this.shareDataService.URL$.subscribe((data: any) => {
      this.receivedCurrentPath = data;
    });

    this.store.dispatch(new FetchSelectedFields());
  }

  ngOnInit() {
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(
          this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
        );
      });

    this.store
      .select(getSelectedFields)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.info?.length) {
          this.selectedFieldsInfo = data?.info;
          this.selectedFieldsInfo = this.selectedFieldsInfo
            .map((section: any) => ({
              ...section,
              customFields: [...section.customFields]
                .filter((field: any) => field.isSelected)
                .sort((a: any, b: any) => a?.orderRank - b?.orderRank),
              isHide: false,
            }))
            .filter((section: any) => section.customFields.length > 0)
            .sort((a: any, b: any) => a?.orderRank - b?.orderRank);
          this.initForm();
        }

        this.selectedFieldsIsLoading = data?.loader;

        // this.selectedInfo?.forEach((contentItem: any) => {
        //   const controlName = contentItem?.controlName;
        //   if (this.customForm.get(controlName)) {
        //     this.customForm.get(controlName).patchValue(true);
        //     this.isLabelClicked[controlName] = contentItem.isRequiredField || false;
        //   }
        // });
      });

    this.activatedRoute.params.subscribe((params: any) => {
      if ((params || {}).id) {
        this.selectedLeadId = params.id;
        this.store
          .select(getLeads)
          .pipe(take(1))
          .subscribe((leads: any) => {
            const activeLead = leads.filter(
              (lead: any) => this.selectedLeadId === lead?.id
            )?.[0];
            if (activeLead) {
              this.store.dispatch(new FetchLeadIdSuccess({ ...activeLead }));
            } else {
              this.store.dispatch(new FetchLeadById(this.selectedLeadId));
            }
          });
      }
    });

    this.store
      .select(getActiveLead)
      .pipe(
        takeUntil(this.stopper),
        filter((leadData: any) => leadData.id === this.selectedLeadId)
      )
      .subscribe((leadData: any) => {
        // if (this.activeLeadIsLoading) {
        //   this.selectedLeadInfo = {};
        //   return;
        // }

        //   this.selectedLeadInfo = {
        //     "15edc361-031b-418d-93f8-9eb759e0ff22": "Magic bricks",
        //     "1e2bf28d-653c-44a1-9a51-dd2e1eb6dee2": "Mounika",
        //     "5ee3a2a2-505f-4ce9-b74d-c29acdcab194": "Manasa Pampana",
        //     "849848a0-2bb1-42ef-bb20-099f5282e9c5": "<EMAIL>",
        //     "c1851241-ab76-40c2-be87-faef31af53e2": "**********",
        //     "1ffa2b30-200e-4ede-b16b-4f24a4258e04": "**********",
        //     "20d290ac-d3d2-40b9-a2d5-7ccedca5c4ef": "account-mb",
        //     "22464d2e-0512-4f42-a6f1-6a65c9dd9e80": "rajeev-referral",
        //     "8aec27a2-f346-43eb-95d4-d6c80e02cc87": "**********"
        // }

        this.selectedLeadInfo = leadData || {};
        if (!isEmptyObject(this.selectedLeadInfo)) {
          this.patchFormDetails(this.selectedLeadInfo);
        }
      });
  }

  initForm(): void {
    const formGroup: any = {};

    this.selectedFieldsInfo.forEach((section: any) => {
      section.customFields.forEach((field: any) => {
        formGroup[field.controlName] = [
          null,
          field.isRequiredField ? Validators.required : [],
        ];
      });
    });

    this.leadForm = this.fb.group(formGroup);
  }

  // Patch previously filled details into the form while editing
  patchFormDetails(savedData: { [key: string]: any }): void {
    // Map savedData (IDs as keys) back to the form control names
    const patchValues = Object.entries(savedData).reduce((acc, [id, value]) => {
      const field = this.selectedFieldsInfo
        .flatMap((group: any) => group.customFields)
        .find((f: any) => f.id === id);
      if (field) {
        if (field.controlName === 'possessionDate' && (value === null || value === undefined)) {
          acc[field.controlName] = ''; 
        } else {
          acc[field.controlName] = value; 
        }
      }
      return acc;
    }, {} as { [key: string]: any });
  
    this.leadForm?.patchValue(patchValues);

    const possessionDateValue = patchValues['possessionDate'];
    if (possessionDateValue && possessionDateValue !== '') {
      try {
        const date = new Date(possessionDateValue);
        this.selectedMonthAndYear = date;
        this.selectedMonth = date.toLocaleString('default', { month: 'short' });
        this.selectedYear = date.getFullYear().toString();
        this.selectedPossession = `${this.selectedMonth} ${this.selectedYear}`;
      } catch (e) {
        this.selectedPossession = '';
      }
    } else {
      this.selectedPossession = '';
    }
  }

  toggleSectionVisibility(section: any): void {
    section.isHide = !section.isHide;
  }

  scrollTo(sectionName: string, index: number): void {
    this.currentActive = index;
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
      targetSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  goToManageLead() {
    if (this.receivedCurrentPath === '/invoice') {
      return this.router.navigate(['/invoice']);
    } else {
      this.store.dispatch(new cancelAddLead(true));
      return this.router.navigate(['leads/manage-leads']);
    }
  }

  onScroll(event: Event): void {
    const container = event.target as HTMLElement;
    const containerTop = container.getBoundingClientRect().top;

    this.selectedFieldsInfo.forEach((section: { name: string }, index: any) => {
      const sectionElement = document.getElementById(section.name);

      if (sectionElement) {
        const sectionRect = sectionElement.getBoundingClientRect();
        const sectionTop = sectionRect.top - containerTop - 100;
        const sectionBottom = sectionRect.bottom - containerTop - 100;
        if (sectionTop <= 0 && sectionBottom > 0) {
          this.currentActive = index;
        }
      }
    });
  }

  monthChanged(event: any) {
    const selectedMonth = event.getMonth();
    const selectedYear = event.getFullYear();
    const lastDateOfMonth = new Date(selectedYear, selectedMonth + 1, 0);
    this.selectedMonthAndYear = lastDateOfMonth;

    const possessionDateField = this.selectedFieldsInfo
      .flatMap((group: any) => group.customFields)
      .find((field: any) => field.controlName === 'possessionDate');

    if (possessionDateField && this.leadForm.get('possessionDate')) {
      this.leadForm.get('possessionDate').setValue(this.selectedMonthAndYear);
      this.leadForm.get('possessionDate').markAsDirty();
    }

    this.isValidPossDate = false;
    this.selectedMonth = this.selectedMonthAndYear.toLocaleString('default', {
      month: 'short',
    });
    this.selectedYear = this.selectedMonthAndYear.getFullYear().toString();
    this.selectedPossession = `${this.selectedMonth} ${this.selectedYear}`;
    this.isOpenPossessionModal = false;
    if (this.dt5) {
      this.dt5.close();
    }
  }

  closePossessionModal() {
    this.isOpenPossessionModal = false;
  }

  postData() {
    if (this.leadForm.invalid) {
      validateAllFormFields(this.leadForm);
      return;
    }
    const leadData = this.leadForm.value;

    const selectedFields: any = this.selectedFieldsInfo.flatMap((group: any) =>
      group.customFields.filter((field: any) => field.isSelected)
    );

    // Build the payload with only the selected fields
    const payload = selectedFields.reduce((acc: any, field: any) => {
      const controlValue = this.leadForm.get(field.controlName)?.value || null; // Get value from the form
      if (controlValue !== null) {
        acc[field.id] = controlValue; // Map the field ID to its value
      }
      return acc;
    }, {} as { [key: string]: any });

    //   if (this.selectedLeadId) {
    //     this.store.dispatch(new UpdateLead(this.selectedLeadId, leadData));
    //   } else {
    //     this.store.dispatch(new AddLead(leadData));
    //   }

    //   this.isPostingData = true;
    //   this.store
    //     .select(getLeadListIsPosting)
    //     .pipe(skipWhile((isLoading) => isLoading))
    //     .subscribe((isLoading: boolean) => {
    //       if (!isLoading) {
    //         this.isPostingData = isLoading;
    //         this.goToManageLead();
    //       }
    //     });
  }
}
