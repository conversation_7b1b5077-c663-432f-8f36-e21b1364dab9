import { Action } from '@ngrx/store';

export enum IndexedDBActionTypes {
  // IndexedDB Initialization
  INIT_INDEXED_DB = '[IndexedDB] Initialize IndexedDB',
  INIT_INDEXED_DB_SUCCESS = '[IndexedDB] Initialize IndexedDB Success',
  INIT_INDEXED_DB_FAILURE = '[IndexedDB] Initialize IndexedDB Failure',

  // Check for Updates
  CHECK_FOR_UPDATES = '[IndexedDB] Check For Updates',
  CHECK_FOR_UPDATES_SUCCESS = '[IndexedDB] Check For Updates Success',
  CHECK_FOR_UPDATES_NO_CHANGE = '[IndexedDB] Check For Updates No Change',
  CHECK_FOR_UPDATES_FAILURE = '[IndexedDB] Check For Updates Failure',

  // Fetch Modified Properties
  FETCH_MODIFIED_PROPERTIES = '[IndexedDB] Fetch Modified Properties',
  FETCH_MODIFIED_PROPERTIES_SUCCESS = '[IndexedDB] Fetch Modified Properties Success',
  FETCH_MODIFIED_PROPERTIES_FAILURE = '[IndexedDB] Fetch Modified Properties Failure',

  // Fetch Initial Properties
  FETCH_INITIAL_PROPERTIES = '[IndexedDB] Fetch Initial Properties',
  FETCH_INITIAL_PROPERTIES_SUCCESS = '[IndexedDB] Fetch Initial Properties Success',
  FETCH_INITIAL_PROPERTIES_FAILURE = '[IndexedDB] Fetch Initial Properties Failure',

  // Store Properties in IndexedDB
  STORE_PROPERTIES = '[IndexedDB] Store Properties',
  STORE_PROPERTIES_SUCCESS = '[IndexedDB] Store Properties Success',
  STORE_PROPERTIES_FAILURE = '[IndexedDB] Store Properties Failure',

  // Batch Processing
  GET_BATCH_PROCESSING_STATUS = '[IndexedDB] Get Batch Processing Status',
  GET_BATCH_PROCESSING_STATUS_SUCCESS = '[IndexedDB] Get Batch Processing Status Success',
  GET_BATCH_PROCESSING_STATUS_FAILURE = '[IndexedDB] Get Batch Processing Status Failure',
  RESUME_BATCH_PROCESSING = '[IndexedDB] Resume Batch Processing',
  RESUME_BATCH_PROCESSING_SUCCESS = '[IndexedDB] Resume Batch Processing Success',
  RESUME_BATCH_PROCESSING_FAILURE = '[IndexedDB] Resume Batch Processing Failure',
  RESET_BATCH_PROCESSING = '[IndexedDB] Reset Batch Processing',
  RESET_BATCH_PROCESSING_SUCCESS = '[IndexedDB] Reset Batch Processing Success',
  RESET_BATCH_PROCESSING_FAILURE = '[IndexedDB] Reset Batch Processing Failure',

  // Get Properties from IndexedDB
  GET_PROPERTIES_FROM_INDEXED_DB = '[IndexedDB] Get Properties From IndexedDB',
  GET_PROPERTIES_FROM_INDEXED_DB_SUCCESS = '[IndexedDB] Get Properties From IndexedDB Success',
  GET_PROPERTIES_FROM_INDEXED_DB_EMPTY = '[IndexedDB] Get Properties From IndexedDB Empty',
  GET_PROPERTIES_FROM_INDEXED_DB_FAILURE = '[IndexedDB] Get Properties From IndexedDB Failure',

  // Fetch Remaining Properties
  FETCH_REMAINING_PROPERTIES = '[IndexedDB] Fetch Remaining Properties',
  FETCH_REMAINING_PROPERTIES_SUCCESS = '[IndexedDB] Fetch Remaining Properties Success',
  FETCH_REMAINING_PROPERTIES_FAILURE = '[IndexedDB] Fetch Remaining Properties Failure',

  // Clear IndexedDB
  CLEAR_INDEXED_DB = '[IndexedDB] Clear IndexedDB',
  CLEAR_INDEXED_DB_SUCCESS = '[IndexedDB] Clear IndexedDB Success',
  CLEAR_INDEXED_DB_FAILURE = '[IndexedDB] Clear IndexedDB Failure',

  // Reset IndexedDB
  RESET_INDEXED_DB = '[IndexedDB] Reset IndexedDB',
  RESET_INDEXED_DB_SUCCESS = '[IndexedDB] Reset IndexedDB Success',
  RESET_INDEXED_DB_FAILURE = '[IndexedDB] Reset IndexedDB Failure',
}

// IndexedDB Initialization
export class InitIndexedDB implements Action {
  readonly type = IndexedDBActionTypes.INIT_INDEXED_DB;
}

export class InitIndexedDBSuccess implements Action {
  readonly type = IndexedDBActionTypes.INIT_INDEXED_DB_SUCCESS;
}

export class InitIndexedDBFailure implements Action {
  readonly type = IndexedDBActionTypes.INIT_INDEXED_DB_FAILURE;
  constructor(public error: any) { }
}

// Check for Updates
export class CheckForUpdates implements Action {
  readonly type = IndexedDBActionTypes.CHECK_FOR_UPDATES;
}

export class CheckForUpdatesSuccess implements Action {
  readonly type = IndexedDBActionTypes.CHECK_FOR_UPDATES_SUCCESS;
  constructor(
    public apiLastModifiedDate: string,
    public storedLastModifiedDate: string
  ) { }
}

export class CheckForUpdatesNoChange implements Action {
  readonly type = IndexedDBActionTypes.CHECK_FOR_UPDATES_NO_CHANGE;
}

export class CheckForUpdatesFailure implements Action {
  readonly type = IndexedDBActionTypes.CHECK_FOR_UPDATES_FAILURE;
  constructor(public error: any) { }
}

// Fetch Modified Properties
export class FetchModifiedProperties implements Action {
  readonly type = IndexedDBActionTypes.FETCH_MODIFIED_PROPERTIES;
  constructor(
    public lastModifiedDate: string,
    public pageNumber: number = 1,
    public pageSize: number = 50
  ) { }
}

export class FetchModifiedPropertiesSuccess implements Action {
  readonly type = IndexedDBActionTypes.FETCH_MODIFIED_PROPERTIES_SUCCESS;
  constructor(public properties: any[], public totalCount: number) { }
}

export class FetchModifiedPropertiesFailure implements Action {
  readonly type = IndexedDBActionTypes.FETCH_MODIFIED_PROPERTIES_FAILURE;
  constructor(public error: any) { }
}

// Fetch Initial Properties
export class FetchInitialProperties implements Action {
  readonly type = IndexedDBActionTypes.FETCH_INITIAL_PROPERTIES;
}

export class FetchInitialPropertiesSuccess implements Action {
  readonly type = IndexedDBActionTypes.FETCH_INITIAL_PROPERTIES_SUCCESS;
  constructor(public properties: any[], public totalCount: number) { }
}

export class FetchInitialPropertiesFailure implements Action {
  readonly type = IndexedDBActionTypes.FETCH_INITIAL_PROPERTIES_FAILURE;
  constructor(public error: any) { }
}

// Store Properties in IndexedDB
export class StoreProperties implements Action {
  readonly type = IndexedDBActionTypes.STORE_PROPERTIES;
  constructor(public properties: any[]) { }
}

export class StorePropertiesSuccess implements Action {
  readonly type = IndexedDBActionTypes.STORE_PROPERTIES_SUCCESS;
  constructor(public count: number) { }
}

export class StorePropertiesFailure implements Action {
  readonly type = IndexedDBActionTypes.STORE_PROPERTIES_FAILURE;
  constructor(public error: any) { }
}

// Get Properties from IndexedDB
export class GetPropertiesFromIndexedDB implements Action {
  readonly type = IndexedDBActionTypes.GET_PROPERTIES_FROM_INDEXED_DB;
  constructor(public payload: any) { }
}

export class GetPropertiesFromIndexedDBSuccess implements Action {
  readonly type = IndexedDBActionTypes.GET_PROPERTIES_FROM_INDEXED_DB_SUCCESS;
  constructor(public response: any) { }
}

export class GetPropertiesFromIndexedDBEmpty implements Action {
  readonly type = IndexedDBActionTypes.GET_PROPERTIES_FROM_INDEXED_DB_EMPTY;
}

export class GetPropertiesFromIndexedDBFailure implements Action {
  readonly type = IndexedDBActionTypes.GET_PROPERTIES_FROM_INDEXED_DB_FAILURE;
  constructor(public error: any) { }
}

// Fetch Remaining Properties
export class FetchRemainingProperties implements Action {
  readonly type = IndexedDBActionTypes.FETCH_REMAINING_PROPERTIES;
  constructor(public totalCount: number) { }
}

export class FetchRemainingPropertiesSuccess implements Action {
  readonly type = IndexedDBActionTypes.FETCH_REMAINING_PROPERTIES_SUCCESS;
}

export class FetchRemainingPropertiesFailure implements Action {
  readonly type = IndexedDBActionTypes.FETCH_REMAINING_PROPERTIES_FAILURE;
  constructor(public error: any) { }
}

// Clear IndexedDB
export class ClearIndexedDB implements Action {
  readonly type = IndexedDBActionTypes.CLEAR_INDEXED_DB;
  constructor(public payload: any = {}) { }
}

export class ClearIndexedDBSuccess implements Action {
  readonly type = IndexedDBActionTypes.CLEAR_INDEXED_DB_SUCCESS;
}

export class ClearIndexedDBFailure implements Action {
  readonly type = IndexedDBActionTypes.CLEAR_INDEXED_DB_FAILURE;
  constructor(public error: any) { }
}

// Reset IndexedDB
export class ResetIndexedDB implements Action {
  readonly type = IndexedDBActionTypes.RESET_INDEXED_DB;
}

export class ResetIndexedDBSuccess implements Action {
  readonly type = IndexedDBActionTypes.RESET_INDEXED_DB_SUCCESS;
}

export class ResetIndexedDBFailure implements Action {
  readonly type = IndexedDBActionTypes.RESET_INDEXED_DB_FAILURE;
  constructor(public error: any) { }
}

// Batch Processing Actions
export class GetBatchProcessingStatus implements Action {
  readonly type = IndexedDBActionTypes.GET_BATCH_PROCESSING_STATUS;
}

export class GetBatchProcessingStatusSuccess implements Action {
  readonly type = IndexedDBActionTypes.GET_BATCH_PROCESSING_STATUS_SUCCESS;
  constructor(public status: any) { }
}

export class GetBatchProcessingStatusFailure implements Action {
  readonly type = IndexedDBActionTypes.GET_BATCH_PROCESSING_STATUS_FAILURE;
  constructor(public error: any) { }
}

export class ResumeBatchProcessing implements Action {
  readonly type = IndexedDBActionTypes.RESUME_BATCH_PROCESSING;
}

export class ResumeBatchProcessingSuccess implements Action {
  readonly type = IndexedDBActionTypes.RESUME_BATCH_PROCESSING_SUCCESS;
  constructor(public result: any) { }
}

export class ResumeBatchProcessingFailure implements Action {
  readonly type = IndexedDBActionTypes.RESUME_BATCH_PROCESSING_FAILURE;
  constructor(public error: any) { }
}

export class ResetBatchProcessing implements Action {
  readonly type = IndexedDBActionTypes.RESET_BATCH_PROCESSING;
}

export class ResetBatchProcessingSuccess implements Action {
  readonly type = IndexedDBActionTypes.RESET_BATCH_PROCESSING_SUCCESS;
  constructor(public result: any) { }
}

export class ResetBatchProcessingFailure implements Action {
  readonly type = IndexedDBActionTypes.RESET_BATCH_PROCESSING_FAILURE;
  constructor(public error: any) { }
}

export type IndexedDBActions =
  | InitIndexedDB
  | InitIndexedDBSuccess
  | InitIndexedDBFailure
  | CheckForUpdates
  | CheckForUpdatesSuccess
  | CheckForUpdatesNoChange
  | CheckForUpdatesFailure
  | FetchModifiedProperties
  | FetchModifiedPropertiesSuccess
  | FetchModifiedPropertiesFailure
  | FetchInitialProperties
  | FetchInitialPropertiesSuccess
  | FetchInitialPropertiesFailure
  | StoreProperties
  | StorePropertiesSuccess
  | StorePropertiesFailure
  | GetPropertiesFromIndexedDB
  | GetPropertiesFromIndexedDBSuccess
  | GetPropertiesFromIndexedDBEmpty
  | GetPropertiesFromIndexedDBFailure
  | FetchRemainingProperties
  | FetchRemainingPropertiesSuccess
  | FetchRemainingPropertiesFailure
  | GetBatchProcessingStatus
  | GetBatchProcessingStatusSuccess
  | GetBatchProcessingStatusFailure
  | ResumeBatchProcessing
  | ResumeBatchProcessingSuccess
  | ResumeBatchProcessingFailure
  | ResetBatchProcessing
  | ResetBatchProcessingSuccess
  | ResetBatchProcessingFailure
  | ClearIndexedDB
  | ClearIndexedDBSuccess
  | ClearIndexedDBFailure
  | ResetIndexedDB
  | ResetIndexedDBSuccess
  | ResetIndexedDBFailure;
