<div *ngIf="canView" class="pt-12 px-24 flex-grow-1">
  <div class="flex-start align-center">
    <div class="icon ic-triangle-up rotate-270 ic-x-xs ic-black cursor-pointer" routerLink='/teams/manage-team'></div>
    <div class="flex-col ml-10">
      <h3 class="fw-600">{{teamName}}</h3>
      <h5 class="fw-500">{{managerName}}</h5>
    </div>
  </div>
  <div class="align-center bg-white w-100 border-gray ip-align-center-unset ip-flex-col mt-10">
    <div class="align-center border-end flex-grow-1 no-validation tb-br-0">
      <ng-container>
        <div class="align-center w-100 px-10 py-12">
          <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
          <input (keyup.enter)="onSearch($event)" (input)="isEmptyInput($event)" [(ngModel)]="searchTerm"
            autocomplete="off" placeholder="type to search" name="search" class="border-0 outline-0 w-100">
        </div>
        <small class="text-muted text-nowrap ph-d-none pr-8">({{ 'LEADS.lead-search-prompt' | translate }})</small>
      </ng-container>
      <div *ngIf="canExport"
        class="bg-accent-green text-white ml-10 px-20 py-12 h-100 align-center cursor-pointer border-start w-70px tb-br-top"
        (click)="exportTeams()">{{ 'REPORTS.export' | translate }}</div>
    </div>
    <div class="align-center ip-br-top">
      <!-- <div class="px-10 align-center cursor-pointer tb-flex-grow-1 ph-w-40px ph-flex-grow-unset border-right">
        <div class="icon ic-filter-solid ic-xxs ic-black mr-10">
        </div>
        <span class="fw-600 ph-d-none">{{'PROPERTY.advanced-filters' | translate}}</span>
      </div> -->
      <div class="align-center position-relative cursor-pointer d-flex border-end">
        <span class="position-absolute left-15 z-index-2 fw-600 text-sm">
          {{ 'BUTTONS.manage-columns' | translate }}</span>
        <div class="show-hide-gray w-140">
          <ng-select [virtualScroll]="true" class="bg-white" [items]="columns" [multiple]="true" appSelectAll ResizableDropdown
            [searchable]="false" [closeOnSelect]="false" [ngModel]="defaultColumns"
            (change)="onColumnsSelected($event)">
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                  class="checkmark"></span>{{item.label}}</div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="bg-coal text-white px-10 py-12 ip-w-30px h-100 align-center cursor-pointer">
        <span class="ip-d-none" (click)="onSetColumnDefault()">{{ 'GLOBAL.default' | translate }}</span> <span
          class="ic-refresh d-none ip-d-block"></span>
      </div>
      <div class="show-dropdown-white align-center position-relative">
        <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
            {{ 'GLOBAL.show' | translate }}</span> {{ 'GLOBAL.entries' | translate }}</span>
        <ng-select (change)="assignCount()" [(ngModel)]="selectedPageSize" [virtualScroll]="true"
          [placeholder]="pageSize" class="w-150 tb-w-120px" ResizableDropdown [searchable]="false">
          <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
            {{pageSize}}</ng-option>
        </ng-select>
      </div>
    </div>
  </div>
  <div class="bg-white px-4 py-12 tb-w-100-34 w-100-190"></div>
  <ng-container *ngIf="!teamMemberIsLoading else memberLoader">
    <ng-container *ngIf="!rowData?.length || !dataTotalCount; else memberData">
      <div class="d-flex flex-column align-center mt-40">
        <img src="assets/images/layered-cards.svg" alt="No member found" class="">
        <div class="header-3 fw-600 text-center">{{'TEAM.no-member-found' |translate}}</div>
      </div>
    </ng-container>
    <ng-template #memberData>
      <div class="manage-members" *ngIf="!teamMemberIsLoading else memberLoader">
        <ag-grid-angular #agGrid class="ag-theme-alpine" [pagination]="true" [paginationPageSize]="pageSize"
          [gridOptions]="gridOptions" [rowData]="rowData" [alwaysShowHorizontalScroll]="true"
          [alwaysShowVerticalScroll]="true" [suppressPaginationPanel]="true"
          (gridReady)="onGridReady($event)"></ag-grid-angular>
      </div>
      <div class="justify-center">
        <div class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 z-index-2"
          [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}">
          <div class="align-center tb-mb-10">
            <div class="fw-600 text-coal mr-20 text-xl">{{gridApi?.getSelectedNodes()?.length}}
              {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' | translate}}</div>
          </div>
          <div class="flex-center flex-wrap">
            <button class="btn-bulk-red" (click)="openBulkDeleteModal(BulkDeleteModal)"
              [disabled]="gridApi?.getSelectedNodes()?.length < 1" id="btnBulkDelete" data-automate-id="btnBulkDelete">
              {{ 'LEADS.bulk' | translate }} {{ ('BUTTONS.delete') | translate }}</button>
          </div>
        </div>
      </div>
      <div class="mt-20 flex-end">
        <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{(currOffset * pageSize) + 1}} {{ 'GLOBAL.to-small' |
          translate}}
          {{(currOffset * pageSize) + pageSize > dataTotalCount ? dataTotalCount : (currOffset * pageSize) + pageSize}}
          {{'GLOBAL.of-small' | translate }} {{dataTotalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
        <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]="getPages(dataTotalCount,pageSize)"
          (pageChange)="onPageChange($event)"></pagination>
      </div>
    </ng-template>
  </ng-container>
</div>

<ng-template #BulkDeleteModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>Bulk Delete</h3>
      <div class="icon ic-close  ic-sm cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-12">
      <div class="field-label mb-10">Selected Member(s) -
        {{gridApi?.getSelectedNodes()?.length}}
      </div>
      <div class="flex-column scrollbar max-h-100-176">
        <ng-container *ngFor="let team of selectedMember">
          <div class="flex-between p-12 fw-600 text-sm border-bottom text-secondary bg-white">
            <span class="text-truncate-1 break-all mr-8">{{ team?.name }}</span>
            <div (click)="openConfirmDeleteModal(team?.name, team?.id)"
              class="bg-light-red icon-badge" id="clkBulkDelete" data-automate-id="clkBulkDelete">
              <span class="icon ic-trash m-auto ic-xxxs"></span>
            </div>
          </div>
        </ng-container>
      </div>
      <div class="flex-center" *ngIf="!bulkDeleteIsLoading else ratLoader">
        <button class="btn-coal mt-20" (click)="bulkDelete()">{{ 'BUTTONS.delete' | translate }}</button>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #ratLoader>
  <div class="flex-center w-100 my-8">
    <img src="assets/images/loader-rat.svg" class="rat-loader h-20px w-20px" alt="loader">
  </div>
</ng-template>
<ng-template #memberLoader>
  <div class="flex-center h-100-270">
    <application-loader></application-loader>
  </div>
</ng-template>