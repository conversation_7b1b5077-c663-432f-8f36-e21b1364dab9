import {
  Compo<PERSON>,
  <PERSON>E<PERSON>ter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
} from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { VALIDATION_CLEAR, VALIDATION_SET } from 'src/app/app.constants';

import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAssignedToDetails,
  getMSUrl,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import {
  FetchPriorityList,
  FetchUserAssignmentByEntity,
  UpdateUserAssignment,
} from 'src/app/reducers/automation/automation.actions';
import {
  getPriorityList,
  getUserAssignmentByEntity,
} from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  DeleteProject,
  FetchProjectCount,
  FetchProjectList,
} from 'src/app/reducers/project/project.action';
import {
  getAdminsAndReportees,
  getAdminsAndReporteesIsLoading,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { ProjectsService } from 'src/app/services/controllers/projects.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'project-action',
  templateUrl: './project-action.component.html',
})
export class ProjectActionComponent
  implements ICellRendererAngularComp, OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  canEdit: boolean = false;
  canDelete: boolean = false;
  canPermanentDelete: boolean = false;
  canAssign: boolean = false;
  canAssignToAny: boolean = false;
  // assignedUser: FormControl = new FormControl('');
  getAssignedToDetails = getAssignedToDetails;
  activeUsers: any;
  allActiveUsers: any;
  assignedUserDetails: Array<string> = [];
  userList: any;
  allUserList: any;
  project: any;
  moduleId: string;
  isAssignedUserIds: boolean = false;
  filtersPayload: {
    id: any;
  };

  leadAssignmentOptions: string[] = ['Configuration', 'Selected Users'];
  canAllowSecondaryUsers: boolean;
  assignedUser: any[] = [];
  assignedDuplicateUser: any[] = [];
  canEnableAllowSecondaryUsers: boolean;
  assignedSecondaryUsers: any[] = [];
  assignedPrimaryUsers: any[] = [];
  selectedSectionLeadAssignment: 'Configuration' | 'Selected Users' =
    'Configuration';
  selectedUserType: 'Primary User(s)' | 'Secondary User(s)' = 'Primary User(s)';
  listSelection: string = 'original';
  canAllowDuplicates: boolean;
  canEnableAllowDuplicates: boolean;
  sameAsPrimaryUsers: boolean;
  sameAsSelectedUsers: boolean;
  sameAsAbove: boolean;
  integrationDuplicateForm: FormGroup;
  integrationDualOwnerForm: FormGroup;
  allowDuplicatesPopupRef: any;
  message: string;
  notes: string;
  updatedIntegrationList: Array<any>;
  selectedCount: number;
  canAssignSequentially: boolean;
  isActiveUsersLoading: boolean = true;
  isAllActiveUsersLoading: boolean = true;
  canViewForFilter: boolean;

  get filteredUsers(): string[] {
    return this.listSelection === 'original'
      ? this.assignedUser
      : this.assignedDuplicateUser;
  }

  get primarySeondaryUsers(): string[] {
    return this.listSelection === 'primary'
      ? this.assignedPrimaryUsers
      : this.listSelection === 'secondary'
        ? this.assignedSecondaryUsers
        : this.assignedDuplicateUser;
  }

  constructor(
    private _translateService: TranslateService,
    private _notificationService: NotificationsService,
    private sharedDataService: ShareDataService,
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    public router: Router,
    private store: Store<AppState>,
    private fb: FormBuilder,
    private projectsService: ProjectsService,
    public trackingService: TrackingService,
    private api: ProjectsService
  ) {
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Projects.Update')) {
          this.canEdit = true;
        }
        if (permissions?.includes('Permissions.Projects.Delete')) {
          this.canDelete = true;
        }
        if (permissions?.includes('Permissions.Projects.PermanentDelete')) {
          this.canPermanentDelete = true;
        }
        if (permissions?.includes('Permissions.Projects.Assign')) {
          this.canAssign = true;
        }
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
        }
        if(permissions?.includes('Permissions.Users.ViewForFilter')){
          this.canViewForFilter = true;
        }
      });

    this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
        this.selectAllForDropdownItems(this.activeUsers);
      });

    this.store
      .select(getAdminsAndReporteesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isActiveUsersLoading = isLoading;
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
        this.selectAllForDropdownItems(this.allActiveUsers);
      });

    this.store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isAllActiveUsersLoading = isLoading;
      });
  }

  ngOnInit(): void {
    this.integrationDuplicateForm = this.fb.group({
      assignedUser: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]],
    });

    this.integrationDualOwnerForm = this.fb.group({
      assignedPrimaryUsers: [null, Validators.required],
      assignedSecondaryUsers: [null, Validators.required],
      assignedDuplicateUser: [null, Validators.required],
      selectedUserType: ['Primary User(s)', Validators.required],
    });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.canEnableAllowDuplicates =
          data?.duplicateFeatureInfo?.isFeatureAdded;
        this.canEnableAllowSecondaryUsers = data?.isDualOwnershipEnabled;
      });

    this.store
      .select(getUserAssignmentByEntity)
      .pipe(takeUntil(this.stopper))
      .subscribe((res) => {
        this.canAllowSecondaryUsers = res?.isDualAssignmentEnabled;
        this.canAllowDuplicates = res?.isDuplicateAssignmentEnabled;
        this.assignedUser = res?.userIds;
        this.assignedUserDetails = res?.userIds;
        this.assignedPrimaryUsers = this.assignedUser;
        this.assignedSecondaryUsers = res?.secondaryUserIds;
        this.assignedDuplicateUser = res?.duplicateUserIds;
        this.canAssignSequentially = !res?.shouldCreateMultipleDuplicates;
        this.toggleAssignedUserValidation();
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  setListSelection() {
    this.listSelection =
      this.canAllowDuplicates && !this.canAllowSecondaryUsers
        ? this.assignedUser?.length
          ? 'original'
          : 'duplicate'
        : this.assignedPrimaryUsers?.length
          ? 'primary'
          : this.assignedSecondaryUsers?.length
            ? 'secondary'
            : 'duplicate';
  }

  resetIntegrationForm() {
    // this.assignedUser = [];
    // this.assignedPrimaryUsers = [];
    // this.assignedSecondaryUsers = [];
    // this.assignedDuplicateUser = [];
    this.sameAsPrimaryUsers = false;
    this.sameAsSelectedUsers = false;
    this.sameAsAbove = false;
    this.listSelection = 'original';
    this.toggleAssignedUserValidation();
    this.revertValidation();
  }

  toggleAssignedUserValidation() {
    if (this.canAllowDuplicates) {
      toggleValidation(
        VALIDATION_SET,
        this.integrationDuplicateForm,
        'assignedUser',
        [Validators.required]
      );
      return;
    }
    toggleValidation(
      VALIDATION_CLEAR,
      this.integrationDuplicateForm,
      'assignedUser'
    );
  }

  revertValidation() {
    const integrationDuplicateFormControlNames = [
      'assignedUser',
      'assignedDuplicateUser',
    ];
    integrationDuplicateFormControlNames?.forEach((controlName: string) => {
      this.integrationDuplicateForm.get(controlName).markAsPristine();
      this.integrationDuplicateForm.get(controlName).markAsUntouched();
      if (controlName !== 'assignedUser')
        this.integrationDuplicateForm.get(controlName).setErrors(null);
    });

    if (!this.canAllowSecondaryUsers) {
      const integrationDualOwnerFormControlNames = [
        'assignedPrimaryUsers',
        'assignedSecondaryUsers',
        'assignedDuplicateUser',
      ];
      integrationDualOwnerFormControlNames?.forEach((controlName: string) => {
        this.integrationDualOwnerForm.get(controlName).markAsPristine();
        this.integrationDualOwnerForm.get(controlName).markAsUntouched();
        this.integrationDualOwnerForm.get(controlName).setErrors(null);
      });
    }

    this.selectedUserType = 'Primary User(s)';
  }

  validateAssignmentForm() {
    if (this.canAllowDuplicates && !this.canAllowSecondaryUsers) {
      if (!this.integrationDuplicateForm?.valid) {
        validateAllFormFields(this.integrationDuplicateForm);
        return false;
      }
      if (
        this.assignedUser?.length == 1 &&
        this.assignedDuplicateUser?.length == 1
      ) {
        if (this.assignedUser?.[0] == this.assignedDuplicateUser?.[0]) {
          this._notificationService.warn(
            'Warning',
            'Duplicate user assignment detected.'
          );
          return false;
        }
      }
    } else if (this.canAllowDuplicates && this.canAllowSecondaryUsers) {
      if (!this.integrationDualOwnerForm?.valid) {
        validateAllFormFields(this.integrationDualOwnerForm);
        return false;
      }
      if (
        (this.assignedPrimaryUsers?.length == 1 &&
          this.assignedSecondaryUsers?.length == 1) ||
        (this.assignedDuplicateUser?.length == 1 &&
          this.assignedSecondaryUsers?.length == 1) ||
        (this.assignedDuplicateUser?.length == 1 &&
          this.assignedPrimaryUsers?.length == 1)
      ) {
        if (
          (this.assignedPrimaryUsers?.[0] == this.assignedDuplicateUser?.[0] &&
            this.assignedPrimaryUsers?.length == 1 &&
            this.assignedDuplicateUser?.length == 1) ||
          (this.assignedPrimaryUsers?.[0] == this.assignedSecondaryUsers?.[0] &&
            this.assignedPrimaryUsers?.length == 1 &&
            this.assignedSecondaryUsers?.length == 1) ||
          (this.assignedSecondaryUsers?.[0] ==
            this.assignedDuplicateUser?.[0] &&
            this.assignedSecondaryUsers?.length == 1 &&
            this.assignedDuplicateUser?.length == 1)
        ) {
          this._notificationService.warn(
            'Warning',
            'Duplicate user assignment detected.'
          );
          return false;
        }
      }
    } else if (!this.canAllowDuplicates && this.canAllowSecondaryUsers) {
      if (
        !this.integrationDualOwnerForm.controls['assignedPrimaryUsers'].valid ||
        !this.integrationDualOwnerForm.controls['assignedSecondaryUsers'].valid
      ) {
        validateAllFormFields(this.integrationDualOwnerForm);
        return false;
      }
      if (
        this.assignedPrimaryUsers?.length &&
        this.assignedSecondaryUsers?.length
      ) {
        if (
          this.assignedPrimaryUsers?.length == 1 &&
          this.assignedSecondaryUsers?.length == 1
        ) {
          if (
            this.assignedPrimaryUsers?.[0] == this.assignedSecondaryUsers?.[0]
          ) {
            this._notificationService.warn(
              'Warning',
              'Duplicate user assignment detected.'
            );
            return false;
          }
        }
        return true;
      }
    }
    return true;
  }

  openConfirmModal(allowDuplicatesPopupRef: any, settingType: string) {
    this.allowDuplicatesPopupRef = this.modalService.show(
      allowDuplicatesPopupRef,
      {
        class: 'modal-600 top-modal ip-modal-unset',
        ignoreBackdropClick: true,
        keyboard: false,
      }
    );
    switch (settingType) {
      case 'allowDuplicateLeads':
        this.message =
          'To use this feature “Allow Lead Duplicates” must be enabled.';
        this.notes = 'Please read the instructions clearly and proceed.';
        break;
      case 'allowSecondaryUsers':
        this.message =
          'To use this feature “Dual Lead Ownership” must be enabled.';
        this.notes = 'Please read the instructions clearly and proceed.';
        break;
    }
  }

  handleSelectAll(isAssignedUser: boolean = false) {
    const allUsers: string[] = (
      this.canAssignToAny ? this.allActiveUsers : this.activeUsers
    ).map((user: any) => user?.id);
    if (this.assignedUser?.includes('selectedAllGroup')) {
      this.assignedUser = allUsers;
    }
    if (this.assignedDuplicateUser?.includes('selectedAllGroup')) {
      this.assignedDuplicateUser = allUsers;
    }
    if (this.assignedPrimaryUsers?.includes('selectedAllGroup')) {
      this.assignedPrimaryUsers = allUsers;
    }
    if (this.assignedSecondaryUsers?.includes('selectedAllGroup')) {
      this.assignedSecondaryUsers = allUsers;
    }

    if (isAssignedUser) {
      this.assignedPrimaryUsers = this.assignedUser;
    } else {
      this.assignedUser = this.assignedPrimaryUsers;
    }
  }

  sameAsPrimarySecondaryUsersClicked() {
    if (this.sameAsAbove) return;
    this.assignedDuplicateUser =
      this.selectedUserType == 'Primary User(s)'
        ? [...this.assignedPrimaryUsers]
        : [...this.assignedSecondaryUsers];
  }

  originalDuplicateListToggle(selection: string) {
    this.listSelection = selection;
  }

  removeUserFromSelection(userId: any) {
    if (this.canAllowSecondaryUsers) {
      this.sameAsPrimaryUsers = false;
      if (this.listSelection == 'secondary') {
        this.assignedSecondaryUsers = this.assignedSecondaryUsers?.filter(
          (user: any) => user !== userId
        );
        if (!this.assignedSecondaryUsers?.length)
          this.selectedSectionLeadAssignment = 'Configuration';
        return;
      } else if (this.listSelection == 'duplicate') {
        this.assignedDuplicateUser = this.assignedDuplicateUser?.filter(
          (user: any) => user !== userId
        );
        if (!this.assignedDuplicateUser?.length)
          this.selectedSectionLeadAssignment = 'Configuration';
        return;
      }
      this.assignedPrimaryUsers = this.assignedPrimaryUsers?.filter(
        (user: any) => user !== userId
      );
      if (!this.assignedPrimaryUsers?.length)
        this.selectedSectionLeadAssignment = 'Configuration';
      return;
    }
    if (this.canAllowDuplicates && !this.canAllowSecondaryUsers) {
      this.sameAsSelectedUsers = false;
      if (this.listSelection == 'original') {
        this.assignedUser = this.assignedUser.filter(
          (user: any) => user !== userId
        );
        this.assignedUserDetails = this.assignedUser;
        if (!this.assignedUser?.length)
          this.selectedSectionLeadAssignment = 'Configuration';
        return;
      }
      this.assignedDuplicateUser = this.assignedDuplicateUser.filter(
        (user: any) => user !== userId
      );
      if (!this.assignedDuplicateUser?.length)
        this.selectedSectionLeadAssignment = 'Configuration';
      return;
    }
    this.assignedUser = this.assignedUser.filter(
      (user: any) => user !== userId
    );
    this.assignedUserDetails = this.assignedUser;
    if (!this.assignedUser?.length)
      this.selectedSectionLeadAssignment = 'Configuration';
  }

  hideAssignmentPopup() {
    // this.isShowAssignModalChanged.emit(false)
  }

  closePopup() {
    this.allowDuplicatesPopupRef.hide();
  }

  goToGlobalConfig() {
    this.hideAssignmentPopup();
    this.closePopup();
    this.closeModal();
    this.router.navigate(['global-config', 'lead-settings']);
  }

  closeModal() {
    this.modalRef.hide();
    this.reset();
  }

  reset() {
    this.updatedIntegrationList?.forEach((item) => (item.isSelected = false));
    this.selectedCount = 0;
    this.assignedUser = [];
  }

  selectAllForDropdownItems(items: any[]) {
    let allSelect = (items: any) => {
      items.forEach((element: any) => {
        element['selectedAllGroup'] = 'selectedAllGroup';
      });
    };

    allSelect(items);
  }

  refresh(): boolean {
    return false;
  }

  editProject(project: any) {
    this.trackingService.trackFeature(`Web.Project.Actions.Edit.Click`);
    const id: string = this.params?.data?.id;
    this.router.navigate(['/projects/edit-project/basic-details/' + id]);
  }

  openDeleteProjectModal(data: any) {
    this.trackingService.trackFeature(`Web.Project.Actions.Delete.Click`);
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.name,
      fieldType: 'project',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.projectsService.archive(data?.id).subscribe((res: any) => {
            if (res)
              this.store.dispatch(new FetchProjectList());
          })
          this.trackingService.trackFeature(
            `Web.Project.Actions.Deleted.Click`
          );
          this.store.dispatch(new DeleteProject(data?.id));
        }
      });
    }
  }

  openArchiveModal(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'restore',
      title: data?.name,
      fieldType: 'project',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.api.bulkRestoreProject([data?.id]).subscribe((res: any) => {
            if (res.succeeded) {
              this._notificationService.success(
                'Project restored successfully'
              );
              this.store.dispatch(new FetchProjectCount());
              this.store.dispatch(new FetchProjectList());
            }
          });
        }
      });
    }
  }

  openAssignmentModal(assign: TemplateRef<any>) {
    if (this.params?.data?.id) {
      this.store.dispatch(
        new FetchUserAssignmentByEntity(this.params?.data?.id)
      );
    }
    this.trackingService.trackFeature(`Web.Project.Actions.AssignTo.Click`);
    if (this.params.data?.assignedUserIds?.length === 0) {
      this.isAssignedUserIds = true;
    }
    this.modalRef = this.modalService.show(assign, {
      class: 'modal-350 right-modal',
      keyboard: false,
    });
    // this.assignedUser.patchValue(this.params.data?.assignedUserIds);
    this.assignedUserDetails = this.params.data?.assignedUserIds;
    this.store.dispatch(new FetchPriorityList());
    this.store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter((item) => item.name === 'Project');
        this.moduleId = filteredData.map((item) => item.id).join(', ');
      });
  }

  assignAccount() {
    // if (this.isAssignedUserIds && this.assignedUserDetails.length === 0) {
    //   this._notificationService.warn(`Please Select atleast one User`);
    //   return;
    // }
    if (!this.validateAssignmentForm()) return;
    // let payload: any = {
    //   // userIds: this.assignedUser.value,
    //   moduleId: this.moduleId,
    //   entityId: this.params.data?.id,
    // };
    let payload: any = {
      entityId: this.params.data?.id,
      userIds: this.canAllowSecondaryUsers
        ? this.assignedPrimaryUsers?.includes('selectedAllGroup')
          ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map(
            (user: any) => user?.id
          )
          : this.assignedPrimaryUsers
        : (this.assignedUser?.includes('selectedAllGroup')
          ? (this.canAssignToAny
            ? this.allActiveUsers
            : this.activeUsers
          ).map((user: any) => user?.id)
          : this.assignedUser) || [],
      secondaryUserIds: this.assignedSecondaryUsers?.includes(
        'selectedAllGroup'
      )
        ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map(
          (user: any) => user?.id
        )
        : this.assignedSecondaryUsers || [],
      duplicateUserIds: this.assignedDuplicateUser?.includes('selectedAllGroup')
        ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map(
          (user: any) => user?.id
        )
        : this.assignedDuplicateUser || [],
      isDuplicateAssignmentEnabled: this.canAllowDuplicates || false,
      isDualAssignmentEnabled: this.canAllowSecondaryUsers || false,
      moduleId: this.moduleId,
      shouldCreateMultipleDuplicates: !this.canAssignSequentially,
    };
    this.store.dispatch(new UpdateUserAssignment(payload, 'Project'));
    this.modalService.hide();
  }

  copyUrl(): void {
    this.trackingService.trackFeature(`Web.Project.Actions.Copy.Click`);
    navigator.clipboard?.writeText(getMSUrl(this.params.data?.serialNo, true));
    this._notificationService.success(
      this._translateService.instant('GLOBAL.link-copied')
    );
  }

  sameAsSelectedUsersClicked(isPrimaryUser: boolean = false) {
    if (this.sameAsPrimaryUsers) return;
    if (isPrimaryUser) {
      this.assignedSecondaryUsers = [...this.assignedPrimaryUsers];
      return;
    }
    this.assignedDuplicateUser = [...this.assignedUser];
  }

  deletePermanently(data: any) {
    let initialState: any = {
      message: data?.leadsCount
        ? `This project is associated with ${data?.leadsCount} lead${data?.leadsCount !== 1 ? 's' : ''}${data?.prospectCount ? ` and ${data?.prospectCount} data${data?.prospectCount !== 1 ? '' : ''}` : ''}. Do you still want to`
        : data?.prospectCount
          ? `This project is associated with ${data?.prospectCount} Data${data?.prospectCount !== 1 ? '' : ''}. Do you still want to`
          : 'GLOBAL.user-confirmation',
      confirmType: 'permanently delete',
      title: data?.name,
      fieldType: 'project',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.projectsService.deleteProjectPermanently([data?.id]).subscribe((res: any) => {
            if (res)
              this.store.dispatch(new FetchProjectList());
            this._notificationService.success(`Property deleted successfully.`);
            this.modalService.hide();
          })
        }
      });
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
