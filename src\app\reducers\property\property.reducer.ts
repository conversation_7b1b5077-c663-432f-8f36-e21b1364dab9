import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { Property } from 'src/app/core/interfaces/property.interface';
import {
  AddWaterMarkSuccess,
  BulkReassignPropertySuccess,
  FetchActivePropertyId,
  FetchArchivedPropertyListSuccess,
  FetchBrochureListSuccess,
  FetchExportPropertyStatusSuccess,
  FetchGalleryDropdownDataSuccess,
  FetchLocationListSuccess,
  FetchMSSimilarPropertiesSuccess,
  FetchMatchingLeadsListSuccess,
  FetchMicrositePropertySuccess,
  FetchMicrositeUserDetailsSuccess,
  FetchOwnerNamesSuccess,
  FetchPropertyAssignmentsSuccess,
  FetchPropertyByIdSuccess,
  FetchPropertyCountSuccess,
  FetchPropertyCurrencySuccess,
  FetchPropertyExcelUploadedSuccess,
  FetchPropertyLeadsCountByIdsSuccess,
  FetchPropertyListSuccess,
  FetchPropertyModifiedOnSuccess,
  FetchPropertyWithGoogleLocationSuccess,
  FetchPropertyWithIdNameListSuccess,
  PropertyActionTypes,
  PropertyExcelUploadSuccess,
  UpdateFilterPayload,
  UploadBrochureSuccess,
} from 'src/app/reducers/property/property.actions';

export type PropertyState = {
  ownerNames: any;
  exportPropertyStatus: any;
  exportPropertyStatusLoading: boolean;
  properties?: Array<Property>;
  isPropertiesLoading?: boolean;
  propertyCount?: any;
  bulkReassignDetails?: any;
  selectedProperty?: Property;
  addedPropertyId?: Property;
  filtersPayload: any;
  totalCount?: number;
  itemsCount?: number;
  locationList?: any;
  isLocationListLoading?: boolean;
  micrositeProperty: any;
  micrositeUserDetails: any;
  MSSimilarProperties: any;
  uploadDoc: any;
  broList: any;
  gallerydropdowndata: Array<String>;
  isGallerydropdowndataLoading: boolean;
  matchingLeads?: any[];
  archivedList?: Array<Property>;
  isArchivedListLoading?: boolean;
  excelColumnHeading?: any;
  excelUploadedList?: any;
  propertyExport?: any;
  MSleadIsLoading: boolean;
  currencyListLoading: boolean;
  bulkReassignPropertyIsLoading: boolean;
  MSSimilarPropertiesIsLoading: boolean;
  leadsCount: any[];
  propertyCurrency: any[];
  waterMarkImage: any[];
  isExcelUploadedListLoading: boolean;
  propertyWithIdNameList?: any;
  propertyWithIdNameListIsLoading: boolean;
  propertyListByIdIsLoading: boolean;
  propertyAssignments: any[];
  propertyModifiedOnData: any[];
  isPropertyModifiedOnLoading: boolean;
  propertyWithGoogleLocation: any[];
  isPropertyGoogleLocationLoading: boolean;
  isPropertyUpdateIsLoading: boolean;
  isPropertyAddIsLoading: boolean;
};

const initialState: PropertyState = {
  properties: [],
  isPropertiesLoading: true,
  propertyCount: {},
  selectedProperty: {} as Property,
  addedPropertyId: {} as Property,
  totalCount: 0,
  itemsCount: 0,
  locationList: [],
  isLocationListLoading: true,
  micrositeProperty: [],
  micrositeUserDetails: [],
  MSSimilarProperties: [],
  bulkReassignDetails: [],
  uploadDoc: [],
  broList: [],
  matchingLeads: [],
  archivedList: [],
  propertyExport: [],
  filtersPayload: {
    pageNumber: 1,
    pageSize: 10,
    path: 'Property/new/all',
    dateType: null,
    fromDate: null,
    toDate: null,
    BasePropertyTypeId: null,
    PropertySubTypes: null,
    'PropertySize.Area': null,
    'PropertySize.AreaUnitId': null,
    PriceRange: null,
    Locations: null,
    Cities: null,
    States: null,
    EnquiredFor: null,
    PropertySearch: null,
  },
  gallerydropdowndata: [],
  isGallerydropdowndataLoading: true,
  excelColumnHeading: {},
  excelUploadedList: [],
  exportPropertyStatus: [],
  exportPropertyStatusLoading: true,
  ownerNames: [],
  MSleadIsLoading: true,
  currencyListLoading: true,
  MSSimilarPropertiesIsLoading: true,
  bulkReassignPropertyIsLoading: true,
  leadsCount: [],
  propertyCurrency: [],
  waterMarkImage: [],
  isExcelUploadedListLoading: true,
  propertyWithIdNameListIsLoading: true,
  propertyWithIdNameList: [],
  propertyListByIdIsLoading: false,
  propertyAssignments: [],
  propertyModifiedOnData: [],
  isPropertyModifiedOnLoading: false,
  propertyWithGoogleLocation: [],
  isPropertyGoogleLocationLoading: false,
  isPropertyUpdateIsLoading: false,
  isPropertyAddIsLoading: false,
};

export function propertyReducer(
  state: PropertyState = initialState,
  action: Action
): PropertyState {
  switch (action.type) {
    case PropertyActionTypes.FETCH_PROPERTY_LIST:
      return {
        ...state,
        isPropertiesLoading: true
      };
    case PropertyActionTypes.FETCH_PROPERTY_LIST_SUCCESS:
      return {
        ...state,
        properties: (action as FetchPropertyListSuccess).properties.items,
        totalCount: (action as FetchPropertyListSuccess).properties.totalCount,
        isPropertiesLoading: false
      };
    case PropertyActionTypes.FETCH_PROPERTY_COUNT_SUCCESS:
      return {
        ...state,
        propertyCount: (action as FetchPropertyCountSuccess)?.resp,
      };
    case PropertyActionTypes.FETCH_PROPERTY_BY_ID_SUCCESS:
      return {
        ...state,
        selectedProperty: (action as FetchPropertyByIdSuccess).selectedProperty,
        propertyListByIdIsLoading: false,
      };
    case PropertyActionTypes.FETCH_PROPERTY_BY_ID:
      return {
        ...state,
        propertyListByIdIsLoading: true,
      };
    case PropertyActionTypes.FETCH_PROPERTY_SUBMITTED_ID:
      return {
        ...state,
        addedPropertyId: (action as FetchActivePropertyId).propertyId,
      };
    case PropertyActionTypes.UPDATE_FILTER_PAYLOAD:
      return {
        ...state,
        filtersPayload: (action as UpdateFilterPayload).filter,
      };
    case PropertyActionTypes.BULK_REASSIGN_PROPERTY:
      return {
        ...state,
        bulkReassignPropertyIsLoading: true,
      };
    case PropertyActionTypes.BULK_REASSIGN_PROPERTY_SUCCESS:
      return {
        ...state,
        bulkReassignDetails: (action as BulkReassignPropertySuccess).resp,
        bulkReassignPropertyIsLoading: false,
      };
    case PropertyActionTypes.FETCH_LOCATION_LIST:
      return {
        ...state,
        isLocationListLoading: true
      };
    case PropertyActionTypes.FETCH_LOCATION_LIST_SUCCESS:
      return {
        ...state,
        locationList: (action as FetchLocationListSuccess).response,
        isLocationListLoading: false
      };
    case PropertyActionTypes.UPLOAD_BROCHURE_SUCCESS:
      return {
        ...state,
        uploadDoc: action as UploadBrochureSuccess,
      };
    case PropertyActionTypes.FETCH_BROCHURE_LIST_SUCCESS:
      return {
        ...state,
        broList: (action as FetchBrochureListSuccess).response,
      };
    case PropertyActionTypes.FETCH_GALLERY_DROPDOWN_DATA:
      return {
        ...state,
        isGallerydropdowndataLoading: true
      };
    case PropertyActionTypes.FETCH_GALLERY_DROPDOWN_DATA_SUCCESS:
      return {
        ...state,
        gallerydropdowndata: (action as FetchGalleryDropdownDataSuccess)
          .response,
        isGallerydropdowndataLoading: false
      };
    case PropertyActionTypes.FETCH_MATCHING_LEADS_LIST_SUCCESS:
      return {
        ...state,
        matchingLeads: (action as FetchMatchingLeadsListSuccess).response || [],
      };
    case PropertyActionTypes.FETCH_ARCHIVED_PROPERTY_LIST:
      return {
        ...state,
        isArchivedListLoading: true
      };
    case PropertyActionTypes.FETCH_ARCHIVED_PROPERTY_LIST_SUCCESS:
      return {
        ...state,
        archivedList:
          (action as FetchArchivedPropertyListSuccess).response || [],
        isArchivedListLoading: false
      };
    case PropertyActionTypes.PROPERTY_EXCEL_UPLOAD_SUCCESS:
      return {
        ...state,
        excelColumnHeading: (action as PropertyExcelUploadSuccess).resp,
      };
    case PropertyActionTypes.FETCH_EXCEL_UPLOADED_LIST:
      return {
        ...state,
        isExcelUploadedListLoading: true,
      };
    case PropertyActionTypes.FETCH_EXCEL_UPLOADED_LIST_SUCCESS:
      return {
        ...state,
        excelUploadedList: (action as FetchPropertyExcelUploadedSuccess)
          .response,
        isExcelUploadedListLoading: false,
      };
    case PropertyActionTypes.FETCH_EXPORT_PROPERTY_STATUS:
      return {
        ...state,
        exportPropertyStatusLoading: true,
      };
    case PropertyActionTypes.FETCH_EXPORT_PROPERTY_STATUS_SUCCESS:
      return {
        ...state,
        exportPropertyStatus: (action as FetchExportPropertyStatusSuccess)
          .response,
        exportPropertyStatusLoading: false,
      };
    case PropertyActionTypes.FETCH_OWNER_NAMES_SUCCESS:
      return {
        ...state,
        ownerNames: (action as FetchOwnerNamesSuccess).response,
      };
    case PropertyActionTypes.FETCH_MICROSITE_PROPERTY_SUCCESS:
      return {
        ...state,
        micrositeProperty: (action as FetchMicrositePropertySuccess).property,
      };
    case PropertyActionTypes.FETCH_MICROSITE_SIMILAR_PROPERTIES:
      return {
        ...state,
        MSSimilarPropertiesIsLoading: true,
      };
    case PropertyActionTypes.FETCH_MICROSITE_SIMILAR_PROPERTIES_SUCCESS:
      return {
        ...state,
        MSSimilarProperties: (action as FetchMSSimilarPropertiesSuccess)
          .properties,
        MSSimilarPropertiesIsLoading: false,
      };
    case PropertyActionTypes.FETCH_MICROSITE_USER_DETAILS_SUCCESS:
      return {
        ...state,
        micrositeUserDetails: (action as FetchMicrositeUserDetailsSuccess)
          .response,
      };
    case PropertyActionTypes.ADD_MS_LEAD:
      return {
        ...state,
        MSleadIsLoading: true,
      };
    case PropertyActionTypes.ADD_MS_LEAD_SUCCESS:
      return {
        ...state,
        MSleadIsLoading: false,
      };
    case PropertyActionTypes.FETCH_PROPERTY_LEADS_COUNT_BY_IDS_SUCCESS:
      return {
        ...state,
        leadsCount: (action as FetchPropertyLeadsCountByIdsSuccess).counts,
      };
    case PropertyActionTypes.FETCH_PROPERTY_CURRENCY_LIST:
      return {
        ...state,
        currencyListLoading: true,
      };
    case PropertyActionTypes.FETCH_PROPERTY_CURRENCY_LIST_SUCCESS:
      return {
        ...state,
        propertyCurrency: (action as FetchPropertyCurrencySuccess).response,
        currencyListLoading: false,
      };
    case PropertyActionTypes.ADD_WATER_MARK_SUCCESS:
      return {
        ...state,
        waterMarkImage: (action as AddWaterMarkSuccess).response,
      };
    case PropertyActionTypes.FETCH_PROPERTY_WITH_ID_NAME_LIST_SUCCESS:
      return {
        ...state,
        propertyWithIdNameList: (action as FetchPropertyWithIdNameListSuccess)
          .response,
        propertyWithIdNameListIsLoading: false,
      };
    case PropertyActionTypes.FETCH_PROPERTY_ASSIGNMENTS_SUCCESS:
      return {
        ...state,
        propertyAssignments: (action as FetchPropertyAssignmentsSuccess).response,
      };
    case PropertyActionTypes.FETCH_PROPERTY_MODIFIED_ON:
      return {
        ...state,
        isPropertyModifiedOnLoading: true,
      };
    case PropertyActionTypes.FETCH_PROPERTY_MODIFIED_ON_SUCCESS:
      return {
        ...state,
        propertyModifiedOnData: (action as FetchPropertyModifiedOnSuccess).modifiedOnData,
        isPropertyModifiedOnLoading: false,
      };
    case PropertyActionTypes.FETCH_PROPERTY_WITH_GOOGLE_LOCATION:
      return {
        ...state,
        isPropertyGoogleLocationLoading: true,
      };
    case PropertyActionTypes.FETCH_PROPERTY_WITH_GOOGLE_LOCATION_SUCCESS:
      return {
        ...state,
        propertyWithGoogleLocation: (action as FetchPropertyWithGoogleLocationSuccess).response,
        isPropertyGoogleLocationLoading: false,
      };
    case PropertyActionTypes.UPDATE_PROPERTY:
      return {
        ...state,
        isPropertyUpdateIsLoading: true,
      };
    case PropertyActionTypes.UPDATE_PROPERTY_SUCCESS:
      return {
        ...state,
        isPropertyUpdateIsLoading: false,
      };
    case PropertyActionTypes.ADD_PROPERTY:
      return {
        ...state,
        isPropertyAddIsLoading: true,
      };
    case PropertyActionTypes.ADD_PROPERTY_SUCCESS:
      return {
        ...state,
        isPropertyAddIsLoading: false,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.property;

export const getPropertiesIsLoading = createSelector(
  selectFeature,
  (state: PropertyState) => {
    return state.isPropertiesLoading;
  }
);

export const getProperties = createSelector(
  selectFeature,
  (state: PropertyState) => {
    return {
      properties: state.properties,
      propertyCount: state.propertyCount,
    };
  }
);

export const getPropertiesTotalCount = createSelector(
  selectFeature,
  (state: PropertyState) => {
    return {
      totalCount: state.totalCount,
      itemsCount: state.itemsCount,
    };
  }
);

export const getFiltersPayload = createSelector(
  selectFeature,
  (state: PropertyState) => {
    return state.filtersPayload;
  }
);

export const getLocationList = createSelector(
  selectFeature,
  (state: PropertyState) => state.locationList
);

export const getLocationListIsLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.isLocationListLoading
);

export const getBrochureList = createSelector(
  selectFeature,
  (state: PropertyState) => state.broList
);

export const getBulkPropertyDetails = createSelector(
  selectFeature,
  (state: PropertyState) => state.bulkReassignDetails
);

export const getBulkReassignPropertyIsLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.bulkReassignPropertyIsLoading
);

export const getGalleryDropdownDataIsLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.isGallerydropdowndataLoading
);

export const getGalleryDropdownData = createSelector(
  selectFeature,
  (state: PropertyState) => state.gallerydropdowndata
);

export const getMatchingLeads = createSelector(
  selectFeature,
  (state: PropertyState) => state.matchingLeads
);

export const getArchivedProperties = createSelector(
  selectFeature,
  (state: PropertyState) => state.archivedList
);

export const getArchivedPropertiesIsLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.isArchivedListLoading
);

export const getColumnHeadings = createSelector(
  selectFeature,
  (state: PropertyState) => {
    return state.excelColumnHeading;
  }
);

export const getPropertyExcelUploadedList = createSelector(
  selectFeature,
  (state: PropertyState) => state.excelUploadedList
);

export const getPropertyExport = createSelector(
  selectFeature,
  (state: PropertyState) => state.propertyExport
);

export const getExportPropertyStatus = createSelector(
  selectFeature,
  (state: PropertyState) => state.exportPropertyStatus
);

export const getExportPropertyStatusLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.exportPropertyStatusLoading
);

export const getOwnerNames = createSelector(
  selectFeature,
  (state: PropertyState) => state.ownerNames
);

export const getMicrositeProperty = createSelector(
  selectFeature,
  (state: PropertyState) => state.micrositeProperty
);

export const getMicrositeSimilarProperties = createSelector(
  selectFeature,
  (state: PropertyState) => state.MSSimilarProperties
);

export const getMicrositeUserDetails = createSelector(
  selectFeature,
  (state: PropertyState) => state.micrositeUserDetails
);

export const getMSleadIsLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.MSleadIsLoading
);

export const getMSSimilarPropertiesIsLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.MSSimilarPropertiesIsLoading
);

export const getPropertyLeadsCount = createSelector(
  selectFeature,
  (state: PropertyState) => state.leadsCount
);

export const getPropertyCurrencyList = createSelector(
  selectFeature,
  (state: PropertyState) => state.propertyCurrency
);

export const getPropertyCurrencyListIsLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.currencyListLoading
);

export const getWaterMarkImage = createSelector(
  selectFeature,
  (state: PropertyState) => state.waterMarkImage
);

export const getPropertyExcelUploadedListIsLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.isExcelUploadedListLoading
);

export const getPropertyWithIdNameList = createSelector(
  selectFeature,
  (state: PropertyState) => state.propertyWithIdNameList
);

export const getPropertyWithIdLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.propertyWithIdNameListIsLoading
);

export const getPropertyListDetails = createSelector(
  selectFeature,
  (state: PropertyState) => state.selectedProperty
);

export const getPropertyListDetailsIsLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.propertyListByIdIsLoading
);

export const getPropertyAssignments = createSelector(
  selectFeature,
  (state: PropertyState) => state.propertyAssignments
)

export const getPropertyModifiedOnData = createSelector(
  selectFeature,
  (state: PropertyState) => state.propertyModifiedOnData
);

export const getPropertyModifiedOnLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.isPropertyModifiedOnLoading
);

export const getPropertyWithGoogleLocation = createSelector(
  selectFeature,
  (state: PropertyState) => state.propertyWithGoogleLocation
);

export const getPropertyWithGoogleLocationLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.isPropertyGoogleLocationLoading
);

export const getPropertyUpdateIsLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.isPropertyUpdateIsLoading
);

export const getPropertyAddIsLoading = createSelector(
  selectFeature,
  (state: PropertyState) => state.isPropertyAddIsLoading
);
