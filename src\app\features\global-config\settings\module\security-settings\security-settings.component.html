<ng-container *ngIf="canView">
    <div class="py-12 px-30 position-relative h-100-46 scrollbar">
        <div class="flex-between">
            <div class="pt-12 align-center">
                <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-16" routerLink='/global-config'></div>
                <span class="icon ic-guard ic-sm ic-black cursor-pointer mr-8"></span>
                <h5 class="fw-600">Security {{ 'GLOBAL.settings' | translate }}</h5>
            </div>
        </div>
        <form [formGroup]="securityForm" [ngClass]="{'pe-none blinking' : isGlobalSettingLoading}">
            <div class="bg-white pl-20 py-16 mt-20 flex-between br-6">
                <div>
                    <h5 class="fw-600">Two Factor Authentication</h5>
                    <h6 class="text-dark-gray pt-4">Enable two factor authentication to protect your account.</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{securityForm.get('otpEnabled').value == true ? 'on' : 'off'}}</div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, '2FA') : ''" formControlName="otpEnabled"
                        id="chkOtp" name="otpEnabled" [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkOtp" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
            <ng-container *ngIf="securityForm.controls['otpEnabled'].value">
                <div class="border-bottom"></div>
                <div class="py-20 px-30 bg-white text-black-100">
                    <h5 class="field-label-req">OTP Configuration</h5>
                    <!-- <form-errors-wrapper [control]="securityForm.controls['otpChannels']" label="OTP Configuration"> -->
                    <div class="d-flex flex-wrap">
                        <div *ngFor="let channel of securityForm.get('otpChannels').controls; let i = index">
                            <div class="flex-wrap align-center mr-10 mb-12 bg-light-pearl br-4 p-10">
                                <label class="checkbox-container">
                                    <input type="checkbox" [formControl]="channel.get('selected')"
                                        (click)="handleCheckboxClick($event, channel.value.value)">
                                    <span class="ms-1 checkmark"></span>
                                    <div class="text-dark-gray cursor-pointer text-large text-sm ml-6">
                                        {{ channel.value.displayName }}
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                    <h5 class="field-label-req">OTP Receiver</h5>
                    <div class="d-flex flex-wrap">
                        <ng-container *ngFor="let type of otpReceiver">
                            <label class="form-check form-check-inline mr-10 mb-12 bg-light-pearl br-20 p-10 mt-10"
                                for="inpOtpReceiver{{type.value}}">
                                <input type="radio" id="inpOtpReceiver{{type.value}}" name="otpReceiver"
                                    formControlName="otpReceiver" [value]="type.value" class="radio-check-input">
                                <div class="text-dark-gray cursor-pointer text-large text-sm ml-6">
                                    {{type.displayName}}</div>
                            </label>
                        </ng-container>
                    </div>
                    <div class="d-flex ip-flex-col" *ngIf="securityForm.controls['otpReceiver'].value == 1">
                        <div class="align-center">
                            <form-errors-wrapper [control]="securityForm.controls['receiverUserIds']" label="Admin">
                                <div class="ng-select-sm-gray">
                                    <div class="field-label-req">Select Admin(s)</div>
                                    <ng-select [virtualScroll]="true" [multiple]="true" [closeOnSelect]="false"
                                        ResizableDropdown [items]="adminsList" bindLabel="fullName" bindValue="id"
                                        name="receiverUserIds" formControlName="receiverUserIds"
                                        class="bg-white w-250 ph-w-100" placeholder="{{ 'GLOBAL.select' | translate }}"
                                        [ngClass]="{'pe-none' : !canUpdate}">
                                        <ng-template ng-label-tmp let-item="item">
                                            {{item.firstName}} {{item.lastName}}
                                        </ng-template>
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                    class="checkmark"></span><span class="text-truncate-1 break-all">
                                                    {{item.firstName}} {{item.lastName}}</span></div>
                                        </ng-template>
                                    </ng-select>
                                </div>
                            </form-errors-wrapper>
                            <div class="border-bottom w-10 mx-10 ip-d-none"
                                *ngIf="securityForm.controls['receiverUserIds'].value?.length"></div>
                        </div>
                        <div class="mt-20 ip-mt-10" *ngIf="securityForm.controls['receiverUserIds'].value?.length">
                            <div class="text-sm text-dark-gray">Selected</div>
                            <div class="d-flex flex-wrap scrollbar max-h-100px ip-ml-0 mt-6">
                                <ng-container *ngFor="let user of securityForm.controls['receiverUserIds'].value">
                                    <div
                                        class="flex-wrap ph-mr-4 mr-10 bg-secondary px-10 py-12 br-20 align-center text-nowrap mb-20">
                                        <div class="align-center fw-semi-bold cursor-pointer text-black-200"
                                            *ngIf="user">{{getAssignedToDetails(user, adminsList, true) || ''}}</div>
                                        <!-- <span class="ml-10 ic-cancel icon ic-x-xs ic-light-gray"></span> -->
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                    <!-- <ng-container
                        *ngIf="securityForm.controls['otpReceiver'].value == 1 || securityForm.controls['otpReceiver'].value == 2">
                        <div class="border-bottom my-12"></div>
                        <div class="d-flex">
                            <label class="checkbox-container">
                                <input type="checkbox">
                                <span class="checkmark"></span><span class="d-flex">Time frame to get <h5
                                        class="fw-700 ml-4">
                                        “Try another way” </h5> to the user</span>
                                <div class="mt-4 text-sm text-dark-gray">incase of unavailability of manager / admin(s)
                                </div>
                            </label>
                        </div>
                    </ng-container> -->
                </div>
                <div class="border-bottom"></div>
                <div class=" br-4 bg-white px-30 py-20">
                    <h5 class="align-center">Apply two factor authentication to <span
                            class="text-sm text-dark-gray ml-4">
                            (OTP will be sent to you on your WORK Phone/Email)</span>
                    </h5>
                    <div class="d-flex flex-wrap">
                        <ng-container *ngFor="let type of userTypeOptions">
                            <div class="form-check form-check-inline mr-10 mb-12 bg-light-pearl br-20 p-10 mt-10">
                                <input type="radio" id="inpSelectedUser{{type.value}}" name="userOption"
                                    formControlName="userOption" [value]="type.value" class="radio-check-input">
                                <label class="text-dark-gray cursor-pointer text-large text-sm ml-6"
                                    for="inpSelectedUser{{type.value}}">{{type.displayName}}</label>
                            </div>
                        </ng-container>
                    </div>
                    <div class="d-flex ip-flex-col" *ngIf="!securityForm.controls['userOption'].value">
                        <div class="align-center ng-select-sm-gray">
                            <form-errors-wrapper [control]="securityForm.controls['userList']" label="User">
                                <div class="field-label-req">Select User(s)</div>
                                <ng-select [virtualScroll]="true" [multiple]="true" [closeOnSelect]="false"
                                    ResizableDropdown [items]="allActiveUsers" bindLabel="fullName" bindValue="id"
                                    name="user" formControlName="userList" class="bg-white w-250 ph-w-100"
                                    placeholder="{{ 'GLOBAL.select' | translate }}"
                                    [ngClass]="{'pe-none' : !canUpdate}">
                                    <ng-template ng-label-tmp let-item="item">
                                        {{item.firstName}} {{item.lastName}}
                                    </ng-template>
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span><span class="text-truncate-1 break-all">
                                                {{item.firstName}} {{item.lastName}}</span></div>
                                    </ng-template>
                                </ng-select>
                            </form-errors-wrapper>
                            <div class="border-bottom w-10 mx-10 ip-d-none">
                            </div>
                        </div>
                        <div class="mt-20  ip-mt-10" *ngIf="securityForm.controls['userList'].value?.length">
                            <div class="text-sm text-dark-gray">Selected</div>
                            <div class="d-flex flex-wrap scrollbar max-h-100px ip-ml-0 mt-6">
                                <ng-container *ngFor="let user of securityForm.controls['userList'].value">
                                    <div
                                        class="flex-wrap ph-mr-4 mr-10 bg-secondary px-10 py-12 br-20 align-center text-nowrap mb-20">
                                        <div class="align-center fw-semi-bold cursor-pointer text-black-200"
                                            *ngIf="user">{{getAssignedToDetails(user, allActiveUsers, true) ||
                                            ''}}</div>
                                        <!-- <span class="ml-10 ic-cancel icon ic-x-xs ic-light-gray"></span> -->
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="border-bottom"></div>
                <div class="flex-end px-20 py-16 bg-white">
                    <!-- <div class="text-decoration-underline cursor-pointer mr-10" (click)="onCancel()">
                        {{ 'BUTTONS.cancel' | translate }}</div> -->
                    <div class="btn-coal" (click)="TwoFactorAuthenticationSave()" [ngClass]="{'pe-none opacity-5': !securityForm?.dirty}">{{ 'BUTTONS.save' | translate }}
                    </div>
                </div>
            </ng-container>
            <div class="bg-white pl-20 py-16 mt-20 flex-between br-6">
                <div>
                    <h5 class="fw-600">Copy/Paste</h5>
                    <h6 class="text-dark-gray pt-4">Enable Copy - Paste Functionality.</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{securityForm.get('copyPaste').value == true ? 'on' : 'off'}}</div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'copyPaste') : ''"
                        formControlName="copyPaste" id="cpyPst" name="copyPaste" [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="cpyPst" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
            <div class="bg-white pl-20 py-16 mt-20 flex-between br-6">
                <div>
                    <h5 class="fw-600">Disable Screenshot Functionality</h5>
                    <h6 class="text-dark-gray pt-4">This Option will Stop users taking screenshots inside the CRM (Currently Available only on Mobile Application).
                    </h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{securityForm.get('screenshot').value == true ? 'on' : 'off'}}</div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'screenshot') : ''"
                        formControlName="screenshot" id="scrnshot" name="screenshot"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="scrnshot" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
        </form>
    </div>

    <ng-template #changePopup>
        <div class="p-20">
            <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
            <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div>
            <div class="flex-end mt-30">
                <button class="btn-gray mr-20" (click)="closePopup()" id="clkSettingsNo" data-automate-id="clkSettingsNo">
                    {{ 'GLOBAL.no' | translate }}</button>
                <button class="btn-green"
                    (click)="settingType === '2FA' ? TwoFactorAuthenticationSave('modal') : onSave()"
                    id="clkSettingsYes" data-automate-id="clkSettingsYes">
                    {{ 'GLOBAL.yes' | translate }}</button>
            </div>
        </div>
    </ng-template>
</ng-container>