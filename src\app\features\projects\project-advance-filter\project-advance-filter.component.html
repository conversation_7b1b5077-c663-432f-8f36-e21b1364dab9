<div class="property-adv-filter px-30 bg-white brbl-15 brbr-15">
    <!-- Project Type Section -->
    <div class="field-label">{{'PROJECTS.project'| translate}} {{'LABEL.type'| translate}}</div>
    <div class="d-flex flex-wrap">
        <div (click)="projectTypeData(null)" class="mr-8 px-20 py-4 mb-4 br-4 ip-mb-10 align-center cursor-pointer"
            [ngClass]="(appliedFilter.ProjectType =='All' || !appliedFilter.ProjectType) ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'">
            {{ 'GLOBAL.all' | translate }}
        </div>
        <ul *ngFor="let projectType of projectTypeList">
            <div name="ProjectType" (click)="projectTypeData(projectType)"
                class="mr-8 mb-4 br-4 px-20 py-6 ip-mb-10 align-center cursor-pointer"
                id="clkProp{{projectType?.displayName}}" data-automate-id="clkProp{{projectType?.displayName}}"
                [ngClass]="appliedFilter.ProjectType === projectType?.displayName ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'">
                {{projectType?.displayName}}
            </div>
        </ul>
    </div>

    <!-- Status Section -->
    <div class="field-label">{{'GLOBAL.status'| translate}}</div>
    <div class="d-flex flex-wrap">
        <ul *ngFor="let projectStatus of projectStatusList;let index=index">
            <div class="mr-8 mb-4 br-4 ip-mb-10 px-20 py-6 text-light-gray cursor-pointer" name="projectStatus"
                (click)="statusFilterApply(index)" id="clkProp{{projectStatus}}"
                data-automate-id="clkProp{{projectStatus}}" [ngStyle]="changeStatusColor(projectStatus,index)">
                {{projectStatus}}
            </div>
        </ul>
    </div>

    <!-- Availability Section -->
    <div class="field-label">Availability</div>
    <div class="d-flex flex-wrap">
        <ul *ngFor="let currentStatus of projectCurrentStatusList">
            <button name="currentStatus" (click)="appliedFilter.currentStatus = currentStatus;"
                class="mr-8 mb-4 br-4 py-6 px-20 ip-mb-10 cursor-pointer" id="clkProp{{currentStatus}}"
                data-automate-id="clkProp{{currentStatus}}"
                [ngClass]="appliedFilter.currentStatus == currentStatus ? 'bg-black-200 text-white border-0' : 'btn-transparent'">
                {{currentStatus}}
            </button>
        </ul>
    </div>

    <!-- Advanced Filters Grid -->
    <div class="d-flex w-100 flex-wrap ng-select-sm">
        <!-- Builder/Developer Name -->
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Builder/developer Name</div>
            <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="builderDetails" [multiple]="true" appSelectAll [closeOnSelect]="false" 
                    ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindValue="name" bindLabel="name"
                    [(ngModel)]="appliedFilter.BuilderName">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container">
                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                [checked]="item$.selected">
                            <span class="checkmark"></span>
                            <span class="text-truncate-1 break-all">{{item.name}}</span>
                        </div>
                    </ng-template>
                </ng-select>
            </div>
        </div>

        <!-- Project Sub Type -->
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'Project sub type'}}</div>
            <div class="mr-20">
                <form-errors-wrapper label="Sub type">
                    <ng-select [clearSearchOnAdd]="true" ResizableDropdown [items]="projectSubType" [multiple]="true" appSelectAll 
                        [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName"
                        bindValue="id" [(ngModel)]="appliedFilter.projectSubType">
                        <ng-template ng-label-tmp let-item="item" let-clear="clear">
                            <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                            <span class="ng-value-label"> {{item?.displayName}}</span>
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="d-flex">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span>
                                    <div class="w-100">
                                        <span class="text-truncate-1 break-all">{{item?.displayName}}</span>
                                        <i class="text-truncate-1 break-all text-dark-gray">({{item?.projectType}})</i>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ng-select>
                </form-errors-wrapper>
            </div>
        </div>
        <!-- Possession -->
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Possession</div>
            <div class="mr-20">
                <app-possession-filter
                    #possessionFilter
                    [initialPossessionType]="appliedFilter.PossesionType"
                    [initialFromPossessionDate]="appliedFilter.FromPossesionDate"
                    [initialToPossessionDate]="appliedFilter.ToPossesionDate"
                    [userTimeZoneOffset]="userData?.timeZoneInfo?.baseUTcOffset"
                    (possessionFilterChange)="onPossessionFilterChange($event)">
                </app-possession-filter>
            </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Facing</div>
            <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="facing" [multiple]="true" appSelectAll [closeOnSelect]="false"
                    ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" [(ngModel)]="appliedFilter.facings">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container">
                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                [checked]="item$.selected">
                            <span class="checkmark"></span>{{item}}
                        </div>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'GLOBAL.min' | translate}} {{'PROPERTY.PROPERTY_DETAIL.price' | translate}}</div>
            <div class="w-100 align-center">
                <div class="w-70 position-relative no-input-validation input-sm">
                    <form-errors-wrapper>
                        <div class="w-100 d-flex">
                            <div class="w-50">
                                <input type="number" (input)="minBudgetCheck()" (keydown)="onlyNumbers($event)" min="0"
                                    [(ngModel)]="appliedFilter.FromMinPrice" placeholder="ex. 123">
                            </div>
                            <h6 class="text-sm text-mud align-center m-4">To</h6>
                            <div class="w-50">
                                <input type="number" (input)="minBudgetCheck()" (keydown)="onlyNumbers($event)" min="0"
                                    [(ngModel)]="appliedFilter.ToMinPrice" placeholder="ex. 123">
                            </div>
                        </div>
                    </form-errors-wrapper>
                    <div *ngIf="appliedFilter.ToMinPrice" class="position-absolute right-10 top-32">
                        <span class="text-nowrap text-xs text-accent-green fw-semi-bold">
                            {{formatBudget(appliedFilter.ToMinPrice,appliedFilter?.Currency || defaultCurrency)}}
                        </span>
                    </div>
                    <div *ngIf="appliedFilter.FromMinPrice" class="position-absolute left-10 top-32">
                        <span class="text-nowrap text-xs text-accent-green fw-semi-bold">
                            {{formatBudget(appliedFilter.FromMinPrice,appliedFilter?.Currency || defaultCurrency)}}
                        </span>
                    </div>
                </div>
                <div class="text-xs text-red mt-60 fw-semi-bold position-absolute"
                    *ngIf="(appliedFilter.FromMinPrice && appliedFilter.ToMinPrice) && !minBudgetValidation">
                    {{'Min price cannot be greater than max price'}}
                </div>
                <div class="w-30 ml-8 mr-20">
                    <form-errors-wrapper label="Currency">
                        <ng-container *ngIf="projectCurrency?.length > 1; else showCurrencySymbol">
                            <ng-select [ngClass]="{'pe-none blinking': isProjectCurrencyLoading}"
                                [(ngModel)]="appliedFilter.Currency" ResizableDropdown class="manage-dropdown">
                                <ng-option *ngFor="let curr of projectCurrency" [value]="curr">{{curr}}</ng-option>
                            </ng-select>
                        </ng-container>
                        <ng-template #showCurrencySymbol>
                            <h5 class="rupees px-12 py-4 fw-600 m-4">{{ defaultCurrency }}</h5>
                        </ng-template>
                    </form-errors-wrapper>
                </div>
            </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'GLOBAL.max' | translate}} {{'PROPERTY.PROPERTY_DETAIL.price' | translate}}</div>
            <div class="w-100 align-center">
                <div class="w-70 position-relative no-input-validation input-sm">
                    <form-errors-wrapper>
                        <div class="w-100 d-flex">
                            <div class="w-50">
                                <input type="number" (input)="maxBudgetCheck()" (keydown)="onlyNumbers($event)" min="0"
                                    [(ngModel)]="appliedFilter.FromMaxPrice" placeholder="ex. 123">
                            </div>
                            <h6 class="text-sm text-mud align-center m-4">To</h6>
                            <div class="w-50">
                                <input type="number" (input)="maxBudgetCheck()" (keydown)="onlyNumbers($event)" min="0"
                                    [(ngModel)]="appliedFilter.ToMaxPrice" placeholder="ex. 123">
                            </div>
                        </div>
                    </form-errors-wrapper>
                    <div *ngIf="appliedFilter.ToMaxPrice" class="position-absolute right-10 top-32">
                        <span class="text-nowrap text-xs text-accent-green fw-semi-bold">
                            {{formatBudget(appliedFilter.ToMaxPrice,appliedFilter?.Currency || defaultCurrency)}}
                        </span>
                    </div>
                    <div *ngIf="appliedFilter.FromMaxPrice" class="position-absolute left-10 top-32">
                        <span class="text-nowrap text-xs text-accent-green fw-semi-bold">
                            {{formatBudget(appliedFilter.FromMaxPrice,appliedFilter?.Currency || defaultCurrency)}}
                        </span>
                    </div>
                </div>
                <div class="text-xs text-red mt-60 fw-semi-bold position-absolute"
                    *ngIf="(appliedFilter.FromMaxPrice && appliedFilter.ToMaxPrice) && !maxBudgetValidation">
                    {{'Min price cannot be greater than max price'}}
                </div>
                <div class="w-30 ml-8 mr-20">
                    <form-errors-wrapper label="Currency">
                        <ng-container *ngIf="projectCurrency?.length > 1; else showCurrencySymbol">
                            <ng-select [ngClass]="{'pe-none blinking': isProjectCurrencyLoading}"
                                [(ngModel)]="appliedFilter.Currency" ResizableDropdown class="manage-dropdown">
                                <ng-option *ngFor="let curr of projectCurrency" [value]="curr">{{curr}}</ng-option>
                            </ng-select>
                        </ng-container>
                        <ng-template #showCurrencySymbol>
                            <h5 class="rupees px-12 py-4 fw-600 m-4">{{ defaultCurrency }}</h5>
                        </ng-template>
                    </form-errors-wrapper>
                </div>
            </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100 field-rupees-tag">
            <div class="field-label">{{ 'PROJECTS.start-date' | translate }}</div>
            <div class="position-relative mr-20">
                <form-errors-wrapper>
                    <div class="rupees-sm icon ic-calendar ic-xxs ic-coal"></div>
                    <input type="text" [owlDateTime]="dt2" [owlDateTimeTrigger]="dt2" readonly bsDatepicker
                        [max]="appliedFilter.ToDate" [(ngModel)]="appliedFilter.FromDate" id="inpStartDate"
                        placeholder="ex. 05/03/2025" data-automate-id="inpStartDate">
                    <div (click)="appliedFilter.FromDate=null" *ngIf="appliedFilter.FromDate"
                        class="right-4 align-center cursor-pointer position-absolute top-8 pr-10">
                        <span class="icon ic-refresh ic-coal ic-xs"></span>
                    </div>
                    <owl-date-time [pickerType]="'calendar'" #dt2
                        (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                </form-errors-wrapper>
            </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100 field-rupees-tag">
            <div class="field-label">{{ 'PROJECTS.end-date' | translate }}</div>
            <div class="position-relative mr-20" [ngClass]="{'pe-none disabled' : !appliedFilter.FromDate}">
                <form-errors-wrapper>
                    <div class="rupees-sm icon ic-calendar ic-xxs ic-coal"></div>
                    <input type="text" [owlDateTime]="dt3" [owlDateTimeTrigger]="dt3" readonly bsDatepicker
                        [min]="appliedFilter.FromDate" [(ngModel)]="appliedFilter.ToDate" id="inpEndDate"
                        placeholder="ex. 05/03/2025" data-automate-id="inpEndDate">
                    <div (click)="appliedFilter.ToDate=null" *ngIf="appliedFilter.ToDate"
                        class="right-4 align-center cursor-pointer position-absolute top-8 pr-10">
                        <span class="ic-refresh ic-coal icon ic-xs"></span>
                    </div>
                    <owl-date-time [pickerType]="'calendar'" #dt3
                        (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                </form-errors-wrapper>
            </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Land Area</div>
            <div class="w-100 align-center">
                <div class="w-60pr no-input-validation input-sm">
                    <form-errors-wrapper>
                        <div class="w-100 d-flex">
                            <div class="w-50">
                                <input type="number" (input)="validateCarpetArea()"
                                    (keydown)="onlyNumbersWithDecimal($event,$event.target.value)" min="0"
                                    [(ngModel)]="appliedFilter.MinCarpetArea" id="inpMinCarpetArea"
                                    data-automate-id="inpMinCarpetArea" placeholder="ex. 123">
                            </div>
                            <h6 class="text-sm text-mud align-center m-4">To</h6>
                            <div class="w-50">
                                <input type="number" [(ngModel)]="appliedFilter.MaxCarpetArea"
                                    (keydown)="onlyNumbersWithDecimal($event,$event.target.value)" min="0"
                                    (input)="validateCarpetArea()" id="inpMaxCarpetArea"
                                    data-automate-id="inpMaxCarpetArea" placeholder="ex. 123">
                            </div>
                        </div>
                    </form-errors-wrapper>
                </div>
                <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                    *ngIf="appliedFilter.MinCarpetArea && appliedFilter.MaxCarpetArea && !carpetAreaValidations ">
                    {{'PROPERTY.area-validation' | translate}}</div>
                <div class="w-40pr ml-8 mr-20 no-validation">
                    <form-errors-wrapper>
                        <ng-select [virtualScroll]="true" tabindex="4" placeholder="ex. sq. feet."
                            [items]="areaSizeUnits" ResizableDropdown bindValue="id"
                            [(ngModel)]="appliedFilter.CarpetAreaUnitId" bindLabel="unit">
                        </ng-select>
                    </form-errors-wrapper>
                </div>
            </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LOCATION.location'| translate}}</div>
            <div class="mr-20">
                <ng-select [virtualScroll]="true" [ngClass]="{'pe-none blinking': isLocalityListLoading}"
                    [items]="localityList" [multiple]="true" appSelectAll [closeOnSelect]="false" ResizableDropdown
                    placeholder="{{'GLOBAL.select' | translate}}" [(ngModel)]="appliedFilter.locations">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container">
                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                [checked]="item$.selected">
                            <span class="checkmark"></span>
                            <span class="text-truncate-1 break-all">{{item}}</span>
                        </div>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'PROPERTY.STEPS.amenities'| translate}}</div>
            <div class="mr-20">
                <ng-select [virtualScroll]="true" [ngClass]="{'pe-none blinking': isAllAmenityListLoading}"
                    [items]="allAmenityList" [multiple]="true" appSelectAll [closeOnSelect]="false" ResizableDropdown bindValue="id"
                    bindLabel="amenityDisplayName" placeholder="{{'GLOBAL.select' | translate}}"
                    [(ngModel)]="appliedFilter.AmenitesIds">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container">
                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                [checked]="item$.selected">
                            <span class="checkmark"></span>
                            <span class="text-truncate-1 break-all">{{item.amenityDisplayName}}</span>
                        </div>
                    </ng-template>
                </ng-select>
            </div>
        </div>

        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Lead Count</div>
            <div class="mr-20">
                <div class="w-100 align-center">
                    <div class="w-100 no-input-validation">
                        <form-errors-wrapper>
                            <div class="w-100 d-flex">
                                <div class="w-50">
                                    <input type="number" (keydown)="onlyNumbers($event)" (input)="minMaxLeadCheck()"
                                        [max]="appliedFilter.MaxLeadCount" placeholder="ex. 10" maxlength="10"
                                        [(ngModel)]="appliedFilter.MinLeadCount">
                                </div>
                                <h6 class="text-sm text-mud align-center m-4">To</h6>
                                <div class="w-50">
                                    <input type="number" (keydown)="onlyNumbers($event)" (input)="minMaxLeadCheck()"
                                        [min]="appliedFilter.MinLeadCount" placeholder="ex. 1000" maxlength="10"
                                        [(ngModel)]="appliedFilter.MaxLeadCount">
                                </div>
                            </div>
                        </form-errors-wrapper>
                        <div class="text-xs text-red fw-semi-bold position-absolute"
                            *ngIf="(appliedFilter.MinLeadCount && appliedFilter.MaxLeadCount) && !minMaxLeadValidation">
                            {{'Min lead cannot be greater than max lead'}}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Data Count</div>
            <div class="mr-20">
                <div class="w-100 align-center">
                    <div class="w-100 no-input-validation">
                        <form-errors-wrapper>
                            <div class="w-100 d-flex">
                                <div class="w-50">
                                    <input type="number" (keydown)="onlyNumbers($event)" (input)="minMaxDataCheck()"
                                        [max]="appliedFilter.MaxProspectCount" placeholder="ex. 10" maxlength="10"
                                        [(ngModel)]="appliedFilter.MinProspectCount">
                                </div>
                                <h6 class="text-sm text-mud align-center m-4">To</h6>
                                <div class="w-50">
                                    <input type="number" (keydown)="onlyNumbers($event)" (input)="minMaxDataCheck()"
                                        [min]="appliedFilter.MinProspectCount" placeholder="ex. 1000" maxlength="10"
                                        [(ngModel)]="appliedFilter.MaxProspectCount">
                                </div>
                            </div>
                        </form-errors-wrapper>
                        <div class="text-xs text-red fw-semi-bold position-absolute"
                            *ngIf="(appliedFilter.MinProspectCount && appliedFilter.MaxProspectCount) && !minMaxDataValidation">
                            {{'Min data cannot be greater than max data'}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="flex-end py-20">
        <u class="mr-10 fw-semi-bold text-mud cursor-pointer" (click)="modalRef.hide()">
            {{'BUTTONS.cancel' | translate}}</u>
        <div class="btn-gray mr-10" (click)="reset()">{{ 'GLOBAL.reset' | translate }}</div>
        <div (click)="applyFilter()" class="btn-coal">{{ 'GLOBAL.search' | translate }}</div>
    </div>
</div>