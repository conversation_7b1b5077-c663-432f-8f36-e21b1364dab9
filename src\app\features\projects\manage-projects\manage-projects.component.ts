import {
  Component,
  EventE<PERSON>ter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';
import { Store } from '@ngrx/store';
import { CellClickedEvent } from 'ag-grid-community';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { Subject, firstValueFrom, skipWhile, takeUntil } from 'rxjs';

import {
  EMPTY_GUID,
  PAGE_SIZE,
  PROJECT_FILTERS_KEY_LABEL,
  PROJECT_STATUS,
  SHOW_ENTRIES,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import { Facing, PossessionType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  formatBudget,
  getAssignedToDetails,
  getISODateFormat,
  getLocationDetailsByObj,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  setTimeZoneDate,
  toggleValidation,
  validateAllFormFields
} from 'src/app/core/utils/common.util';
import { ProjectActionComponent } from 'src/app/features/projects/manage-projects/project-action/project-action.component';
import { ProjectStatusComponent } from 'src/app/features/projects/manage-projects/project-status/project-status.component';
import { MatchingLeadsComponent } from 'src/app/features/property/matching-leads/matching-leads.component';
import { getAllAmenities } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';
import {
  FetchPriorityList,
  UpdateMultiUserAssignment,
} from 'src/app/reducers/automation/automation.actions';
import { getPriorityList } from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchAreaUnitList,
  FetchProjectTypes,
} from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits, getProjectTypes } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchExportProjectStatus,
  FetchProjectCount,
  FetchProjectExcelUploadedList,
  FetchProjectList,
  UpdateProjectsFiltersPayload,
} from 'src/app/reducers/project/project.action';
import {
  getFiltersPayload,
  getProject,
  getProjectAllCount,
  getProjectIsLoading,
  getProjectLeadsCount,
  getProjectTotalCount,
} from 'src/app/reducers/project/project.reducer';
import { FetchProjectExportSuccess } from 'src/app/reducers/reports/reports.actions';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { ProjectsService } from 'src/app/services/controllers/projects.service';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { BaseGridComponent } from 'src/app/shared/components/base-grid/base-grid.component';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { ExportProjectTrackerComponent } from 'src/app/shared/components/export-project-tracker/export-project-tracker.component';
import { ShareExternalComponent } from 'src/app/shared/components/share-external/share-external.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { ExcelUploadedStatusComponent } from '../../leads/excel-uploaded-status/excel-uploaded-status.component';
import { ProjectAdvanceFilterComponent } from '../project-advance-filter/project-advance-filter.component';

@Component({
  selector: 'manage-projects',
  templateUrl: './manage-projects.component.html',
})
export class ManageProjectsComponent
  extends BaseGridComponent
  implements OnInit, OnDestroy {
  @ViewChild('dt4') dt4: OwlDateTimeComponent<any>;
  getPages = getPages;
  getAssignedToDetails = getAssignedToDetails;
  readonly PAGE_SIZE = PAGE_SIZE;
  readonly SHOW_ENTRIES = SHOW_ENTRIES;
  readonly EMPTY_GUID = EMPTY_GUID;
  readonly PROJECT_STATUS = PROJECT_STATUS;
  readonly projectFiltersKeyLabel = PROJECT_FILTERS_KEY_LABEL;
  readonly statusMap: any = {
    1: { label: 'Upcoming', color: '#FF7A00', bgColor: '#FF7A0014' },
    2: { label: 'Ongoing', color: '#08A3B9', bgColor: '#08A3B914' },
    3: { label: 'Ready to Move', color: '#2FA64D', bgColor: '#2FA64D14' },
    4: { label: 'New', color: '#57D2FF', bgColor: '#d3f3ec' },
    5: { label: 'Resale', color: '#DCD427', bgColor: '#fff6d9' },
    6: { label: 'Pre Launch', color: '#FB9A64', bgColor: '#fad6d6' },
    7: { label: 'Launch', color: '#769933', bgColor: '#cdede6' },
  };
  pageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  private stopper = new EventEmitter<void>();
  searchTermSubject = new Subject<string>();
  gridApi: any;
  gridColumnApi: any;
  gridOptions: any;
  projectTotalCount: any;
  columnDropDown: { field: string; hide: boolean }[] = [];
  columns: any[];
  defaultColumns: any[];
  rowData: any = [];
  integrationDuplicateForm: FormGroup;
  integrationDualOwnerForm: FormGroup;
  selectedPageSize = 10;
  currOffset = 0;
  searchTerm: string;
  showLeftNav = true;
  showFilters = false;
  isViewAllProjects = true;
  isProjectsLoading = true;
  selectedNodes: any;
  moduleId: string;
  defaultCurrency = 'INR';
  currentDate = new Date();
  date: any = new Date();
  allAmenityList: any[] = [];
  areaSizeUnits: any[] = [];
  projectTypeList: Array<any> = JSON.parse(localStorage.getItem('projectType') || '[]');
  projectAllTypeList = ['All', 'Residential', 'Commercial', 'Agricultural'];
  projectSubType: any[] = [];
  projectTypeCount: any;
  allUsers: any[] = [];
  usersList: any[] = [];
  users: any[] = [];
  allActiveUsers: any;
  allUserList: any;
  activeUsers: any;
  userList: any;
  userData: any;
  canSearch = false;
  canView = false;
  canBulkPermanentDelete = false;
  canAssign = false;
  canEdit = false;
  canAdd: boolean = false;
  canBulkDelete: boolean = false;
  canBulkShare: boolean = false;
  canAssignToAny = false;
  canAllowSecondaryUsers = false;
  canEnableAllowSecondaryUsers = false;
  canAllowDuplicates = false;
  canEnableAllowDuplicates = false;
  canAssignSequentially = false;
  canExport: boolean = false;
  canBulkUpload: boolean = false;
  assignedUser: any[] = [];
  assignedDuplicateUser: any[] = [];
  assignedPrimaryUsers: any[] = [];
  assignedSecondaryUsers: any[] = [];
  selectedSectionLeadAssignment: 'Configuration' | 'Selected Users' =
    'Configuration';
  listSelection = 'original';
  selectedUserType: 'Primary User(s)' | 'Secondary User(s)' = 'Primary User(s)';
  sameAsPrimaryUsers = false;
  sameAsSelectedUsers = false;
  sameAsAbove = false;
  appliedFilter: any = {
    pageNumber: 1,
    pageSize: this.PAGE_SIZE,
    path: 'project',
    SearchText: null,
    ProjectVisibility: 0,
  };
  noDataFound: AnimationOptions = {
    path: 'assets/animations/empty-notes.json',
  };
  allowDuplicatesPopupRef: any;
  message: string;
  notes: string;
  isBulkDeleting: boolean = false
  isPermanentDelete: boolean;
  canBulkRestore: boolean = false;
  canBulkReassign: boolean = false;
  selectedTrackerOption: string;
  selectedOption: string;
  globalSettingsData: any;
  get filteredUsers(): string[] {
    return this.listSelection === 'original'
      ? this.assignedUser
      : this.assignedDuplicateUser;
  }
  get primarySeondaryUsers(): string[] {
    return this.listSelection === 'primary'
      ? this.assignedPrimaryUsers
      : this.listSelection === 'secondary'
        ? this.assignedSecondaryUsers
        : this.assignedDuplicateUser;
  }

  constructor(
    private fb: FormBuilder,
    public router: Router,
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private store: Store<AppState>,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private _notificationService: NotificationsService,
    private shareDataService: ShareDataService,
    private bulkReassignModalRef: BsModalRef,
    private bulkDeleteModalRef: BsModalRef,
    private bulkRestoreModalRef: BsModalRef,
    private api: ProjectsService,
    public trackingService: TrackingService
  ) {
    super();
    this.metaTitle.setTitle('CRM | Projects');
    this.store.dispatch(new FetchUsersListForReassignment());

    this._store.select(getAllAmenities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const allAmenities = data.flatMap((category: any) => category.amenities);
        this.allAmenityList = allAmenities
        this.allAmenityList?.sort((a: any, b: any) =>
          a.amenityDisplayName.localeCompare(b.amenityDisplayName)
        );
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canSearch = permissionsSet.has('Permissions.Projects.Search');
        this.canView = permissionsSet.has('Permissions.Projects.View');
        this.canBulkPermanentDelete = permissionsSet.has('Permissions.Projects.BulkPermanentDelete');
        this.canAssign = permissionsSet.has('Permissions.Projects.Assign');
        this.canEdit = permissionsSet.has('Permissions.Projects.Update');
        this.canAdd = permissionsSet.has('Permissions.Projects.Create');
        this.canExport = permissionsSet.has('Permissions.Projects.Export');
        this.canBulkUpload = permissionsSet.has('Permissions.Projects.BulkUpload');
        this.canBulkReassign = permissionsSet.has(
          'Permissions.Projects.BulkReassign'
        );
        this.canBulkDelete = permissionsSet.has(
          'Permissions.Projects.BulkDelete'
        );
        this.canBulkShare = permissionsSet.has(
          'Permissions.Projects.BulkShare'
        );
        this.canBulkRestore = permissionsSet.has(
          'Permissions.Projects.BulkRestore'
        );
        this.canAssignToAny = permissionsSet.has(
          'Permissions.Users.AssignToAny'
        );
        if (this.canAssignToAny) {
          this.store.dispatch(new FetchUsersListForReassignment());
        } else {
          this.store.dispatch(new FetchAdminsAndReportees());
        }
        this.initializeGridSettings();
      });

    this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.userList = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.userList = assignToSort(this.userList, '');
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
        this.selectAllForDropdownItems(this.activeUsers);
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
        this.allUserList = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allUserList = assignToSort(this.allUserList, '');
        this.gridApi?.refreshCells();
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
        this.selectAllForDropdownItems(this.allActiveUsers);
      });

    this.store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter((item) => item.name === 'Project');
        this.moduleId = filteredData.map((item) => item.id).join(', ');
      });

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
    this.filterFunction();
  }

  ngOnInit() {
    this.integrationDuplicateForm = this.fb.group({
      assignedUser: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]],
    });

    this.integrationDualOwnerForm = this.fb.group({
      assignedPrimaryUsers: [null, Validators.required],
      assignedSecondaryUsers: [null, Validators.required],
      assignedDuplicateUser: [null, Validators.required],
      selectedUserType: ['Primary User(s)', Validators.required],
    });

    if (!this.projectTypeList) {
      this._store.dispatch(new FetchProjectTypes());
      this._store
        .select(getProjectTypes)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          let projectTypeList = data?.items;
          this.projectTypeList = projectTypeList;
        });
    }

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data
        this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : null;
        this.canEnableAllowDuplicates =
          data?.duplicateFeatureInfo?.isFeatureAdded;
        this.canEnableAllowSecondaryUsers = data?.isDualOwnershipEnabled;
      });

    this.projectSubType = [];
    this.projectTypeList?.map((item: any) => {
      item?.childTypes.map((item: any) => {
        this.projectSubType.push(item);
      });
    });
    this.projectSubType?.sort((a: any, b: any) =>
      a.displayName.localeCompare(b.displayName)
    );

    this.shareDataService.setProjectTitleId(null);

    this._store
      .select(getProjectAllCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectTypeCount = data?.data;
      });

    this._store.dispatch(new FetchAreaUnitList());
    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this.selectedPageSize = 10;

    this.store
      .select(getProjectIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProjectsLoading = isLoading;
      });

    this.store
      .select(getProjectLeadsCount)
      .pipe(
        skipWhile(() => this.isProjectsLoading),
        takeUntil(this.stopper)
      )
      .subscribe((data: any) => {
        this.rowData = this.rowData?.map?.((row: any) => {
          const matchingData = data?.find(
            (item: any) => item.projectId === row.id
          );
          const leadsCount = matchingData ? matchingData.leadCount : 0;
          const meetingDoneCount = matchingData
            ? matchingData.meetingDoneCount
            : 0;
          const meetingDoneUniqueCount = matchingData
            ? matchingData.meetingDoneUniqueCount
            : 0;
          const siteVisitDoneCount = matchingData
            ? matchingData.siteVisitDoneCount
            : 0;
          const siteVisitDoneUniqueCount = matchingData
            ? matchingData.siteVisitDoneUniqueCount
            : 0;
          const prospectCount = matchingData ? matchingData.prospectCount : 0;

          return {
            ...row,
            leadsCount,
            prospectCount,
            meetingDoneCount,
            meetingDoneUniqueCount,
            siteVisitDoneCount,
            siteVisitDoneUniqueCount,
          };
        });
      });

    this.store
      .select(getProject)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data?.filter((obj: any) => obj.currentStatus !== 1);
        data?.map((item: any) => {
          if (item.currentStatus === 1) {
            this.rowData?.push(item);
          }
        });
      });

    this.headerTitle.setLangTitle('SIDEBAR.manage-projects');
    this.store.select(getProjectTotalCount).subscribe((count: any) => {
      this.projectTotalCount = count.totalCount;
    });

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.users = data;
        this.usersList = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.usersList = assignToSort(this.usersList, '');
        this.allUsers = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allUsers = assignToSort(this.allUsers, '');
      });

    this.store
      .select(getFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.appliedFilter = data;
        this.currOffset = this.appliedFilter?.pageNumber - 1;
        this.appliedFilter = {
          ...this.appliedFilter,
          projectName: this.appliedFilter?.ProjectName,
          pageNumber: this.appliedFilter?.pageNumber,
          pageSize: this.appliedFilter?.pageSize,
          SearchText: this.appliedFilter?.SearchText,
        };
      });

    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });
    this.filterFunction();
    this.trackingService.trackFeature('Web.Project.Page.Project.Visit')
  }

  selectAllForDropdownItems(items: any[]) {
    let allSelect = (items: any) => {
      items.forEach((element: any) => {
        element['selectedAllGroup'] = 'selectedAllGroup';
      });
    };

    allSelect(items);
  }

  originalDuplicateListToggle(selection: string) {
    this.listSelection = selection;
  }

  validateAssignmentForm() {
    if (this.canAllowDuplicates && !this.canAllowSecondaryUsers) {
      if (!this.integrationDuplicateForm?.valid) {
        validateAllFormFields(this.integrationDuplicateForm);
        return false;
      }
      if (
        this.assignedUser?.length == 1 &&
        this.assignedDuplicateUser?.length == 1
      ) {
        if (this.assignedUser?.[0] == this.assignedDuplicateUser?.[0]) {
          this._notificationService.warn(
            'Warning',
            'Duplicate user assignment detected.'
          );
          return false;
        }
      }
    } else if (this.canAllowDuplicates && this.canAllowSecondaryUsers) {
      if (!this.integrationDualOwnerForm?.valid) {
        validateAllFormFields(this.integrationDualOwnerForm);
        return false;
      }
      if (
        (this.assignedPrimaryUsers?.length == 1 &&
          this.assignedSecondaryUsers?.length == 1) ||
        (this.assignedDuplicateUser?.length == 1 &&
          this.assignedSecondaryUsers?.length == 1) ||
        (this.assignedDuplicateUser?.length == 1 &&
          this.assignedPrimaryUsers?.length == 1)
      ) {
        if (
          (this.assignedPrimaryUsers?.[0] == this.assignedDuplicateUser?.[0] &&
            this.assignedPrimaryUsers?.length == 1 &&
            this.assignedDuplicateUser?.length == 1) ||
          (this.assignedPrimaryUsers?.[0] == this.assignedSecondaryUsers?.[0] &&
            this.assignedPrimaryUsers?.length == 1 &&
            this.assignedSecondaryUsers?.length == 1) ||
          (this.assignedSecondaryUsers?.[0] ==
            this.assignedDuplicateUser?.[0] &&
            this.assignedSecondaryUsers?.length == 1 &&
            this.assignedDuplicateUser?.length == 1)
        ) {
          this._notificationService.warn(
            'Warning',
            'Duplicate user assignment detected.'
          );
          return false;
        }
      }
    } else if (!this.canAllowDuplicates && this.canAllowSecondaryUsers) {
      if (
        !this.integrationDualOwnerForm.controls['assignedPrimaryUsers'].valid ||
        !this.integrationDualOwnerForm.controls['assignedSecondaryUsers'].valid
      ) {
        validateAllFormFields(this.integrationDualOwnerForm);
        return false;
      }
      if (
        this.assignedPrimaryUsers?.length &&
        this.assignedSecondaryUsers?.length
      ) {
        if (
          this.assignedPrimaryUsers?.length == 1 &&
          this.assignedSecondaryUsers?.length == 1
        ) {
          if (
            this.assignedPrimaryUsers?.[0] == this.assignedSecondaryUsers?.[0]
          ) {
            this._notificationService.warn(
              'Warning',
              'Duplicate user assignment detected.'
            );
            return false;
          }
        }
        return true;
      }
    }
    return true;
  }

  openConfirmModal(allowDuplicatesPopupRef: any, settingType: string) {
    this.allowDuplicatesPopupRef = this.modalService.show(
      allowDuplicatesPopupRef,
      {
        class: 'modal-600 top-modal ip-modal-unset',
        ignoreBackdropClick: true,
        keyboard: false,
      }
    );
    switch (settingType) {
      case 'allowDuplicateLeads':
        this.message =
          'To use this feature “Allow Lead Duplicates” must be enabled.';
        this.notes = 'Please read the instructions clearly and proceed.';
        break;
      case 'allowSecondaryUsers':
        this.message =
          'To use this feature “Dual Lead Ownership” must be enabled.';
        this.notes = 'Please read the instructions clearly and proceed.';
        break;
    }
  }

  openAdvFiltersModal() {
    this.trackingService.trackFeature(`Web.Project.Button.AdvanceFilter.Click`)
    if (!this.appliedFilter.Currency) {
      this.appliedFilter.Currency = this.defaultCurrency;
    }
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
      initialState: {
        applyAdvancedFilter: this.applyAdvancedFilter.bind(this),
        onClearAllFilters: this.reset.bind(this),
        filterPayload: this.appliedFilter,
        areaSizeUnits: this.areaSizeUnits,
      },
    };
    const modalRef = this.modalService.show(
      ProjectAdvanceFilterComponent,
      initialState
    );
    modalRef.onHide.subscribe(() => {
      this.appliedFilter.allAmenityList = this.allAmenityList;
    });
  }

  async initializeGridSettings() {
      this.globalSettingsData = await firstValueFrom(
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(skipWhile((data) => !Object.keys(data).length))
  );
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 50;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Availability',
        minWidth: 100,
        maxWidth: 100,
        filter: false,
        lockPosition: true,
        valueGetter: (params: any) => [params.data?.status],
        cellRenderer: ProjectStatusComponent,
      },
      {
        headerName: 'Project Name',
        autoHeight: true,
        field: 'Project Name',
        lockPosition: true,
        valueGetter: (params: any) => [params.data?.name],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.data?.name}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Location',
        field: 'Location',
        hide: true,
        valueGetter: (params: any) => ({
          formattedAddress: params.data?.address ? getLocationDetailsByObj(params.data.address) : '--'
        }),
        cellRenderer: (params: any) => {
          const address = params.value.formattedAddress;
          if (address === '') return '--';
          return `
            <p class="text-truncate-1 break-all" title="${address}">
              <i class="pi pi-map-marker" aria-hidden="true"></i>
              ${address}
            </p>
          `;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Land Area',
        field: 'area',
        hide: true,
        valueGetter: (params: any) =>
          params.data?.area
            ? params.data?.area +
            ' ' +
            this.getUnitById(params.data?.areaUnitId)
            : '--',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Project Type',
        field: 'Project Type',
        valueGetter: (params: any) => [
          params?.data?.projectType
            ? params.data?.projectType?.displayName
            : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params?.value}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Certificates',
        field: 'certificates',
        hide: true,
        valueGetter: (params: any) =>
          params.data?.certificates ? params.data?.certificates : '--',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Min. Price',
        field: 'Min. Price',
        minWidth: 200,
        hide: true,
        valueGetter: (params: any) => [
          params.data.minimumPrice || '',
          params.data.minimumPrice
            ? params.data?.monetaryInfo?.currency || this.defaultCurrency
            : '',
        ],
        cellRenderer: (params: any) => {
          const [budget, currency] = params.value;
          const formattedBudget = formatBudget(budget, currency);
          const budgetString = budget
            ? `${params.value[1]}${params.value[0]} (${formattedBudget})`
            : '';
          return `<p>${budgetString}</p>`;
        },
      },
      {
        headerName: 'Max. Price',
        field: 'Max. Price',
        minWidth: 200,
        hide: true,
        valueGetter: (params: any) => [
          params.data.maximumPrice || '',
          params.data.maximumPrice
            ? params.data?.monetaryInfo?.currency || this.defaultCurrency
            : '',
        ],
        cellRenderer: (params: any) => {
          const [budget, currency] = params.value;
          const formattedBudget = formatBudget(budget, currency);
          const budgetString = budget
            ? `${params.value[1]}${params.value[0]} (${formattedBudget})`
            : '';
          return `<p>${budgetString}</p>`;
        },
      },
      {
        headerName: 'Brokerage',
        field: 'brokerage amount',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.monetaryInfo?.brokerage,
          params.data?.monetaryInfo?.brokerage
            ? params.data?.monetaryInfo?.brokerageCurrency
            : '',
        ],
        cellRenderer: (params: any) => {
          return !params.value[1] && !params.value[0]
            ? '--'
            : `<p>
          ${params.value[1] == 'None' || params.value[1] == '%'
              ? ''
              : [params.value[1]]
            }
          ${params.value[0] ? params.value[0] : ''}
          ${params.value[1] == 'None' || params.value[1] !== '%'
              ? ''
              : [params.value[1]]
            }</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Facing',
        field: 'facing',
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => {
          if (params.data?.facings && Array.isArray(params.data.facings)) {
            return params.data.facings
              .filter((index: number) => index !== 0)
              .map((index: number) => Facing[index])
              .join(', ');
          }
          return '--';
        },
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params.value ? params.value : '--'
            }</p>`;
        },
      },
      {
        headerName: 'Description',
        field: 'Project Description',
        hide: true,
        valueGetter: (params: any) =>
          params.data?.description ? params.data?.description : '--',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Notes',
        field: 'Notes',
        hide: true,
        valueGetter: (params: any) =>
          params.data?.notes ? params.data?.notes : '--',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'RERA details',
        field: 'RERA details',
        hide: true,
        valueGetter: (params: any) =>
          params.data?.reraNumbers ? params.data?.reraNumbers : '--',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Assigned to',
        field: 'Assigned to',
        valueGetter: (params: any) => [
          params.data?.assignedUserIds
            ? params.data.assignedUserIds?.map((id: any) =>
              id !== EMPTY_GUID
                ? ' ' + getAssignedToDetails(id, this.allUserList, true)
                : '--'
            )
            : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate-2">${params.value?.[0]?.length === 0 ? '--' : params.value
            }</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Builder Info',
        field: 'Builder Info',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.builderDetail?.name,
          params.data?.builderDetail?.contactNo,
        ],
        cellRenderer: (params: any) => {
          return `<p class="gap-2 align-center text-truncate">
            ${params.data?.builderDetail?.name
              ? `<span class="icon ic-address-card ic-dark ic-sm"></span> ${params.data?.builderDetail.name}`
              : ''
            }</p><span class="align-center">
                    <p class="gap-2 align-center">${params.data?.builderDetail?.contactNo
              ? `<span class="icon ic-Call ic-dark ic-xxs"></span> ${params.data?.builderDetail.contactNo}`
              : '--'
            }`;
        },
      },
      {
        headerName: 'Leads Count',
        field: 'Leads Count',
        minWidth: 100,
        maxWidth: 100,
        valueGetter: (params: any) => [
          params?.data?.leadsCount,
          params?.data?.name,
        ],
        cellRenderer: (params: any) => {
          return `<p>
            <a ${params?.value?.[0]
              ? `href="leads/manage-leads?isNavigatedFromProjects=true&Projects=${encodeURIComponent(
                JSON.stringify([params?.value?.[1]])
              )}"`
              : ''
            } onclick="return false;">${params?.value?.[0] || 0}</a>
          </p>`;
        },
        onCellClicked: (event: any) => {
          if (!event?.data?.leadsCount) return;
          const projectName = event?.data?.name;
          const isCtrlClick = event?.event?.ctrlKey;
          if (isCtrlClick) {
            window?.open(
              `leads/manage-leads?isNavigatedFromProjects=true&Projects=${encodeURIComponent(
                JSON.stringify([projectName])
              )}`,
              '_blank'
            );
            return;
          }
          this.router.navigate(['leads', 'manage-leads'], {
            queryParams: {
              Projects: JSON.stringify([projectName]),
              isNavigatedFromProjects: true,
            },
          });
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Data Count',
        field: 'Data Count',
        minWidth: 100,
        maxWidth: 100,
        filter: false,
        valueGetter: (params: any) => [
          params?.data?.prospectCount,
          params?.data?.name,
        ],
        cellRenderer: (params: any) => {
          return `<p>
            <a ${params?.value?.[0]
              ? `href="data/manage-data?isNavigatedFromProjects=true&Projects=${encodeURIComponent(params?.value?.[1])}"`
              : ''
            } onclick="return false;">${params?.value?.[0] || 0}</a>
          </p>`;
        },
        onCellClicked: (event: any) => {
          if (!event?.data?.prospectCount) return;
          const projectName = event?.data?.name;
          const isCtrlClick = event?.event?.ctrlKey;
          if (isCtrlClick) {
            window?.open(
              `data/manage-data?isNavigatedFromProjects=true&Projects=${encodeURIComponent(projectName)}`,
              '_blank'
            );
            return;
          }
          this.router.navigate(['data', 'manage-data'], {
            queryParams: {
              Projects: projectName,
              isNavigatedFromProjects: true,
            },
          });
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'City',
        field: 'City',
        hide: true,
        valueGetter: (params: any) => {
          const cityArray = params.data?.address?.city
          return [cityArray];
        },
        cellRenderer: (params: any) => {
          const fullCity = params.value ? params.value : '';
          return `<p class="text-truncate-1 break-all" title="${fullCity}">${fullCity}</p>`;
        },
        cellClass: 'cursor-pointer',
        sortable: true,
        unSortIcon: true,
      },
      {
        headerName: 'State',
        field: 'State',
        hide: true,
        valueGetter: (params: any) => {
          const stateArray = params.data?.address?.state
          return [stateArray];
        },
        cellRenderer: (params: any) => {
          const fullState = params.value ? params.value : '';
          return `<p class="text-truncate-1 break-all" title="${fullState}">${fullState}</p>`;
        },
        cellClass: 'cursor-pointer',
        sortable: true,
        unSortIcon: true,
      },
      {
        headerName: 'Country',
        field: 'Country',
        hide: true,
        valueGetter: (params: any) => {
          const countryArray = params.data?.address?.country
          return [countryArray];
        },
        cellRenderer: (params: any) => {
          const fullCountry = params.value ? params.value : '';
          return `<p class="text-truncate-1 break-all" title="${fullCountry}">${fullCountry}</p>`;
        },
        cellClass: 'cursor-pointer',
        sortable: true,
        unSortIcon: true,
      },
      {
        headerName: 'Created',
        field: 'Created',
        minWidth: 200,
        hide: true,
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data.createdBy, this.allUsers, true) ||
          '',
          params.data?.createdOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
            <p>${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Modified',
        field: 'Modified',
        minWidth: 200,
        hide: true,
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data.lastModifiedBy,
            this.allUsers,
            true
          ) || '',
          params.data?.lastModifiedOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
            <p>${params.value[1]}</p>
          <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName:  !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
        field:  !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
        filter: false,
        hide: true,
        valueGetter: (params: any) => [
          params.data?.siteVisitDoneCount,
          params.data?.siteVisitDoneUniqueCount,
          params?.data?.name,
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>`;
        },
      },
      {
        headerName: 'Meeting Done',
        field: 'Meeting Done',
        filter: false,
        hide: true,
        valueGetter: (params: any) => [
          params.data?.meetingDoneCount,
          params.data?.meetingDoneUniqueCount,
          params?.data?.name,
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>`;
        },
      },
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 160,
        cellClass: 'cursor-pointer',
        filter: false,
        valueGetter: (params: any) => [params.data?.status],
        cellRenderer: (params: any) => {
          const statusInfo = this.statusMap[params.data?.status];
          if (statusInfo) {
            const { label, color, bgColor } = statusInfo;
            return `<span class="status-label-badge" style="color:${color}; background-color:${bgColor};">
              <span class="dot dot-xs mr-6" style="background-color:${color};"></span> ${label}
            </span>`;
          }
          return '<span class="ml-12 text-sm">--</span>';
        },
      },
      {
        headerName: 'Possession',
        field: 'Possession',
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data?.possessionDate
            ? getTimeZoneDate(
              params.data?.possessionDate,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]}</p>
              <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[0]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Possession Type',
        field: 'Possession Type',
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data?.possesionType
            ? PossessionType[params.data?.possesionType]
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Matching Leads',
        field: 'Matching Leads',
        hide: true,
        filter: false,
        valueGetter: (params: any) => ['Projects', params?.data],
        cellRenderer: MatchingLeadsComponent,
      },
      {
        headerName: 'Actions',
        maxWidth: 200,
        minWidth: 200,
        filter: false,
        suppressMovable: true,
        lockPosition: 'right',
        valueGetter: (params: any) => ['Actions'],
        cellRenderer: ProjectActionComponent,
      },
    ];
    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    if (this.appliedFilter?.ProjectVisibility !== 4) {
      if (this.canBulkReassign || this.canBulkDelete || this.canBulkShare) {
        this.gridOptions.columnDefs.unshift({
          cellRenderer: 'agGroupCellRenderer',
          headerCheckboxSelection: true,
          headerCheckboxSelectionFilteredOnly: true,
          checkboxSelection: true,
          filter: false,
          resizable: false,
          lockPosition: true,
          maxWidth: 35,
        });
      }
    } else if (this.canBulkRestore || this.canBulkPermanentDelete) {
      this.gridOptions.columnDefs.unshift({
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        filter: false,
        resizable: false,
        lockPosition: true,
        maxWidth: 35,
      });
    }
    this.gridOptions.rowData = this.rowData;
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.columns = this.gridColumnApi.getColumns();
    this.columns = this.columns.map((column: any) => {
      return {
        label: column.getColDef().headerName,
        value: column,
      };
    });
    this.columns = this.columns
      .slice(3, this.columns.length - 1)
      .sort((a: any, b: any) => a?.label.localeCompare(b?.label));
    this.defaultColumns = this.columns?.filter(
      (col) => col.value.getColDef().hide !== true
    );

    let columnState = JSON.parse(localStorage.getItem('myColumnStateProject'));
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }

    let columnData = localStorage.getItem('manage-project-columns')?.split(',');
    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
  }

  onColumnMoved(params: any) {
    var columnState = JSON.stringify(params.columnApi.getColumnState());
    localStorage.setItem('myColumnStateProject', columnState);
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns?.filter(
      (col) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
  }

  onColumnsSelected(columns: any[]) {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('manage-project-columns', colData?.toString());
    if (!columns) {
      columns = this.defaultColumns;
    }
    const cols = columns?.map((col) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col) => col.value),
      false
    );
    var columnState: any = this.gridColumnApi.getColumnState();
    localStorage.setItem('myColumnStateProject', JSON.stringify(columnState));
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
  }

  getAssignedToNames(assignedUserIds: any[]): string {
    if (!assignedUserIds || assignedUserIds.length === 0) {
      return '';
    }
    const assignedToNames = assignedUserIds
      .filter((id) => id !== EMPTY_GUID)
      .map((id) => {
        return getAssignedToDetails(id, this.allUserList, true) || '';
      });

    return assignedToNames.join(', ');
  }

  openAddProjectModal(AddProjectModal: TemplateRef<any>) {
    let initialState: any = {
      class: 'top-modal modal-300 ph-modal-unset',
    };
    this.modalService.show(AddProjectModal, initialState);
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.trackingService.trackFeature(`Web.Project.Option.${this.pageSize}.Click`);
    this.appliedFilter = {
      ...this.appliedFilter,
      pageNumber: 1,
      pageSize: this.pageSize,
    };
    this.store.dispatch(new UpdateProjectsFiltersPayload(this.appliedFilter));
    this.isProjectsLoading = true;
    this.store.dispatch(new FetchProjectList(null, true));
    this.currOffset = 0;
  }

  filterFunction() {
    this.appliedFilter = {
      Search: this.searchTerm,
      pageNumber: this.appliedFilter?.pageNumber || 1,
      pageSize: this.appliedFilter?.pageSize || 10,
      ProjectType: this.appliedFilter?.ProjectType
        ? this.appliedFilter?.ProjectType
        : 'All',
      ProjectVisibility: this.appliedFilter?.ProjectVisibility,
      ProjectStatuses: this.appliedFilter?.ProjectStatuses,
      currentStatus: this.appliedFilter.currentStatus
        ? this.appliedFilter.currentStatus === 'Available'
          ? 0
          : 1
        : null,
      PossesionType: this.appliedFilter?.PossesionType || this.appliedFilter?.Possesion,
      FromPossesionDate: this.appliedFilter?.FromPossesionDate,
      ToPossesionDate: this.appliedFilter?.ToPossesionDate,
      facings: this.appliedFilter?.facings?.map((item: any) => Facing[item]),
      FromDate: setTimeZoneDate(
        this.appliedFilter.FromDate,
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      ToDate: setTimeZoneDate(
        this.appliedFilter.ToDate,
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      MinCarpetArea: this.appliedFilter?.MinCarpetArea,
      CarpetAreaUnitId: this.appliedFilter?.CarpetAreaUnitId,
      AmenitesIds: this.appliedFilter?.AmenitesIds,
      BuilderName: this.appliedFilter?.BuilderName,
      projectSubType: this.appliedFilter?.projectSubType,
      FromMinPrice: this.appliedFilter?.FromMinPrice,
      ToMinPrice: this.appliedFilter?.ToMinPrice,
      FromMaxPrice: this.appliedFilter?.FromMaxPrice,
      ToMaxPrice: this.appliedFilter?.ToMaxPrice,
      locations: this.appliedFilter?.locations,
      ...(this.appliedFilter.FromMaxPrice || this.appliedFilter.ToMaxPrice || this.appliedFilter.FromMinPrice || this.appliedFilter.ToMinPrice
        ? { Currency: this.appliedFilter?.Currency }
        : { Currency: null }),
    };
    this.store.dispatch(new UpdateProjectsFiltersPayload(this.appliedFilter));
    this.store.dispatch(new FetchProjectList(null, true));
    if (
      this.appliedFilter?.currentStatus === 0 ||
      this.appliedFilter?.currentStatus === 1 ||
      this.appliedFilter?.ProjectStatuses?.length ||
      this.appliedFilter?.facings?.length ||
      this.appliedFilter?.projectSubType?.length ||
      this.appliedFilter?.ownerDetails?.length ||
      this.appliedFilter?.FromMaxPrice ||
      this.appliedFilter?.ToMaxPrice ||
      this.appliedFilter?.FromMinPrice ||
      this.appliedFilter?.ToMinPrice ||
      this.appliedFilter?.AmenitesIds?.length ||
      this.appliedFilter?.locations?.length ||
      this.appliedFilter?.BuilderName?.length ||
      this.appliedFilter?.Possesion || this.appliedFilter?.PossesionType ||
      this.appliedFilter?.FromDate ||
      this.appliedFilter?.ToDate ||
      this.appliedFilter?.MinCarpetArea ||
      this.appliedFilter?.MaxCarpetArea ||
      this.appliedFilter?.carpetArea ||
      this.appliedFilter?.MinLeadCount ||
      this.appliedFilter?.MaxLeadCount ||
      this.appliedFilter?.MinProspectCount ||
      this.appliedFilter?.MaxProspectCount
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
  }

  onPageChange(e: number) {
    this.currOffset = e;
    this.appliedFilter = {
      ...this.appliedFilter,
      pageNumber: e + 1,
      pageSize: this.pageSize,
    };
    this.store.dispatch(new UpdateProjectsFiltersPayload(this.appliedFilter));
    this.store.dispatch(new FetchProjectList(null, true));
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.trackingService.trackFeature(`Web.Project.DataEntry.Search.DataEntry`);
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  currentVisibility(visibility: any): void {
    this.appliedFilter = {
      ...this.appliedFilter,
      ProjectVisibility: visibility,
      pageNumber: 1,
    };
    this.filterFunction();
    this.initializeGridSettings();
  }

  currentProjectType(projectType: any) {
    this.trackingService.trackFeature(`Web.Project.Filter.${projectType}.Click`)
    this.appliedFilter = {
      ...this.appliedFilter,
      ProjectType: projectType,
      pageNumber: 1,
    };
    this.filterFunction();
  }

  applyAdvancedFilter(filterData: any) {
    this.appliedFilter = filterData;
    if (
      this.appliedFilter.possessionRange === 'Custom Date' &&
      !this.appliedFilter.possessionDate
    ) {
      return;
    }
    this.filterFunction();
  }

  getAmenity(value: string) {
    const result = this.allAmenityList.length
      ? this.allAmenityList.find((amenity: any) => amenity?.id == value)
        ?.amenityDisplayName
      : this.appliedFilter.allAmenityList.find(
        (amenity: any) => amenity?.id == value
      )?.amenityDisplayName;
    return result;
  }

  getArrayOfFilters(key: string, values: any) {
    if (
      [
        'pageSize',
        'pageNumber',
        'date',
        'dateType',
        'ProjectVisibility',
        'path',
        'CarpetAreaUnitId',
        'ProjectType',
        'Search',
        'allAmenityList',
      ].includes(key) ||
      values?.length === 0
    ) {
      return [];
    } else if (key === 'Currency') {
      return null;
    } else if (key === 'locations' || key === 'BuilderName') {
      return values;
    } else if (key === 'ProjectStatuses') {
      return values?.map((value: any) => PROJECT_STATUS[value]);
    } else if (key === 'facings') {
      return Array.isArray(values)
        ? values.map((value: any) => typeof value === 'number' ? Facing[value] : value)
        : [];
    } else if (key === 'Possesion' || key === 'PossesionType') {
      return values ? [PossessionType[values]] : [];
    } else if (
      (key === 'FromMaxPrice' || key === 'ToMaxPrice' || key === 'FromMinPrice' || key === 'ToMinPrice' || key === 'Currency') &&
      values
    ) {
      return [
        `${this.appliedFilter?.Currency || this.defaultCurrency} ${values}`,
      ];
    }
    return values?.toString()?.split(',');
  }

  onRemoveFilter(key: any, value: any) {
    if (key === 'MaxCarpetArea' || key === 'MinCarpetArea') {
      this.appliedFilter.MinCarpetArea = null;
      this.appliedFilter.MaxCarpetArea = null;
      this.appliedFilter.CarpetAreaUnitId = null;
      this.store.dispatch(new UpdateProjectsFiltersPayload(this.appliedFilter));
      this.filterFunction()
      return;
    }
    if (
      [
        'FromMinPrice',
        'ToMinPrice',
        'FromMaxPrice',
        'ToMaxPrice',
      ].includes(key)
    ) {
      const modifiedKey = key.replace(/^(From|To)/, '');
      delete this.appliedFilter[`To${modifiedKey}`];
      delete this.appliedFilter[`From${modifiedKey}`];
      delete this.appliedFilter['Currency'];
      this.filterFunction()
      return;
    }
    if (
      key === 'FromPossesionDate' ||
      key === 'ToPossesionDate' ||
      key === 'Possesion' ||
      key === 'PossesionType'
    ) {
      this.appliedFilter = {
        ...this.appliedFilter,
        Possesion: null,
        PossesionType: null,
        FromPossesionDate: null,
        ToPossesionDate: null,
        selectedPossession: null,
        selectedMonth: null,
        selectedYear: null,
        selectedMonthAndYear: null,
      };
      this.appliedFilter.facings = this.appliedFilter?.facings?.map(
        (item: any) => Facing[item]
      );
      this.filterFunction();
      return;
    }
    if (key === 'AmenitesIds') {
      this.appliedFilter.AmenitesIds = this.appliedFilter.AmenitesIds.filter(
        (id: any) => id !== value
      );

      this.filterFunction();
      return;
    }
    if (key === 'facings') {
      const enumToRemove = Facing[value as keyof typeof Facing];
      if (enumToRemove !== undefined) {
        this.appliedFilter.facings = this.appliedFilter.facings.filter(
          (enumValue: any) => enumValue !== enumToRemove
        );
        this.appliedFilter.facings = this.appliedFilter?.facings?.map(
          (item: any) => Facing[item]
        );
        this.filterFunction();
      }
      return;
    }
    if (key === 'ProjectStatuses') {
      const indexToRemove = PROJECT_STATUS.indexOf(value);
      if (indexToRemove !== -1) {
        this.appliedFilter.ProjectStatuses =
          this.appliedFilter.ProjectStatuses.filter(
            (statusIndex: number) => statusIndex !== indexToRemove
          );
        this.appliedFilter.facings = this.appliedFilter?.facings?.map(
          (item: any) => Facing[item]
        );
        this.filterFunction();
      }
      return;
    }
    if (typeof value === 'string') {
      this.appliedFilter = { ...this.appliedFilter, [key]: null };
      this._store.dispatch(new UpdateProjectsFiltersPayload(this.appliedFilter));
      this.filterFunction();
      return
    }
    if (Array.isArray(this.appliedFilter[key])) {
      let indexToRemove = this.appliedFilter[key].indexOf(value);
      const list = [...this.appliedFilter[key]];
      list.splice(indexToRemove, 1);
      this.appliedFilter[key] = list;
      this.appliedFilter.facings = this.appliedFilter?.facings?.map(
        (item: any) => Facing[item]
      );
      this.filterFunction();
      return;
    }
    this.appliedFilter[key] = null;
    this.appliedFilter.facings = this.appliedFilter?.facings?.map(
      (item: any) => Facing[item]
    );
    this.filterFunction();
  }

  onCellClicked(event: CellClickedEvent) {
    const userName: any = JSON.parse(
      localStorage.getItem('userDetails')
    )?.preferred_username;

    const headerName = event.colDef.headerName;
    if (
      this.appliedFilter?.ProjectVisibility !== 4 &&
      headerName !== 'Actions' &&
      headerName !== 'Availability' &&
      headerName != 'Meeting Done' &&
      headerName != 'Site Visit Done' &&
      headerName !== 'Leads Count' &&
      headerName !== 'Data Count' &&
      headerName !== 'Matching Leads' &&
      event.data.serialNo
    ) {
      window.open(
        `external/project-preview/${userName}/${event.data.serialNo}`,
        '_blank'
      );
    }
  }

  reset() {
    for (let key in this.appliedFilter) {
      if (this.appliedFilter.hasOwnProperty(key)) {
        if (key === 'pageNumber' || key === 'pageSize') {
          continue;
        }

        if (key === 'Currency') {
          this.appliedFilter[key] = this.defaultCurrency;
        } else {
          this.appliedFilter[key] = null;
        }
      }
    }
    this._store.dispatch(new UpdateProjectsFiltersPayload({ ...this.appliedFilter, pageNumber: 1, pageSize: 10, path: 'project' }));
    this.filterFunction();
  }

  DateFormat(value: Date) {
    this.date = setTimeZoneDate(
      value,
      this.userData?.timeZoneInfo?.baseUTcOffset
    );
    return getTimeZoneDate(
      this.date,
      this.userData?.timeZoneInfo?.baseUTcOffset,
      'dayMonthYear'
    );
  }

  openShareExternalModal() {
    let selectedNodes: any;
    if (this.gridApi) {
      selectedNodes = this.gridApi?.getSelectedNodes();
    }
    let selectedData = selectedNodes.map((node: any) => node?.data);
    let initialState: any = {
      data: selectedData,
      key: 'bulk-share-project',
      type: 'modal',
      moduleName: 'project',
      closeShareExternalComponent: () => {
        shareExternalComponentRef.hide();
      },
    };
    const shareExternalComponentRef = this.modalService.show(
      ShareExternalComponent,
      {
        class: 'modal-350 top-modal',
        ignoreBackdropClick: true,
        keyboard: false,
        initialState,
      }
    );
  }

  changeMonthYearFormat(value: any) {
    const prevDate = String(value)?.includes('00.000Z')
      ? value
      : getISODateFormat(value);
    const date = new Date(prevDate);
    const year = date.getFullYear();
    const month = ('0' + (date.getMonth() + 1)).slice(-2);
    const formattedMonthYear = `${month}-${year}`;
    return formattedMonthYear;
  }

  getProjectSubType(value: any) {
    return this.projectSubType.find((item: any) => item.id === value)
      ?.displayName;
  }

  openBulkReassignModal(BulkReassignModal: TemplateRef<any>): void {
    this.selectedNodes = this.gridApi
      ?.getSelectedNodes()
      .map((project: any) => {
        return project.data;
      });
    let initialState: any = {
      data: this.selectedNodes,
      class: 'right-modal modal-500 ip-modal-unset',
    };
    this.bulkReassignModalRef = this.modalService.show(
      BulkReassignModal,
      initialState
    );
    this.store.dispatch(new FetchPriorityList());
  }

  userAssignment() {
    if (!this.validateAssignmentForm()) return;
    let selectedIds: any = this.selectedNodes?.map((node: any) => node?.id);
    let payload: any = {
      moduleId: this.moduleId,
      entityIds: selectedIds,
      userIds: this.canAllowSecondaryUsers
        ? this.assignedPrimaryUsers?.includes('selectedAllGroup')
          ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map(
            (user: any) => user?.id
          )
          : this.assignedPrimaryUsers
        : this.assignedUser?.includes('selectedAllGroup')
          ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map(
            (user: any) => user?.id
          )
          : this.assignedUser,
      secondaryUserIds: this.assignedSecondaryUsers?.includes(
        'selectedAllGroup'
      )
        ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map(
          (user: any) => user?.id
        )
        : this.assignedSecondaryUsers,
      duplicateUserIds: this.assignedDuplicateUser?.includes('selectedAllGroup')
        ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map(
          (user: any) => user?.id
        )
        : this.assignedDuplicateUser,
      isDuplicateAssignmentEnabled: this.canAllowDuplicates,
      isDualAssignmentEnabled: this.canAllowSecondaryUsers,
      shouldCreateMultipleDuplicates: !this.canAssignSequentially,
    };
    this.store.dispatch(new UpdateMultiUserAssignment(payload, 'Project'));
    this.bulkReassignModalRef.hide();
  }

  openBulkDeleteModal(BulkDeleteModal: TemplateRef<any>) {
    this.selectedNodes = this.gridApi
      ?.getSelectedNodes()
      .map((project: any) => {
        return project.data;
      });
    let initialState: any = {
      data: this.selectedNodes,
      class: 'right-modal modal-300',
    };
    this.bulkDeleteModalRef = this.modalService.show(
      BulkDeleteModal,
      initialState
    );
  }

  bulkDelete(): void {
    const ids =
      this.gridApi?.getSelectedNodes()?.map((node: any) => node?.data?.id) ??
      [];
    if (ids.length === 0) {
      return;
    }
    this.isBulkDeleting = true;
    this.api.bulkDeleteProject(ids).subscribe({
      next: () => {
        this.modalService.hide();
        this.isBulkDeleting = false;
        this._notificationService.success('Projects deleted successfully.');
        this._store.dispatch(new FetchProjectCount());
        this._store.dispatch(new FetchProjectList());
      },
    });
  }

  openBulkRestoreModal(BulkRestoreModal: TemplateRef<any>, isPermanentDelete: boolean = false) {
    this.isPermanentDelete = isPermanentDelete
    this.selectedNodes = this.gridApi
      ?.getSelectedNodes()
      .map((project: any) => {
        return project.data;
      });
    let initialState: any = {
      data: this.selectedNodes,
      class: 'right-modal modal-500 ph-modal-unset',
    };
    this.bulkRestoreModalRef = this.modalService.show(
      BulkRestoreModal,
      initialState
    );
  }

  bulkRestoreProjects(): void {
    const ids = this.gridApi
      ?.getSelectedNodes()
      .map((node: any) => node?.data?.id);
    this.isBulkDeleting = true
    if (this.isPermanentDelete) {
      this.api.deleteProjectPermanently(ids).subscribe({
        next: (res: any) => {
          if (res) {
            this.modalService.hide();
            this._notificationService.success('Project Deleted Successfully');
            this._store.dispatch(new FetchProjectList());
            this.isBulkDeleting = false
            return
          }
        }
      });
      return
    }
    this.isBulkDeleting = true;
    this.api.bulkRestoreProject(ids).subscribe((res: any) => {
      this.modalService.hide();
      this._notificationService.success('Project Restored Successfully');
      this.isBulkDeleting = false;
      this._store.dispatch(new FetchProjectCount());
      this._store.dispatch(new FetchProjectList());
    });
  }

  getCarpetArea(carpetArea: number) {
    const areaUnit =
      this.areaSizeUnits?.find(
        (type: any) => type.id === this.appliedFilter?.CarpetAreaUnitId
      )?.unit || '';
    return `${carpetArea} ${areaUnit}`;
  }

  setListSelection() {
    this.listSelection =
      this.canAllowDuplicates && !this.canAllowSecondaryUsers
        ? this.assignedUser?.length
          ? 'original'
          : 'duplicate'
        : this.assignedPrimaryUsers?.length
          ? 'primary'
          : this.assignedSecondaryUsers?.length
            ? 'secondary'
            : 'duplicate';
  }

  toggleAssignedUserValidation() {
    if (this.canAllowDuplicates) {
      toggleValidation(
        VALIDATION_SET,
        this.integrationDuplicateForm,
        'assignedUser',
        [Validators.required]
      );
      return;
    }
    toggleValidation(
      VALIDATION_CLEAR,
      this.integrationDuplicateForm,
      'assignedUser'
    );
  }

  resetIntegrationForm() {
    this.sameAsPrimaryUsers = false;
    this.sameAsSelectedUsers = false;
    this.sameAsAbove = false;
    this.listSelection = 'original';
    this.toggleAssignedUserValidation();
    this.revertValidation();
  }

  revertValidation() {
    const integrationDuplicateFormControlNames = [
      'assignedUser',
      'assignedDuplicateUser',
    ];
    integrationDuplicateFormControlNames?.forEach((controlName: string) => {
      this.integrationDuplicateForm.get(controlName).markAsPristine();
      this.integrationDuplicateForm.get(controlName).markAsUntouched();
      if (controlName !== 'assignedUser')
        this.integrationDuplicateForm.get(controlName).setErrors(null);
    });

    if (!this.canAllowSecondaryUsers) {
      const integrationDualOwnerFormControlNames = [
        'assignedPrimaryUsers',
        'assignedSecondaryUsers',
        'assignedDuplicateUser',
      ];
      integrationDualOwnerFormControlNames?.forEach((controlName: string) => {
        this.integrationDualOwnerForm.get(controlName).markAsPristine();
        this.integrationDualOwnerForm.get(controlName).markAsUntouched();
        this.integrationDualOwnerForm.get(controlName).setErrors(null);
      });
    }
    this.selectedUserType = 'Primary User(s)';
  }

  onSelectionChange(event: any, controlName: string) {
    const lastUser = event[event.length - 1];
    if (lastUser && !lastUser.isActive) {
      event.pop();
    }
    const newlySelectedItems = event.map((item: any) => item?.id);
    this.integrationDuplicateForm?.get(controlName)?.setValue(newlySelectedItems);
  }

  onSelectionDualChange(event: any, controlName: string) {
    const lastUser = event[event.length - 1];
    if (lastUser && !lastUser.isActive) {
      event.pop();
    }
    const newlySelectedItems = event.map((item: any) => item?.id);
    this.integrationDualOwnerForm?.get(controlName)?.setValue(newlySelectedItems);
  }

  sameAsSelectedUsersClicked(isPrimaryUser: boolean = false) {
    if (this.sameAsPrimaryUsers) return;
    if (isPrimaryUser) {
      this.assignedSecondaryUsers = [...this.assignedPrimaryUsers];
      return;
    }
    this.assignedDuplicateUser = [...this.assignedUser];
  }

  sameAsPrimarySecondaryUsersClicked() {
    if (this.sameAsAbove) return;
    this.assignedDuplicateUser =
      this.selectedUserType == 'Primary User(s)'
        ? [...this.assignedPrimaryUsers]
        : [...this.assignedSecondaryUsers];
  }

  removeUserFromSelection(userId: any) {
    if (this.canAllowSecondaryUsers) {
      this.sameAsPrimaryUsers = false;
      if (this.listSelection == 'secondary') {
        this.assignedSecondaryUsers = this.assignedSecondaryUsers?.filter(
          (user: any) => user !== userId
        );
        if (!this.assignedSecondaryUsers?.length)
          this.selectedSectionLeadAssignment = 'Configuration';
        return;
      } else if (this.listSelection == 'duplicate') {
        this.assignedDuplicateUser = this.assignedDuplicateUser?.filter(
          (user: any) => user !== userId
        );
        if (!this.assignedDuplicateUser?.length)
          this.selectedSectionLeadAssignment = 'Configuration';
        return;
      }
      this.assignedPrimaryUsers = this.assignedPrimaryUsers?.filter(
        (user: any) => user !== userId
      );
      if (!this.assignedPrimaryUsers?.length)
        this.selectedSectionLeadAssignment = 'Configuration';
      return;
    }
    if (this.canAllowDuplicates && !this.canAllowSecondaryUsers) {
      this.sameAsSelectedUsers = false;
      if (this.listSelection == 'original') {
        this.assignedUser = this.assignedUser.filter(
          (user: any) => user !== userId
        );
        if (!this.assignedUser?.length)
          this.selectedSectionLeadAssignment = 'Configuration';
        return;
      }
      this.assignedDuplicateUser = this.assignedDuplicateUser.filter(
        (user: any) => user !== userId
      );
      if (!this.assignedDuplicateUser?.length)
        this.selectedSectionLeadAssignment = 'Configuration';
      return;
    }
    this.assignedUser = this.assignedUser.filter(
      (user: any) => user !== userId
    );
    if (!this.assignedUser?.length)
      this.selectedSectionLeadAssignment = 'Configuration';
  }

  removeProject(id: string): void {
    const node = this.gridApi
      ?.getSelectedNodes()
      ?.filter((project: any) => project?.data?.id === id);
    this.gridApi?.deselectNode(node?.[0]);

    this.selectedNodes = this.selectedNodes?.filter(
      (project: any) => project?.id !== id
    );
    if (this.selectedNodes?.length <= 0) {
      this.bulkReassignModalRef.hide();
      this.bulkDeleteModalRef.hide();
      this.bulkRestoreModalRef.hide();
    }
  }

  openConfirmDeleteModal(projectName: string, projectId: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: projectName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeProject(projectId);
        }
      });
    }
  }

  getUnitById(id: any): string {
    const unitObject = this.areaSizeUnits.find((unit) => unit.id === id);
    return unitObject ? unitObject.unit : '--';
  }

  openProjectTracker() {
    if (this.selectedTrackerOption === 'bulkUpload') {
      let initialState: any = {
        fieldType: 'project',
      };
      this._store.dispatch(new FetchProjectExcelUploadedList(1, 10));
      this.modalService.show(ExcelUploadedStatusComponent, {
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      });
    } else if (this.selectedTrackerOption === 'export') {
      this._store.dispatch(new FetchExportProjectStatus(1, 10));
      this.modalService.show(ExportProjectTrackerComponent, {
        class: 'modal-900 modal-dialog-centered h-100 tb-modal-unset',
      });
    }
    this.selectedTrackerOption = '';
  }

  openProjectBulkUpload() {
    if (this.selectedOption === 'bulkUpload') {
      this.router.navigate(['projects/project-bulk-upload']);
    }
    this.selectedOption = '';
  }

  exportProjectReport() {
    this.trackingService.trackFeature(`Web.Project.Button.Export.Click`)
    this._store.dispatch(new FetchProjectExportSuccess(''));
    this.filterFunction();

    let initialState: any = {
      payload: {
        ...this.appliedFilter,
        timeZoneId:
          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset:
          this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  trackFilter(filter: any) {
    this.trackingService.trackFeature(`Web.Project.Menu.${filter}.Click`);
  }

  onTrackerChange(selectedValue: string) {
    if (selectedValue === 'bulkUpload') {
      this.openProjectTracker()
      this.trackingService.trackFeature(`Web.Project.Button.TrackerBulkUpload.Cllick`);
    } else if (selectedValue === 'export') {
      this.openProjectTracker()
      this.trackingService.trackFeature(`Web.Project.Button.TrackerExport.Click`);

    }
    this.selectedTrackerOption = null;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
