:root {
  --primary-theme-color: #4e3eb2;
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f5;
  --text-primary: #130f26;
  --text-secondary: #707070;
  --secondary-border-color: #cccccc;
  --bs-body-color: #130f26;
}

.bg-unset {
  background-color: unset !important;
}

.bg-theme-color {
  background-color: var(--primary-theme-color) !important;
}

.bg-primary {
  color: var(--bg-primary);
}

.bg-secondary {
  background-color: var(--bg-secondary) !important;
}

.border-secondary-color {
  border-color: --secondary-border-color !important;
}

.bg-white {
  background-color: $white;
}

.bg-white-100 {
  background-color: $white-100;
}

.bg-coal {
  background-color: $primary-black !important;
}

.bg-black {
  background-color: $black;
}

.bg-black-10 {
  background-color: $black-10;
}

.bg-black-100 {
  background-color: $black-100 !important;
}

.bg-black-200 {
  background-color: $black-200 !important;
}

.bg-black-4 {
  background-color: #00000066;
}

.bg-black-50 {
  background-color: $black-50;
}

.bg-black-6 {
  background-color: #00000099;
}

.bg-black-9 {
  background-color: #000000e6;
}

.bg-black-500 {
  background-color: $black-500;
}

.bg-black-600 {
  background-color: $black-600;
}

.bg-black-900 {
  background-color: $black-900;
}

.bg-dark-150 {
  background-color: $dark-150;
}

.bg-blue-160 {
  background-color: $blue-160;
}

.bg-blue-550 {
  background-color: $blue-550;
}

.bg-blue-900 {
  background-color: $blue-900;
}

.bg-dark-700 {
  background-color: $dark-700;
}

.bg-blue-750 {
  background-color: $blue-750;
}

.bg-blue-1200 {
  background-color: $blue-1200;
}

.bg-brown {
  background-color: $brown-100;
}

.bg-brown-150 {
  background-color: $brown-150;
}

.bg-brown-200 {
  background-color: $brown-200;
}

.bg-glass {
  background: #a0a0a099;
  opacity: 0.85;
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}

.bg-linear-black {
  background: linear-gradient(180deg, #00000033 0%, #00000066 50%, #00000099 100%);
}

.bg-black-linear-down::after {
  content: "";
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  bottom: 0;
  left: 0;
  opacity: 0.8;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
  border-radius: 6px;
}

//blue backgrounds

.bg-blue-150 {
  background-color: $blue-150;
}

.bg-blue-800 {
  background-color: $blue-800;
}

.bg-blue-850 {
  background-color: $blue-850;
}

.bg-blue-1000 {
  background-color: $blue-1000;
}

.bg-blue-1150 {
  background-color: $blue-1150;
}

.bg-light-blue {
  background-color: $blue-100;
}

.bg-blue-50 {
  background-color: $blue-50;
}

.bg-dark-blue {
  background-color: $blue-500;
}

.bg-dark-blue-400 {
  background-color:$dark-blue-400;
}

.bg-dark-blue-500 {
  background-color:$dark-blue-500;
}

.bg-blue-450 {
  background-color: $blue-450;
}

.bg-blue-350 {
  background-color: $blue-350;
}

.bg-blue-1100 {
  background-color: $blue-1100;
}

.bg-accent-blue {
  background-color: $blue-200;
}

.bg-blue-250 {
  background-color: $blue-250;
}

.bg-aqua-400 {
  background-color: $aqua-400;
}

.bg-aqua-850 {
  background-color: $aqua-850;
}

.bg-aqua-950 {
  background-color: $aqua-950;
}

.bg-light-aqua {
  background-color: $aqua-200;
}

.bg-linear-navy {
  background: linear-gradient(180deg, $dark-150 0%, $primary-black 47.91%);
}

.bg-linear-dark-blue {
  background: linear-gradient(100.45deg, #124674 8.65%, #0a263f 74.4%);
}

.bg-navy-200 {
  background-color: $navy-200;
}

.bg-navy-300 {
  background-color: $navy-300;
}

.bg-violet {
  background-color: $violet-100;
}

.bg-light-violet {
  background-color: $violet-200;
}

.bg-violet-600 {
  background-color: $violet-600;
}

.bg-violet-800 {
  background-color: $violet-800;
}

.bg-dark-purple {
  background-color: $dark-purple;
}

.bg-orange-550 {
  background-color: $orange-550;
}

.bg-orange-800 {
  background-color: $orange-800;
}

.bg-dark-pink {
  background-color: $dark-pink;
}

.bg-pink-700 {
  background-color: $pink-700;
}

//Green background colors
.bg-linear-green-100 {
  background: linear-gradient(135deg, $green-90 0%, $green-110 100%);
}

.bg-accent-green {
  background-color: $accent-green;
}

.bg-green-30 {
  background-color: $green-30;
}

.bg-accent-green-30 {
  background-color: $green-300;
}

.bg-accent-green-40 {
  background-color: $accent-green-40;
}

.bg-green-40 {
  background-color: $green-40;
}

.bg-green-130 {
  background-color: $green-130;
}

.bg-green-150 {
  background-color: $green-150;
}

.bg-accent-green-100 {
  background-color: $green-100;
}

.bg-accent-green-200 {
  background-color: $accent-green-200;
}

.bg-accent-green-300 {
  background-color: $accent-green-300;
}

.bg-light-green-400 {
  background-color: $light-green-400;
}

.bg-dark-green {
  background-color: $dark-green;
}

.bg-dark-green-100 {
  background-color: $dark-green-100;
}

.bg-green-50 {
  background-color: $green-5 !important;
}

.bg-green-70 {
  background-color: $green-70;
}

.bg-green-100 {
  background-color: $green-700;
}

.bg-green-140 {
  background-color: $green-140;
}

.bg-green-170 {
  background-color: $green-170;
}

.bg-green-180 {
  background-color: $green-180;
}

.bg-green-190 {
  background-color: $green-190;
}

.bg-green-350 {
  background-color: $green-350 !important;
}

.bg-green-800 {
  background-color: $green-800;
}

.bg-green-900 {
  background-color: $green-900;
}

.bg-green-1050 {
  background-color: $green-1050;
}

.bg-accent-green-light {
  background-color: $light-green-100 !important;
}

.bg-hover-green {
  &:hover {
    background-color: $accent-green;

    .icon-hover-white {
      color: $white;
    }
  }
}

.bg-light-pearl {
  background-color: $slate-20;
}

.bg-pearl {
  background-color: $slate-200;
}

.bg-pearl-90 {
  background-color: $slate-90;
}

.bg-gray-dark {
  background-color: $dark-400 !important;
}

.bg-dark-450 {
  background-color: $dark-450 !important;
}

.bg-gray-darker {
  background-color: $slate-60;
}

.bg-light-slate {
  background-color: $dark-500;
}

.bg-slate {
  background-color: $slate-50;
}

.bg-slate-120 {
  background-color: $slate-120;
}

.bg-slate-130 {
  background-color: $slate-130;
}

.bg-slate-150 {
  background-color: $slate-150;
}

.bg-slate-250 {
  background-color: $slate-250 !important;
}

.bg-slate-600 {
  background-color: $slate-600;
}

.bg-dark-slate {
  background-color: $bg-secondary;
}

.bg-ash {
  background-color: $dark-900;
}

.bg-grey {
  background-color: $grey-10;
}

//Orange background colors
.bg-orange {
  background-color: $orange-400;
}

.bg-orange-150 {
  background-color: $orange-150;
}

.bg-orange-150-active {
  background-color: $orange-900;
  border: 1px solid $orange-250 !important;
  font-weight: 600 !important;
}

.bg-light-orange {
  background-color: $light-orange;
}

.bg-orange-200 {
  background-color: $orange-200;
}

.bg-dark-orange {
  background-color: $orange-300;
}

//Yellow background colors
.bg-yellow {
  background-color: $yellow-200;
}

.bg-light-yellow-100 {
  background-color: $light-yellow-100;
}

.bg-dark-yellow-100 {
  background-color: $dark-yellow-100;
}

//Red background colors
.bg-red {
  background-color: $dark-red-10;
}

.bg-red-30 {
  background-color: $red-30;
}

.bg-red-70 {
  background-color: $red-70;
}

.bg-red-350 {
  background-color: $red-350;
}

.bg-red-650 {
  background-color: $red-650;
}

.bg-red-850 {
  background-color: $red-850;
}

.bg-red-950 {
  background-color: $red-950;
}

.bg-light-red {
  background-color: $red-700;
}

.bg-red-750 {
  background-color: $red-750;
}

.bg-accent-red {
  background-color: rgba($red-200, 0.2);
}

.bg-accent-red-40 {
  background-color: $red-40;
}

.bg-red-450 {
  background-color: $red-450;
}

.bg-dark-red-20 {
  background-color: $dark-red-20;
}

.bg-dark-red-30 {
  background-color: $dark-red-30;
}

.bg-dark-red-40 {
  background-color: $dark-red-40;
}

.bg-alert {
  background-color: #FAEDEC;
}

.bg-orange-450 {
  background-color: $orange-450;
}

.bg-hover-red {
  &:hover {
    background-color: $red-700;
    opacity: unset;
  }
}

.bg-purple-500 {
  background-color: $purple-500;
}

//task card colors and card active colors
.bg-linear-gradient-purple {
  background: rgba($accent-purple, 0.5);
}

.bg-linear-gradient-purple-active {
  background: linear-gradient(261.52deg,
      $accent-purple 42.47%,
      #7d88e7a6 159.82%);
  box-shadow: 5px 5px 10px $accent-purple-60;
}

.bg-linear-gradient-green {
  background: rgba($accent-green, 0.5);
}

.bg-linear-gradient-green-active {
  background: linear-gradient(257.63deg,
      $accent-green 41%,
      rgba(80, 190, 167, 0.59) 138.13%);
  box-shadow: 5px 5px 10px $accent-green-60;
}

.bg-linear-gradient-red {
  background: rgba($accent-red, 0.5);
}

.bg-linear-gradient-red-active {
  background: linear-gradient(258.32deg, $accent-red 41.44%, #e77d7d8f 133.62%);
  box-shadow: 5px 5px 10px $accent-red;
}

.bg-linear-gradient-orange {
  background: rgba($accent-orange, 0.5);
}

.bg-linear-gradient-orange-active {
  background: linear-gradient(261.52deg,
      $accent-orange 42.47%,
      #ff9a8a9e 159.82%);
  box-shadow: 5px 5px 10px $accent-orange-60;
}

.bg-linear-gradient-aqua {
  background: rgba($accent-aqua, 0.5);
}

.bg-linear-gradient-aqua-active {
  background: linear-gradient(258.32deg, $blue-200 41.44%, #7dc8e78f 133.62%);
  box-shadow: 5px 5px 10px $accent-aqua-60;
}

// need to this change class
.bg-linear {
  background: linear-gradient(135deg, #80CFBF 0%, #55A493 100%);
}


.bg-blur {
  background: #b6b6b66e;
  box-shadow: 0px -10px 10px #00000033;
  backdrop-filter: blur(15px);
}