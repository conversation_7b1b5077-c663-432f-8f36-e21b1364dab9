<ng-container *ngIf="permissionsSet?.has('Permissions.Dashboard.View')">
    <div class="w-100">
        <div class="dashboard align-end position-absolute z-index-1021 tb-left-32"
            [ngClass]="[showLeftNav ? 'left-170' : 'left-70', globalForm.controls['dashboardVisibility'].value !== 0 ? 'top-11' : 'top-33']">
            <div class="d-none tb-d-block icon ic-filter-solid mt-16 cursor-pointer"
                (click)="showFilters = !showFilters"></div>
            <form [formGroup]="globalForm" *ngIf="showFilters || screen?.innerWidth > 1279">
                <div
                    class="align-end tb-flex-end tb-flex-wrap tb-position-absolute tb-left-8 bg-dark tb-w-200 tb-br tb-px-10 tb-py-10">
                    <div class="flex-between tb-w-100">
                        <h5 class="fw-semi-bold text-light d-none tb-d-block">Filters</h5>
                        <div class="icon ic-close-secondary ic-sm cursor-pointer d-none tb-d-block"
                            (click)="showFilters = !showFilters">
                        </div>
                    </div>
                    <ng-select [virtualScroll]="true" [items]="dashboardTypes" [searchable]="false" [clearable]="false"
                        bindLabel="displayName" bindValue="value" name="dashboardList" ResizableDropdown
                        formControlName="dashboardVisibility" class="h-30 mr-10 tb-w-100 tb-mt-10 tb-mr-0">
                    </ng-select>
                    <div class="align-end tb-flex-wrap">
                        <ng-container *ngIf="globalForm.controls['dashboardVisibility'].value !== 0">
                            <!-- <div class="align-end-col mr-10 tb-mt-10 tb-mr-0 tb-w-100 dashboard-input-black">
                                <label class="checkbox-container mb-6 mr-2">
                                    <input type="checkbox" formControlName="withTeamGM">
                                    <span class="checkmark"></span><span class="text-white">With Team</span>
                                </label>
                                <div class="position-relative tb-w-100">
                                    <span class="position-absolute top-9 left-6 z-index-1021 text-sm text-white">General
                                        Manager</span>
                                    <ng-select [virtualScroll]="true" [items]="generalManagerList" [multiple]="true" appSelectAll
                                        [closeOnSelect]="false" bindLabel="fullName" bindValue="id" [searchable]="true"
                                        name="dashboardUsers" formControlName="generalManager"
                                        class="w-130 text-black tb-w-100" ResizableDropdown>
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                    class="checkmark"></span>{{item.firstName}} {{item.lastName}} <span
                                                    class="error-message-custom top-13"
                                                    *ngIf="!item.isActive">(Disabled)</span>
                                            </div>
                                        </ng-template>
                                    </ng-select>
                                </div>
                            </div> -->
                            <div class="position-relative tb-mt-10 tb-w-100 dashboard-input-black">
                                <span
                                    class="position-absolute top-9 left-6 z-index-1021 text-sm text-white">Designation</span>
                                <ng-select [virtualScroll]="true" [items]="designationList" [multiple]="true" appSelectAll
                                    [searchable]="true" ResizableDropdown bindLabel="name" bindValue="id"
                                    [closeOnSelect]="false" formControlName="designation"
                                    class="w-120 text-black mr-10 tb-mr-0 tb-w-100">
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span>{{item?.name}}
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </div>
                            <div class="align-end-col mr-10 tb-mt-10 tb-mr-0 tb-w-100 dashboard-input-black">
                                <label class="checkbox-container mb-6 mr-2">
                                    <input type="checkbox" formControlName="withTeamUsers">
                                    <span class="checkmark"></span><span class="text-white">With Team</span>
                                </label>
                                <div class="position-relative tb-w-100">
                                    <span
                                        class="position-absolute top-9 left-6 z-index-1021 text-sm text-white">Users</span>
                                    <ng-select [virtualScroll]="true" [items]="filteredUsers" [multiple]="true" appSelectAll
                                        [searchable]="true" ResizableDropdown [closeOnSelect]="false"
                                        bindLabel="fullName" bindValue="id" name="dashboardUsers"
                                        formControlName="userIds" class="w-120 tb-w-100 text-black">
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                    class="checkmark"></span>{{item.firstName}} {{item.lastName}}
                                                <span class="error-message-custom top-13"
                                                    *ngIf="!item.isActive">(Disabled)</span>
                                            </div>
                                        </ng-template>
                                    </ng-select>
                                </div>
                            </div>
                        </ng-container>
                        <div class="position-relative tb-mt-10 tb-mr-0 tb-w-100 dashboard-input-black">
                            <span class="position-absolute top-9 left-6 z-index-1021 text-sm text-white">Project</span>
                            <ng-select [virtualScroll]="true" [items]="projectList" [multiple]="true" appSelectAll
                                [searchable]="true" [closeOnSelect]="false" formControlName="projects"
                                class="w-140 tb-w-100 text-black" ResizableDropdown>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span>{{item}}
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                        <div class="mx-8 my-4 border-dark-600 h-20px tb-d-none"></div>
                        <div class="position-relative tb-mt-10 tb-mr-0 tb-w-100">
                            <!-- <span class="position-absolute top-9 left-6 z-index-1021 text-sm text-white">Date
                Type</span> -->
                            <ng-select [virtualScroll]="true" [items]="dateTypeFilterList" [searchable]="false"
                                [clearable]="false" bindLabel="value" formControlName="globalDType" placeholder="All"
                                class="text-white mr-10 tb-w-100 ng-select-w-171">
                            </ng-select>
                        </div>
                        <div class="position-relative dashboard-filter h-padding form-group mr-6 tb-mt-10 tb-mr-0 tb-w-100"
                            [ngClass]="{'pe-none disabled' : !globalForm.controls['globalDType'].value}">
                            <form-errors-wrapper [control]="globalForm.controls['globalDate']" label="Date">
                                <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                                    [selectMode]="'range'" formControlName="globalDate" placeholder="Select date"
                                    class="bg-dark text-white br-2" />
                                <owl-date-time [pickerType]="'calendar'" #dt1
                                    (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                            </form-errors-wrapper>
                            <div class="icon ic-close ic-xxs position-absolute top-10 right-5 cursor-pointer"
                                *ngIf="globalForm.controls['globalDate'].value" title="reset date"
                                (click)="resetDateFilter()"></div>
                        </div>
                    </div>
                    <h5 class="px-8 pb-6 pt-5 bg-light fw-600 mr-4 cursor-pointer br-2 border-white tb-mt-10"
                        (click)="applyFilterFunc()">
                        search</h5>
                    <div class="icon ic-refresh mb-4 cursor-pointer tb-mt-10 tb-mr-0" title="reset all"
                        (click)="onResetAllFilters()"></div>
                </div>
            </form>
        </div>
        <div>
            <!-- Top-nav -->
            <div class="bg-dark w-100 fw-600 text-gray-850 h-80 pt-20">
                <div class="flex-between">
                    <drag-scroll class="scrollbar scroll-hide ml-30 tb-w-100-50"
                        [ngClass]="showLeftNav ? 'w-100-250' : 'w-100-150'">
                        <div class="align-center text-nowrap py-10">
                            <div (click)="navigateStatus('All')"
                                [ngClass]="allStatusCount && permissionsSet?.has('Permissions.Leads.View') ? 'cursor-pointer' : 'pe-none'">
                                <div class="text-sm">All Leads</div>
                                <h2 class="text-white mt-2" *ngIf="!isStatusCountLoading else loaderWhite">
                                    {{allStatusCount || 0}}</h2>
                            </div>
                            <div class="mx-20 border-dark-600 h-40"></div>
                            <ng-container *ngFor="let status of customStatusCount; let last = last">
                                <div (click)="navigateStatus(status.key)"
                                    [ngClass]="status.value && permissionsSet?.has('Permissions.Leads.View') ? 'cursor-pointer' : 'pe-none'">
                                    <div class="fw-semi-bold text-sm">{{status.key}}</div>
                                    <h3 class="text-white" *ngIf="!isStatusCountLoading else loaderWhite">
                                        {{status.value || 0}}</h3>
                                </div>
                                <div class="w-10 h-10 text-mud mx-20" *ngIf="!last">+</div>
                            </ng-container>
                        </div>
                    </drag-scroll>
                    <img src="../../../../assets/images/muso-hi.svg" alt="img"
                        class="mr-20 mt-10 position-relative d-block tb-d-none" width="50px" height="51px">
                </div>
            </div>
            <div class="scrollbar scroll-hide h-100-130 position-relative">
                <div class="p-30">
                    <ng-container *ngIf="showAppliedFilters">
                        <div class="bg-white px-4 py-12 tb-w-100-40 mb-16"
                            [ngClass]="showLeftNav ? 'w-100-200' : 'w-100-100'">
                            <div class="bg-secondary flex-between">
                                <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
                                    <div class="d-flex" *ngFor="let filter of filtersPayload | keyvalue">
                                        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                                            *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                                            {{filtersKeyLabel[filter.key] || filter.key}}:
                                            {{filter.key === 'Designation'? getDesignation(value) :
                                            filter.key === 'GeneralManagerIds' ? getUserName(value) :
                                            filter.key === 'UserIds' ? getUserName(value) :
                                            value }}
                                            <span (click)="onRemoveFilter(filter.key, value)"
                                                class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"></span>
                                        </div>
                                    </div>
                                </drag-scroll>
                                <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                                    (click)="onResetAllFilters()">Clear All</div>
                            </div>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="permissionsSet?.has('Permissions.Leads.ViewLeadSource')">
                        <div class="bg-white w-100">
                            <div class="p-10 flex-between border-bottom">
                                <!--app Tour id="ad-sale" -->
                                <h5 class="text-black-200">Leads From Source</h5>
                                <label class="checkbox-container text-normal">
                                    <input type="checkbox" [(ngModel)]="isAssociatedData"
                                        (change)="updateDataFlags()" />
                                    <span class="checkmark"></span>Associated Data</label>
                            </div>
                            <div class="w-100 py-6 px-10 d-flex flex-wrap scrollbar max-h-310 min-h-300">
                                <ng-container *ngIf="hasAssociatedData else noSourceData">
                                    <!-- Social Profiles -->
                                    <div class="w-33 tb-w-50 ph-w-100 fw-semi-bold">
                                        <div class="text-sm text-light-slate text-decoration-underline mb-6">
                                            social profiles</div>
                                        <ng-container *ngIf="!isSourceLoading else shimmerLoader">
                                            <ng-container *ngIf="hasSPData else noAssociatedData">
                                                <ng-container *ngFor="let profile of socialProfileSource">
                                                    <div *ngIf="(!isAssociatedData || (isAssociatedData && profile.count > 0))"
                                                        class="h-35px mb-10 mr-10 ph-mr-0 position-relative shadow-hover-sm"
                                                        (click)="navigateStatus('All', profile.leadSource)"
                                                        [ngClass]="profile.count && permissionsSet?.has('Permissions.Leads.View') ? 'cursor-pointer' : 'pe-none'"
                                                        [ngStyle]="{'background-color': profile.backgroundColor || '#EBEEF1'}">
                                                        <div class="position-absolute h-100 top-0 left-0"
                                                            [style.width]="(profile.count / allLeadsCount) * 100 + '%'"
                                                            [ngStyle]="{'background-color': profile.progressColor || '#CDD5DB'}">
                                                        </div>
                                                        <div
                                                            class="flex-between position-absolute w-100 h-100 top-0 left-0 p-10">
                                                            <div class="align-center">
                                                                <img [type]="'leadrat'"
                                                                    [appImage]="profile.imageURL ? s3BucketUrl+profile.imageURL : ''"
                                                                    alt="logo" width=14px height=14px class="mr-10">
                                                                <div class="text-sm">{{LeadSource[profile.leadSource]}}
                                                                </div>
                                                            </div>
                                                            <h5>{{ profile.count }}</h5>
                                                        </div>
                                                    </div>
                                                </ng-container>
                                            </ng-container>
                                        </ng-container>
                                    </div>

                                    <!-- Third Parties -->
                                    <div class="w-33 tb-w-50 ph-w-100 fw-semi-bold">
                                        <div class="text-sm text-light-slate text-decoration-underline mb-6">3rd parties
                                        </div>
                                        <ng-container *ngIf="!isSourceLoading else shimmerLoader">
                                            <ng-container *ngIf="hasTPData else noAssociatedData">
                                                <ng-container *ngFor="let party of thirdPartySource">
                                                    <div *ngIf="(!isAssociatedData || (isAssociatedData && party.count > 0))"
                                                        class="h-35px mb-10 mr-10 ip-mr-0 position-relative shadow-hover-sm"
                                                        (click)="navigateStatus('All', party.leadSource)"
                                                        [ngClass]="party.count && permissionsSet?.has('Permissions.Leads.View') ? 'cursor-pointer' : 'pe-none'"
                                                        [ngStyle]="{'background-color': party.backgroundColor || '#EBEEF1'}">
                                                        <div class="position-absolute h-100 top-0 left-0"
                                                            [style.width]="(party.count / allLeadsCount) * 100 + '%'"
                                                            [ngStyle]="{'background-color': party.progressColor || '#CDD5DB'}">
                                                        </div>
                                                        <div
                                                            class="flex-between position-absolute w-100 h-100 top-0 left-0 p-10">
                                                            <div class="align-center">
                                                                <img [type]="'leadrat'"
                                                                    [appImage]="party.imageURL ? s3BucketUrl+party.imageURL : ''"
                                                                    alt="logo" width=14px height=14px class="mr-10">
                                                                <div class="text-sm">{{ LeadSource[party.leadSource] }}
                                                                </div>
                                                            </div>
                                                            <h5>{{ party.count }}</h5>
                                                        </div>
                                                    </div>
                                                </ng-container>
                                            </ng-container>
                                        </ng-container>
                                    </div>

                                    <!-- Others -->
                                    <div class="w-33 tb-w-50 ph-w-100 fw-semi-bold">
                                        <!--app Tour id="add-sal" -->
                                        <div class="text-sm text-light-slate text-decoration-underline mb-6">others
                                        </div>
                                        <ng-container *ngIf="!isSourceLoading else shimmerLoader">
                                            <ng-container *ngIf="hasOthersData else noAssociatedData">
                                                <ng-container *ngFor="let others of othersSource">
                                                    <div *ngIf="(!isAssociatedData || (isAssociatedData && others.count > 0))"
                                                        class="h-35px mb-10 position-relative shadow-hover-sm"
                                                        (click)="navigateStatus('All', others.leadSource)"
                                                        [ngClass]="others.count && permissionsSet?.has('Permissions.Leads.View') ? 'cursor-pointer' : 'pe-none'"
                                                        [ngStyle]="{'background-color': others.backgroundColor || '#EBEEF1'}">
                                                        <div class="position-absolute h-100 top-0 left-0"
                                                            [style.width]="(others.count / allLeadsCount) * 100 + '%'"
                                                            [ngStyle]="{'background-color': others.progressColor || '#CDD5DB'}">
                                                        </div>
                                                        <div
                                                            class="flex-between position-absolute w-100 h-100 top-0 left-0 p-10">
                                                            <div class="align-center">
                                                                <img [type]="'leadrat'"
                                                                    [appImage]="others.imageURL ? s3BucketUrl+others.imageURL : ''"
                                                                    alt="logo" width=14px height=14px class="mr-10">
                                                                <div class="text-sm">{{ LeadSource[others.leadSource] }}
                                                                </div>
                                                            </div>
                                                            <h5>{{ others.count }}</h5>
                                                        </div>
                                                    </div>
                                                </ng-container>
                                            </ng-container>
                                        </ng-container>
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="accountDetails?.length">
                    <cpl-tracking></cpl-tracking>
                    </ng-container>
                    <!-- Lead Reports -->
                    <leads-report></leads-report>
                    <!-- Data Reports -->
                    <data-report></data-report>
                    <!-- calls -->
                    <calls-report [usersByDesignation]="usersByDesignation"></calls-report>
                    <!-- Whatsapp -->
                    <whatsapp-report [usersByDesignation]="usersByDesignation"></whatsapp-report>
                </div>
            </div>
        </div>
    </div>
    <ng-template #shimmerLoader>
        <ng-container *ngFor="let skeleton of [1,2,3,4,5,6]">
            <ng-container *ngTemplateOutlet="smoothSkeletonLoader"></ng-container>
        </ng-container>
    </ng-template>
    <ng-template #loaderWhite>
        <div class="container p-4">
            <ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-falling dot-white"></div>
            </ng-container>
        </div>
    </ng-template>
    <ng-template #smoothSkeletonLoader>
        <div class="mb-6">
            <span class="icon icon__skeleton shimmer-multiple"></span>
        </div>
    </ng-template>
    <ng-template #spinLoader>
        <div class="spin-loader my-20"></div>
    </ng-template>
    <ng-template #noDataFound>
        <tr>
            <td class="h-100 header-4 text-secondary">No Data Available for Selected Criteria. Please try again with
                Different Filter Options.</td>
        </tr>
    </ng-template>
    <ng-template #noAssociatedData>
        <div class="mt-6">No Data Available</div>
    </ng-template>
    <ng-template #noSourceData>
        <div class="flex-center-col w-100">
            <img src="assets/images/no-data-dark.svg" alt="No records found" />
            <h4 class="text-center mt-10">No Data Available</h4>
        </div>
    </ng-template>
</ng-container>