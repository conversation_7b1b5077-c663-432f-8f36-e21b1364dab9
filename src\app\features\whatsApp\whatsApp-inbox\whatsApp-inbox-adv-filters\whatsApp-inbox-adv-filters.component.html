<div class="lead-adv-filter pl-30 bg-white brbl-15 brbr-15">
    <div class="d-flex flex-wrap ng-select-sm">
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="justify-between align-end mr-20">
                <div class="field-label mt-0">Assign To</div>
                <label class="checkbox-container mb-4">
                    <input type="checkbox" [(ngModel)]="advanceFilters.IsWithTeam">
                    <span class="checkmark"></span>{{'DASHBOARD.with-team' | translate}}
                </label>
            </div>
            <div class="mr-20">
                <ng-select [virtualScroll]="true" *ngIf="!reporteesIsLoading else fieldLoader" [items]="reportees"
                    ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                    placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id"
                    [(ngModel)]="advanceFilters.UserIds">
                    <ng-template ng-label-tmp let-item="item" let-clear="clear">
                        <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                        <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                class="checkmark"></span>{{item.firstName}} {{item.lastName}} <span
                                class="error-message-custom" *ngIf="!item.isActive">( Disabled )</span>
                        </div>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Text By</div>
            <div class="mr-20">
                <ng-select [virtualScroll]="true" *ngIf="!reporteesIsLoading else fieldLoader" [items]="reportees"
                    ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                    placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id"
                    [(ngModel)]="advanceFilters.TextByIds">
                    <ng-template ng-label-tmp let-item="item" let-clear="clear">
                        <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                        <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                class="checkmark"></span>{{item.firstName}} {{item.lastName}} <span
                                class="error-message-custom" *ngIf="!item.isActive">( Disabled )</span>
                        </div>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'GLOBAL.status' | translate}}</div>
            <div class="mr-20">
                <!-- *ngIf="(isCustomStatusEnabled? !isCustomStatusListLoading : !statusListIsLoading) else fieldLoader" -->
                <ng-select [virtualScroll]="true" [items]="isCustomStatusEnabled ? customStatusList : masterLeadStatus"
                    ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                    placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="id"
                    [(ngModel)]="advanceFilters.StatusesIds" (change)="updateSubStatus()">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                data-automate-id="item-{{index}}" [checked]="item$.selected">
                            <span class="checkmark"></span>{{item.displayName}}
                        </div>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.sub-status' | translate}}</div>
            <!-- *ngIf="!statusListIsLoading else fieldLoader" -->
            <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="subStatusList" [multiple]="true" [closeOnSelect]="false"
                    ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName"
                    bindValue="id" [(ngModel)]="advanceFilters.SubStatusesIds">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                class="checkmark"></span>{{item.displayName}}
                        </div>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100" *ngIf="canViewLeadSource">
            <div class="field-label">{{'LEADS.source' | translate}}</div>
            <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="leadSources" [multiple]="true" [closeOnSelect]="false"
                    ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" [(ngModel)]="advanceFilters.Sources"
                    (change)="updateSubSource()">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                class="checkmark"></span>{{item}}
                        </div>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100" *ngIf="canViewLeadSource">
            <div class="field-label">{{'LEADS.sub-source' | translate}}</div>
            <div class="mr-20">
                <ng-select [virtualScroll]="true" *ngIf="!subSourceListIsLoading else fieldLoader"
                    [items]="subSourceList" ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                    placeholder="{{'GLOBAL.select' | translate}}" [(ngModel)]="advanceFilters.SubSources">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container" title="{{item}}"><input type="checkbox" id="item-{{index}}"
                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                class="checkmark"></span>{{item}}
                        </div>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'SIDEBAR.project'| translate}}</div>
            <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="projectList" ResizableDropdown [multiple]="true"
                    [closeOnSelect]="false" *ngIf="!projectListIsLoading else fieldLoader"
                    placeholder="{{'GLOBAL.select' | translate}}" bindLabel="id" [(ngModel)]="advanceFilters.Projects">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                class="checkmark"></span>{{item}}
                        </div>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LABEL.property'| translate}}</div>
            <div class="mr-20">
                <ng-select [virtualScroll]="true" [items]="propertyList" *ngIf="!propertyListIsLoading else fieldLoader"
                    ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                    placeholder="{{'GLOBAL.select' | translate}}" bindLabel="id"
                    [(ngModel)]="advanceFilters.Properties">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                class="checkmark"></span>{{item}}
                        </div>
                    </ng-template>
                </ng-select>
            </div>
        </div>
    </div>
    <div class="flex-end mt-10">
        <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="modalService.hide()">{{'BUTTONS.cancel' |
            translate }}</u>
        <button class="btn-gray mr-20" (click)="onClearAllFilters()">{{ 'GLOBAL.reset' | translate }}</button>
        <button class="btn-coal" (click)="applyAdvancedFilter()">{{ 'GLOBAL.search' | translate }}</button>
    </div>
    <ng-template #fieldLoader>
        <ng-select [virtualScroll]="true" class="pe-none blinking"></ng-select>
    </ng-template>
</div>