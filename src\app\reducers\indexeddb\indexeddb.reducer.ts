import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
  CheckForUpdatesFailure,
  CheckForUpdatesSuccess,
  GetBatchProcessingStatusFailure,
  GetBatchProcessingStatusSuccess,
  IndexedDBActionTypes,
  InitIndexedDBFailure,
  ResetBatchProcessingFailure,
  ResumeBatchProcessingFailure,
  ResumeBatchProcessingSuccess
} from './indexeddb.actions';

export type IndexedDBState = {
  // IndexedDB initialization state
  isInitialized: boolean;
  isInitializing: boolean;
  error: any;

  // Data synchronization state
  lastModifiedDate: string;
  storedLastModifiedDate: string;
  isCheckingForUpdates: boolean;
  isFetchingModifiedProperties: boolean;
  isStoringProperties: boolean;
  isFetchingInitialProperties: boolean;
  isFetchingRemainingProperties: boolean;
  isClearingIndexedDB: boolean;
  isResettingIndexedDB: boolean;
  useIndexedDB: boolean;

  // Batch processing state
  batchProcessingStatus: any;
  isGettingBatchProcessingStatus: boolean;
  isResumingBatchProcessing: boolean;
  isResettingBatchProcessing: boolean;
};

const initialState: IndexedDBState = {
  // IndexedDB initialization state
  isInitialized: false,
  isInitializing: false,
  error: null,

  // Data synchronization state
  lastModifiedDate: '',
  storedLastModifiedDate: '',
  isCheckingForUpdates: false,
  isFetchingModifiedProperties: false,
  isStoringProperties: false,
  isFetchingInitialProperties: false,
  isFetchingRemainingProperties: false,
  isClearingIndexedDB: false,
  isResettingIndexedDB: false,
  useIndexedDB: true,

  // Batch processing state
  batchProcessingStatus: null,
  isGettingBatchProcessingStatus: false,
  isResumingBatchProcessing: false,
  isResettingBatchProcessing: false,
};

export function indexedDBReducer(
  state: IndexedDBState = initialState,
  action: Action
): IndexedDBState {
  switch (action.type) {
    // IndexedDB Initialization
    case IndexedDBActionTypes.INIT_INDEXED_DB:
      return {
        ...state,
        isInitializing: true,
        error: null,
      };
    case IndexedDBActionTypes.INIT_INDEXED_DB_SUCCESS:
      return {
        ...state,
        isInitialized: true,
        isInitializing: false,
      };
    case IndexedDBActionTypes.INIT_INDEXED_DB_FAILURE:
      return {
        ...state,
        isInitializing: false,
        isInitialized: false,
        error: (action as InitIndexedDBFailure).error,
      };

    // Check for Updates
    case IndexedDBActionTypes.CHECK_FOR_UPDATES:
      return {
        ...state,
        isCheckingForUpdates: true,
      };
    case IndexedDBActionTypes.CHECK_FOR_UPDATES_SUCCESS:
      return {
        ...state,
        isCheckingForUpdates: false,
        lastModifiedDate: (action as CheckForUpdatesSuccess)
          .apiLastModifiedDate,
        storedLastModifiedDate: (action as CheckForUpdatesSuccess)
          .storedLastModifiedDate,
      };
    case IndexedDBActionTypes.CHECK_FOR_UPDATES_NO_CHANGE:
      return {
        ...state,
        isCheckingForUpdates: false,
      };
    case IndexedDBActionTypes.CHECK_FOR_UPDATES_FAILURE:
      return {
        ...state,
        isCheckingForUpdates: false,
        error: (action as CheckForUpdatesFailure).error,
      };

    // Fetch Modified Properties
    case IndexedDBActionTypes.FETCH_MODIFIED_PROPERTIES:
      return {
        ...state,
        isFetchingModifiedProperties: true,
      };
    case IndexedDBActionTypes.FETCH_MODIFIED_PROPERTIES_SUCCESS:
      return {
        ...state,
        isFetchingModifiedProperties: false,
      };
    case IndexedDBActionTypes.FETCH_MODIFIED_PROPERTIES_FAILURE:
      return {
        ...state,
        isFetchingModifiedProperties: false,
        error: (action as CheckForUpdatesFailure).error,
      };

    // Fetch Initial Properties
    case IndexedDBActionTypes.FETCH_INITIAL_PROPERTIES:
      return {
        ...state,
        isFetchingInitialProperties: true,
      };
    case IndexedDBActionTypes.FETCH_INITIAL_PROPERTIES_SUCCESS:
      return {
        ...state,
        isFetchingInitialProperties: false,
      };
    case IndexedDBActionTypes.FETCH_INITIAL_PROPERTIES_FAILURE:
      return {
        ...state,
        isFetchingInitialProperties: false,
        error: (action as CheckForUpdatesFailure).error,
      };

    // Store Properties
    case IndexedDBActionTypes.STORE_PROPERTIES:
      return {
        ...state,
        isStoringProperties: true,
      };
    case IndexedDBActionTypes.STORE_PROPERTIES_SUCCESS:
      return {
        ...state,
        isStoringProperties: false,
      };
    case IndexedDBActionTypes.STORE_PROPERTIES_FAILURE:
      return {
        ...state,
        isStoringProperties: false,
        error: (action as CheckForUpdatesFailure).error,
      };

    // Fetch Remaining Properties
    case IndexedDBActionTypes.FETCH_REMAINING_PROPERTIES:
      return {
        ...state,
        isFetchingRemainingProperties: true,
      };
    case IndexedDBActionTypes.FETCH_REMAINING_PROPERTIES_SUCCESS:
      return {
        ...state,
        isFetchingRemainingProperties: false,
      };
    case IndexedDBActionTypes.FETCH_REMAINING_PROPERTIES_FAILURE:
      return {
        ...state,
        isFetchingRemainingProperties: false,
        error: (action as CheckForUpdatesFailure).error,
      };

    // Clear IndexedDB
    case IndexedDBActionTypes.CLEAR_INDEXED_DB:
      return {
        ...state,
        isClearingIndexedDB: true,
      };
    case IndexedDBActionTypes.CLEAR_INDEXED_DB_SUCCESS:
      return {
        ...state,
        isClearingIndexedDB: false,
      };
    case IndexedDBActionTypes.CLEAR_INDEXED_DB_FAILURE:
      return {
        ...state,
        isClearingIndexedDB: false,
        error: (action as CheckForUpdatesFailure).error,
      };

    // Reset IndexedDB
    case IndexedDBActionTypes.RESET_INDEXED_DB:
      return {
        ...state,
        isResettingIndexedDB: true,
      };
    case IndexedDBActionTypes.RESET_INDEXED_DB_SUCCESS:
      return {
        ...state,
        isResettingIndexedDB: false,
      };
    case IndexedDBActionTypes.RESET_INDEXED_DB_FAILURE:
      return {
        ...state,
        isResettingIndexedDB: false,
        error: (action as CheckForUpdatesFailure).error,
      };

    // Batch Processing Actions
    case IndexedDBActionTypes.GET_BATCH_PROCESSING_STATUS:
      return {
        ...state,
        isGettingBatchProcessingStatus: true,
      };
    case IndexedDBActionTypes.GET_BATCH_PROCESSING_STATUS_SUCCESS:
      return {
        ...state,
        isGettingBatchProcessingStatus: false,
        batchProcessingStatus: (action as GetBatchProcessingStatusSuccess)
          .status,
      };
    case IndexedDBActionTypes.GET_BATCH_PROCESSING_STATUS_FAILURE:
      return {
        ...state,
        isGettingBatchProcessingStatus: false,
        error: (action as GetBatchProcessingStatusFailure).error,
      };

    case IndexedDBActionTypes.RESUME_BATCH_PROCESSING:
      return {
        ...state,
        isResumingBatchProcessing: true,
      };
    case IndexedDBActionTypes.RESUME_BATCH_PROCESSING_SUCCESS:
      return {
        ...state,
        isResumingBatchProcessing: false,
        batchProcessingStatus: (action as ResumeBatchProcessingSuccess).result
          .status,
      };
    case IndexedDBActionTypes.RESUME_BATCH_PROCESSING_FAILURE:
      return {
        ...state,
        isResumingBatchProcessing: false,
        error: (action as ResumeBatchProcessingFailure).error,
      };

    case IndexedDBActionTypes.RESET_BATCH_PROCESSING:
      return {
        ...state,
        isResettingBatchProcessing: true,
      };
    case IndexedDBActionTypes.RESET_BATCH_PROCESSING_SUCCESS:
      return {
        ...state,
        isResettingBatchProcessing: false,
        batchProcessingStatus: null,
      };
    case IndexedDBActionTypes.RESET_BATCH_PROCESSING_FAILURE:
      return {
        ...state,
        isResettingBatchProcessing: false,
        error: (action as ResetBatchProcessingFailure).error,
      };

    default:
      return state;
  }
}

// Selectors
export const selectIndexedDBFeature = (state: AppState) => state.indexedDB;

// IndexedDB initialization selectors
export const getIsIndexedDBInitialized = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.isInitialized
);

export const getIsIndexedDBInitializing = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.isInitializing
);

// Data synchronization selectors
export const getLastModifiedDate = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.lastModifiedDate
);

export const getStoredLastModifiedDate = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.storedLastModifiedDate
);

export const getIsCheckingForUpdates = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.isCheckingForUpdates
);

export const getIsFetchingModifiedProperties = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.isFetchingModifiedProperties
);

export const getIsStoringProperties = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.isStoringProperties
);

export const getIsFetchingInitialProperties = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.isFetchingInitialProperties
);

export const getIsFetchingRemainingProperties = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.isFetchingRemainingProperties
);

export const getIsClearingIndexedDB = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.isClearingIndexedDB
);

export const getIsResettingIndexedDB = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.isResettingIndexedDB
);

export const getUseIndexedDB = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.useIndexedDB
);

// Batch processing selectors
export const getBatchProcessingStatus = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.batchProcessingStatus
);

export const getIsGettingBatchProcessingStatus = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.isGettingBatchProcessingStatus
);

export const getIsResumingBatchProcessing = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.isResumingBatchProcessing
);

export const getIsResettingBatchProcessing = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.isResettingBatchProcessing
);

export const getIndexedDBError = createSelector(
  selectIndexedDBFeature,
  (state: IndexedDBState) => state.error
);
