<div class="d-flex w-100">
    <div class="w-35 tb-position-absolute z-index-2 tb-w-360px" *ngIf="showEvents || screen?.innerWidth > 1279">
        <div class="w-100 flex-between bg-black-100 p-16">
            <div class="text-white">Form Fields</div>
            <div class="icon ic-close-secondary ic-sm cursor-pointer d-none tb-d-block mr-10 mt-10"
                (click)="showEvents = !showEvents"></div>
        </div>
        <form class="p-16 bg-white h-100-97 scrollbar scroll-hide tb-h-100-108" [formGroup]="customForm">
            <div class="p-10 flex-between position-relative" #BasicInfo>
                <div class="position-absolute ntop-10" id="BasicInfo"></div>
                <div class="cursor-pointer align-center" (click)="isHideBasicInfo = !isHideBasicInfo">
                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                        [ngClass]="{'rotate-270' : isHideBasicInfo}"></span>
                    <h6 class="text-black-200">Basic Info</h6>
                </div>
                <div class="d-flex">
                    <div class="text-dark-gray text-sm align-center me-2"> Select all</div>
                    <div class="position-relative flex-between" (change)="toggleAllCheckboxes(basicInfoFields)">
                        <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                            [checked]="allCheckboxesChecked(basicInfoFields)">
                        <label for="chkToggle" class="switch-label"></label>
                    </div>
                </div>

            </div>
            <ng-container *ngIf="!isHideBasicInfo">
                <div class="border-bottom mb-6"></div>
                <div class="d-flex flex-wrap w-100 position-relative">
                    <ng-container *ngFor="let field of basicInfoFields; let i = index">
                        <div class="w-50 mt-10">
                            <div class="border br-4" [ngClass]="{'mr-10': i % 2 == 0}">
                                <div class="py-8 px-10 flex-between">
                                    <div class="fw-600 text-sm text-mud">{{field.displayName}}</div>
                                    <div
                                        [ngClass]="field.controlName === 'name' || field.controlName === 'contactNo' ? 'pe-none' : ''">
                                        <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                                            [formControlName]="field?.controlName"
                                            [checked]="field.controlName === 'name' || field.controlName === 'contactNo' ? true : null"
                                            [disabled]="field.controlName === 'name' || field.controlName === 'contactNo'">
                                        <label for="chkToggle" class="switch-label"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </ng-container>

            <div class="mt-20 p-10 flex-between position-relative" #EnquiryInfo>
                <div class="position-absolute ntop-10" id="EnquiryInfo"></div>
                <div class="cursor-pointer align-center" (click)="isHideEnquiryInfo = !isHideEnquiryInfo">
                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                        [ngClass]="{'rotate-270' : isHideEnquiryInfo}"></span>
                    <h6 class="text-black-200">Enquiry Info</h6>
                </div>
                <div class="d-flex">
                    <div class="text-dark-gray text-sm align-center me-2"> Select all</div>
                    <div class="position-relative flex-between" (change)="toggleAllCheckboxes(enquiryInfoFields)">
                        <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                            [formControlName]="field?.controlName" [checked]="allCheckboxesChecked(enquiryInfoFields)">
                        <label for="chkToggle" class="switch-label"></label>
                    </div>
                </div>

            </div>
            <ng-container *ngIf="!isHideEnquiryInfo">
                <div class="border-bottom mb-6"></div>
                <div class="d-flex flex-wrap w-100 position-relative">
                    <ng-container *ngFor="let field of enquiryInfoFields; let i = index">
                        <div class="w-50 mt-10">
                            <div class="border br-4" [ngClass]="{'mr-10': i % 2 == 0}">
                                <div class="py-8 px-10 flex-between">
                                    <div class="fw-600 text-sm text-mud">{{field.displayName}}</div>
                                    <div>
                                        <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                                            [formControlName]="field.controlName"
                                            (change)="updatePropertyType(field.controlName)">
                                        <label for="chkToggle" class="switch-label"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </ng-container>
            <div class="mt-20 p-10 flex-between position-relative" #AdditionalInfo>
                <div class="position-absolute ntop-10" id="AdditionalInfo"></div>
                <div class="cursor-pointer align-center" (click)="isHideAdditionalInfo = !isHideAdditionalInfo">
                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                        [ngClass]="{'rotate-270' : isHideAdditionalInfo}"></span>
                    <h6 class="text-black-200">Additional Info</h6>
                </div>
                <div class="d-flex">
                    <div class="text-dark-gray text-sm align-center me-2"> Select all</div>
                    <div class="position-relative flex-between" (change)="toggleAllCheckboxes(additionalInfoFields)">
                        <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                            [checked]="allCheckboxesChecked(additionalInfoFields)">
                        <label for="chkToggle" class="switch-label"></label>
                    </div>
                </div>

            </div>
            <ng-container *ngIf="!isHideAdditionalInfo">
                <div class="border-bottom mb-6"></div>
                <div class="d-flex flex-wrap w-100 position-relative">
                    <ng-container *ngFor="let field of additionalInfoFields; let i = index">
                        <div class="w-50 mt-10">
                            <div class="border br-4" [ngClass]="{'mr-10': i % 2 == 0}">
                                <div class="py-8 px-10 flex-between">
                                    <div class="fw-600 text-sm text-mud">{{field.displayName}}</div>
                                    <div>
                                        <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                                            [formControlName]="field?.controlName">
                                        <label for="chkToggle" class="switch-label"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </ng-container>
            <div class="mt-20 p-10 flex-between position-relative" #MaritalStatus>
                <div class="position-absolute ntop-10" id="MaritalStatus"></div>
                <div class="cursor-pointer align-center" (click)="isHideMartialStatusInfo = !isHideMartialStatusInfo">
                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                        [ngClass]="{'rotate-270' : isHideMartialStatusInfo}"></span>
                    <h6 class="text-black-200">Marital status</h6>
                </div>
                <div class="d-flex">
                    <div class="text-dark-gray text-sm align-center me-2"> Select all</div>
                    <div class="position-relative flex-between" (change)="toggleAllCheckboxes(martialStatusInfoFields)">
                        <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                            [checked]="allCheckboxesChecked(martialStatusInfoFields)"
                            [formControlName]="field?.controlName">
                        <label for="chkToggle" class="switch-label"></label>
                    </div>
                </div>

            </div>
            <ng-container *ngIf="!isHideMartialStatusInfo">
                <div class="border-bottom mb-6"></div>
                <div class="d-flex flex-wrap w-100 position-relative">
                    <ng-container *ngFor="let field of martialStatusInfoFields; let i = index">
                        <div class="w-50 mt-10">
                            <div class="border br-4" [ngClass]="{'mr-10': i % 2 == 0}">
                                <div class="py-8 px-10 flex-between">
                                    <div class="fw-600 text-sm text-mud">{{field.displayName}}</div>
                                    <div>
                                        <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                                            [formControlName]="field?.controlName">
                                        <label for="chkToggle" class="switch-label"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </ng-container>
            <div class="mt-20 p-10 flex-between position-relative" #NRI>
                <div class="position-absolute ntop-10" id="NRI"></div>
                <div class="cursor-pointer align-center" (click)="isHideNRIInfo = !isHideNRIInfo">
                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                        [ngClass]="{'rotate-270' : isHideNRIInfo}"></span>
                    <h6 class="text-black-200">NRI</h6>
                </div>
                <div class="d-flex">
                    <div class="text-dark-gray text-sm align-center me-2"> Select all</div>
                    <div class="position-relative flex-between" (change)="toggleAllCheckboxes(nriInfoFields)">
                        <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                            [checked]="allCheckboxesChecked(nriInfoFields)" [formControlName]="field?.controlName">
                        <label for="chkToggle" class="switch-label"></label>
                    </div>
                </div>

            </div>
            <ng-container *ngIf="!isHideNRIInfo">
                <div class="border-bottom mb-6"></div>
                <div class="d-flex flex-wrap w-100 position-relative">
                    <ng-container *ngFor="let field of nriInfoFields; let i = index">
                        <div class="w-50 mt-10">
                            <div class="border br-4" [ngClass]="{'mr-10': i % 2 == 0}">
                                <div class="py-8 px-10 flex-between">
                                    <div class="fw-600 text-sm text-mud">{{field.displayName}}</div>
                                    <div>
                                        <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                                            [formControlName]="field?.controlName">
                                        <label for="chkToggle" class="switch-label"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </ng-container>
            <div class="mt-20 p-10 flex-between position-relative" #Others>
                <div class="position-absolute ntop-10" id="Others"></div>

                <div class="cursor-pointer align-center" (click)="isHideOtherInfo = !isHideOtherInfo">
                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                        [ngClass]="{'rotate-270' : isHideOtherInfo}"></span>
                    <h6 class="text-black-200">Others</h6>
                </div>
                <div class="d-flex">
                    <div class="text-dark-gray text-sm align-center me-2"> Select all</div>
                    <div class="position-relative flex-between" (change)="toggleAllCheckboxes(otherInfoFields)">
                        <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                            [checked]="allCheckboxesChecked(otherInfoFields)">
                        <label for="chkToggle" class="switch-label"></label>
                    </div>
                </div>

            </div>
            <ng-container *ngIf="!isHideOtherInfo">
                <div class="border-bottom mb-6"></div>
                <div class="d-flex flex-wrap w-100">
                    <ng-container *ngFor="let field of otherInfoFields; let i = index">
                        <div class="w-50 mt-10">
                            <div class="border br-4" [ngClass]="{'mr-10': i % 2 == 0}">
                                <div class="py-8 px-10 flex-between position-relative">
                                    <div class="fw-600 text-sm text-mud">{{field.displayName}}</div>
                                    <div>
                                        <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                                            [formControlName]="field.controlName">
                                        <label for="chkToggle" class="switch-label"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </ng-container>
        </form>
    </div>
    <div class="w-75 tb-w-100">
        <div class="mt-20 mx-30">
            <div class="w-100">
                <div class="d-flex">
                    <div class="border br-20 bg-white align-center user">
                        <div class="activation" [ngClass]="{ 'active': currentActive == 0 }"
                            (click)="scrollTo('BasicInfo', 0)">
                            <span class="icon ic-notes-pen ic-sm mr-8 ip-mr-4"
                                [ngClass]="{ 'active': currentActive !== 0 }"></span>
                            <span class="ph-d-none"> Basic Info</span>
                        </div>
                        <div class="activation" [ngClass]="{ 'active': currentActive == 1 }"
                            (click)="scrollTo('EnquiryInfo', 1)">
                            <span class="icon ic-circle-exclamation-solid ic-sm mr-8 ip-mr-4"
                                [ngClass]="{ 'active': currentActive !== 1 }"></span>
                            <span class="ph-d-none">Enquiry Info</span>
                        </div>
                        <div class="activation" [ngClass]="{ 'active': currentActive == 2 }"
                            (click)="scrollTo('AdditionalInfo', 2)">
                            <span class="icon ic-file-clip ic-sm mr-8 ip-mr-4"
                                [ngClass]="{ 'active': currentActive !== 2 }"></span>
                            <span class="ph-d-none">Additional Info</span>
                        </div>
                        <div class="activation" [ngClass]="{ 'active': currentActive == 3 }"
                            (click)="scrollTo('MaritalStatus', 3)">
                            <span class="icon ic-two-persons ic-sm mr-8 ip-mr-4"
                                [ngClass]="{ 'active': currentActive !== 3 }"></span>
                            <span class="ph-d-none">Marital status</span>
                        </div>
                        <div class="activation" [ngClass]="{ 'active': currentActive == 4 }"
                            (click)="scrollTo('NRI', 4)">
                            <span class="icon ic-currency ic-sm mr-8 ip-mr-4"
                                [ngClass]="{ 'active': currentActive !== 4 }"></span>
                            <span class="ph-d-none">NRI</span>
                        </div>
                        <div class="activation" [ngClass]="{ 'active': currentActive == 5 }"
                            (click)="scrollTo('Others', 5)">
                            <span class="icon ic-hexagon-arrow ic-sm mr-8 ip-mr-4"
                                [ngClass]="{ 'active': currentActive !== 5 }"></span>
                            <span class="ph-d-none">Others</span>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-wrap mb-10 w-100">
                    <div class="w-33 ip-w-50 ph-w-100">
                        <div class="field-label" *ngIf="!isDualOwnershipEnabled">Primary {{'LEADS.assign-to' |
                            translate}}</div>
                        <div class="field-label" *ngIf="isDualOwnershipEnabled">Primary
                            Assign To
                        </div>
                        <form-errors-wrapper label="primary">
                            <ng-select [virtualScroll]="true" ResizableDropdown placeholder="ex. Manasa"
                                class="bg-white mr-20 ph-mr-0">
                                <ng-option *ngFor="let user of primaryAgentList" [value]="user.id">{{user.firstName}}
                                    {{user.lastName}}</ng-option>
                            </ng-select>
                        </form-errors-wrapper>
                    </div>
                    <ng-container *ngIf="isDualOwnershipEnabled">
                        <div class="w-33 ip-w-50 ph-w-100">
                            <div class="field-label">Secondary {{'LEADS.assign-to' | translate}}</div>
                            <ng-select [virtualScroll]="true" ResizableDropdown class="bg-white mr-20 ph-mr-0"
                                placeholder="ex. Samyuktha">
                                <ng-option *ngFor="let user of secondaryAgentList" [value]="user.id">{{user.firstName}}
                                    {{user.lastName}}</ng-option>
                            </ng-select>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="canViewLeadSource">
                        <div class="w-33 ip-w-50 ph-w-100">
                            <div class="field-label">{{'LEADS.source' | translate}}</div>
                            <ng-select [virtualScroll]="true" ResizableDropdown class="bg-white mr-20 ph-mr-0"
                                placeholder="{{ 'GLOBAL.select' | translate }} {{ 'LEADS.source' | translate }}">
                                <ng-option *ngFor="let source of leadSources" [value]="source.displayName"
                                    class="w-100">
                                    {{source.displayName}}
                                </ng-option>
                            </ng-select>
                        </div>
                        <div class="w-33 ip-w-50 ph-w-100">
                            <div class="field-label">{{'LEADS.sub-source' | translate}}</div>
                            <ng-select [virtualScroll]="true" ResizableDropdown [items]="subSources" [addTag]="true"
                                addTagText="Create New sub-source" class="bg-white mr-20 ph-mr-0"
                                placeholder="{{ 'GLOBAL.select' | translate }}/Create {{ 'LEADS.sub-source' | translate }}">
                                <ng-template ng-option-tmp let-item="item">
                                    <div title="{{item}}">{{item}}</div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </ng-container>
                </div>
                <div class="w-100 h-100-255 scrollbar">
                    <div>
                        <div class="mr-12">
                            <div class="bg-white position-relative" #BasicInfo>
                                <div class="position-absolute ntop-10" id="BasicInfo"></div>

                                <div class="cursor-pointer align-center py-12 px-20"
                                    [ngClass]="!basicInfo ? 'border-bottom': ''" (click)="basicInfo = !basicInfo">
                                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                                        [ngClass]="{'rotate-270' : basicInfo}"></span>
                                    <h6 class="text-black-200">Basic Info</h6>
                                </div>
                                <div class="d-flex flex-wrap pl-20 pb-16" *ngIf="!basicInfo">
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['name'].value">
                                        <div class="field-label-req">{{ 'GLOBAL.name' |
                                            translate }}</div>
                                        <div class="mr-20 ph-mr-0">
                                            <form-errors-wrapper label="{{ 'GLOBAL.name' | translate }}"
                                                class="position-relative">
                                                <input type="text" required id="inpLeadName"
                                                    data-automate-id="inpLeadName" placeholder="ex. Mounika Pampana"
                                                    maxlength="75">
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                    <div class="position-relative w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['contactNo'].value">
                                        <div class="field-label-req">{{'INTEGRATION.primary' | translate}}
                                            {{'GLOBAL.number'
                                            |
                                            translate}}</div>
                                        <div class="mr-20 ph-mr-0">
                                            <form-errors-wrapper label="{{'USER.phone-number' | translate}}"
                                                [ngClass]="{'non-editable pe-none' : !canUpdateInfo && !isAddLead }">
                                                <ngx-mat-intl-tel-input #contactNoInput *ngIf="hasInternationalSupport"
                                                    [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
                                                    [enableSearch]="true" class="no-validation contactNoInput">
                                                </ngx-mat-intl-tel-input>
                                                <ngx-mat-intl-tel-input #contactNoInput *ngIf="!hasInternationalSupport"
                                                    [preferredCountries]="preferredCountries"
                                                    [onlyCountries]="preferredCountries" [enablePlaceholder]="true"
                                                    [enableSearch]="true" class="no-validation contactNoInput">
                                                </ngx-mat-intl-tel-input>
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                    <div class="position-relative w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['alternateContactNo'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.alternateContactNo ? 'field-label-req' : 'field-label'">
                                                {{'GLOBAL.alternate'
                                                | translate}} {{'GLOBAL.number' |
                                                translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('alternateContactNo')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="mr-20 ph-mr-0">
                                            <form-errors-wrapper
                                                label="{{'GLOBAL.alternate' | translate}} {{'GLOBAL.number' | translate}}">
                                                <ngx-mat-intl-tel-input #alternateNoInput
                                                    *ngIf="hasInternationalSupport"
                                                    [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
                                                    [enableSearch]="true" class="alternateNoInput no-validation">
                                                </ngx-mat-intl-tel-input>
                                                <ngx-mat-intl-tel-input #alternateNoInput
                                                    *ngIf="!hasInternationalSupport"
                                                    [preferredCountries]="preferredCountries"
                                                    [onlyCountries]="preferredCountries" [enablePlaceholder]="true"
                                                    [enableSearch]="true" class="alternateNoInput no-validation">
                                                </ngx-mat-intl-tel-input>
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['email'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div [ngClass]="isLabelClicked?.email ? 'field-label-req' : 'field-label'">
                                                {{'USER.email'
                                                |
                                                translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('email')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="mr-20 ph-mr-0">
                                            <form-errors-wrapper label="{{'USER.email' | translate}}">
                                                <input type="email" id="inpLeadMail" data-automate-id="inpLeadMail"
                                                    placeholder="ex. <EMAIL>">
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                    <ng-container>
                                        <div class="w-33 ip-w-50 ph-w-100"
                                            *ngIf="customForm.controls['referralName'].value">
                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                <div
                                                    [ngClass]="isLabelClicked?.referralName ? 'field-label-req' : 'field-label'">
                                                    Referral
                                                    Name</div>
                                                <div class="position-relative  visiblity-req d-none">:
                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                        (click)="toggleLabel('referralName')">Mark as mandatory</div>
                                                </div>
                                            </div>
                                            <div class="form-group mr-20 ph-mr-0">
                                                <input type="text" id="inpReferralName"
                                                    data-automate-id="inpReferralName" placeholder="ex. Manasa">
                                            </div>
                                        </div>
                                        <div class="w-33 ip-w-50 ph-w-100"
                                            *ngIf="customForm.controls['referralContactNo'].value">
                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                <div
                                                    [ngClass]="isLabelClicked?.referralContactNo ? 'field-label-req' : 'field-label'">
                                                    Referral Phone No</div>
                                                <div class="position-relative  visiblity-req d-none">:
                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                        (click)="toggleLabel('referralContactNo')">Mark as mandatory
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mr-20 ph-mr-0">
                                                <form-errors-wrapper
                                                    label="{{'LEAD_FORM.referral-phone-no' | translate}}">
                                                    <ngx-mat-intl-tel-input #referralNoInput
                                                        *ngIf="hasInternationalSupport"
                                                        [preferredCountries]="preferredCountries"
                                                        [enablePlaceholder]="true" [enableSearch]="true"
                                                        class="no-validation referralNoInput">
                                                    </ngx-mat-intl-tel-input>
                                                    <ngx-mat-intl-tel-input #referralNoInput
                                                        *ngIf="!hasInternationalSupport"
                                                        [preferredCountries]="preferredCountries"
                                                        [onlyCountries]="preferredCountries" [enablePlaceholder]="true"
                                                        [enableSearch]="true" class="no-validation referralNoInput">
                                                    </ngx-mat-intl-tel-input>
                                                </form-errors-wrapper>
                                            </div>
                                        </div>
                                        <div class="w-33 ip-w-50 ph-w-100"
                                            *ngIf="customForm.controls['referralEmail'].value">
                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                <div
                                                    [ngClass]="isLabelClicked?.referralEmail ? 'field-label-req' : 'field-label'">
                                                    Referral
                                                    Email</div>
                                                <div class="position-relative  visiblity-req d-none">:
                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                        (click)="toggleLabel('referralEmail')">Mark as mandatory</div>
                                                </div>
                                            </div>
                                            <div class="mr-20 ph-mr-0">
                                                <form-errors-wrapper label="{{'USER.email' | translate}}">
                                                    <input type="email" id="inpReferralEmail"
                                                        data-automate-id="inpReferralEmail"
                                                        placeholder="ex. <EMAIL>"
                                                        [readOnly]="!canUpdateInfo && !isAddLead">
                                                </form-errors-wrapper>
                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>
                            <div class="bg-white position-relative mt-12" #EnquiryInfo *ngIf="isHideEnquiryFieldInfo">
                                <div class="position-absolute ntop-10" id="EnquiryInfo"></div>

                                <div class="cursor-pointer align-center py-12 px-20"
                                    [ngClass]="!enquiryInfo ? 'border-bottom': ''" (click)="enquiryInfo = !enquiryInfo">
                                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                                        [ngClass]="{'rotate-270' : enquiryInfo}"></span>
                                    <h6 class="text-black-200">Enquiry Info</h6>
                                </div>
                                <div class="d-flex flex-wrap pl-20 pb-16" *ngIf="!enquiryInfo">
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['enquiredFor'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.enquiredFor ? 'field-label-req' : 'field-label'">
                                                {{'LEAD_FORM.enquired-for'
                                                | translate }}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('enquiredFor')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" [items]="enquiredForList"
                                                [clearSearchOnAdd]="true" [multiple]="true" appSelectAll [closeOnSelect]="false"
                                                placeholder="{{'GLOBAL.select' | translate}}"
                                                formControlName="enquiredFor" class="bg-white">
                                                <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                    let-index="index">
                                                    <div class="checkbox-container"><input type="checkbox"
                                                            id="item-{{index}}" data-automate-id="item-{{index}}"
                                                            [checked]="item$.selected"><span
                                                            class="checkmark"></span>{{item}}
                                                    </div>
                                                </ng-template>
                                            </ng-select>
                                        </div>
                                    </div>

                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['basePropertyType'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.basePropertyType ? 'field-label-req' : 'field-label'">
                                                {{'LABEL.property'
                                                | translate }} {{'LABEL.type' |
                                                translate
                                                }}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('basePropertyType')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" [items]="PropertyType"
                                                [closeOnSelect]="true" placeholder="{{'GLOBAL.select' | translate}}"
                                                class="bg-white">
                                            </ng-select>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100 remove-form-group"
                                        *ngIf="customForm.controls['subPropertyType'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.subPropertyType ? 'field-label-req' : 'field-label'">
                                                {{'PROPERTY.sub-type'
                                                | translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('subPropertyType')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="mr-20 ph-mr-0">
                                            <form-errors-wrapper label="{{'PROPERTY.sub-type' | translate}}">
                                                <ng-select [virtualScroll]="true"
                                                    placeholder="{{ 'GLOBAL.select' | translate }} {{'PROPERTY.sub-type' | translate}}"
                                                    [items]="propSubTypes" bindValue="displayName"
                                                    bindLabel="displayName" class="bg-white">
                                                </ng-select>
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                    <ng-container>
                                        <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['noOfBHK'].value">
                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                <div
                                                    [ngClass]="isLabelClicked?.noOfBHK ? 'field-label-req' : 'field-label'">
                                                    {{'PROPERTY.bhk'
                                                    | translate }}</div>
                                                <div class="position-relative  visiblity-req d-none">:
                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                        (click)="toggleLabel('noOfBHK')">Mark as mandatory</div>
                                                </div>
                                            </div>
                                            <div class="mr-20 ph-mr-0">
                                                <ng-select [virtualScroll]="true" [items]="noOfBhk"
                                                    [clearSearchOnAdd]="true" [multiple]="true" appSelectAll [closeOnSelect]="false"
                                                    placeholder="{{'GLOBAL.select' | translate}}" class="bg-white">
                                                    <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                        let-index="index">
                                                        <div class="checkbox-container"><input type="checkbox"
                                                                id="item-{{index}}" data-automate-id="item-{{index}}"
                                                                [checked]="item$.selected"><span
                                                                class="checkmark"></span>{{item}}
                                                        </div>
                                                    </ng-template>
                                                </ng-select>
                                            </div>
                                        </div>
                                        <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['bhkType'].value">
                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                <div
                                                    [ngClass]="isLabelClicked?.bhkType ? 'field-label-req' : 'field-label'">
                                                    {{'PROPERTY.bhk'
                                                    | translate }} {{'LABEL.type' |
                                                    translate}}</div>
                                                <div class="position-relative  visiblity-req d-none">:
                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                        (click)="toggleLabel('bhkType')">Mark as mandatory</div>
                                                </div>
                                            </div>
                                            <div class="mr-20 ph-mr-0">
                                                <ng-select [virtualScroll]="true" [items]="bhkTypes"
                                                    [clearSearchOnAdd]="true" [multiple]="true" appSelectAll [closeOnSelect]="false"
                                                    [multiple]="true" appSelectAll [closeOnSelect]="false"
                                                    placeholder="{{'GLOBAL.select' | translate}}" class="bg-white">
                                                    <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                        let-index="index">
                                                        <div class="checkbox-container"><input type="checkbox"
                                                                id="item-{{index}}" data-automate-id="item-{{index}}"
                                                                [checked]="item$.selected"><span
                                                                class="checkmark"></span>{{item}}
                                                        </div>
                                                    </ng-template>
                                                </ng-select>
                                            </div>
                                        </div>
                                    </ng-container>
                                    <div class="w-33 ip-w-50 ph-w-100 qr-code"
                                        *ngIf="customForm.controls['budget'].value">
                                        <div class="field-rupees-tag w-100">
                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                <div
                                                    [ngClass]="isLabelClicked?.budget ? 'field-label-req' : 'field-label'">
                                                    Budget</div>
                                                <div class="position-relative  visiblity-req d-none">:
                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                        (click)="toggleLabel('budget')">Mark as mandatory</div>
                                                </div>
                                            </div>
                                            <div class="align-center w-100">
                                                <div class="position-relative w-50 budget-dropdown">
                                                    <form-errors-wrapper label="Min. {{'LABEL.budget' | translate}}">
                                                        <input type="number" min="1" id="inpLeadLowerBudget"
                                                            data-automate-id="inpLeadLowerBudget"
                                                            placeholder="ex. 4000000" maxlength="10"
                                                            (keydown)="onlyNumbers($event)">
                                                        <div class="no-validation">
                                                            <ng-container
                                                                *ngIf="currency?.length > 1; else showCurrencySymbol">
                                                                <ng-select formControlName="currency"
                                                                    class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                                                                    <ng-option *ngFor="let curr of currency"
                                                                        [value]="curr.currency">
                                                                        <span [title]="curr.currency">
                                                                            {{curr.currency}}
                                                                        </span>
                                                                    </ng-option>
                                                                </ng-select>
                                                            </ng-container>
                                                            <ng-template #showCurrencySymbol>
                                                                <h5 class="rupees px-12 py-4 fw-600 m-4">{{
                                                                    defaultCurrency }}</h5>
                                                            </ng-template>
                                                        </div>
                                                    </form-errors-wrapper>
                                                </div>
                                                <div class="mx-10">to</div>
                                                <div class="position-relative w-50">
                                                    <div class="mr-20 ph-mr-0 budget-dropdown">
                                                        <!-- [control]="addLeadForm.controls['upperBudget']" -->
                                                        <form-errors-wrapper
                                                            label="Max. {{'LABEL.budget' | translate}}">
                                                            <input type="number" min="1" id="inpLeadUpperBudget"
                                                                data-automate-id="inpLeadUpperBudget"
                                                                placeholder="ex. 4000000" maxlength="10"
                                                                (keydown)="onlyNumbers($event)">
                                                            <div class="no-validation">
                                                                <ng-container
                                                                    *ngIf="currency?.length > 1; else showCurrencySymbol">
                                                                    <ng-select formControlName="currency"
                                                                        class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                                                                        <ng-option *ngFor="let curr of currency"
                                                                            [value]="curr.currency">
                                                                            <span [title]="curr.currency">
                                                                                {{curr.currency}}
                                                                            </span>
                                                                        </ng-option>
                                                                    </ng-select>
                                                                </ng-container>
                                                            </div>
                                                        </form-errors-wrapper>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <ng-container *ngIf="customForm.controls['enquiredLocation'].value">
                                        <div
                                            [ngClass]="isShowManualLocation == false ? 'w-33 ip-w-50 ph-w-100': 'w-100'">
                                            <ng-container *ngIf="isShowManualLocation == false else manualLocation">
                                                <div class="w-100">

                                                    <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                        <div
                                                            [ngClass]="isLabelClicked?.enquiredLocation ? 'field-label-req' : 'field-label'">
                                                            {{'INTEGRATION.enquired-location'
                                                            |
                                                            translate}}</div>
                                                        <div class="position-relative  visiblity-req d-none">:
                                                            <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                                (click)="toggleLabel('enquiredLocation')">Mark as
                                                                mandatory</div>
                                                        </div>
                                                    </div>
                                                    <div class="field-tag mr-20 ph-mr-0">
                                                        <ng-select [virtualScroll]="true" ResizableDropdown
                                                            (search)="searchPlaceTerm$.next($event.term)"
                                                            [multiple]="true" appSelectAll [closeOnSelect]="false"
                                                            [clearSearchOnAdd]="true" [editableSearchTerm]="true"
                                                            placeholder="Select Location" class="bg-white"
                                                            [items]="placesList" bindLabel="location"
                                                            (clear)="removeLocation('location')"
                                                            (change)="removeLocation('changeLocation')">
                                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                                let-index="index">
                                                                <div class="checkbox-container"><input type="checkbox"
                                                                        id="item-{{index}}"
                                                                        data-automate-id="item-{{index}}"
                                                                        [checked]="item$.selected"><span
                                                                        class="checkmark"></span>{{item.location}}</div>
                                                            </ng-template>
                                                        </ng-select>
                                                        <div class="search icon ic-search ic-sm ic-coal"></div>
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <ng-template #manualLocation>
                                                <div class="d-flex flex-wrap w-100">
                                                    <div class="w-33 ip-w-50 ph-w-100">
                                                        <div class="form-group mr-20 ph-mr-0">
                                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                                <div
                                                                    [ngClass]="isLabelClicked ? 'field-label-req' : 'field-label'">
                                                                    Enquired
                                                                    Locality</div>
                                                                <div class="position-relative  visiblity-req d-none">:
                                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                                        (click)="toggleLabel()">Mark as mandatory</div>
                                                                </div>
                                                            </div>
                                                            <input type="text" placeholder="ex. HSR Layout"
                                                                (change)="removeLocation('changeLocality')">
                                                        </div>
                                                    </div>
                                                    <div class="w-33 ip-w-50 ph-w-100">
                                                        <div class="form-group mr-20 ph-mr-0">
                                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                                <div
                                                                    [ngClass]="isLabelClicked ? 'field-label-req' : 'field-label'">
                                                                    {{'INTEGRATION.enquired-city'
                                                                    |
                                                                    translate}}</div>
                                                                <div class="position-relative  visiblity-req d-none">:
                                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                                        (click)="toggleLabel()">Mark as mandatory</div>
                                                                </div>
                                                            </div>
                                                            <input type="text" placeholder="ex. Bengaluru"
                                                                (change)="removeLocation('changeLocality')">
                                                        </div>
                                                    </div>
                                                    <div class="w-33 ip-w-50 ph-w-100">
                                                        <div class="form-group mr-20 ph-mr-0">
                                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                                <div
                                                                    [ngClass]="isLabelClicked ? 'field-label-req' : 'field-label'">
                                                                    {{'INTEGRATION.enquired-state'
                                                                    |
                                                                    translate}}</div>
                                                                <div class="position-relative  visiblity-req d-none">:
                                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                                        (click)="toggleLabel()">Mark as mandatory</div>
                                                                </div>
                                                            </div>
                                                            <input type="text" placeholder="ex. Karnataka"
                                                                (change)="removeLocation('changeLocality')">
                                                        </div>
                                                    </div>
                                                </div>

                                            </ng-template>
                                            <div class="d-flex flex-between">
                                                <div class="cursor-pointer align-center fw-semi-bold text-accent-green mt-4"
                                                    (click)="isShowManualLocation = !isShowManualLocation">
                                                    <span class="icon ic-xs ic-accent-green"
                                                        [ngClass]="isShowManualLocation ? 'ic-search' : 'ic-add'"></span>
                                                    <span>{{isShowManualLocation ? 'Location List' :
                                                        'Manually Enter Location'}}</span>
                                                </div>
                                                <div *ngIf="isShowManualLocation"
                                                    class="cursor-pointer align-center fw-semi-bold text-accent-green mt-4"
                                                    (click)="addMoreLocation()">
                                                    <span class="icon ic-xs ic-accent-green"></span>
                                                    <span>Add another location</span>
                                                </div>
                                            </div>
                                        </div>
                                    </ng-container>

                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['carpetArea'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.carpetArea ? 'field-label-req' : 'field-label'">
                                                {{'LEADS.carpet-area'
                                                | translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('carpetArea')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="align-center mr-20 ph-mr-0">
                                            <div class="w-50">
                                                <div class="mr-20 ph-mr-0">
                                                    <form-errors-wrapper label="{{'PROJECTS.carpet-size' | translate}}">
                                                        <input type="number" min="0" placeholder="ex. 1906">
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                            <div class="w-50">
                                                <form-errors-wrapper
                                                    label="{{'PROJECTS.carpet-area-unit' | translate}}">
                                                    <ng-select [virtualScroll]="true" [items]="areaSizeUnits"
                                                        ResizableDropdown placeholder="ex. sq. feet." class="bg-white"
                                                        bindValue="id" bindLabel="unit"
                                                        [items]="areaSizeUnits"></ng-select>
                                                </form-errors-wrapper>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['buildUpArea'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.buildUpArea ? 'field-label-req' : 'field-label'">
                                                Build Up
                                                Area</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('buildUpArea')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="align-center mr-20 ph-mr-0">
                                            <div class="w-50">
                                                <div class="mr-20 ph-mr-0">
                                                    <form-errors-wrapper label="build Up Area">
                                                        <input type="number" min="0" placeholder="ex. 1906">
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                            <div class="w-50">
                                                <form-errors-wrapper
                                                    label="{{'PROJECTS.carpet-area-unit' | translate}}">
                                                    <ng-select [virtualScroll]="true" [items]="areaSizeUnits"
                                                        class="bg-white" placeholder="ex. sq. feet." bindValue="id"
                                                        bindLabel="unit" [items]="areaSizeUnits"></ng-select>
                                                </form-errors-wrapper>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['saleableArea'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.saleableArea ? 'field-label-req' : 'field-label'">
                                                Saleable
                                                Area</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('saleableArea')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="align-center mr-20 ph-mr-0">
                                            <div class="w-50">
                                                <div class="mr-20 ph-mr-0">
                                                    <form-errors-wrapper label="saleable Area Unit ">
                                                        <input type="number" min="0" placeholder="ex. 1906">
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                            <div class="w-50">
                                                <form-errors-wrapper
                                                    label="{{'PROJECTS.carpet-area-unit' | translate}}">
                                                    <ng-select [virtualScroll]="true" [items]="areaSizeUnits"
                                                        class="bg-white" placeholder="ex. sq. feet." bindValue="id"
                                                        bindLabel="unit" [items]="areaSizeUnits"></ng-select>
                                                </form-errors-wrapper>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['possession'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.possession ? 'field-label-req' : 'field-label'">
                                                {{'PROPERTY.possession-availability'
                                                | translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('possession')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <span>
                                                <span
                                                    class="icon ic-calendar ic-sm ic-coal position-absolute right-20 top-12 cursor-pointer"
                                                    [owlDateTimeTrigger]="dt1"></span>
                                                <input type="text" [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1"
                                                    readonly bsDatepicker placeholder="ex. 19/06/2025"
                                                    id="inpPossession" data-automate-id="inpPossession">
                                                <owl-date-time [pickerType]="'calendar'" #dt1></owl-date-time>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['preferredFloor'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.preferredFloor ? 'field-label-req' : 'field-label'">
                                                Preferred
                                                Floor</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('preferredFloor')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" [items]="projectList"
                                                [closeOnSelect]="true" name="preferredFloor" class="bg-white"
                                                placeholder="{{ 'GLOBAL.select' | translate }} Preferred Floor">
                                            </ng-select>

                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['project'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.project ? 'field-label-req' : 'field-label'">
                                                {{'SIDEBAR.project' | translate }}(s)</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('project')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <ng-select [virtualScroll]="true" [items]="projectList" [multiple]="true" appSelectAll
                                            [closeOnSelect]="false" ResizableDropdown [addTag]="true"
                                            bindLabel="project" bindValue="project" name="projectsList"
                                            addTagText="Create New Project" class="bg-white mr-20 ph-mr-0"
                                            placeholder="{{ 'GLOBAL.select' | translate }}/Create {{ 'SIDEBAR.project' | translate }}">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <div class="checkbox-container"><input type="checkbox"
                                                        id="item-{{index}}" data-automate-id="item-{{index}}"
                                                        [checked]="item$.selected"><span
                                                        class="checkmark"></span>{{item.project || item}}
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['property'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.property ? 'field-label-req' : 'field-label'">
                                                {{ 'LABEL.property' | translate}}(s)</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('property')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <ng-select [virtualScroll]="true" [items]="propertyList" [multiple]="true" appSelectAll
                                            [closeOnSelect]="false" ResizableDropdown [addTag]="true"
                                            bindLabel="property" bindValue="property" name="propertiesList"
                                            addTagText="Create New Property"
                                            placeholder="{{ 'GLOBAL.select' | translate }}/Create Property"
                                            class="bg-white mr-20 ph-mr-0">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <div class="checkbox-container"><input type="checkbox"
                                                        id="item-{{index}}" data-automate-id="item-{{index}}"
                                                        [checked]="item$.selected"><span
                                                        class="checkmark"></span>{{item.property || item}}
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['agencyName'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.agencyName ? 'field-label-req' : 'field-label'">
                                                {{'INTEGRATION.agency-name'
                                                | translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('agencyName')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <ng-select [virtualScroll]="true" [items]="agencyNameList" [multiple]="true" appSelectAll
                                            [closeOnSelect]="false" ResizableDropdown [addTag]="true" bindLabel="name"
                                            bindValue="name" name="name" addTagText="Create New Agency Name"
                                            class="bg-white mr-20 ph-mr-0" placeholder="Select/Create Agency Name">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <div class="checkbox-container"><input type="checkbox"
                                                        id="item-{{index}}" data-automate-id="item-{{index}}"
                                                        [checked]="item$.selected"><span
                                                        class="checkmark"></span>{{item}}
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['channelPartnerName'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.channelPartnerName ? 'field-label-req' : 'field-label'">
                                                {{'LEAD_FORM.channel-partner-name'
                                                | translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('channelPartnerName')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" [items]="channelPartnerList"
                                                [multiple]="true" appSelectAll [closeOnSelect]="false" [addTag]="true"
                                                addTagText="Create New Channel Partner"
                                                placeholder="{{ 'GLOBAL.select' | translate }}/Create Channel Partner"
                                                class="bg-white">
                                                <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                    let-index="index">
                                                    <div class="checkbox-container"><input type="checkbox"
                                                            id="item-{{index}}" data-automate-id="item-{{index}}"
                                                            [checked]="item$.selected"><span
                                                            class="checkmark"></span>{{item}}
                                                    </div>
                                                </ng-template>
                                            </ng-select>
                                        </div>
                                    </div>

                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['campaignName'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.campaignName ? 'field-label-req' : 'field-label'">
                                                Campaign
                                                Name</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('campaignName')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" [items]="channelPartnerList"
                                                [multiple]="true" appSelectAll [closeOnSelect]="false" [addTag]="true"
                                                addTagText="Create New  Campaign Name"
                                                placeholder="{{ 'GLOBAL.select' | translate }}/Create Campaign Name"
                                                class="bg-white">
                                                <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                    let-index="index">
                                                    <div class="checkbox-container"><input type="checkbox"
                                                            id="item-{{index}}" data-automate-id="item-{{index}}"
                                                            [checked]="item$.selected"><span
                                                            class="checkmark"></span>{{item}}
                                                    </div>
                                                </ng-template>
                                            </ng-select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white position-relative mt-12" #AdditionalInfo
                                *ngIf="isHideAdditionalFieldInfo">
                                <div class="position-absolute ntop-10" id="AdditionalInfo"></div>

                                <div class="cursor-pointer align-center py-12 px-20"
                                    [ngClass]="!additionalInfo ? 'border-bottom': ''"
                                    (click)="additionalInfo = !additionalInfo">
                                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                                        [ngClass]="{'rotate-270' : additionalInfo}"></span>
                                    <h6 class="text-black-200">Additional Info</h6>
                                </div>
                                <div class="d-flex flex-wrap  pl-20 pb-16" *ngIf="!additionalInfo">
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['gender'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div [ngClass]="isLabelClicked?.gender ? 'field-label-req' : 'field-label'">
                                                Gender</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('gender')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Select Gender">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['dateofBirth'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.dateofBirth ? 'field-label-req' : 'field-label'">
                                                Date
                                                Of Birth</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('dateofBirth')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <span
                                                class="icon ic-calendar ic-sm ic-coal position-absolute right-20 top-12 cursor-pointer"
                                                [owlDateTimeTrigger]="dt1"></span>
                                            <input type="text" [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1" readonly
                                                bsDatepicker placeholder="ex. 19/06/2025" id="inpDateOfBirth"
                                                data-automate-id="inpDateOfBirth">
                                            <owl-date-time [pickerType]="'calendar'" #dt1></owl-date-time>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['fatherName'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.fatherName ? 'field-label-req' : 'field-label'">
                                                Father Name</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('fatherName')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Add Father's name">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['motherName'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.motherName ? 'field-label-req' : 'field-label'">
                                                Mother Name</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('motherName')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Add Mother's name">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['religion'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.religion ? 'field-label-req' : 'field-label'">
                                                Religion</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('religion')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Select Religion">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['age'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div [ngClass]="isLabelClicked?.age ? 'field-label-req' : 'field-label'">Age
                                            </div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('age')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="number" placeholder="ex. 25">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['panNumber'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.panNumber ? 'field-label-req' : 'field-label'">
                                                PAN
                                                Number</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('panNumber')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="number" placeholder="ex. 25">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['aadharNumber'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.aadharNumber ? 'field-label-req' : 'field-label'">
                                                Aadhar
                                                Number</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('aadharNumber')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="number" placeholder="ex. 122454255566">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['voterID'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.voterID ? 'field-label-req' : 'field-label'">
                                                Voter
                                                ID</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('voterID')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="number" placeholder="ex. 12wq566">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['drivingLicense'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.drivingLicense ? 'field-label-req' : 'field-label'">
                                                Driving
                                                License</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('drivingLicense')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. 12wq566">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['profession'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.profession ? 'field-label-req' : 'field-label'">
                                                {{'LEADS.profession'
                                                | translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('profession')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" [items]="profession"
                                                [closeOnSelect]="true" ResizableDropdown class="bg-white"
                                                placeholder="Select Profession">
                                            </ng-select>
                                        </div>
                                    </div>
                                    <div [ngClass]="isShowManualCustomerLocation == false ? 'w-33 ip-w-50 ph-w-100': 'w-100'"
                                        *ngIf="customForm.controls['customerLocation'].value">
                                        <ng-container
                                            *ngIf="isShowManualCustomerLocation == false else manualCustomerLocation">
                                            <div class="w-100">
                                                <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                    <div
                                                        [ngClass]="isLabelClicked?.customerLocation ? 'field-label-req' : 'field-label'">
                                                        {{'LEADS.customer-location'
                                                        | translate}}</div>
                                                    <div class="position-relative  visiblity-req d-none">:
                                                        <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                            (click)="toggleLabel('customerLocation')">Mark as mandatory
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="field-tag mr-20 ph-mr-0">
                                                    <ng-select [virtualScroll]="true" ResizableDropdown
                                                        (search)="searchPlaceTerm$.next($event.term)"
                                                        [editableSearchTerm]="true" placeholder="Select Location"
                                                        class="bg-white" [items]="placesList" bindLabel="location">
                                                        <ng-template ng-option-tmp let-item="item">
                                                            <div title="{{item?.location}}">{{item?.location}}</div>
                                                        </ng-template>
                                                    </ng-select>
                                                    <div class="search icon ic-search ic-sm ic-coal"></div>
                                                </div>
                                            </div>
                                        </ng-container>
                                        <ng-template #manualCustomerLocation>
                                            <div class="d-flex flex-wrap w-100">

                                                <div class="w-33 ip-w-50 ph-w-100">
                                                    <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                        <div
                                                            [ngClass]="isLabelClicked ? 'field-label-req' : 'field-label'">
                                                            Customer
                                                            Locality</div>
                                                        <div class="position-relative  visiblity-req d-none">:
                                                            <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                                (click)="toggleLabel()">Mark as mandatory</div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group mr-20 ph-mr-0">
                                                        <input type="text" placeholder="ex. HSR Layout">
                                                    </div>
                                                </div>
                                                <div class="w-33 ip-w-50 ph-w-100">
                                                    <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                        <div
                                                            [ngClass]="isLabelClicked ? 'field-label-req' : 'field-label'">
                                                            Customer
                                                            City</div>
                                                        <div class="position-relative  visiblity-req d-none">:
                                                            <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                                (click)="toggleLabel()">Mark as mandatory</div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group mr-20 ph-mr-0">
                                                        <input type="text" placeholder="ex. Bengaluru">
                                                    </div>
                                                </div>
                                                <div class="w-33 ip-w-50 ph-w-100">
                                                    <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                        <div
                                                            [ngClass]="isLabelClicked ? 'field-label-req' : 'field-label'">
                                                            Customer
                                                            State</div>
                                                        <div class="position-relative  visiblity-req d-none">:
                                                            <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                                (click)="toggleLabel()">Mark as mandatory</div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group mr-20 ph-mr-0">
                                                        <input type="text" placeholder="ex. Karnataka">
                                                    </div>
                                                </div>
                                            </div>
                                        </ng-template>
                                        <div class="d-flex">
                                            <div class="cursor-pointer align-center fw-semi-bold text-accent-green mt-4"
                                                (click)="isShowManualCustomerLocation = !isShowManualCustomerLocation">
                                                <span class="icon ic-xs ic-accent-green"
                                                    [ngClass]="isShowManualCustomerLocation ? 'ic-search' : 'ic-add'"></span>
                                                <span>{{isShowManualCustomerLocation ? 'Location List' :
                                                    'Manually Enter Location'}}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['industry'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.industry ? 'field-label-req' : 'field-label'">
                                                Industry</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('industry')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" [items]="profession"
                                                [closeOnSelect]="true" placeholder="Select Industry" class="bg-white">
                                            </ng-select>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['companyName'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.companyName ? 'field-label-req' : 'field-label'">
                                                {{'AUTH.company-name'
                                                | translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('companyName')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" id="inpLeadCompanyName"
                                                data-automate-id="inpLeadCompanyName" placeholder="ex. ABC Company">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['designation'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.designation ? 'field-label-req' : 'field-label'">
                                                {{'USER_MANAGEMENT.designation'
                                                | translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('designation')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group  mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Software Developer">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['annualIncome'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">

                                            <div
                                                [ngClass]="isLabelClicked?.annualIncome ? 'field-label-req' : 'field-label'">
                                                Annual
                                                Income</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('annualIncome')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" [items]="profession"
                                                [closeOnSelect]="true" placeholder="Select Annual Income"
                                                class="bg-white">
                                            </ng-select>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['workLocation'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">

                                            <div
                                                [ngClass]="isLabelClicked?.workLocation ? 'field-label-req' : 'field-label'">
                                                Work
                                                Location</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('workLocation')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="field-tag form-group mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" ResizableDropdown
                                                (search)="searchPlaceTerm$.next($event.term)"
                                                [editableSearchTerm]="true" placeholder="Select Location"
                                                class="bg-white" [items]="placesList" bindLabel="location">
                                                <ng-template ng-option-tmp let-item="item">
                                                    <div title="{{item?.location}}">{{item?.location}}</div>
                                                </ng-template>
                                            </ng-select>
                                            <div class="search icon ic-search ic-sm ic-coal"></div>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['foodPreference'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">

                                            <div
                                                [ngClass]="isLabelClicked?.foodPreference ? 'field-label-req' : 'field-label'">
                                                Food
                                                Preference</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('foodPreference')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group  mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Non Veg">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['purposeofPurchase'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">

                                            <div
                                                [ngClass]="isLabelClicked?.purposeofPurchase ? 'field-label-req' : 'field-label'">
                                                Food
                                                Purchase</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('purposeofPurchase')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group  mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. abc">
                                        </div>
                                    </div>

                                    <div class="align-center w-33 ip-w-50 ph-w-100 qr-code"
                                        *ngIf="customForm.controls['securityDeposit'].value">
                                        <div class="field-rupees-tag w-100 mr-20 ph-mr-0">
                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                <div
                                                    [ngClass]="isLabelClicked?.securityDeposit ? 'field-label-req' : 'field-label'">
                                                    Security
                                                    Deposite</div>
                                                <div class="position-relative  visiblity-req d-none">:
                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                        (click)="toggleLabel('securityDeposit')">Mark as mandatory</div>
                                                </div>
                                            </div>
                                            <div class="align-center w-100">
                                                <div class="position-relative w-100 budget-dropdown">
                                                    <form-errors-wrapper label="Security Deposit">
                                                        <input type="number" min="1" id="inpsecurityDeposit"
                                                            data-automate-id="inpsecurityDeposit"
                                                            placeholder="ex. 4000000" maxlength="10"
                                                            (keydown)="onlyNumbers($event)">
                                                        <div class="no-validation">
                                                            <ng-container
                                                                *ngIf="currency?.length > 1; else showCurrencySymbol">
                                                                <ng-select formControlName="currency"
                                                                    class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                                                                    <ng-option *ngFor="let curr of currency"
                                                                        [value]="curr.currency">
                                                                        <span [title]="curr.currency">
                                                                            {{curr.currency}}
                                                                        </span>
                                                                    </ng-option>
                                                                </ng-select>
                                                            </ng-container>
                                                        </div>
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100 qr-code"
                                        *ngIf="customForm.controls['maintenanceCost'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">

                                            <div
                                                [ngClass]="isLabelClicked?.maintenanceCost ? 'field-label-req' : 'field-label'">
                                                Maintenance
                                                Cost</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('maintenanceCost')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="field-rupees-tag w-100">
                                            <div class="align-center w-100">
                                                <div class="position-relative w-100 budget-dropdown mr-20 ph-mr-0">
                                                    <form-errors-wrapper label="Maintenance Cost">
                                                        <input type="number" min="1" id="inpmaintenanceCost"
                                                            data-automate-id="inpmaintenanceCost"
                                                            placeholder="ex. 4000000" maxlength="10"
                                                            (keydown)="onlyNumbers($event)">
                                                        <div class="no-validation">
                                                            <ng-container
                                                                *ngIf="currency?.length > 1; else showCurrencySymbol">
                                                                <ng-select
                                                                    class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                                                                    <ng-option *ngFor="let curr of currency"
                                                                        [value]="curr.currency">
                                                                        <span [title]="curr.currency">
                                                                            {{curr.currency}}
                                                                        </span>
                                                                    </ng-option>
                                                                </ng-select>
                                                            </ng-container>
                                                        </div>
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="align-center w-33 ip-w-50 ph-w-100 qr-code"
                                        *ngIf="customForm.controls['downPayment'].value">
                                        <div class="field-rupees-tag w-100 mr-20 ph-mr-0">
                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                <div
                                                    [ngClass]="isLabelClicked?.downPayment ? 'field-label-req' : 'field-label'">
                                                    Down
                                                    Payment
                                                </div>
                                                <div class="position-relative  visiblity-req d-none">:
                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                        (click)="toggleLabel('downPayment')">Mark as mandatory</div>
                                                </div>
                                            </div>
                                            <div class="align-center w-100">
                                                <div class="position-relative w-100 budget-dropdown">
                                                    <form-errors-wrapper label="Down Payment">
                                                        <input type="number" min="1" id="inpdownPayment"
                                                            data-automate-id="inpdownPayment" placeholder="ex. 4000000"
                                                            maxlength="10" (keydown)="onlyNumbers($event)">
                                                        <div class="no-validation">
                                                            <ng-container
                                                                *ngIf="currency?.length > 1; else showCurrencySymbol">
                                                                <ng-select formControlName="currency"
                                                                    class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                                                                    <ng-option *ngFor="let curr of currency"
                                                                        [value]="curr.currency">
                                                                        <span [title]="curr.currency">
                                                                            {{curr.currency}}
                                                                        </span>
                                                                    </ng-option>
                                                                </ng-select>
                                                            </ng-container>
                                                        </div>
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['modeofPayment'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.modeofPayment ? 'field-label-req' : 'field-label'">
                                                Mode
                                                of
                                                Payment</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('modeofPayment')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group  mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" [closeOnSelect]="true" ResizableDropdown
                                                placeholder="select" [items]="PaymentModeList" class="bg-white">
                                            </ng-select>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['facing'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div [ngClass]="isLabelClicked?.facing ? 'field-label-req' : 'field-label'">
                                                Facing</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('facing')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Select">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['noofParking'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.noofParking ? 'field-label-req' : 'field-label'">
                                                Number
                                                of
                                                Parking</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('noofParking')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" id="inpParking" data-automate-id="inpParking"
                                                placeholder="ex. 2">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['currentAddress'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.currentAddress ? 'field-label-req' : 'field-label'">
                                                Current
                                                Address
                                            </div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('currentAddress')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="field-tag form-group mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" ResizableDropdown
                                                (search)="searchPlaceTerm$.next($event.term)"
                                                [editableSearchTerm]="true" placeholder="Select Location"
                                                class="bg-white" [items]="placesList" bindLabel="location">
                                                <ng-template ng-option-tmp let-item="item">
                                                    <div title="{{item?.location}}">{{item?.location}}</div>
                                                </ng-template>
                                            </ng-select>
                                            <div class="search icon ic-search ic-sm ic-coal"></div>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['currentAddressType'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.currentAddressType ? 'field-label-req' : 'field-label'">
                                                Current
                                                Address
                                                Type</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('currentAddressType')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Select Current Address Type">
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="bg-white position-relative mt-12" #MaritalStatus *ngIf="isHideMaritalFieldInfo">
                                <div class="position-absolute ntop-10" id="MaritalStatus"></div>
                                <div class="cursor-pointer align-center py-12 px-20"
                                    [ngClass]="!maritalFieldInfo ? 'border-bottom': ''"
                                    (click)="maritalFieldInfo = !maritalFieldInfo">
                                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                                        [ngClass]="{'rotate-270' : maritalFieldInfo}"></span>
                                    <h6 class="text-black-200">Marital Info</h6>
                                </div>
                                <div class="d-flex flex-wrap pl-20 pb-16" *ngIf="!maritalFieldInfo">
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['spouseName'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.spouseName ? 'field-label-req' : 'field-label'">
                                                Spouse
                                                Name</div>
                                            <div class="position-relative visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('spouseName')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" id="inpParking" data-automate-id="inpParking"
                                                placeholder="ex. 2">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['anniversaryDate'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.anniversaryDate ? 'field-label-req' : 'field-label'">
                                                Anniversary
                                                Date</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('anniversaryDate')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <span
                                                class="icon ic-calendar ic-sm ic-coal position-absolute right-20 top-12 cursor-pointer"
                                                [owlDateTimeTrigger]="dt1"></span>
                                            <input type="text" [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1" readonly
                                                bsDatepicker placeholder="ex. 19/06/2025" id="inpAnniversaryDate"
                                                data-automate-id="inpAnniversaryDate" formControlName="anniversaryDate">
                                            <owl-date-time [pickerType]="'calendar'" #dt1></owl-date-time>
                                        </div>
                                    </div>
                                    <div class="position-relative w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['spousePhoneNo'].value">
                                        <div class="mr-20 ph-mr-0">

                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                <div
                                                    [ngClass]="isLabelClicked?.spousePhoneNo ? 'field-label-req' : 'field-label'">
                                                    {{'INTEGRATION.primary'
                                                    | translate}}
                                                    {{'GLOBAL.number' | translate}}</div>
                                                <div class="position-relative  visiblity-req d-none">:
                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                        (click)="toggleLabel('spousePhoneNo')">Mark as mandatory</div>
                                                </div>
                                            </div>
                                            <form-errors-wrapper label="{{'USER.phone-number' | translate}}">
                                                <div *ngIf="!resetNumber">
                                                    <ngx-mat-intl-tel-input #contactNoInput
                                                        *ngIf="hasInternationalSupport"
                                                        [preferredCountries]="preferredCountries"
                                                        [enablePlaceholder]="true" [enableSearch]="true"
                                                        class="no-validation spousePhoneNoInput">
                                                    </ngx-mat-intl-tel-input>
                                                    <ngx-mat-intl-tel-input #contactNoInput
                                                        *ngIf="!hasInternationalSupport"
                                                        [preferredCountries]="preferredCountries"
                                                        [onlyCountries]="preferredCountries" [enablePlaceholder]="true"
                                                        [enableSearch]="true" class="no-validation spousePhoneNoInput">
                                                    </ngx-mat-intl-tel-input>
                                                </div>
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['spousealtPhoneNo'].value">
                                        <div class="mr-20 ph-mr-0">
                                            <div class="flex-between mr-30 ph-mr-10 label-hover">
                                                <div
                                                    [ngClass]="isLabelClicked?.spousealtPhoneNo ? 'field-label-req' : 'field-label'">
                                                    {{'GLOBAL.alternate'
                                                    | translate}}
                                                    {{'GLOBAL.number' | translate}}</div>
                                                <div class="position-relative  visiblity-req d-none">:
                                                    <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                        (click)="toggleLabel('spousealtPhoneNo')">Mark as mandatory
                                                    </div>
                                                </div>
                                            </div>
                                            <form-errors-wrapper
                                                label="{{'GLOBAL.alternate' | translate}} {{'GLOBAL.number' | translate}}">
                                                <div *ngIf="!resetNumber">
                                                    <ngx-mat-intl-tel-input #alternateSpousePhoneNoInput
                                                        *ngIf="hasInternationalSupport"
                                                        [preferredCountries]="preferredCountries"
                                                        [enablePlaceholder]="true" [enableSearch]="true"
                                                        class="no-validation alternateSpousePhoneNoInput">
                                                    </ngx-mat-intl-tel-input>
                                                    <ngx-mat-intl-tel-input #alternateSpousePhoneNoInput
                                                        *ngIf="!hasInternationalSupport"
                                                        [preferredCountries]="preferredCountries"
                                                        [onlyCountries]="preferredCountries" [enablePlaceholder]="true"
                                                        [enableSearch]="true"
                                                        class="no-validation alternateSpousePhoneNoInput">
                                                    </ngx-mat-intl-tel-input>
                                                </div>
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['spouseDOB'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.spouseDOB ? 'field-label-req' : 'field-label'">
                                                Spousal
                                                Date Of
                                                Birth</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('spouseDOB')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <span
                                                class="icon ic-calendar ic-sm ic-coal position-absolute right-20 top-12 cursor-pointer"
                                                [owlDateTimeTrigger]="dt1"></span>
                                            <input type="text" [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1" readonly
                                                bsDatepicker placeholder="ex. 19/06/2025" id="inpSpouseDateOfBirth"
                                                data-automate-id="inpSpouseDateOfBirth"
                                                formControlName="spouseDateOfBirth">
                                            <owl-date-time [pickerType]="'calendar'" #dt1></owl-date-time>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['noofChildren'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.noofChildren ? 'field-label-req' : 'field-label'">
                                                Number
                                                of
                                                Children</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('noofChildren')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="number" id="inpNoOfChildren" data-automate-id="inpNoOfChildren"
                                                placeholder="ex. 2">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['spouseProfession'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.spouseProfession ? 'field-label-req' : 'field-label'">
                                                Spouse
                                                {{'LEADS.profession' | translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('spouseProfession')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Select Spouse Profession">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['spouseCompanyName'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.spouseCompanyName ? 'field-label-req' : 'field-label'">
                                                {{'AUTH.company-name'
                                                | translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('spouseCompanyName')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" id="inpSpouseCompanyName"
                                                data-automate-id="inpSpouseCompanyName" placeholder="ex. ABC Company">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['spouseDesignation'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.spouseDesignation ? 'field-label-req' : 'field-label'">
                                                Designation</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('spouseDesignation')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" id="inpSpouseDesignation"
                                                data-automate-id="inpSpouseDesignation"
                                                placeholder="ex. Software Developer">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['spouseAnnualIncome'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.spouseAnnualIncome ? 'field-label-req' : 'field-label'">
                                                Spouse
                                                Annual Income</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('spouseAnnualIncome')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="number" placeholder="ex. Select Spouse Annual Income">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['spouseWorkLocation'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.spouseWorkLocation ? 'field-label-req' : 'field-label'">
                                                Office
                                                Locality</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('spouseWorkLocation')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="field-tag form-group mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" ResizableDropdown
                                                (search)="searchPlaceTerm$.next($event.term)"
                                                [editableSearchTerm]="true" placeholder="Select Office Locality"
                                                class="bg-white" [items]="placesList" bindLabel="location">
                                                <ng-template ng-option-tmp let-item="item">
                                                    <div title="{{item?.location}}">{{item?.location}}</div>
                                                </ng-template>
                                            </ng-select>
                                            <div class="search icon ic-search ic-sm ic-coal"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white position-relative mt-12" #NRI *ngIf="isHideNRIFieldInfo">
                                <div class="position-absolute ntop-10" id="NRI"></div>

                                <div class="cursor-pointer align-center py-12 px-20"
                                    [ngClass]="!NRIFieldInfo ? 'border-bottom': ''"
                                    (click)="NRIFieldInfo = !NRIFieldInfo">
                                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                                        [ngClass]="{'rotate-270' : NRIFieldInfo}"></span>
                                    <h6 class="text-black-200">NRI</h6>
                                </div>
                                <div class="d-flex flex-wrap pl-20 pb-16">
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['nriState'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.nriState ? 'field-label-req' : 'field-label'">
                                                State</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('nriState')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Karnataka">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['nriCity'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.nriCity ? 'field-label-req' : 'field-label'">
                                                City</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('nriCity')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Bengaluru">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['nriPinCode'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.nriPinCode ? 'field-label-req' : 'field-label'">
                                                Pin
                                                Code</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('nriPinCode')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Bengaluru">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['nriLocality'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.nriLocality ? 'field-label-req' : 'field-label'">
                                                Locality</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('nriLocality')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0 mt-0">
                                            <input type="text" placeholder="ex. HSR Layout">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['passportType'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.passportType ? 'field-label-req' : 'field-label'">
                                                Passport
                                                Type</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('passportType')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="number" placeholder="ex. Select Passport Type">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['visaStatus'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.visaStatus ? 'field-label-req' : 'field-label'">
                                                Visa
                                                Status</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('visaStatus')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Select Visa Status">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white position-relative mt-12" #Others *ngIf="isHideOtherFieldInfo">
                                <div class="position-absolute ntop-10" id="Others"></div>
                                <div class="cursor-pointer align-center py-12 px-20"
                                    [ngClass]="!otherFieldInfo ? 'border-bottom': ''"
                                    (click)="otherFieldInfo = !otherFieldInfo">
                                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                                        [ngClass]="{'rotate-270' : otherFieldInfo}"></span>
                                    <h6 class="text-black-200">Others</h6>
                                </div>
                                <div class="d-flex flex-wrap pl-20 pb-16" *ngIf="!otherFieldInfo">
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['sourcingManager'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.sourcingManager ? 'field-label-req' : 'field-label'">
                                                {{'LEADS.sourcing-manager'
                                                | translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('sourcingManager')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" [items]="allUsers" ResizableDropdown
                                                placeholder="Select Sourcing Manager" class="bg-white"
                                                bindLabel="fullName" bindValue="id"></ng-select>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['closingManager'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.closingManager ? 'field-label-req' : 'field-label'">
                                                {{'LEADS.closing-manager'
                                                | translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('closingManager')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <ng-select [virtualScroll]="true" [items]="allUsers" ResizableDropdown
                                                placeholder="Select Closing Manager" class="bg-white"
                                                bindLabel="fullName" bindValue="id"></ng-select>
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100"
                                        *ngIf="customForm.controls['channelPartner'].value">

                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.channelPartner ? 'field-label-req' : 'field-label'">
                                                {{'LEAD_FORM.channel-partner-name'
                                                |
                                                translate}}</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('channelPartner')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <ng-select [virtualScroll]="true" [items]="channelPartnerList" [multiple]="true" appSelectAll
                                            [closeOnSelect]="false" ResizableDropdown [addTag]="true"
                                            addTagText="Create New Channel Partner"
                                            placeholder="{{ 'GLOBAL.select' | translate }}/Create Channel Partner"
                                            class="bg-white mr-20 ph-mr-0">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <div class="checkbox-container"><input type="checkbox"
                                                        id="item-{{index}}" data-automate-id="item-{{index}}"
                                                        [checked]="item$.selected"><span
                                                        class="checkmark"></span>{{item}}
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                    </div>

                                    <div class="w-33 ip-w-50 ph-w-100" *ngIf="customForm.controls['teamHead'].value">
                                        <div class="flex-between mr-30 ph-mr-10 label-hover">
                                            <div
                                                [ngClass]="isLabelClicked?.teamHead ? 'field-label-req' : 'field-label'">
                                                Team
                                                Head</div>
                                            <div class="position-relative  visiblity-req d-none">:
                                                <div class="position-absolute border br-4 p-6 right-0 z-index-2 text-nowrap bg-white cursor-pointer text-red-350"
                                                    (click)="toggleLabel('teamHead')">Mark as mandatory</div>
                                            </div>
                                        </div>
                                        <div class="form-group mr-20 ph-mr-0">
                                            <input type="text" placeholder="ex. Select Visa Status">
                                        </div>
                                    </div>
                                    <div class="w-33 ip-w-50 ph-w-100">
                                        <div [ngClass]="isNotesMandatory ? 'field-label-req' : 'field-label'">
                                            {{'TASK.notes'|
                                            translate}}</div>
                                        <div class="mr-20 ph-mr-0">
                                            <form-errors-wrapper label="{{'TASK.notes' | translate}}">
                                                <textarea rows="4" id="txtLeadNotes" data-automate-id="txtLeadNotes"
                                                    placeholder="ex. I want to say .... "></textarea>
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="flex-end px-20 py-12 bg-white">
            <button class="btn-gray">{{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal ml-20" (click)="saveData()">{{
                'BUTTONS.save' | translate }}</button>
        </div>
    </div>
</div>

<!-- <ng-template #ratLoader>
        <div class="flex-end h-20px mt-20 mr-20">
            <img src="assets/images/loader-rat.svg" class="rat-loader h-20px w-20px" alt="loader">
        </div>
    </ng-template> -->