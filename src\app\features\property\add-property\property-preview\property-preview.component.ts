import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';
import { ATTRIBUTES_MAP } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { PropertyDimension } from 'src/app/core/interfaces/property.interface';
import {
  formatCurrency,
  getAreaUnit,
  getAWSImagePath,
  getBHKDisplayString,
  getBRDisplayString,
} from 'src/app/core/utils/common.util';
import { FetchAllAmenities } from 'src/app/reducers/amenities-attributes/amenities-attributes.action';
import { getAllAmenities, getAmenitiesLoading } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';
import {
  getGlobalAnonymousIsLoading,
  getGlobalSettingsAnonymous,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchPropertyAmenityList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';

@Component({
  selector: 'property-preview',
  templateUrl: './property-preview.component.html',
})
export class PropertyPreviewComponent implements OnInit, OnDestroy, OnChanges {
  tick: AnimationOptions = { path: 'assets/animations/circle-tick.json' };
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() liveBasicInfoFormValues: any;
  @Input() livePropInfoFormValues: any;
  @Input() liveAttrFormValues: any;
  @Input() userSelectedAmenities: Array<string> = [];
  @Input() propInfo: any;
  @Input() imgPath: any;

  formValues: any;
  propFormValues: any;
  attrValues: any;
  currentDate = moment(new Date().toISOString());
  areaSizeUnits: Array<PropertyDimension> = [];
  attrMap: any[] = ATTRIBUTES_MAP;
  moment = moment;
  formatCurrency = formatCurrency;
  getBHKDisplayString = getBHKDisplayString;
  getBRDisplayString = getBRDisplayString;
  getAWSImagePath = getAWSImagePath;
  amenitiesArr: any;
  selectedAmenities: any;
  filteredSelectedAmenitiesData: any;
  isFilteredSelectedAmenitiesDataLoading: boolean;
  currencySymbol: string = 'INR';
  isGlobalSettingsLoading: boolean = true;
  isPropertySoldOut: boolean = false;
  loading: AnimationOptions = {
    path: 'assets/animations/loading-text-animation.json',
  };
  isListing: boolean = window.location.pathname.includes('listing');
  constructor(private store: Store<AppState>) {
    this.store
      .select(getGlobalAnonymousIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isGlobalSettingsLoading = isLoading;
      });
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.shouldEnablePropertyListing) {
          this.store.dispatch(new FetchPropertyAmenityList('listing'));
        } else {
          this.store.dispatch(new FetchPropertyAmenityList());
        }
        this.currencySymbol =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
      });

    this.store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });;

    this.store.dispatch(new FetchAllAmenities());
    // this.store.dispatch(new FetchAllAttributes());

    this.store
      .select(getAmenitiesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isFilteredSelectedAmenitiesDataLoading = isLoading;
      })

    this.store
      .select(getAllAmenities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.amenitiesArr = data
      })
  }

  ngOnChanges(changes: SimpleChanges) {
    this.formValues = changes.liveBasicInfoFormValues?.currentValue;
    this.propFormValues = changes.livePropInfoFormValues?.currentValue;
    this.attrValues = changes.liveAttrFormValues?.currentValue;
    if (this.propInfo) {
      this.isPropertySoldOut = this.propInfo.status === 1 ||
        this.propInfo.status === 'Sold'
    }

    this.ngOnInit();
    this.selectedAmenities = changes.userSelectedAmenities?.currentValue
      ? changes.userSelectedAmenities?.currentValue
      : this.propInfo?.amenities;

    this.filteredSelectedAmenitiesData = this.amenitiesArr
      .flatMap((category: any) => category.amenities)
      .filter((item: any) => this.selectedAmenities?.includes(item?.id) && item.isActive)
      .map((item: any) => ({
        imageURL: item.imageURL,
        amenityDisplayName: item.amenityDisplayName,
      }));

    // this.filteredSelectedAmenitiesData = this.amenitiesArr?.Basic?.concat(
    //   this.amenitiesArr?.Featured,
    //   this.amenitiesArr?.Nearby
    // )
    //   .filter((item: any) => this.selectedAmenities?.includes(item.id))
    //   .map((item: any) => {
    //     return {
    //       imageURL: item.imageURL,
    //       amenityDisplayName: item.amenityDisplayName,
    //     };
    //   });

  }

  ngOnInit() {
    this.formValues = {
      title: this.liveBasicInfoFormValues?.title,
      propertyType: this.liveBasicInfoFormValues?.propertyType,
      propertySubType: this.liveBasicInfoFormValues?.propertySubType,
      noOfBHK: this.liveBasicInfoFormValues?.noOfBHK,
      bhkType: this.liveBasicInfoFormValues?.bhkType,
      enquiredFor: this.liveBasicInfoFormValues?.enquiredFor,
      propertySize: this.liveBasicInfoFormValues?.propertySize,
      highlighted: this.liveBasicInfoFormValues?.isFeatured,
      areaUnit: getAreaUnit(
        this.liveBasicInfoFormValues?.areaUnit,
        this.areaSizeUnits
      )?.unit,
    };

    if (this.liveBasicInfoFormValues) {
      if (this.liveBasicInfoFormValues.status === 1 ||
        this.liveBasicInfoFormValues.status === 'Sold') {
        this.isPropertySoldOut = true;
      }
    }
    this.propFormValues = {
      budget: this.livePropInfoFormValues?.expectedPrice,
      currency: this.livePropInfoFormValues?.currency,
      possessionDate: this.livePropInfoFormValues?.possessionDate,
      negotiable: this.livePropInfoFormValues?.isNegotiable,
      city: this.livePropInfoFormValues?.placeId
        ? this.livePropInfoFormValues?.placeId?.includes(
          this.livePropInfoFormValues?.city
        )
          ? this.livePropInfoFormValues?.placeId
          : `${this.livePropInfoFormValues?.city}, ${this.livePropInfoFormValues?.state}`
        : '',
    };
    this.attrValues = {
      furnishStatus: this.liveAttrFormValues?.furnishStatus,
      facing: this.liveAttrFormValues?.facing,
      floorNumber: this.liveAttrFormValues?.floorNumber,
      numberOfFloors: this.liveAttrFormValues?.numberOfFloors,
      numberOfBalconies: this.liveAttrFormValues?.numberOfBalconies,
      numberOfBathrooms: this.attrValues?.numberOfBathrooms,
      numberOfBedrooms: this.attrValues?.numberOfBedrooms,
      numberOfDrawingOrLivingRooms:
        this.attrValues?.numberOfDrawingOrLivingRooms,
      numberOfKitchens: this.attrValues?.numberOfKitchens,
      numberOfUtilities: this.attrValues?.numberOfUtilities,
    };
  }

  nth(d: any) {
    if (d > 3 && d < 21) return 'th';
    switch (d % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }

  getFirstCharacter(name: string) {
    return name?.charAt(0).toUpperCase();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
