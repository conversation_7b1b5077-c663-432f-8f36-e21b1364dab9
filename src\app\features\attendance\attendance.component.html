<div *ngIf="(canViewAllUsers || canViewReportees)">
    <div class="flex-between bg-white border-bottom px-24 py-8 flex-grow-1 z-index-1021">
        <ul class="align-center top-nav-bar text-nowrap ip-scrollbar scroll-hide">
            <ng-container *ngFor="let visibilityImage of visibilityList;let i=index">
                <div [title]="usersData?.[visibilityImage?.visibility] ? usersData?.[visibilityImage?.visibility] : ''"
                    (click)="currentVisibility(visibilityImage.userStatus)" class="cursor-pointer">
                    <div class="align-center">
                        <a [class.active]="appliedFilter.userStatus == visibilityImage.userStatus"><img
                                [type]="'leadrat'" [appImage]="s3BucketUrl + visibilityImage.image" alt="muso"
                                width="22" height="22"></a>
                        <span [class.active]="appliedFilter.userStatus == visibilityImage.userStatus"
                            class="text-large ml-8 mr-16">{{ visibilityImage.name }}
                        </span>
                    </div>
                </div>
            </ng-container>
        </ul>
        <div class="btn-coal" *ngIf="canAttendanceExportAllUsers || canAttendanceExportReportees"
            id="btnAttendanceTracker" data-automate-id="btnAttendanceTracker" (click)="openAttendanceTracker()">
            <span class="ic-tracker icon ic-xxs"></span>
            <span class="ml-8 ph-d-none">Export Tracker</span>
        </div>
    </div>
    <div class="px-24">
        <div class="d-flex ip-flex-col bg-white w-100 border-gray mt-20">
            <div class="align-center pl-10 border-end ip-br-0 flex-grow-1 no-validation">
                <ng-container>
                    <div class="align-center w-100 py-12">
                        <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4">
                        </span>
                        <input placeholder="Search by User" (keydown)="onSearch($event)" (input)="isEmptyInput($event)"
                            name="search" [(ngModel)]="searchTerm" class="border-0 outline-0 w-100"
                            autocomplete="off" />
                    </div>
                    <small class="text-muted text-nowrap ph-d-none mr-10">
                        ({{ "LEADS.lead-search-prompt" | translate}})</small>
                </ng-container>
                <div *ngIf="canExportAllUsers || canExportReportees" (click)="exportAttendanceReport()"
                    class="bg-accent-green text-white px-20 py-12 h-100 align-center cursor-pointer border-start w-70px tb-br-top">
                    {{ "REPORTS.export" | translate }}
                </div>
                <div class="p-8 flex-center cursor-pointer tb-flex-grow-1 ph-w-40px ph-flex-grow-unset"
                    (click)="openAdvFiltersModal(AdvancedFilters)">
                    <div class="icon ic-filter-solid ic-xxs ic-black"></div>
                    <span class="fw-600 ph-d-none text-nowrap ml-10">{{'PROPERTY.advanced-filters' | translate}}</span>
                </div>
            </div>
            <div class="d-flex ip-br-top">
                <div class="border-end px-10 py-4 align-center ip-flex-grow-1">
                    <span class="border p-10 br-4 cursor-pointer" (click)="previousMonth()">
                        <span class="icon ic-chevron-left ic-xxs ic-coal"></span>
                    </span>
                    <span class="fw-700 text-large text-black-200 px-12 cursor-pointer" [owlDateTimeTrigger]="dt1">
                        {{ currentMonth | date : "MMM" }}, {{ selectedYear }}</span>
                    <input [ngModel]="selectedMonthAndYear" [owlDateTimeTrigger]="dt1" [max]="maxDate"
                        [owlDateTime]="dt1" class="w-0 p-0 border-0 border-remove" />
                    <owl-date-time #dt1 startView="year" [yearOnly]="true" [pickerType]="'calendar'"
                        (afterPickerOpen)="onPickerOpened(currentDate, 'month')"
                        (monthSelected)="monthChanged($event)"></owl-date-time>
                    <span class="border p-10 br-4 cursor-pointer" [class.disabled]="isPresentMonth"
                        (click)="nextMonth()">
                        <span class="icon ic-chevron-right ic-xxs ic-coal"></span>
                    </span>
                </div>
                <div class="show-dropdown-white align-center position-relative">
                    <span class="fw-600 position-absolute left-5 z-index-2">
                        <span class="tb-d-none">{{ "GLOBAL.show" | translate }}</span>
                        {{ "GLOBAL.entries" | translate }}</span>
                    <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" class="w-150 tb-w-120px"
                        [(ngModel)]="selectedPageSize" (change)="assignCount()" [searchable]="false" ResizableDropdown>
                        <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                            {{ pageSize }}</ng-option>
                    </ng-select>
                </div>
            </div>
        </div>
        <div class="bg-white px-4 py-8 tb-w-100-50" [ngClass]="showLeftNav ? 'w-100-190' : 'w-100-90'">
            <ng-container *ngIf="showFilters">
                <div class="bg-secondary flex-between">
                    <drag-scroll class="br-4 scrollbar d-flex scroll-hide w-100">
                        <div class="d-flex" *ngFor="let filter of appliedFilter | keyvalue">
                            <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                                *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                                {{attendanceFiltersKeyLabel[filter.key] || filter.key}}: {{
                                filter.key === 'reportsTo' ? getReportsName(value) :
                                filter.key === 'users' ? getReportsName(value) :
                                filter.key === 'department' ? getDepartmentName(value) :
                                filter.key === 'designation' ? getDesignationName(value) :
                                value }}
                                <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                                    (click)="onRemoveFilter(filter.key, value)"></span>
                            </div>
                        </div>
                    </drag-scroll>
                    <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                        (click)="reset(true);filterAttendanceList();">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' |
                        translate}}
                    </div>
                </div>
            </ng-container>
        </div>
        <ng-template #attendanceData>
            <div class="attendance-grid">
                <ag-grid-angular #agGrid class="ag-theme-alpine" [pagination]="true" [paginationPageSize]="pageSize + 1"
                    [gridOptions]="gridOptions" [rowData]="rowData" [frameworkComponents]="frameworkComponents"
                    [suppressPaginationPanel]="true" [columnDefs]="gridOptions.columnDefs"
                    [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true"
                    (gridReady)="onGridReady($event)">
                </ag-grid-angular>
            </div>
            <div class="flex-between my-20 ip-flex-col ip-flex-start">
                <div class="align-center">
                    <span class="dot dot-xsm bg-pink-700 mr-6 border"></span>
                    <span class="fw-semi-bold text-sm text-black-200 mr-10">
                        {{"USER.unmarked-attendance" | translate}}</span>
                    <span class="dot dot-xsm bg-blue-900 mr-6 border"></span>
                    <span class="fw-semi-bold text-sm text-black-200 mr-10">{{"USER.future-day" | translate}}</span>
                </div>
                <div class="align-center ip-mt-20">
                    <div class="mr-10" *ngIf="totalCount">
                        {{ "GLOBAL.showing" | translate }} {{ currOffset * pageSize + 1 }}
                        {{ "GLOBAL.to-small" | translate }}
                        {{ currOffset * pageSize + rowData?.length }}
                        {{ "GLOBAL.of-small" | translate }} {{ totalCount }}
                        {{ "GLOBAL.entries-small" | translate }}
                    </div>
                    <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]="getPages(totalCount, pageSize)"
                        (pageChange)="onPageChange($event)">
                    </pagination>
                </div>
            </div>
        </ng-template>
        <ng-container *ngIf="!isAttendanceLoading; else loader">
            <ng-container *ngIf="!rowData?.length; else attendanceData">
                <div class="flex-center-col h-100-337">
                    <img src="assets/images/layered-cards.svg" alt="No Data Found">
                    <div class="header-3 fw-600 text-center">{{'PROFILE.no-data-found' | translate }}</div>
                </div>
            </ng-container>
        </ng-container>
    </div>
    <ng-template #loader>
        <application-loader class="flex-center h-100-200"></application-loader>
    </ng-template>
    <ng-template #AdvancedFilters>
        <div class="lead-adv-filter p-30 bg-white brbl-15 brbr-15">
            <div class="adv-filter">
                <div class="d-flex w-100 flex-wrap ng-select-sm">
                    <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="justify-between align-end mr-20">
                            <div class="field-label">{{ "USER.user" | translate }}</div>
                            <label class="checkbox-container mb-4">
                                <input type="checkbox" [(ngModel)]="appliedFilter.withTeam" />
                                <span class="checkmark"></span>{{ "DASHBOARD.with-team" | translate }}
                            </label>
                        </div>
                        <div class="mr-20 ph-mr-0">
                            <ng-select [virtualScroll]="true" [items]="canViewAllUsers ? allUsers : onlyReportees"
                                [multiple]="true" appSelectAll [closeOnSelect]="false" name="user" placeholder="ex. Manasa Pampana"
                                ResizableDropdown [(ngModel)]="appliedFilter.users" bindLabel="fullName" bindValue="id"
                                [ngClass]="{'pe-none blinking': canViewAllUsers ? allUserListIsLoading : isReporteesWithInactiveLoading}">
                                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                    <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                    <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="flex-between">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span><span
                                                class="text-truncate-1 break-all">{{item.firstName}}
                                                {{item.lastName}}</span>
                                        </div>
                                        <span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">{{"USER_MANAGEMENT.reporting-to" | translate}}</div>
                        <div class="mr-20 ph-mr-0">
                            <ng-select [virtualScroll]="true" [items]="usersList" [multiple]="true" appSelectAll
                                [closeOnSelect]="false" name="reportsTo" ResizableDropdown 
                                placeholder="ex. Manasa Pampana" [(ngModel)]="appliedFilter.reportsTo"
                                bindLabel="fullName" bindValue="id"
                                [ngClass]="{'pe-none blinking': allUserListIsLoading}">
                                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                    <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                    <span class="ng-value-label">
                                        {{ item.firstName + " " + item.lastName }}</span>
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="flex-between">

                                        <div class="checkbox-container">
                                            <input type="checkbox" id="item-{{ index }}"
                                                data-automate-id="item-{{ index }}" [checked]="item$.selected" /><span
                                                class="checkmark"></span><span
                                                class="text-truncate-1 break-all">{{item.firstName}}
                                                {{item.lastName}}</span>
                                        </div>
                                        <span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">{{ 'USER_MANAGEMENT.designation' | translate}}</div>
                        <div class="mr-20 ph-mr-0">
                            <ng-select [virtualScroll]="true" [items]="designationList" [multiple]="true" appSelectAll
                                name="designation" [closeOnSelect]="false" ResizableDropdown 
                                placeholder="Select designation" bindLabel="name" bindValue="id"
                                [(ngModel)]="appliedFilter.designation"
                                [ngClass]="{'pe-none blinking': isDesignationLoading}">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.name}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">{{ 'USER_MANAGEMENT.department' | translate}}</div>
                        <div class="mr-20 ph-mr-0">
                            <ng-select [virtualScroll]="true" [items]="departmentList" [multiple]="true" appSelectAll
                                name="department" [closeOnSelect]="false" ResizableDropdown 
                                placeholder="Select department" bindValue="id" bindLabel="name"
                                [(ngModel)]="appliedFilter.department"
                                [ngClass]="{'pe-none blinking': isDepartmentLoading}">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.name}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                </div>
                <div class="flex-end mt-10 tb-mr-20 ph-mr-0">
                    <u class="mr-20 fw-semi-bold text-mud cursor-pointer"
                        (click)="modalService.hide()">{{'BUTTONS.cancel'
                        |translate }}</u>
                    <button class="btn-gray" (click)="reset()">{{ "GLOBAL.reset" | translate }}</button>
                    <button class="btn-coal ml-20" (click)="applyFilter()">{{ "GLOBAL.search" | translate }}</button>
                </div>
            </div>
        </div>
    </ng-template>