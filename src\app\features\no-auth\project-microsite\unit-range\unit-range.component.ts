import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import {
  formatBudget,
  getAreaUnit,
  getBHKDisplayString,
} from 'src/app/core/utils/common.util';
import { ProjectEnquiryFormComponent } from 'src/app/features/no-auth/project-microsite/project-enquiry-form/project-enquiry-form.component';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'unit-range',
  templateUrl: './unit-range.component.html',
})
export class UnitRangeComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Output() sortedUniqueNoOfBHKChange: EventEmitter<number[]> =
    new EventEmitter<number[]>();
  selectedIndex = 0;
  selectedBhkIndex = 0;
  selectedUnitAreaIndex = 0;
  activeTab: 'images' | 'documents' = 'images';
  interval: any;
  @Input() projectUnit: any;
  @Input() areaSizeUnits: any;
  @Input() projectInfo: any;
  getAreaUnit = getAreaUnit;
  sortedUniqueNoOfBHK: any[] = [];
  defaultImagePath: string = '';
  s3BucketUrl = environment.s3ImageBucketURL;
  formatBudget = formatBudget;
  getBHKDisplayString = getBHKDisplayString;
  globalSettingsDetails: any;

  constructor(
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    private store: Store<AppState>,
  ) { }

  ngOnInit(): void {
    this.interval = setInterval(() => {
      this.next();
    }, 10000);
    this.generateUniqueNoOfBHK();
    this.sortedUniqueNoOfBHKChange.emit(this.sortedUniqueNoOfBHK);
    if (this.sortedUniqueNoOfBHK.length > 0) {
      this.selectBhk(0);
    }

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
      });
  }

  selectBhk(index: number): void {
    this.selectedBhkIndex = index;
    const bhkType = this.sortedUniqueNoOfBHK[index];
    if (bhkType === 'Others') {
      this.selectedUnitAreaIndex = this.projectUnit.unitTypes.findIndex(
        (bhk: { noOfBHK: number | null | undefined }) =>
          !bhk.noOfBHK || bhk.noOfBHK === 0
      );
    } else {
      this.selectedUnitAreaIndex = this.projectUnit.unitTypes.findIndex(
        (bhk: { noOfBHK: number }) => bhk.noOfBHK === bhkType
      );
    }
  }

  generateUniqueNoOfBHK() {
    const uniqueNoOfBHK = new Set<number>();
    let hasOthers = false;

    this.projectUnit.unitTypes.forEach(
      (unit: { noOfBHK: number | null | undefined }) => {
        if (unit.noOfBHK && unit.noOfBHK !== 0) {
          uniqueNoOfBHK.add(unit.noOfBHK);
        } else {
          hasOthers = true;
        }
      }
    );
    this.sortedUniqueNoOfBHK = Array.from(uniqueNoOfBHK).sort((a, b) => a - b);
    if (hasOthers) {
      this.sortedUniqueNoOfBHK.push('Others');
    }
  }

  selectUnitArea(index: number): void {
    this.selectedUnitAreaIndex = index;
    if (this.activeTab === 'documents' &&
      (!this.projectUnit?.unitTypes[this.selectedUnitAreaIndex]?.unitInfoGalleries?.length)) {
      this.activeTab = 'images';
    }
  }

  isUnitSoldOut(unitIndex: number): boolean {
    return this.projectUnit?.unitTypes[unitIndex]?.unitTypeStatus === 1;
  }

  next(): void {
    if (
      this.projectUnit?.unitTypes[this.selectedUnitAreaIndex]?.images.length > 1
    ) {
      this.selectedIndex++;
      if (
        this.selectedIndex >=
        this.projectUnit?.unitTypes[this.selectedUnitAreaIndex]?.images.length
      ) {
        this.selectedIndex = 0;
      }
    }
  }

  previous(): void {
    if (
      this.projectUnit?.unitTypes[this.selectedUnitAreaIndex]?.images.length > 1
    ) {
      this.selectedIndex--;
      if (this.selectedIndex < 0) {
        this.selectedIndex =
          this.projectUnit?.unitTypes[this.selectedUnitAreaIndex]?.images
            .length - 1;
      }
    }
  }

  showEnquire(unitId: string) {
    this.modalService.show(ProjectEnquiryFormComponent, {
      initialState: {
        defaultUnitId: unitId,
        projectUnit: this.projectUnit,
        projectInfo: this.projectInfo,
        isShowCloseBtn: true,
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    });
  }

  isIncludes3BucketUrl(url: any) {
    if (url?.includes(this.s3BucketUrl)) {
      return url;
    } else {
      return this.s3BucketUrl + url;
    }
  }

  switchTab(tab: 'images' | 'documents'): void {
    this.activeTab = tab;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
