import { DatePipe } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getPages } from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchExportReportAutomationStatus, FetchReportAutomationList, FetchReportAutomationTypeList, UpdateReportAutomationsFiltersPayload } from 'src/app/reducers/reports/reports.actions';
import { getIsReportAutomationLoading, getReportAutomationFiltersPayload, getReportAutomationList, getReportAutomationTypeList } from 'src/app/reducers/reports/reports.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { AddReportAutomationComponent } from './report-automation-components/add-report-automation/add-report-automation.component';
import { ReportAutomationActionsComponent } from './report-automation-components/report-automation-actions/report-automation-actions.component';
import { ReportAutomationStatusComponent } from './report-automation-components/report-automation-status/report-automation-status.component';
import { ReprtAutomationExportTrackerComponent } from './reprt-automation-export-tracker/reprt-automation-export-tracker.component';

@Component({
  selector: 'report-automation',
  templateUrl: './report-automation.component.html',
  providers: [DatePipe],
})
export class ReportAutomationComponent implements OnInit, OnDestroy {
  private stopper = new Subject<void>();
  PageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  selectedPageSize: number = 10;
  currOffset: number = 0;
  filtersPayload: any = { PageNumber: 1, PageSize: 10 };
  appliedFilter: any = {};
  isReportAutomationLoading: boolean = false;
  reportAutomationData: any;
  gridApi: any;
  gridColumnApi: any;
  gridOptions: any;
  defaultColDef: any;
  getPages = getPages
  allUserList: any[] = [];
  reportTypes: any;
  isCustomStatusEnabled: boolean = false;
  canBulkUpload: boolean = true;
  canExport: boolean = true;
  frequencyList: any[] = [
    { label: 'Today', value: 0 },
    { label: 'Last 2 days', value: 2 },
    { label: '7 days', value: 7 },
    { label: '30 days', value: 30 }
  ];
  selectedReports: any = 'All Reports';
  isReportAutomationExpanded: boolean = false;

  constructor(
    private gridOptionsService: GridOptionsService,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    public metaTitle: Title,
    private headerTitle: HeaderTitleService,
    private datePipe: DatePipe
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
  }

  ngOnInit(): void {
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
    this.store.select(getReportAutomationFiltersPayload).subscribe((payload: any) => {
      this.store.dispatch(new FetchReportAutomationList({ ...payload }));
    });
    this.store.dispatch(new FetchReportAutomationTypeList());
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isCustomStatusEnabled = data?.isCustomStatusEnabled;
      });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        // this.canExport = permissionsSet.has('');
        // this.canBulkUpload = permissionsSet.has('');
      });

    this.store
      .select(getReportAutomationTypeList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data) {
          this.reportTypes = [
            { key: 'All Reports', value: null, category: 'All' },
            ...Object.entries(data).flatMap(([category, reports]) =>
              Object.entries(reports)
                .filter(([key, value]) => {
                  if (this.isCustomStatusEnabled) {
                    return key.startsWith('Custom Status') || !key.startsWith('Status');
                  } else {
                    return key.startsWith('Status') || !key.startsWith('Custom Status');
                  }
                })
                .map(([key, value]) => ({ key, value, category }))
            )
          ];
        }
      });

    // this.store
    //   .select(getUserWithRole)
    //   .pipe(
    //     takeUntil(this.stopper),
    //     filter((data: any) => data && data.length > 0))
    //   .subscribe((data: any) => {
    //     this.allUserList = assignToSort(
    //       data.map((user: any) => ({
    //         ...user,
    //         fullName: `${user?.firstName ?? ''} ${user?.lastName ?? ''}`.trim(),
    //       }))
    //     );
    //     this.refreshGrid();
    //   });

    this.store.select(getIsReportAutomationLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isReportAutomationLoading = isLoading;
      });

    this.store.select(getReportAutomationFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((payload: any) => {
        this.filtersPayload = payload;
        this.currOffset = payload?.PageNumber - 1;
        this.PageSize = payload?.PageSize;
        this.selectedPageSize = payload?.PageSize;
        this.appliedFilter = {
          ...this.appliedFilter,
          PageNumber: payload?.PageNumber,
          PageSize: payload?.PageSize,
        };
      });

    this.store.select(getReportAutomationList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.reportAutomationData = data;
      });

    this.reportAutomationGridSettings();
  }

  reportAutomationGridSettings(): void {
    this.gridOptions.rowHeight = 60;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Status',
        minWidth: 100,
        maxWidth: 100,
        filter: false,
        lockPosition: true,
        valueGetter: (params: any) => [params.data?.status],
        cellRenderer: ReportAutomationStatusComponent,
      },
      {
        headerName: 'Report name',
        field: 'Report name',
        minWidth: 150,
        valueGetter: (params: any) => params.data?.displayName || '--',
        cellRenderer: (params: any) => `<p>${params.value || '--'}</p>`,
      },
      {
        headerName: 'Receive reports',
        field: 'Receive reports',
        minWidth: 150,
        valueGetter: (params: any) => ['Daily', 'Weekly', 'Monthly', 'Days'][Number(params.data?.scheduleType) - 1] || '--',
        cellRenderer: (params: any) => `<p>${params.value || '--'}</p>`,
      },
      // {
      //   headerName: 'Recipient',
      //   field: 'Recipient',
      //   minWidth: 150,
      //   valueGetter: (params: any) => {
      //     if (!this.allUserList || this.allUserList.length === 0) {
      //       return 'Loading...';
      //     }
      //     if (!params?.data?.userIds || !Array.isArray(params.data.userIds)) {
      //       return '--';
      //     }
      //     const recipientNames = params.data.userIds
      //       .map((userId: any) => {
      //         const user = this.allUserList.find(u => u?.id === userId);
      //         return user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() : null;
      //       })
      //       .filter((name: any) => name);
      //     return recipientNames.length > 0 ? recipientNames.join(', ') : '--';
      //   },
      //   cellRenderer: (params: any) => {
      //     return `<p class='text-truncate-2 break-all'>${params.value || '--'}</p>`;
      //   }
      // },
      {
        headerName: 'Receive time',
        field: 'Receive time',
        minWidth: 150,
        valueGetter: (params: any) => {
          const time24 = params.data?.scheduleTime;
          if (!time24) return '--';
          const [hours, minutes] = time24.split(':');
          const utcDate = new Date();
          utcDate.setUTCHours(parseInt(hours, 10));
          utcDate.setUTCMinutes(parseInt(minutes, 10));
          const localTime = this.datePipe.transform(utcDate, 'h:mm a');
          return localTime || '--';
        },
        cellRenderer: (params: any) => `<p>${params.value || '--'}</p>`,
      },
      {
        headerName: 'Report frequency',
        field: 'Report frequency',
        minWidth: 150,
        valueGetter: (params: any) => {
          if (params.data?.frequency === 0) return 'Today';
          return params.data?.frequency ?? '--';
        },
        cellRenderer: (params: any) => {
          if (!params.value || params.value === '--') return `<p>--</p>`;
          if (params.value === 'Today') return `<p>Today</p>`;
          let value = this.frequencyList?.find(x => x.value === params.value);
          return `<p>${value?.label ?? (params.value ? params.value + ' Days' : '--')}</p>`;
        }
      },
      {
        headerName: 'Actions',
        minWidth: 110,
        maxWidth: 110,
        menuTabs: [],
        filter: false,
        suppressMovable: true,
        lockPosition: 'right',
        cellRenderer: ReportAutomationActionsComponent,
      },
    ];
  }

  onGridReady(params: any): void {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    // if (this.allUserList && this.allUserList.length > 0) {
    //   this.refreshGrid();
    // }
  }

  assignCount(): void {
    this.PageSize = this.selectedPageSize;
    this.filtersPayload = { ...this.filtersPayload, PageSize: this.PageSize, PageNumber: 1 };
    this.store.dispatch(new UpdateReportAutomationsFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchReportAutomationList(this.filtersPayload));
    this.currOffset = 0;
  }

  onPageChange(e: number): void {
    this.currOffset = e;
    this.filtersPayload = { ...this.filtersPayload, PageNumber: e + 1 };
    this.store.dispatch(new UpdateReportAutomationsFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchReportAutomationList(this.filtersPayload));
  }

  openReportAutomationModal() {
    let initialState: any = {
      isAddNewStatus: true,
      isCustomStatusEnabled: this.isCustomStatusEnabled
    };
    this.modalRef = this.modalService.show(AddReportAutomationComponent, {
      class: 'right-modal modal-350 ip-modal-unset',
      initialState,
    });
  }

  openreportAutomationTracker() {
    this.store.dispatch(new FetchExportReportAutomationStatus(1, 10));
    this.modalService.show(ReprtAutomationExportTrackerComponent, {
      class: 'modal-900 modal-dialog-centered h-100 tb-modal-unset',
    });
  }

  preventDeselect(event: MouseEvent, _selectComponent: any) {
    const currentSelection = this.selectedReports;
    const target = event.target as HTMLElement;
    if (target.classList.contains('ng-value-label') ||
      target.closest('.ng-value-label') ||
      target.classList.contains('ng-value') ||
      target.closest('.ng-value')) {
      event.preventDefault();
      event.stopPropagation();
      setTimeout(() => {
        this.selectedReports = currentSelection;
      });
    }
  }

  onSelectReportType(event: any) {
    if (!event) {
      return;
    }
    this.selectedReports = event.key;
    this.filtersPayload = { ...this.filtersPayload, ReportName: event.value };
    this.store.dispatch(new UpdateReportAutomationsFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchReportAutomationList(this.filtersPayload));
  }

  onPageSizeChange(event: any) {
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: event,
      PageNumber: 1,
    }
    this.store.dispatch(new UpdateReportAutomationsFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchReportAutomationList(this.filtersPayload));
  }

  refreshGrid() {
    if (this.gridApi) {
      this.gridApi?.refreshCells({ force: true });
      // this.gridApi.redrawRows();
    }
  }

  toggleReportAutomation() {
    this.isReportAutomationExpanded = !this.isReportAutomationExpanded;
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}