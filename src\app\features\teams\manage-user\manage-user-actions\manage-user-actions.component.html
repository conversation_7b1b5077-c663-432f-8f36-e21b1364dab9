<ng-container *ngIf="params.value[0] == 'Assigned Leads' else actions">
  <div class="flex-between text-large">
    <div>{{userLeadsCount?.[0]?.leadCount || '--'}}</div>
  </div>
</ng-container>
<ng-template #actions>
  <div class="align-center">
    <div title="Edit" *ngIf="canEditUser" class="bg-accent-green icon-badge" id="clkEditUser"
      data-automate-id="clkEditUser" (click)="editUser()">
      <span class="icon ic-pen ic-xxxs"></span>
    </div>
    <div title="Permanent Delete" *ngIf="canDeleteUser" class="bg-light-red icon-badge"
      (click)="deleteUser($event, params.data)"><span class="icon ic-delete m-auto ic-xxs" id="clkDeleteLead"
        data-automate-id="clkDeleteLead"></span></div>
    <div [title]="isUserActive ? 'Active' : 'Deactive'" *ngIf="canDeleteUser" class="ml-6 align-start cursor-pointer"
      (click)="handleUserStatus(licenseBoughtModal)">
      <ng-container *ngIf="isUserActive else deactive">
        <img [type]="'leadrat'" [appImage]="s3BucketUrl + 'logos/user-activate.svg'" width="22" height="22">
      </ng-container>
      <ng-template #deactive>
        <img [type]="'leadrat'" [appImage]="s3BucketUrl + 'logos/user-deactivate.svg'" width="22" height="22">
      </ng-template>
    </div>
    <div *ngIf="canMakeDefaultPassword" title="Reset to Default Password" class="bg-blue-1200 icon-badge"
      (click)="resetPassword(params.data)"><span class="icon ic-restore-lock m-auto ic-xxs" id="resetPassword"
        data-automate-id="resetPassword"></span></div>
    <div title="Geo Fencing" *ngIf="params?.data?.isGeoFenceActive && canGeoFence && isGeoFenceEnabled"
      class="bg-blue-160 icon-badge" id="clkGeoFencing" data-automate-id="clkGeoFencing" (click)="geoFencing()">
      <span class="icon ic-location-pin ic-xxs"></span>
    </div>
  </div>
</ng-template>

<ng-template #licenseBoughtModal>
  <div class="p-20">
    <h4 class="fw-semi-bold text-balck-200">License Limit Reached Connect with your admin or {{getAppName()}} Support
      **********
      to Buy More </h4>
    <div class="flex-end mt-30">
      <button class="btn-green" (click)="toggleUserStatus(params.data.userId, params.data.isActive); closeModal()">
        ok</button>
    </div>
  </div>
</ng-template>
<ng-template #trackerInfoModal>
  <h5 class="px-20 py-16 fw-semi-bold bg-coal text-white text-capitalize">User Delete Scheduled</h5>
  <div class="p-20 flex-center-col">
    <h4 class="text-black-100 fw-600 mb-10 text-center word-break line-break text-capitalize">User Delete
      has been scheduled.
    </h4>
    <h5 class="text-black-100 fw-semi-bold text-center word-break line-break">You can check
      <span class="cursor-pointer text-accent-green header-3 fw-600" (click)="openDeleteTracker()">“Delete user
        Tracker”</span> to view upload status
    </h5>
    <button class="btn-green mt-30" (click)="navigateToHome();modalService.hide()">
      {{'BULK_LEAD.got-it' | translate}}</button>
  </div>
</ng-template>