<div class="d-flex flex-wrap w-100  p-30 bg-white brbl-15 brbr-15" [formGroup]="filtersForm">
    <h5 class="flex-column w-100 text-dark-gray mt-0 mb-10">Filter by</h5>
    <div class="w-50 ph-w-100">
        <div class="field-label fw-600">Assigned To</div>
        <div class="mr-20">
            <ng-select formControlName="AssignedUserIds" [virtualScroll]="true" [items]="userList" ResizableDropdown
                [multiple]="true" appSelectAll [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                bindValue="id" bindLabel="fullName">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                            class="checkmark"></span><span class="text-truncate-1 break-all">{{item?.fullName}}</span>
                    </div>
                </ng-template>
            </ng-select>
        </div>
    </div>
    <div class="w-50 ph-w-100">
        <div class="field-label fw-600">Associated Property</div>
        <div class="mr-20">
            <ng-select [virtualScroll]="true" [items]="propertyList" ResizableDropdown
                placeholder="{{'GLOBAL.select' | translate}}" [multiple]="true" appSelectAll [closeOnSelect]="false"
                bindValue="id" bindLabel="title" formControlName="AssociatedProperties">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                            class="checkmark"></span><span class="text-truncate-1 break-all">{{item?.title}}</span>
                    </div>
                </ng-template>
            </ng-select>
        </div>
    </div>
    <div class="w-50 ph-w-100">
        <div class="field-label fw-600">Portal Name</div>
        <div class="mr-20">
            <ng-select formControlName="ListingSourceIds" [virtualScroll]="true" [items]="portals" ResizableDropdown
                [multiple]="true" appSelectAll [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                bindValue="id" bindLabel="displayName">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                            class="checkmark"></span><span
                            class="text-truncate-1 break-all">{{item?.displayName}}</span>
                    </div>
                </ng-template>
            </ng-select>
        </div>
    </div>
    <div class="w-50 ph-w-100">
        <div class="field-label fw-600">Property Title</div>
        <div class="mr-20 form-group">
            <input type="text" formControlName="PropertyTitle" autocomplete="off"
                class="pl-20 ph-pl-12 text-large ph-w-150px" id="inpTitle" data-automate-id="inpTitle"
                placeholder="ex. Rohit house">
        </div>
    </div>
    <div class="w-50 ph-w-100">
        <div class="field-label fw-600">Reference Id</div>
        <div class="mr-20">
            <ng-select formControlName="RefrenceIds" [virtualScroll]="true" [items]="allReferenceIds" ResizableDropdown
                [multiple]="true" appSelectAll [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                            class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                    </div>
                </ng-template>
            </ng-select>
        </div>
    </div>
    <div class="w-50 ph-w-100">
        <div class="field-label fw-600">Serial Number</div>
        <div class="mr-20 form-group">
            <input type="text" autocomplete="off" class="pl-20 ph-pl-12 text-large ph-w-150px" id="inpTitle"
                data-automate-id="inpTitle" formControlName="SerialNo" placeholder="ex. Rohit house">
        </div>
    </div>
    <div class="w-100  bg-white justify-end align-center position-sticky bottom-0 z-index-1021">
        <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="modalRef.hide()">Cancel</u>
        <u class="fw-semi-bold text-mud cursor-pointer mr-20" (click)="filtersForm.reset()">Reset</u>
        <button class="btn-coal" (click)="applyAdvancedFilters()">Apply filter</button>
    </div>
</div>