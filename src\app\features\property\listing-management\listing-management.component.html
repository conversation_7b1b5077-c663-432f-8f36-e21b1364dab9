<div *ngIf="permissions?.has('Permissions.Properties.View') || permissions?.has('Permissions.Properties.ViewAssigned')"
  [formGroup]="filtersForm">
  <div class="px-24 bg-white  py-8 tb-left-200 mq-top-navbar" [ngClass]="showLeftNav ? 'left-230' : 'left-140'">
    <div class="flex-between flex-grow-1">
      <ul
        class="align-center top-nav-bar text-nowrap scrollbar scroll-hide ip-w-100-315 tb-w-100-400 user-select-none ph-d-none">
        <ng-container *ngFor="let parentFilter of topFilters">
          <div [title]="parentFilter?.displayName" class="cursor-pointer"
            (click)="getFormValue('PropertyVisiblity') !== parentFilter?.enumValue && this.filtersForm.patchValue({ PropertyVisiblity: parentFilter?.enumValue, PageNumber:1 })">
            <div class="align-center ph-mb-4">
              <span class="text-large fw-semi-bold mx-8 d-flex"
                [class.active]="getFormValue('PropertyVisiblity') === parentFilter.enumValue">{{
                parentFilter?.displayName
                }}
                <ng-container *ngIf="!loaders?.allListingTopCount; else dotLoader">
                  ({{getCount('top',parentFilter?.displayName)}})
                </ng-container>
              </span>
            </div>
          </div>
        </ng-container>
      </ul>
      <div class="ph-d-flex ng-select-sm" [ngClass]="{ 'd-none': !isDropdownVisible }">
        <ng-select class="align-center top-nav-bar ip-w-100-315 tb-w-100-400 user-select-none" [items]="topFilters"
          bindLabel="displayName" bindValue="enumValue" formControlName="PropertyVisiblity"
          (change)="onFilterChange($event?.enumValue)" class="w-120 mr-4">
          <ng-template ng-label-tmp let-item="item">
            {{ item?.displayName }}
            <span class="text-small">({{ getCount('top', item?.displayName) }})</span>
          </ng-template>
          <ng-template ng-option-tmp let-item="item">
            {{ item?.displayName }}
            <span class="text-small">({{ getCount('top', item?.displayName) }})</span>
          </ng-template>
        </ng-select>
      </div>
      <div class="align-center">
        <div class="btn-full-dropdown btn-w-100"
          *ngIf="permissions?.has('Permissions.Properties.BulkUpload')|| permissions?.has('Permissions.Properties.Export')">
          <div class="position-absolute top-9 left-9 ip-top-11 align-center z-index-2">
            <span class="ic-tracker icon ic-xxs"></span>
            <span class="ml-8 ip-d-none">Tracker</span>
          </div>
          <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
            [ngModelOptions]="{standalone: true}" [(ngModel)]="selectedTrackerOption" (click)="openPropertyTracker()">
            <ng-option (click)="selectedTrackerOption = null" value="bulkUpload"
              *ngIf="permissions?.has('Permissions.Properties.BulkUpload')">
              <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
              {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }} Tracker</ng-option>
            <ng-option (click)="selectedTrackerOption = null" value="export"
              *ngIf="permissions?.has('Permissions.Properties.Export')">
              <span class="ic-download icon ic-xxs ic-dark mr-8"></span>
              Export Tracker</ng-option>
            <ng-option (click)="selectedTrackerOption = null" value="sync">
              <span class="ic-menu icon ic-xxs ic-dark mr-8"></span>
              Sync Tracker</ng-option>
          </ng-select>
        </div>
        <ng-container
          *ngIf="permissions?.has('Permissions.Properties.BulkUpload') || permissions?.has('Permissions.Properties.Create')">
          <div class="btn-left-dropdown ml-10"
            (click)="permissions?.has('Permissions.Properties.Create') ? navigateToAddListing() : ''">
            <span class="ic-add icon ic-xxs"></span>
            <span class="ml-8 ip-d-none">{{ 'BUTTONS.add-property' | translate }}</span>
          </div>
          <div class="btn-right-dropdown btn-w-30 black-100">
            <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
              [ngModelOptions]="{standalone: true}" [(ngModel)]="selectedOption" (click)="openPropertyBulkUpload()">
              <ng-option (click)="selectedOption = null" value="bulkUpload"
                *ngIf="permissions?.has('Permissions.Properties.BulkUpload')">
                <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
            </ng-select>
          </div>
        </ng-container>
        <ng-container *ngIf="permissions?.has('Permissions.Properties.Create')">
          <button class="btn-left-dropdown ml-10" (click)="$event.stopPropagation();toggleDropdown()">
            <span class="ic-menu icon ic-xxs"></span>
          </button>
          <div class="w-30px position-relative no-validation cursor-pointer">
            <div class="bg-black-100 p-8 brtr-4 brbr-4 border-bottom-black"
              (click)="$event.stopPropagation();toggleDropdown()">
              <span class="icon ic-triangle ic-x-xs mx-2"></span>
            </div>
            <div class="py-2 w-190 bg-white box-shadow-10 br-10 z-index-1021 position-absolute nright-0 top-32"
              *ngIf="isDropdownOpen && listingSource?.length" [formGroup]="syncDateForm" id="dropdown-element">
              <ng-container *ngFor="let source of listingSource; let isLast = last">
                <div class="flex-between mt-8 px-8" [ngClass]="{'border-bottom': !isLast}">
                  <img [appImage]="source.imageURL ? s3BucketUrl + source.imageURL : ''" [type]="'defaultAvatar'"
                    height="16px" width="16px" />
                  <div class="flex-col">
                    <h6 class="ml-12 text-sm">{{source?.displayName}}</h6>
                    <div class="filters-grid w-120 clear-padding">
                      <div class="date-picker green-900 no-bg border-start-0 align-center rounded-end">
                        <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                          [selectMode]="'range'" placeholder="Select date range" [formControlName]="source?.displayName"
                          class="cursor-pointer">
                        <owl-date-time [pickerType]="'calendar'" #dt1 (afterPickerOpen)="onPickerOpened(currentDate)">
                        </owl-date-time>
                      </div>
                    </div>
                  </div>
                  <div [title]="'sync'" class="d-flex cursor-pointer" (click)="onSyncListing(source)">
                    <div class="border-left h-10 mx-12"></div>
                    <span class="ic-menu ic-dark icon ic-xxs"></span>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
  <div class="d-flex border-top bg-white border-bottom">
    <div class="align-center ml-20">
      <div class="w-95 border box-shadow-50 listing-dropdown br-20 no-validation">
        <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" class="h-32 no-validation br-20"
          formControlName='FirstLevelFilter' ResizableDropdown>
          <ng-option *ngFor="let filter of filters" [value]="filter.enumValue">
            {{ filter.displayName }}</ng-option>
        </ng-select>
      </div>
    </div>
    <div class="border-right mx-8 my-12"></div>
    <div class="align-center p-8 scroll-hide scrollbar tb-w-100-135">
      <div>
        <button class="header-5 fw-300 max-w-150 mr-10 px-12 py-8 border br-20" [ngClass]="{
        'bg-orange-150-active box-shadow-50': !getFormValue('BasePropertyTypeId'),
        'bg-white': getFormValue('BasePropertyTypeId')
      }"
          (click)="getFormValue('BasePropertyTypeId') && filtersForm.patchValue({ BasePropertyTypeId: undefined, PageNumber:1  })">
          All
          <ng-container *ngIf="!loaders?.allListingBaseCount; else dotLoader">
            ({{ getCount('base', 'All') }})
          </ng-container>
        </button>
      </div>
      <div *ngFor="let type of propertyTypeList">
        <button class="header-5 fw-300 max-w-170 mr-10 px-12 py-8 border br-20" [ngClass]="{
            'bg-white': getFormValue('BasePropertyTypeId') !== type.id,
            'btn-residential-active box-shadow-50': type?.displayName === 'Residential' && getFormValue('BasePropertyTypeId') === type.id,
            'btn-commercial-active box-shadow-50': type?.displayName === 'Commercial' && getFormValue('BasePropertyTypeId') === type.id,
            'btn-agricultural-active box-shadow-50': type?.displayName === 'Agricultural' && getFormValue('BasePropertyTypeId') === type.id,
            'bg-orange-150-active box-shadow-50': type?.displayName === 'All' && getFormValue('BasePropertyTypeId') === type.enumValue
          }"
          (click)="getFormValue('BasePropertyTypeId') !== type.id && filtersForm.patchValue({ BasePropertyTypeId: type.id, PageNumber:1  })">
          {{ type?.displayName }}
          <ng-container *ngIf="!loaders?.allListingBaseCount; else dotLoader">
            ({{ getCount('base', type?.displayName) }})
          </ng-container>
        </button>
      </div>
    </div>
  </div>
  <div>
    <div class="py-12 px-24">
      <div class="align-center bg-white w-100 border-gray tb-align-center-unset tb-flex-col">
        <div class="align-center flex-grow-1 no-validation border-end tb-br-0">
          <div *ngIf="permissions?.has('Permissions.Properties.Search')" class="position-relative flex-grow-1">
            <div class="align-center w-100 px-10 py-12">
              <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
              <input placeholder="type to search" autocomplete="off" name="search" class="border-0 outline-0 w-100"
                [value]="getFormValue('PropertySearch')" (input)="isEmptyInput($event)"
                (keydown.enter)="onEnterKey($event)" />
            </div>
            <small class="text-muted text-nowrap ph-d-none mr-10 position-absolute right-0 bottom-0">
              ({{ 'LEADS.lead-search-prompt' | translate }})</small>
          </div>
          <div class="flex-end">
            <div *ngIf="permissions?.has('Permissions.Properties.Export') && globalSettings?.isPropertiesExportEnabled"
              class="bg-accent-green text-white px-20 py-12 h-100 align-center cursor-pointer border-start w-70px tb-br-top"
              (click)="exportListing()">{{ 'REPORTS.export' | translate }}</div>
          </div>
        </div>
        <div class="tb-br-top align-center ip-flex-col ip-align-center-unset">
          <div class="d-flex w-100">
            <div class="px-10 align-center cursor-pointer border-end tb-flex-grow-1 ph-w-40px ph-flex-grow-unset"
              (click)="openAdvFiltersModal()">
              <div class="icon ic-filter-solid ic-xxs ic-black mr-10"></div>
              <span class="fw-600 ph-d-none">{{'PROPERTY.advanced-filters' | translate}}</span>
            </div>
            <div class="filters-grid clear-padding border-end h-100 align-center">
              <div class="align-center h-100 ml-16 tb-ml-0">
                <div class="bg-white manage-select">
                  <ng-select [virtualScroll]="true" placeholder="{{'GLOBAL.all'| translate}}" [searchable]="false"
                    ResizableDropdown class="lead-date ip-max-w-80px min-w-60" [(ngModel)]="dateType"
                    [ngModelOptions]="{standalone: true}" (change)="dateChange()">
                    <ng-option name="dateType" ngDefaultControl *ngFor="let dType of dateTypeList"
                      [value]="dType">{{dType}}</ng-option>
                  </ng-select>
                </div>
                <div class="date-picker border-start-0 align-center ph-p-0">
                  <span class="ic-appointment icon ic-xxs ic-black" [owlDateTimeTrigger]="dt1"></span>
                  <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [selectMode]="'range'"
                    [disabled]="!dateType" class="pl-20 ph-pl-12 text-large ph-w-150px"
                    placeholder="ex. 19-06-2025 - 29-06-2025" (ngModelChange)="filterDate = $event; dateChange()"
                    [ngModelOptions]="{standalone: true}" [ngModel]="filterDate" />
                  <owl-date-time [pickerType]="'calendar'" #dt1
                    (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                </div>
              </div>
              <div (click)="onResetDateFilter()" *ngIf="getFormValue('FromDate')"
                class="bg-coal text-white px-10 py-12 w-50px ip-w-30px h-100 align-center cursor-pointer">
                <span class="ip-d-none">{{ 'GLOBAL.reset' | translate }}</span> <span
                  class="ic-convert d-none ip-d-block"></span>
              </div>
            </div>
          </div>
          <div class="d-flex ip-br-top">
            <div class="align-center position-relative cursor-pointer d-flex border-end">
              <span class="position-absolute left-15 z-index-2 fw-600 text-sm">
                {{ 'BUTTONS.manage-columns' | translate }}</span>
              <div class="show-hide-gray w-140">
                <ng-select [virtualScroll]="true" class="bg-white" [items]="columns" [multiple]="true" ResizableDropdown
                  [searchable]="false" [closeOnSelect]="false" [ngModel]="defaultColumns"
                  [ngModelOptions]="{standalone: true}" (change)="onColumnsSelected($event)">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span>{{item.label}}</div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div class="bg-coal text-white px-10 py-12 ip-w-30px  align-center cursor-pointer"
              (click)="onSetColumnDefault()">
              <span class="ip-d-none">{{ 'GLOBAL.default' | translate }}</span> <span
                class="ic-refresh d-none ip-d-block"></span>
            </div>
            <div class="show-dropdown-white align-center  position-relative">
              <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">{{ 'GLOBAL.show' |
                  translate
                  }}</span> {{ 'GLOBAL.entries' |
                translate }}</span>
              <ng-select [virtualScroll]="true" class="w-150 tb-w-120px" ResizableDropdown [items]="showEntriesSize"
                formControlName="PageSize" (change)="filtersForm.get('PageNumber').patchValue(1)">
              </ng-select>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-white px-4 py-12 tb-w-100-40" [ngClass]="showLeftNav ? 'w-100-190' : 'w-100-90'">
        <div class="bg-secondary flex-between" *ngIf="showFilters">
          <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
            <div class="d-flex" *ngFor="let filter of filtersPayload | keyvalue">
              <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                {{propertyFiltersKeyLabel[filter.key] || filter.key}}: {{
                filter.key === 'NoOfBHK'? getBRDisplayString(value) :
                filter.key === 'UserIds' ? getAssignedToName(value):
                filter.key === 'ListingOnBehalf' ? getAssignedToName(value):
                filter.key === 'EnquiredFor' ? getEnquiredFor(value) :
                filter.key === 'propertyType' ? getPropertyTypeName(value):
                filter.key === 'propertySubType' ? getPropertySubTypeName(value) :
                (filter.key === 'MinPropertySize' || filter.key === 'MaxPropertySize') ? getPropertyArea(value) :
                filter.key === 'FromPossesionDate' ? getPossessionDate(value) :
                filter.key === 'ToPossesionDate' ? getPossessionDate(value) :
                filter.key === 'Amenities' ? getAmenity(value) :
                filter.key === 'PropertySubTypes' ? getPropertySubTypes(value) :
                filter.key === 'CompletionStatus' ? getCompletionStatus(value) :
                filter.key === 'ListingLevel' ? getListingLevel(value) :
                value }}
                <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                  (click)="onRemoveFilter(filter.key, value)"></span>
              </div>
            </div>
          </drag-scroll>
          <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
            (click)="clearAll()">
            {{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}</div>
        </div>
      </div>
      <ng-container *ngIf="!loaders?.allListing; else gridLoader">
        <ng-container *ngIf="rowData?.length else noData">
          <div class="manage-listing">
            <ag-grid-angular class="ag-theme-alpine" #agGrid [gridOptions]="gridOptions"
              (gridReady)="onGridReady($event)" [rowData]="rowData" [suppressPaginationPanel]="true"
              [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true"
              (cellClicked)="onCellClicked($event)" (filterChanged)="onFilterChanged($event)">
            </ag-grid-angular>
          </div>
          <div class="my-20 flex-end">
            <div class="mr-10">{{ 'GLOBAL.showing' | translate }}
              {{(filtersPayload?.PageNumber-1)*filtersPayload?.PageSize + 1}}
              {{ 'GLOBAL.to-small' | translate }} {{(filtersPayload?.PageNumber-1)*filtersPayload?.PageSize +
              gridApi?.getDisplayedRowCount()}}
              {{ 'GLOBAL.of-small' | translate }} {{listingTotalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
            <pagination [offset]="filtersPayload?.PageNumber-1" [limit]="1" [range]="1"
              [size]='getPages(listingTotalCount,filtersPayload?.PageSize)'
              (pageChange)="this.filtersForm.patchValue({PageNumber: $event+1})">
            </pagination>
          </div>
          <!-- <data-bulk-update [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}" [gridApi]="gridApi"
            [allUsers]="allUsers || []" [deactiveUsers]="deactiveUsers || []"></data-bulk-update> -->
        </ng-container>
      </ng-container>
    </div>
  </div>
  <property-bulk-update [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}" [gridApi]="gridApi"
    [allUserList]="allUserList || []" [filtersPayload]="filtersPayload"></property-bulk-update>
  <ng-template #noData>
    <div class="flex-col flex-center h-100-260 min-h-250">
      <img src="assets/images/layered-cards.svg" alt="No Data Found" width="160" height="140">
      <div class="fw-semi-bold text-xl text-mud">{{'PROFILE.no-data-found' | translate}}</div>
    </div>
  </ng-template>
  <ng-template #dotLoader>
    (<div class="container px-4 d-inline">
      <ng-container *ngFor="let dot of [1,2,3]">
        <div class="dot-falling"></div>
      </ng-container>
    </div>)
  </ng-template>
  <ng-template #gridLoader>
    <div class="flex-center h-370">
      <application-loader></application-loader>
    </div>
  </ng-template>