import {
  Component,
  ElementRef,
  EventEmitter,
  On<PERSON><PERSON>roy,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, takeUntil } from 'rxjs';

import {
  LISTING_EXCEL_TEMPLATE,
  PROPERTY_EXCEL_TEMPLATE,
  PropertyDataColumns,
  PropertyDisplayColumns,
  REFERENCE_EXCEL_TEMPLATE,
  ReferenceDisplayColumns,
  ReferenceMappingColumns,
} from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  getAppName,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import {
  FetchPropertyExcelUploadedList,
  PropertyExcelUpload,
  UploadMappedColumns,
} from 'src/app/reducers/property/property.actions';
import { getColumnHeadings } from 'src/app/reducers/property/property.reducer';
import { FetchReferenceExcelUploadedList, ReferenceExcelUpload, UploadReferenceMappedColumns } from 'src/app/reducers/reference-id-management/reference-id-management.action';
import { getReferenceColumnHeadings } from 'src/app/reducers/reference-id-management/reference-id-management.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'bulk-upload',
  templateUrl: './bulk-upload.component.html',
})
export class BulkUploadComponent implements OnDestroy {
  @ViewChild('validModal') validModal: any;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  moduleName: string = location?.href?.includes('reference') ? 'Reference id' : 'Properties';
  excelTemplatePath: string = location?.href?.includes('listing')
    ? LISTING_EXCEL_TEMPLATE
    : location?.href?.includes('reference')
      ? REFERENCE_EXCEL_TEMPLATE
      : PROPERTY_EXCEL_TEMPLATE;
  propertyData = location?.href?.includes('listing')
    ? PropertyDisplayColumns
    : location?.href?.includes('reference')
      ? ReferenceDisplayColumns.slice(0, ReferenceDisplayColumns?.length - 2)
      : PropertyDisplayColumns.slice(0, 48);
  propertyMappingForm: FormGroup;
  referenceMappingForm: FormGroup;
  isFileTypeSupported: boolean = true;
  currentStep: number = 1;
  isValidModal: boolean = false;
  selectedFile: File;
  formKeys: Array<string> = [];
  s3BucketKey: string;
  excelSheets: any = {};
  sheetNames: Array<string> = [];
  selectedSheet: string;
  selectedTitle: string;
  userData: any;
  getAppName = getAppName;
  currentPath: string;
  constructor(
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private router: Router,
    private fb: FormBuilder,
    private headerTitle: HeaderTitleService,
    public metaTitle: Title
  ) {
    this.metaTitle.setTitle(`CRM | Import ${this.moduleName}`);
    this.headerTitle.setLangTitle(`Import ${this.moduleName}`);
    this.propertyMappingForm = this.fb.group({
      title: [null, Validators.required],
      enquiredFor: [null],
      propertyType: [null],
      propertySubType: [null],
      noOfBHK: [null],
      bhkType: [null],
      propertySize: [null],
      areaUnit: [null],
      aboutProperty: [null],
      saleType: [null],
      possessionDate: [null],
      PossesionType: [null],
      expectedPrice: [null],
      currency: [null],
      countryCode: [null],
      isNegotiable: [null],
      brokerage: [null],
      brokerageUnit: [null],
      location: [null],
      city: [null],
      state: [null],
      ownerName: [null],
      ownerPhone: [null],
      OwnerAltContactNo: [null],
      ownerEmail: [null],
      notes: [null],
      rating: [null],
      numberOfBedrooms: [null],
      numberOfBathrooms: [null],
      numberOfKitchens: [null],
      numberOfBalconies: [null],
      numberOfDrawingOrLivingRooms: [null],
      numberOfUtilities: [null],
      numberOfFloors: [null],
      floorNumber: [null],
      furnishStatus: [null],
      facing: [null],
      status: [null],
      project: [null],
      coWorkingOperator: [null],
      securityDeposit: [null],
      securityDepositUnit: [null],
      lockInPeriod: [null],
      noticePeriod: [null],
      escalation: [null],
      tenantPOCName: [null],
      tenantPOCDesignation: [null],
      tenantPOCPhone: [null],
      coWorkingOperatorPOCName: [null],
      coWorkingOperatorPOCPhone: [null],
      tenantPOCNumberCountryCode: [null],
      coWorkingOperatorPOCNumberCountryCode: [null],
      dldPermitNumber: [null],
      dtcMPermit: [null],
      offeringType: [null],
      completionStatus: [null],
      community: [null],
      subCommunity: [null],
      towerName: [null],
      paymentFrequency: [null],
      netAreaUnit: [null],
      netArea: [null],
      isListed: [null],
      listingPortal: [null],
      imageUrls: [null],
      listedByUser: [null],
    });

    this.referenceMappingForm = this.fb.group({
      referenceId: [null, Validators.required],
      propertyTitle: [null],
      portalName: [null, Validators.required],
      userName: [null],
    });
  }

  ngOnInit() {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });

    this.propertyData = location?.href?.includes('listing')
      ? PropertyDisplayColumns
        .filter((column) => column.value !== 'bhkType') // remove bhkType
        .map((column) => {
          if (column.value === 'noOfBHK') {
            return { ...column, displayName: 'BR' };
          }
          return column;
        })
      : location?.href?.includes('reference')
        ? ReferenceDisplayColumns.slice(0, ReferenceDisplayColumns?.length - 2)
        : PropertyDisplayColumns.slice(0, 48);
  }

  onFileSelection(file: File) {
    this.selectedFile = file;
    this.currentStep =
      this.currentStep < 2 ? this.currentStep + 1 : this.currentStep;
  }

  uploadFile() {
    if (location?.href?.includes('reference')) {
      this._store.dispatch(new ReferenceExcelUpload(this.selectedFile));
      this._store
        .select(getReferenceColumnHeadings)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.excelSheets = data?.multiSheetColumnNames;
          if (this.excelSheets) this.sheetNames = Object.keys(this.excelSheets);
          this.selectedSheet = this.sheetNames[0];
          this.formKeys = data?.columnNames;
          this.s3BucketKey = data?.s3BucketKey;
          if (this.formKeys?.length) {
            this.currentStep = 3;
          }
          const mappingColumns = ReferenceDisplayColumns
          const form = this.referenceMappingForm;
          mappingColumns.forEach((column: any) => {
            if (
              column?.displayName &&
              form?.controls[column?.value] &&
              this.formKeys?.includes(column?.displayName)
            ) {
              form.patchValue({
                [column?.value]: column?.displayName,
              });
            }
          });

        });
    } else {
      this._store.dispatch(new PropertyExcelUpload(this.selectedFile));
      this._store
        .select(getColumnHeadings)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.excelSheets = data?.multiSheetColumnNames;
          if (this.excelSheets) this.sheetNames = Object.keys(this.excelSheets);
          this.selectedSheet = this.sheetNames[0];
          this.formKeys = data?.columnNames;
          this.selectedTitle = this.formKeys?.find(
            (key) => key === 'Property Title'
          );
          this.s3BucketKey = data?.s3BucketKey;
          if (this.formKeys?.length) {
            this.currentStep = 3;
          }
          this.onAutoMapChange();
        });
    }

    this.resetForms();
  }


  replaceFile() {
    this.fileInput.nativeElement.click();
  }

  isValidForm() {
    const isReferencePath = this.currentPath?.includes('reference');
    const formToValidate = isReferencePath ? this.referenceMappingForm : this.propertyMappingForm;

    if (!this.selectedSheet || !formToValidate.valid) {
      validateAllFormFields(formToValidate);
      return;
    }

    this.isValidModal = true;
    this.modalRef = this.modalService.show(this.validModal, {
      class: 'modal-350 modal-dialog-centered ip-modal-unset',
      keyboard: false,
    });
  }


  confirmSheet(trackerInfoModal: TemplateRef<any>) {
    this.modalRef.hide();
    this.sendMappingDetails(trackerInfoModal);
  }

  sendMappingDetails(trackerInfoModal: TemplateRef<any>) {
    const isReferencePath = location?.href?.includes('reference');
    const mappingForm = isReferencePath ? this.referenceMappingForm : this.propertyMappingForm;
    const dataColumns = isReferencePath ? ReferenceMappingColumns : PropertyDataColumns;
    const UploadAction = isReferencePath
      ? UploadReferenceMappedColumns
      : UploadMappedColumns;
    const FetchAction = isReferencePath
      ? FetchReferenceExcelUploadedList
      : FetchPropertyExcelUploadedList;

    if (!this.selectedSheet || (isReferencePath ? false : !mappingForm.controls['title'].value)) {
      return;
    }

    const payload: any = {
      s3BucketKey: this.s3BucketKey,
      fileName: this.selectedFile?.name,
      mappedColumnsData: {},
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset:
        this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      sheetName: this.selectedSheet || this.sheetNames[0],
    };

    dataColumns.forEach((item: any) => {
      if (mappingForm.value[item.value]) {
        payload.mappedColumnsData[item.displayName] =
          mappingForm.value[item.value];
      }
    });

    this._store.dispatch(new UploadAction(payload));
    this._store.dispatch(new FetchAction(1, 10));

    this.modalRef = this.modalService.show(
      trackerInfoModal,
      {
        class: 'modal-400 top-modal ph-modal-unset',
        ignoreBackdropClick: true,
        keyboard: false,
      }
    );
  }

  onAutoMapChange() {
    const mappingColumns = location?.href?.includes('listing')
      ? PropertyDisplayColumns.map((column) => {
        if (column.value === 'noOfBHK') {
          return { ...column, displayName: 'BR' };
        }
        if (column.value === 'bhkType') {
          return { ...column, displayName: 'BR Type' };
        }
        return column;
      })
      : PropertyDisplayColumns.slice(0, PropertyDisplayColumns.length - 4);

    const form = this.propertyMappingForm;
    const titleControl = 'title';
    const titleKey = 'Property Title';
    const mappingNameForTitle = this.formKeys?.find((key) => key === titleKey);

    mappingColumns.forEach((column) => {
      if (
        column?.displayName &&
        form?.controls[column?.value] &&
        this.formKeys?.includes(column?.displayName)
      ) {
        form.patchValue({
          [column?.value]: column?.displayName,
        });
      }
    });

    if (mappingNameForTitle) {
      form.patchValue({
        [titleControl]: mappingNameForTitle,
      });
    }
  }

  openBulkUploadedStatusModal() {
    this.navigateToHome();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      fieldType: location?.href.includes('reference') ? 'reference' : 'property',
    };
    this.modalRef = this.modalService.show(ExcelUploadedStatusComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState,
    });
  }

  onSheetSelection() {
    this.formKeys = this.excelSheets?.[this.selectedSheet];
  }

  navigateToHome() {
    if (location.href?.includes('listing')) {
      this.router.navigate(['properties/manage-listing']);
    } else if (location.href?.includes('reference')) {
      this.router.navigate(['properties/manage-reference-id']);
    } else {
      this.router.navigate(['properties']);
    }
  }

  resetForms() {
    if (this.propertyMappingForm) this.propertyMappingForm.reset();
    if (this.referenceMappingForm) this.referenceMappingForm.reset();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
