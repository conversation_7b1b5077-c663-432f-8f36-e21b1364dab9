import { Injectable } from '@angular/core';
import { firstValueFrom, take, Observable, from, BehaviorSubject } from 'rxjs';
import { getTenantName } from 'src/app/core/utils/common.util';
import { MasterDataService } from '../controllers/master-data.service';
import { PropertyService } from '../controllers/properties.service';
import { ClearIndexedDB } from 'src/app/reducers/indexeddb/indexeddb.actions';
import { getFiltersPayload } from 'src/app/reducers/property/property.reducer';
import { AppState } from 'src/app/app.reducer';
import { Store } from '@ngrx/store';

@Injectable({
  providedIn: 'root'
})
export class IndexedDBService {
  private db: IDBDatabase | null = null;
  private readonly dbName = 'leadratDB';
  private readonly dbVersion = 1;
  private readonly propertyStoreName = 'properties';
  private readonly metadataStoreName = 'metadata';

  // Configuration constants
  private readonly BATCH_PAGE_SIZE = 50;
  private readonly BATCH_DELAY_MS = 1500;
  private readonly MAX_RECORDS = 500;

  // User and tenant tracking
  private userId: string = '';
  private tenantId: string = '';
  private lastKnownUserId: string = '';
  private lastKnownTenantId: string = '';
  private filtersPayload: any = {};
  private forceUseAPI = false;

  // Loading state management
  private indexedDBLoadingSubject = new BehaviorSubject<boolean>(false);
  public indexedDBLoading$ = this.indexedDBLoadingSubject.asObservable();

  constructor(
    private propertyService: PropertyService,
    private masterDataService: MasterDataService,
    private store: Store<AppState>
  ) {
    this.initializeUserTenantTracking();
    this.initIndexedDB();
  }

  // Initialize IndexedDB
  initIndexedDB(): void {
    const request = indexedDB.open(this.dbName, this.dbVersion);

    request.onerror = (event: any) => {
      console.error('IndexedDB error:', event.target.error);
    };

    request.onsuccess = (event: any) => {
      this.db = event.target.result;
      console.log('IndexedDB initialized successfully');
      this.initializeData();
    };

    request.onupgradeneeded = (event: any) => {
      this.db = event.target.result;

      // Create property store
      if (!this.db.objectStoreNames.contains(this.propertyStoreName)) {
        const propertyStore = this.db.createObjectStore(this.propertyStoreName, { keyPath: 'id' });
      }

      // Create metadata store
      if (!this.db.objectStoreNames.contains(this.metadataStoreName)) {
        const metadataStore = this.db.createObjectStore(this.metadataStoreName, { keyPath: 'key' });
      }
    };
  }

  // Initialize user and tenant tracking without triggering cleanup on first load
  private initializeUserTenantTracking(): void {
    const userDetails = localStorage.getItem('userDetails');
    this.userId = userDetails ? JSON.parse(userDetails)?.sub || '' : '';
    this.tenantId = getTenantName() || '';

    // Set initial known values to current values (no cleanup on first load)
    this.lastKnownUserId = this.userId;
    this.lastKnownTenantId = this.tenantId;
  }

  // Update user and tenant IDs and clear metadata if they changed
  updateUserAndTenantId(): void {
    const userDetails = localStorage.getItem('userDetails');
    const newUserId = userDetails ? JSON.parse(userDetails)?.sub || '' : '';
    const newTenantId = getTenantName() || '';

    // Check if user or tenant has changed
    const userChanged = this.lastKnownUserId && this.lastKnownUserId !== newUserId;
    const tenantChanged = this.lastKnownTenantId && this.lastKnownTenantId !== newTenantId;

    if (userChanged || tenantChanged) {
      console.log('User or tenant changed, clearing relevant metadata:', {
        userChanged,
        tenantChanged,
        oldUserId: this.lastKnownUserId,
        newUserId,
        oldTenantId: this.lastKnownTenantId,
        newTenantId
      });

      // Clear user/tenant specific metadata when they change
      this.clearUserTenantSpecificMetadata();
    }

    // Update current values
    this.userId = newUserId;
    this.tenantId = newTenantId;
    this.lastKnownUserId = newUserId;
    this.lastKnownTenantId = newTenantId;
  }

  checkForUserTenantChanges(): void {
    this.updateUserAndTenantId();
  }

  private clearUserTenantSpecificMetadata(): void {
    if (!this.db) return;

    const transaction = this.db.transaction(this.metadataStoreName, 'readwrite');
    const store = transaction.objectStore(this.metadataStoreName);

    // Get all metadata to check which ones need to be cleared
    const getAllRequest = store.getAll();

    getAllRequest.onsuccess = () => {
      const allMetadata = getAllRequest.result;
      let clearedCount = 0;

      // Clear ALL metadata that belongs to the old user/tenant
      allMetadata.forEach((metadata: any) => {
        if (metadata._userId === this.lastKnownUserId ||
            metadata._tenantId === this.lastKnownTenantId) {
          console.log('Clearing metadata for user/tenant change:', metadata.key);
          store.delete(metadata.key);
          clearedCount++;
        }
      });

      console.log(`Cleared ${clearedCount} metadata entries for user/tenant change`);
    };

    getAllRequest.onerror = (event: any) => {
      console.error('Error getting metadata for cleanup:', event.target.error);
    };
  }

  initializeData(): void {
    if (!this.db) return;

    const transaction = this.db.transaction(
      [this.propertyStoreName, this.metadataStoreName],
      'readonly'
    );

    transaction.oncomplete = () => {
      this.checkAndLoadInitialData();
      this.storePropertyPermissionsInMetadata();
    };
    transaction.onerror = (e: any) => console.error('Transaction error:', e.target.error);
  }

  // Generic async database operation
  async performDbOperationAsync<T>(
    storeName: string,
    mode: IDBTransactionMode,
    operation: (store: IDBObjectStore) => Promise<T>
  ): Promise<T | null> {
    this.updateUserAndTenantId();

    if (!this.db) {
      console.error('IndexedDB not initialized');
      return null;
    }

    const transaction = this.db.transaction(storeName, mode);
    const store = transaction.objectStore(storeName);

    const result = await operation(store);
    return result;
  }

  // Store metadata
  async storeMetadataInIndexedDBAsync(key: string, value: any): Promise<void> {
    this.updateUserAndTenantId();

    const metadata = {
      key,
      value,
      _userId: this.userId,
      _tenantId: this.tenantId,
      timestamp: new Date().toISOString(),
    };

    await this.performDbOperationAsync<void>(
      this.metadataStoreName,
      'readwrite',
      async (store) => {
        const request = store.put(metadata);
        return new Promise<void>((resolve) => {
          request.onsuccess = () => resolve();
          request.onerror = () => {
            console.error('Error storing metadata:', request.error);
            resolve();
          };
        });
      }
    );
  }

  // Get metadata
  async getMetadataFromIndexedDBAsync(key: string): Promise<any> {
    this.updateUserAndTenantId();

    const result = await this.performDbOperationAsync<any>(
      this.metadataStoreName,
      'readonly',
      async (store) => {
        const request = store.get(key);
        return new Promise<any>((resolve) => {
          request.onsuccess = () => {
            const result = request.result;

            if (result &&
                result._userId === this.userId &&
                result._tenantId === this.tenantId) {
              resolve(result.value || null);
            } else {
              if (result && (result._userId !== this.userId || result._tenantId !== this.tenantId)) {
                console.log(`Metadata '${key}' belongs to different user/tenant, ignoring:`, {
                  metadataUserId: result._userId,
                  metadataTenantId: result._tenantId,
                  currentUserId: this.userId,
                  currentTenantId: this.tenantId
                });
              }
              resolve(null);
            }
          };

          request.onerror = (event: any) => {
            console.error('Error getting metadata:', event.target.error);
            resolve(null);
          };
        });
      }
    );

    return result;
  }

  // Utility methods
  filterNonArchivedProperties(properties: any[]): any[] {
    return properties.filter(property => property.isArchived !== true);
  }

  addContextToProperties(properties: any[]): any[] {
    return properties.map(property => ({
      ...property,
      _userId: this.userId,
      _tenantId: this.tenantId,
      _timestamp: new Date().toISOString()
    }));
  }

  storePropertyPermissionsInMetadata(): void {
    // Get permissions from localStorage
    const storedPermissions = localStorage.getItem('userPermissions');

    if (!storedPermissions) {
      return;
    }

    const allPermissions = storedPermissions.split(',');

    // Check for view permissions to determine the permission value
    const canView = allPermissions.includes('Permissions.Properties.View');
    const canViewAssigned = allPermissions.includes('Permissions.Properties.ViewAssigned');

    const permissionValue = canView ? 2 : canViewAssigned ? 1 : 0;
    if (this.isInPropertiesModule()) {
      this.storePropertyPermissionValueAsync(permissionValue);
    }
  }

  async checkAndLoadInitialData(): Promise<void> {
    const isEmpty = await this.checkIfPropertiesStoreIsEmptyAsync();
    if (isEmpty && this.isInPropertiesModule()) {
      console.log('Properties store is empty in properties module - fetching initial data');
      await this.fetchInitialPropertiesAsync();
    } else if (!isEmpty) {
      await this.resumeIncompleteBackgroundFetch();
    }
  }

  async resumeIncompleteBackgroundFetch(): Promise<void> {
    const status = await this.getBatchProcessingStatusAsync();
    if (status.inProgress) {
      console.log('Found incomplete batch processing:', status);
      console.log(`Resuming from page ${status.nextPageToProcess || 2} in sequential order`);

      const propertyCount = await this.getMetadataFromIndexedDBAsync('property_count');
      const totalCount = propertyCount?.allPropertiesCount || status.total * this.BATCH_PAGE_SIZE;
      console.log('Resuming background fetch with count:', totalCount);

      await this.fetchPropertiesInBackgroundAsync(totalCount, {
        permission: status.permission,
        path: status.path
      });
    }
  }

  // Store properties
  async storePropertiesInIndexedDBAsync(properties: any[]): Promise<void> {
    if (!properties?.length) {
      return;
    }

    const processedProperties = this.addContextToProperties(
      this.filterNonArchivedProperties(properties)
    );

    if (!processedProperties.length) {
      return;
    }

    await this.performDbOperationAsync<void>(
      this.propertyStoreName,
      'readwrite',
      async (store) => {
        const promises = processedProperties
          .filter(property => property.id)
          .map(property => {
            return new Promise<void>((resolve) => {
              const request = store.put(property);
              request.onsuccess = () => resolve();
              request.onerror = () => {
                console.error('Error storing property:', property.id, request.error);
                resolve();
              };
            });
          });

        await Promise.all(promises);
      }
    );
  }

  // Property permission operations
  async getPropertyPermissionValueAsync(): Promise<number | null> {
    const permissionValue = await this.getMetadataFromIndexedDBAsync('properties_permission');

    if (permissionValue !== null && typeof permissionValue === 'number') {
      return permissionValue;
    }
    return null;
  }

  async storePropertyPermissionValueAsync(permissionValue: number): Promise<void> {
    await this.storeMetadataInIndexedDBAsync('properties_permission', permissionValue);
    console.log('Successfully stored property permission value in metadata store:', permissionValue);
  }

  async updatePropertyPermissionIfChangedAsync(permissionValue: number): Promise<boolean> {
    const storedValue = await this.getPropertyPermissionValueAsync();

    if (storedValue === null || storedValue !== permissionValue) {
      console.log('Property permission value changed or not set, updating:', {
        stored: storedValue,
        current: permissionValue
      });

      const payload: any = await firstValueFrom(this.store.select(getFiltersPayload).pipe(take(1)));
      console.log('Current filter payload for clearing IndexedDB:', payload);
      this.store.dispatch(new ClearIndexedDB(payload));

      const countPayload = {
        ...payload,
        permission: permissionValue
      };
      await this.fetchPropertyCountAndStoreAsync(countPayload);
      return true;
    }

    return true;
  }

  // Utility methods
  async checkIfPropertiesStoreIsEmptyAsync(): Promise<boolean> {
    const count = await this.countPropertiesInIndexedDBAsync();
    return count === 0;
  }

  async countPropertiesInIndexedDBAsync(): Promise<number> {
    const result = await this.performDbOperationAsync<number>(
      this.propertyStoreName,
      'readonly',
      async (store) => {
        const countRequest = store.count();
        return new Promise<number>((resolve) => {
          countRequest.onsuccess = () => resolve(countRequest.result);
          countRequest.onerror = () => resolve(0);
        });
      }
    );

    return result || 0;
  }

  isInPropertiesModule(): boolean {
    return window.location.pathname.toLowerCase().includes('properties/manage-properties');
  }

  // Property count operations
  async fetchPropertyCountAndStoreAsync(payload: any): Promise<any> {
    console.log('Fetching property count with payload:', payload);

    const response: any = await firstValueFrom(this.propertyService.fetchPropertyCount(payload));

    if (response && response.succeeded) {
      const count = response.data;
      console.log('Got property count from API:', count);

      await this.storeMetadataInIndexedDBAsync('property_count', count);
      console.log('Successfully stored property count in IndexedDB:', count);
      return response;
    } else {
      console.error('Invalid response from property count API:', response);
      return response;
    }
  }

  async getPropertyCountFromIndexedDBAsync(): Promise<any> {
    const count = await this.getMetadataFromIndexedDBAsync('property_count');

    if (count !== null) {
      console.log('Got property count from IndexedDB:', count);
      return {
        succeeded: true,
        data: count,
      };
    } else {
      console.log('No property count found in IndexedDB');
      return {
        succeeded: false,
        error: 'No property count found in IndexedDB',
      };
    }
  }

  async getPropertyCountAsync(payload: any): Promise<any> {
    console.log('Getting property count with payload:', payload);

    const response = await this.getPropertyCountFromIndexedDBAsync();

    if (response.succeeded) {
      console.log('Using property count from IndexedDB:', response.data);
      return response;
    } else {
      console.log('Property count not found in IndexedDB, fetching from API');
      return await this.fetchPropertyCountAndStoreAsync(payload);
    }
  }

  // Check for updates
  async checkForUpdatesAsync(): Promise<any> {
    this.updateUserAndTenantId();

    const response: any = await firstValueFrom(this.masterDataService.fetchLastModifiedList());

    if (response && response.succeeded && response.data) {
      const apiLastModifiedDate = response.data.Property;
      const storedLastModifiedDate = await this.getMetadataFromIndexedDBAsync('lastmodifieddata_property');

      const hasChanges = apiLastModifiedDate !== storedLastModifiedDate || storedLastModifiedDate === null;

      return {
        succeeded: true,
        apiLastModifiedDate,
        storedLastModifiedDate,
        hasChanges,
      };
    } else {
      console.error('Invalid response from lastModifiedDates API:', response);
      return {
        succeeded: false,
        error: 'Invalid response from lastModifiedDates API',
      };
    }
  }

  // Main data sync method
  async checkAndUpdateDataAsync(payload: any = {}): Promise<any> {
    this.updateUserAndTenantId();
    this.filtersPayload = payload;

    console.log('checkAndUpdateDataAsync called with payload:', payload);
    console.log('Current module/route:', window.location.pathname);
    console.log('In properties module:', this.isInPropertiesModule());

    if (payload && typeof payload.permission === 'number') {
      await this.updatePropertyPermissionIfChangedAsync(payload.permission);
      this.storePropertyPermissionsInMetadata();
    }

    if (!this.isInPropertiesModule()) {
      console.log('Not in properties module - skipping property data update check');
      return {
        succeeded: true,
        updated: false,
        message: 'Property data update check skipped - not in properties module',
        notInPropertiesModule: true
      };
    }

    const updateResponse = await this.checkForUpdatesAsync();

    if (updateResponse && updateResponse.succeeded) {
      const apiLastModifiedDate = updateResponse.apiLastModifiedDate;
      const storedLastModifiedDate = updateResponse.storedLastModifiedDate;
      const hasChanges = updateResponse.hasChanges;

      if (hasChanges) {
        await this.fetchPropertyCountAndStoreAsync(payload);

        if (storedLastModifiedDate === null) {
          return await this.fetchInitialPropertiesAsync(payload);
        }

        if (!storedLastModifiedDate) {
          console.warn('storedLastModifiedDate is null or empty, skipping modifiedon API call');
          await this.storeMetadataInIndexedDBAsync('lastmodifieddata_property', apiLastModifiedDate);
          console.log('Updated lastModifiedDate in IndexedDB without calling modifiedon API');
          return {
            succeeded: true,
            updated: true,
            message: 'LastModifiedDate updated, skipped modifiedon API call',
            updatedCount: 0,
          };
        }

        return await this.processModifiedPropertiesAsync(storedLastModifiedDate, apiLastModifiedDate, payload);
      } else {
        console.log('No data changes detected. Data is up-to-date.');

        if (payload && typeof payload.permission === 'number') {
          const storedPermissionValue = await this.getPropertyPermissionValueAsync();

          if (storedPermissionValue === null || storedPermissionValue !== payload.permission) {
            console.log('Permission changed, updating property count even though data is up-to-date');
            await this.fetchPropertyCountAndStoreAsync(payload);
            return {
              succeeded: true,
              updated: true,
              message: 'Data is up-to-date, but property count updated due to permission change',
            };
          }
        }

        return {
          succeeded: true,
          updated: false,
          message: 'Data is already up-to-date',
        };
      }
    } else {
      console.error('Error checking for updates:', updateResponse?.error);
      return {
        succeeded: false,
        updated: false,
        error: 'Error checking for updates: ' + updateResponse?.error,
      };
    }
  }

  // Process modified properties
  async processModifiedPropertiesAsync(storedLastModifiedDate: string, apiLastModifiedDate: string, payload: any): Promise<any> {
    const modifiedResponse: any = await firstValueFrom(this.propertyService.getModifiedProperties({
      lastModifiedDate: storedLastModifiedDate,
      pageNumber: 1,
      pageSize: 500,
      permission: payload?.permission
    }));

    if (modifiedResponse && modifiedResponse.succeeded && modifiedResponse.items) {
      console.log(`Retrieved ${modifiedResponse.items.length} modified properties`);

      const nonArchivedProperties = modifiedResponse.items.filter(
        (property: any) => property.isArchived !== true
      );

      const archivedProperties = modifiedResponse.items.filter(
        (property: any) => property.isArchived === true
      );

      if (archivedProperties.length > 0) {
        const archivedIds = archivedProperties.map((property: any) => property.id);
        await this.removePropertiesFromIndexedDBAsync(archivedIds);
        console.log(`Removed ${archivedIds.length} archived properties from IndexedDB`);
      }

      console.log(`After filtering, have ${nonArchivedProperties.length} non-archived properties to update`);

      if (nonArchivedProperties.length > 0) {
        await this.storePropertiesInIndexedDBAsync(nonArchivedProperties);
        console.log('Successfully stored modified properties in IndexedDB');

        await this.storeMetadataInIndexedDBAsync('lastmodifieddata_property', apiLastModifiedDate);
        console.log('Successfully updated lastModifiedDate in IndexedDB');

        await this.fetchPropertyCountAndStoreAsync(payload);

        return {
          succeeded: true,
          updated: true,
          message: 'Data updated successfully',
          updatedCount: nonArchivedProperties.length,
        };
      } else {
        await this.storeMetadataInIndexedDBAsync('lastmodifieddata_property', apiLastModifiedDate);
        console.log('No non-archived properties to update, updated lastModifiedDate only');

        await this.fetchPropertyCountAndStoreAsync(payload);

        return {
          succeeded: true,
          updated: true,
          message: 'LastModifiedDate updated, no properties to update',
          updatedCount: 0,
        };
      }
    } else {
      console.error('Invalid response from modifiedon API:', modifiedResponse);
      return {
        succeeded: false,
        updated: false,
        error: 'Invalid response from modifiedon API',
      };
    }
  }

  // Remove properties from IndexedDB
  async removePropertiesFromIndexedDBAsync(propertyIds: string[]): Promise<void> {
    if (!propertyIds?.length) {
      return;
    }

    await this.performDbOperationAsync<void>(
      this.propertyStoreName,
      'readwrite',
      async (store) => {
        const promises = propertyIds.map(id => {
          return new Promise<void>((resolve) => {
            const request = store.delete(id);
            request.onsuccess = () => resolve();
            request.onerror = (event: any) => {
              console.error(`Error removing property ${id} from IndexedDB:`, event.target.error);
              resolve();
            };
          });
        });

        await Promise.all(promises);
        console.log(`Successfully removed ${propertyIds.length} properties from IndexedDB`);
      }
    );
  }

  // Enhanced fetchInitialProperties with batch processing
  async fetchInitialPropertiesAsync(payload: any = {}): Promise<any> {
    this.updateUserAndTenantId();
    this.filtersPayload = payload;

    const lastModifiedResponse: any = await firstValueFrom(this.masterDataService.fetchLastModifiedList());

    if (lastModifiedResponse && lastModifiedResponse.succeeded && lastModifiedResponse.data) {
      const lastModifiedDate = lastModifiedResponse.data.Property;

      await this.storeMetadataInIndexedDBAsync('lastmodifieddata_property', lastModifiedDate);

      const pageSize = payload.pageSize || 10;
      const pageNumber = payload.pageNumber || 1;

      // First, get the total count to determine how many batches we need
      const countParams = {
        pageNumber: pageSize ,
        pageSize: pageNumber,
        path: payload.path,
        permission: payload.permission || 1,
      };

      console.log('Fetching initial data with params from payload:', countParams);
      const initialResponse: any = await firstValueFrom(this.propertyService.getPropertiesNewAll(countParams));

      if (initialResponse && initialResponse.succeeded) {
        const totalCount = initialResponse.totalCount || 0;
        console.log(`Total properties available: ${totalCount}`);

        // Store property count first
        await this.fetchPropertyCountAndStoreAsync(payload);

        // Process the initial response
        if (initialResponse.items && initialResponse.items.length > 0) {
          const nonArchivedProperties = initialResponse.items.filter(
            (property: any) => property.isArchived !== true
          );

          await this.storePropertiesInIndexedDBAsync(nonArchivedProperties);
          console.log(`Stored initial data: ${nonArchivedProperties.length} properties`);
        }

        // Start batch processing for all data from page 1 onwards
        console.log(`Starting batch processing for all ${totalCount} properties from page 1 onwards`);
        this.fetchPropertiesInBackgroundAsync(totalCount, {
          permission: payload.permission,
          path: payload.path
        });

        // Return the initial response for immediate UI display
        const response = {
          ...initialResponse,
          items: initialResponse.items ? initialResponse.items.filter(
            (property: any) => property.isArchived !== true
          ) : [],
          pageNumber: pageNumber,
          pageSize: pageSize
        };

        return response;
      } else {
        console.error('Invalid response from Property/new/all API:', initialResponse);
        return {
          succeeded: false,
          error: 'Invalid response from Property/new/all API',
        };
      }
    } else {
      console.error('Invalid response from lastModifiedDates API:', lastModifiedResponse);
      return {
        succeeded: false,
        error: 'Invalid response from lastModifiedDates API',
      };
    }
  }

  // Validate sequential order
  private validateSequentialOrder(completedBatches: number[]): boolean {
    const sortedBatches = [...completedBatches].sort((a, b) => a - b);
    for (let i = 0; i < sortedBatches.length - 1; i++) {
      if (sortedBatches[i + 1] - sortedBatches[i] !== 1) {
        console.warn(`⚠️ ORDER WARNING: Gap detected between page ${sortedBatches[i]} and ${sortedBatches[i + 1]}`);
        return false;
      }
    }
    return true;
  }

  // Enhanced background processing with sequential page order
  async fetchPropertiesInBackgroundAsync(totalCount: number, payload: any = {}): Promise<void> {
    const recordsToFetch = Math.min(totalCount, this.MAX_RECORDS);
    const numPages = Math.ceil(recordsToFetch / this.BATCH_PAGE_SIZE);

    console.log(`📊 BACKGROUND PROCESSING: ${recordsToFetch} records in ${numPages} batches of ${this.BATCH_PAGE_SIZE}`);
    console.log(`📋 SEQUENTIAL ORDER: Pages will be processed as 1 → 2 → 3 → 4 → ... → ${numPages}`);

    const batchProgress = await this.getMetadataFromIndexedDBAsync('property_batch_progress');

    let nextPageToProcess = 1; // Start from page 1 to populate IndexedDB completely
    let completedBatches: number[] = []; // No pages completed initially

    if (batchProgress) {
      completedBatches = batchProgress.completedBatches || [];
      console.log('Existing batch progress found:', completedBatches);

      // Find the next page in sequential order that hasn't been completed
      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        if (!completedBatches.includes(pageNum)) {
          nextPageToProcess = pageNum;
          console.log(`Next page to process in order: ${nextPageToProcess}`);
          break;
        }
      }

      // If all batches are complete, no need to continue
      if (completedBatches.length >= numPages) {
        console.log('All batches already completed');
        return;
      }
    } else {
      // Initialize batch progress starting from page 1
      await this.storeMetadataInIndexedDBAsync('property_batch_progress', {
        totalPages: numPages,
        completedBatches: [], // No pages completed initially
        startTime: new Date().toISOString(),
        totalCount: totalCount,
        permission: payload?.permission,
        path: payload?.path,
        nextPageToProcess: 1
      });
    }

    console.log(`🔄 STARTING SEQUENTIAL PROCESSING: Page ${nextPageToProcess} → ${numPages} (starting from page 1)`);
    this.processBatchesInOrderAsync(nextPageToProcess, numPages, completedBatches, payload?.permission, payload?.path);
  }

  async processBatchesInOrderAsync(
    currentPage: number,
    totalPages: number,
    completedBatches: number[],
    permission?: string,
    path?: string
  ): Promise<void> {
    // Check if we've processed all pages
    if (currentPage > totalPages) {
      console.log('All batches completed successfully in sequential order');
      // Mark batch processing as complete
      await this.storeMetadataInIndexedDBAsync('property_batch_progress', {
        totalPages: totalPages,
        completedBatches: Array.from({length: totalPages}, (_, i) => i + 1),
        lastUpdated: new Date().toISOString(),
        completed: true,
        permission: permission,
        path: path,
        nextPageToProcess: totalPages + 1
      });
      return;
    }

    // Skip if this page is already completed
    if (completedBatches.includes(currentPage)) {
      console.log(`Page ${currentPage} already completed, moving to next page in order`);
      await this.processBatchesInOrderAsync(currentPage + 1, totalPages, completedBatches, permission, path);
      return;
    }

    console.log(`🔄 PROCESSING PAGE ${currentPage} of ${totalPages} in sequential order (${this.BATCH_PAGE_SIZE} records per page)`);

    const params = {
      pageNumber: currentPage,
      pageSize: this.BATCH_PAGE_SIZE,
      path: 'Property/new/all',
      permission: permission
    };

    const response: any = await firstValueFrom(this.propertyService.getPropertiesNewAll(params));

    if (response?.succeeded && response.items) {
      const nonArchivedProperties = this.filterNonArchivedProperties(response.items);
      console.log(`✅ PAGE ${currentPage} SUCCESS: Received ${response.items.length} properties, storing ${nonArchivedProperties.length} non-archived`);

      await this.storePropertiesInIndexedDBAsync(nonArchivedProperties);
      console.log(`✅ PAGE ${currentPage} STORED: Properties saved to IndexedDB`);
      await this.updateBatchProgressInOrderAsync(currentPage, totalPages, completedBatches, response.totalCount || totalPages * this.BATCH_PAGE_SIZE, permission, path);
    } else {
      console.error(`❌ PAGE ${currentPage} FAILED: Moving to next page in order`);
      await this.scheduleNextPageInOrderAsync(currentPage + 1, totalPages, completedBatches, permission, path);
    }
  }

  async updateBatchProgressInOrderAsync(
    currentPage: number,
    totalPages: number,
    completedBatches: number[],
    totalCount: number,
    permission?: string,
    path?: string
  ): Promise<void> {
    const updatedCompletedBatches = [...completedBatches, currentPage];
    const progressPercentage = Math.round((updatedCompletedBatches.length / totalPages) * 100);

    console.log(`✅ PAGE ${currentPage} COMPLETED in order. Progress: ${updatedCompletedBatches.length}/${totalPages} (${progressPercentage}%)`);
    console.log(`📋 COMPLETED PAGES: [${updatedCompletedBatches.sort((a, b) => a - b).join(', ')}]`);

    // Validate sequential order
    const isSequential = this.validateSequentialOrder(updatedCompletedBatches);
    if (isSequential) {
      console.log(`✅ ORDER VERIFIED: Pages are being processed in correct sequential order`);
    }

    const nextPageToProcess = currentPage + 1;

    await this.storeMetadataInIndexedDBAsync('property_batch_progress', {
      totalPages: totalPages,
      completedBatches: updatedCompletedBatches,
      lastUpdated: new Date().toISOString(),
      totalCount: totalCount,
      permission: permission,
      path: path,
      progressPercentage: progressPercentage,
      nextPageToProcess: nextPageToProcess
    });

    // Check if all batches are completed
    if (updatedCompletedBatches.length >= totalPages) {
      console.log('All pages completed in sequential order! Background processing finished.');
      await this.storeMetadataInIndexedDBAsync('property_batch_progress', {
        totalPages: totalPages,
        completedBatches: updatedCompletedBatches,
        lastUpdated: new Date().toISOString(),
        totalCount: totalCount,
        permission: permission,
        path: path,
        progressPercentage: 100,
        completed: true,
        completedAt: new Date().toISOString(),
        nextPageToProcess: totalPages + 1
      });
      return;
    }

    await this.scheduleNextPageInOrderAsync(nextPageToProcess, totalPages, updatedCompletedBatches, permission, path);
  }

  async scheduleNextPageInOrderAsync(
    nextPage: number,
    totalPages: number,
    completedBatches: number[],
    permission?: string,
    path?: string
  ): Promise<void> {
    if (nextPage <= totalPages) {
      console.log(`⏰ SCHEDULING PAGE ${nextPage} in sequential order in ${this.BATCH_DELAY_MS}ms...`);
      setTimeout(() => {
        this.processBatchesInOrderAsync(nextPage, totalPages, completedBatches, permission, path);
      }, this.BATCH_DELAY_MS);
    } else {
      console.log('🎉 ALL PAGES SCHEDULED for processing in sequential order');
    }
  }

  // Enhanced getPropertiesFromIndexedDB method with comprehensive logic
  async getPropertiesFromIndexedDBAsync(payload: any): Promise<any> {
    console.log('🔄 INDEXEDDB INTERNAL: Starting getPropertiesFromIndexedDBAsync');

    try {
      // Update user and tenant IDs before retrieving
      this.updateUserAndTenantId();

      // Store the payload for later use
      this.filtersPayload = payload;

      // First check for updates and update IndexedDB if needed
      await this.checkAndUpdateDataAsync(payload);

    if (!this.db) {
      console.error('IndexedDB not initialized in getPropertiesFromIndexedDBAsync');
      return {
        succeeded: false,
        error: 'IndexedDB not initialized'
      };
    }

    const result = await this.performDbOperationAsync<any>(
      this.propertyStoreName,
      'readonly',
      async (store) => {
        const request = store.getAll();
        return new Promise<any>((resolve) => {
          request.onsuccess = async () => {
            const properties = request.result;

            if (properties.length === 0) {
              console.warn('No properties found in IndexedDB. Checking if we should fetch initial data from API.');

              // Only fetch initial data if we're in the properties module
              if (!this.isInPropertiesModule()) {
                console.log('Not in properties module - skipping initial data fetch');
                resolve({
                  succeeded: true,
                  items: [],
                  totalCount: 0,
                  pageNumber: payload.pageNumber,
                  pageSize: payload.pageSize,
                  totalPages: 0,
                  noDataInIndexedDB: true,
                  notInPropertiesModule: true
                });
                return;
              }

              console.log('In properties module - fetching initial data from API');
              // First, get the lastModifiedDate from the API
              const lastModifiedResponse: any = await firstValueFrom(this.masterDataService.fetchLastModifiedList());

              if (lastModifiedResponse && lastModifiedResponse.succeeded && lastModifiedResponse.data) {
                // Store the lastModifiedDate in IndexedDB
                const lastModifiedDate = lastModifiedResponse.data.Property;

                await this.storeMetadataInIndexedDBAsync('lastmodifieddata_property', lastModifiedDate);

                // STEP 1: First fetch data based on current pageNumber and pageSize for immediate display
                const currentPageParams = {
                  pageNumber: payload.pageNumber,
                  pageSize: payload.pageSize,
                  permission: payload.permission,
                  path: payload.path
                };

                console.log('🔄 STEP 1: Fetching current page data for immediate display with params:', currentPageParams);
                const currentPageResponse: any = await firstValueFrom(this.propertyService.getPropertiesNewAll(currentPageParams));

                if (currentPageResponse && currentPageResponse.succeeded && currentPageResponse.items) {
                  console.log(`✅ CURRENT PAGE SUCCESS: Received ${currentPageResponse.items.length} properties for page ${payload.pageNumber}, total count: ${currentPageResponse.totalCount}`);

                  // Filter out archived properties
                  const nonArchivedProperties = currentPageResponse.items.filter(
                    (property: any) => property.isArchived !== true
                  );

                  console.log(`✅ CURRENT PAGE FILTERED: ${nonArchivedProperties.length} non-archived properties`);

                  // Return the API response for immediate display
                  const apiResponse = {
                    ...currentPageResponse,
                    items: nonArchivedProperties,
                    pageNumber: payload.pageNumber,
                    pageSize: payload.pageSize,
                    fromAPI: true,
                    batchProcessing: true
                  };

                  resolve(apiResponse);

                  this.fetchPropertyCountAndStoreAsync(this.filtersPayload);

                  // STEP 2: Start batch-wise fetching from page 1 onwards to populate IndexedDB
                  console.log('🚀 STEP 2: Starting batch-wise fetching from page 1 onwards to populate IndexedDB');
                  this.fetchPropertiesInBackgroundAsync(currentPageResponse.totalCount, {
                    permission: payload.permission,
                    path: payload.path
                  });
                } else {
                  console.error('Invalid response from Property/new/all API:', currentPageResponse);
                  this.handleEmptyResponse(payload, resolve);
                }
              } else {
                console.error('Invalid response from lastModifiedDates API:', lastModifiedResponse);
                this.handleEmptyResponse(payload, resolve);
              }

              return;
            }

            // Update user and tenant IDs before filtering
            this.updateUserAndTenantId();
            console.log('Using userId:', this.userId, 'tenantId:', this.tenantId, 'for filtering properties');

            // Filter properties for this user and tenant
            let filteredProperties = properties.filter(
              (property: any) =>
                property._userId === this.userId &&
                property._tenantId === this.tenantId
            );

            console.log(`Filtered to ${filteredProperties.length} properties for user ${this.userId} and tenant ${this.tenantId}`);

            // Sort properties by status and lastModifiedOn
            filteredProperties.sort((a: any, b: any) => {
              if (a.status !== b.status) {
                return b.status ? -1 : 1;
              }

              const dateFieldA = a.lastModifiedOn || a.lastModifiedDate;
              const dateFieldB = b.lastModifiedOn || b.lastModifiedDate;

              const dateA = new Date(dateFieldA).getTime();
              const dateB = new Date(dateFieldB).getTime();
              return dateB - dateA;
            });

            // Apply pagination
            const pageNumber = payload.pageNumber;
            const pageSize = payload.pageSize;
            const startIndex = (pageNumber - 1) * pageSize;
            const endIndex = startIndex + pageSize;

            // Check if we have enough data in filteredProperties for this page
            if (startIndex >= filteredProperties.length) {
              console.log(`Not enough data in IndexedDB for page ${pageNumber} (start index ${startIndex} >= filtered length ${filteredProperties.length}), calling API`);

              // Get the batch processing status to check if background processing is still in progress
              const status = await this.getBatchProcessingStatusAsync();

              // Continue background processing if it's not complete
              if (status.inProgress) {
                console.log('Background processing is still in progress, continuing...');
              }

              const apiResponse: any = await firstValueFrom(this.propertyService.getPropertiesNewAll({
                pageNumber: pageNumber,
                pageSize: pageSize,
                path: payload.path,
                permission: payload.permission
              }));

              if (apiResponse && apiResponse.succeeded) {
                // Filter out archived properties
                const nonArchivedProperties = apiResponse.items
                  ? apiResponse.items.filter((property: any) => property.isArchived !== true)
                  : [];

                const apiTotalCount = apiResponse.totalCount || 0;
                console.log(`Got data and totalCount from API: ${apiTotalCount}`);

                this.fetchPropertyCountAndStoreAsync(this.filtersPayload);

                // Store the properties in IndexedDB in the background
                if (nonArchivedProperties.length > 0) {
                  // Add user and tenant IDs to each property before storing
                  nonArchivedProperties.forEach((property: any) => {
                    property._userId = this.userId;
                    property._tenantId = this.tenantId;
                  });

                  await this.storePropertiesInIndexedDBAsync(nonArchivedProperties);
                  console.log('Successfully stored properties from API in IndexedDB');

                  // Start background fetching of all properties
                  this.fetchPropertiesInBackgroundAsync(apiTotalCount, {
                    permission: payload.permission,
                    path: payload.path
                  });
                }

                const response: any = {
                  ...apiResponse,
                  items: nonArchivedProperties,
                  fromAPI: true,
                };

                resolve(response);
              } else {
                // If API call fails, return empty response
                const response: any = {
                  succeeded: true,
                  items: [],
                  totalCount: 0,
                  pageNumber: pageNumber,
                  pageSize: pageSize,
                  totalPages: 0,
                  noDataInIndexedDB: true,
                  fromAPI: true,
                };

                resolve(response);
              }
              return;
            }

            // We have enough data in IndexedDB for this page
            const paginatedProperties = filteredProperties.slice(startIndex, endIndex);

            console.log(`Applied pagination: page ${pageNumber}, size ${pageSize}, showing items ${startIndex + 1}-${Math.min(endIndex, filteredProperties.length)} of ${filteredProperties.length}`);

            // Get the stored total count from metadata
            const propertyCount: any = await this.getMetadataFromIndexedDBAsync('property_count');

            if (propertyCount !== null && propertyCount.allPropertiesCount !== undefined) {
              // Use the allPropertiesCount from the detailed property count
              const totalCount = propertyCount.allPropertiesCount;
              console.log(`Using allPropertiesCount from detailed property count: ${totalCount} (filtered: ${filteredProperties.length})`);

              const response: any = {
                succeeded: true,
                items: paginatedProperties,
                totalCount: totalCount,
                pageNumber: pageNumber,
                pageSize: pageSize,
                totalPages: Math.ceil(totalCount / pageSize),
              };

              resolve(response);
            } else {
              // If stored totalCount is null, call the API to get the correct totalCount
              console.log('Stored totalCount is null, calling API to get the correct totalCount');

              const apiResponse: any = await firstValueFrom(this.propertyService.getPropertiesNewAll({
                pageNumber: payload.pageNumber,
                pageSize: payload.pageSize,
                path: payload.path,
                permission: payload.permission,
              }));

              if (apiResponse && apiResponse.succeeded) {
                const totalCount = apiResponse.totalCount;
                console.log('Got totalCount from API:', totalCount);

                // Update the detailed property count
                this.fetchPropertyCountAndStoreAsync(payload);

                const response: any = {
                  succeeded: true,
                  items: paginatedProperties,
                  totalCount: totalCount,
                  pageNumber: pageNumber,
                  pageSize: pageSize,
                  totalPages: Math.ceil(totalCount / pageSize),
                };

                resolve(response);
              } else {
                // Fall back to using filteredProperties.length if API call fails
                console.error('API call failed, falling back to filteredProperties.length');
                const response: any = {
                  succeeded: true,
                  items: paginatedProperties,
                  totalCount: filteredProperties.length,
                  pageNumber: pageNumber,
                  pageSize: pageSize,
                  totalPages: Math.ceil(filteredProperties.length / pageSize),
                };

                resolve(response);
              }
            }
          };

          request.onerror = (event: any) => {
            console.error('Error getting properties from IndexedDB:', event.target.error);
            resolve({
              succeeded: false,
              error: 'Error getting properties from IndexedDB: ' + event.target.error
            });
          };
        });
      }
    );

    return result;
    } catch (error) {
      console.error('🚨 INDEXEDDB ERROR: Error in getPropertiesFromIndexedDBAsync:', error);
      return {
        succeeded: false,
        error: 'Error retrieving data from IndexedDB: ' + error
      };
    }
  }

  private handleEmptyResponse(payload: any, resolve: any): void {
    const response: any = {
      succeeded: true,
      items: [],
      totalCount: 0,
      pageNumber: payload.pageNumber,
      pageSize: payload.pageSize,
      totalPages: 0,
      noDataInIndexedDB: true,
    };
    resolve(response);
  }

  // Enhanced clearPropertyDataFromIndexedDB method
  async clearPropertyDataFromIndexedDBAsync(payload: any = {}): Promise<void> {
    console.log('Clearing IndexedDB with payload:', payload);

    if (!this.db) {
      console.error('IndexedDB not initialized in clearPropertyDataFromIndexedDBAsync');
      return;
    }

    await this.performDbOperationAsync<void>(
      this.propertyStoreName,
      'readwrite',
      async (store) => {
        const clearRequest = store.clear();
        return new Promise<void>((resolve) => {
          clearRequest.onsuccess = () => resolve();
          clearRequest.onerror = () => resolve();
        });
      }
    );

    await this.performDbOperationAsync<void>(
      this.metadataStoreName,
      'readwrite',
      async (store) => {
        const getAllRequest = store.getAllKeys();
        return new Promise<void>((resolve) => {
          getAllRequest.onsuccess = () => {
            const keys = getAllRequest.result;
            const deletePromises = keys
              .filter(key => key !== 'properties_permission')
              .map(key => {
                return new Promise<void>((deleteResolve) => {
                  const deleteRequest = store.delete(key);
                  deleteRequest.onsuccess = () => deleteResolve();
                  deleteRequest.onerror = () => deleteResolve();
                });
              });

            Promise.all(deletePromises).then(() => resolve());
          };
          getAllRequest.onerror = () => resolve();
        });
      }
    );

    console.log('Successfully cleared IndexedDB data');

    // After clearing, fetch property count first to ensure it's updated with current permissions
    await this.fetchPropertyCountAndStoreAsync(payload);
    console.log('Property count updated after clearing IndexedDB');

    // Then fetch initial data with the payload from the component
    // This ensures we use the same pagination parameters that were in use before clearing
    await this.fetchInitialPropertiesAsync(payload);
    console.log('Successfully fetched initial data after clearing cache');
  }

  // Enhanced getPropertyByIdFromIndexedDB method
  async getPropertyByIdFromIndexedDBAsync(id: string): Promise<any> {
    // Update user and tenant IDs before retrieving
    this.updateUserAndTenantId();
    console.log('Getting property by ID from IndexedDB with userId:', this.userId, 'tenantId:', this.tenantId);

    // First check for updates and update IndexedDB if needed
    await this.checkAndUpdateDataAsync({});

    if (!this.db) {
      console.error('IndexedDB not initialized in getPropertyByIdFromIndexedDBAsync');
      return null;
    }

    const result = await this.performDbOperationAsync<any>(
      this.propertyStoreName,
      'readonly',
      async (store) => {
        const request = store.get(id);
        return new Promise<any>((resolve) => {
          request.onsuccess = async () => {
            // Update user and tenant IDs before checking
            this.updateUserAndTenantId();

            if (request.result &&
                request.result._userId === this.userId &&
                request.result._tenantId === this.tenantId) {
              resolve(request.result);
            } else {
              // If property not found, check if we need to fetch initial data
              const isEmpty = await this.checkIfPropertiesStoreIsEmptyAsync();
              if (isEmpty) {
                console.warn('No properties found in IndexedDB. Fetching initial data from API.');

                // Fetch initial data and then try again
                await this.fetchInitialPropertiesAsync({});

                // Try to get the property again
                const retryTransaction = this.db!.transaction(this.propertyStoreName, 'readonly');
                const retryStore = retryTransaction.objectStore(this.propertyStoreName);
                const retryRequest = retryStore.get(id);

                retryRequest.onsuccess = () => {
                  if (retryRequest.result &&
                      retryRequest.result._userId === this.userId &&
                      retryRequest.result._tenantId === this.tenantId) {
                    resolve(retryRequest.result);
                  } else {
                    resolve(null);
                  }
                };

                retryRequest.onerror = (event: any) => {
                  console.error('Error getting property from IndexedDB on retry:', event.target.error);
                  resolve(null);
                };
              } else {
                // Properties exist but this specific one wasn't found
                resolve(null);
              }
            }
          };

          request.onerror = (event: any) => {
            console.error('Error getting property from IndexedDB:', event.target.error);
            resolve(null);
          };
        });
      }
    );

    return result;
  }

  // Delete entire database
  async deleteIndexedDBAsync(): Promise<void> {
    console.log('Deleting IndexedDB database...');

    if (this.db) {
      this.db.close();
      this.db = null;
    }

    return new Promise<void>((resolve, reject) => {
      const deleteRequest = indexedDB.deleteDatabase(this.dbName);

      deleteRequest.onsuccess = () => {
        console.log('IndexedDB database deleted successfully');
        this.initIndexedDB();
        resolve();
      };

      deleteRequest.onerror = (event: any) => {
        console.error('Error deleting IndexedDB database:', event.target.error);
        reject(new Error('Error deleting IndexedDB database: ' + event.target.error));
      };

      deleteRequest.onblocked = () => {
        console.error('IndexedDB deletion blocked');
        reject(new Error('IndexedDB deletion blocked'));
      };
    });
  }

  // Batch processing status with sequential order tracking
  async getBatchProcessingStatusAsync(): Promise<any> {
    const batchProgress = await this.getMetadataFromIndexedDBAsync('property_batch_progress');

    if (!batchProgress) {
      return {
        inProgress: false,
        completed: 0,
        total: 0,
        percentComplete: 0,
        completedBatches: [] as number[],
        permission: null as number | string | null,
        path: null as string | null,
        nextPageToProcess: 1
      };
    }

    const totalPages = batchProgress.totalPages || 0;
    const completedBatches = batchProgress.completedBatches || [];
    const permission = batchProgress.permission || null;
    const path = batchProgress.path || null;
    const nextPageToProcess = batchProgress.nextPageToProcess || 1;

    return {
      inProgress: completedBatches.length < totalPages && !batchProgress.completed,
      completed: completedBatches.length,
      total: totalPages,
      percentComplete: totalPages > 0 ? Math.round((completedBatches.length / totalPages) * 100) : 0,
      startTime: batchProgress.startTime,
      lastUpdated: batchProgress.lastUpdated,
      completedBatches: completedBatches.sort((a: number, b: number) => a - b), // Sort for display
      permission: permission,
      path: path,
      nextPageToProcess: nextPageToProcess,
      isCompleted: batchProgress.completed || false
    };
  }

  // Check if data range is available
  async isDataRangeAvailableInIndexedDBAsync(startRecord: number, endRecord: number): Promise<boolean> {
    const status = await this.getBatchProcessingStatusAsync();

    if (!status.inProgress && status.completed === status.total) {
      return true;
    }

    const batchForStartRecord = Math.ceil(startRecord / this.BATCH_PAGE_SIZE);
    const batchForEndRecord = Math.ceil(endRecord / this.BATCH_PAGE_SIZE);

    const completedBatches = status.completedBatches || [];
    for (let i = batchForStartRecord; i <= batchForEndRecord; i++) {
      if (!completedBatches.includes(i)) {
        return false;
      }
    }

    return true;
  }

  // Should use IndexedDB check
  async shouldUseIndexedDbAsync(payload: any): Promise<boolean> {
    if (!this.isInPropertiesModule()) {
      return false;
    }

    if (this.forceUseAPI) {
      return false;
    }

    const allowedKeys = ['pageNumber', 'pageSize', 'path', 'permission'];

    for (const key in payload) {
      if (allowedKeys.includes(key)) continue;

      const value = payload[key];

      if (value === null || value === undefined || value === '') continue;
      if (Array.isArray(value) && value.length === 0) continue;

      return false;
    }

    const pageSize = payload.pageSize;
    const pageNumber = payload.pageNumber;

    if (pageSize * pageNumber > this.MAX_RECORDS) {
      return false;
    }

    const startRecord = (pageNumber - 1) * pageSize + 1;
    const endRecord = pageNumber * pageSize;

    return await this.isDataRangeAvailableInIndexedDBAsync(startRecord, endRecord);
  }

  // Force API usage
  setForceUseAPI(force: boolean): void {
    this.forceUseAPI = force;
  }

  getForceUseAPI(): boolean {
    return this.forceUseAPI;
  }

  // Observable wrapper for fetchPropertyDataFromIndexedDB (for backward compatibility)
  fetchPropertyDataFromIndexedDB(payload: any): Observable<any> {
    return from(this.fetchPropertyDataFromIndexedDBAsync(payload));
  }

  // Async version of fetchPropertyDataFromIndexedDB
  async fetchPropertyDataFromIndexedDBAsync(payload: any): Promise<any> {
    console.log('🔄 INDEXEDDB FETCH: Starting to fetch property data from IndexedDB');

    // Set loading state to true at the start of the main fetch method
    this.indexedDBLoadingSubject.next(true);
    console.log('🔄 INDEXEDDB LOADING: Set loading to TRUE in fetchPropertyDataFromIndexedDBAsync');

    try {
      const shouldUse = await this.shouldUseIndexedDbAsync(payload);
      if (!shouldUse) {
        console.log('🔍 INDEXEDDB FETCH: Should not use IndexedDB, returning null');
        return null;
      }

      console.log('🔄 INDEXEDDB FETCH: Calling getPropertiesFromIndexedDBAsync');
      const response = await this.getPropertiesFromIndexedDBAsync(payload);

      if (response?.succeeded && response.totalCount > 0 && !response.noDataInIndexedDB) {
        console.log('🔄 INDEXEDDB FETCH: Enhancing response with metadata');

        // Enhance response with property count metadata and permission value
        const count = await this.getMetadataFromIndexedDBAsync('property_count');
        const permissionValue = await this.getPropertyPermissionValueAsync();

        if (count) {
          response.propertyCount = count;
        }
        if (permissionValue !== null) {
          response.permissionValue = permissionValue;
        }

        console.log('✅ INDEXEDDB FETCH: Successfully fetched and enhanced data from IndexedDB');
        return response;
      } else {
        console.log('🔍 INDEXEDDB FETCH: No valid data found in IndexedDB, returning null');
        return null;
      }
    } catch (error) {
      console.error('🚨 INDEXEDDB FETCH ERROR: Error in fetchPropertyDataFromIndexedDB:', error);
      return null;
    } finally {
      // Set loading state to false when fetchPropertyDataFromIndexedDB operations complete
      this.indexedDBLoadingSubject.next(false);
      console.log('✅ INDEXEDDB LOADING: Set loading to FALSE in fetchPropertyDataFromIndexedDBAsync finally block');
    }
  }

  // Observable wrapper for removePropertiesFromIndexedDB (for backward compatibility)
  removePropertiesFromIndexedDB(propertyIds: string[]): Observable<void> {
    return from(this.removePropertiesFromIndexedDBAsync(propertyIds));
  }

  // Observable wrapper for checkForUpdates (for backward compatibility)
  checkForUpdates(): Observable<any> {
    return from(this.checkForUpdatesAsync());
  }

  // Observable wrapper for getPropertyByIdFromIndexedDB (for backward compatibility)
  getPropertyByIdFromIndexedDB(id: string): Observable<any> {
    return from(this.getPropertyByIdFromIndexedDBAsync(id));
  }

  // Observable wrapper for shouldUseIndexedDb (for backward compatibility)
  shouldUseIndexedDb(payload: any): Observable<boolean> {
    return from(this.shouldUseIndexedDbAsync(payload));
  }

  // Observable wrapper for fetchInitialProperties (for backward compatibility)
  fetchInitialProperties(payload?: any): Observable<any> {
    return from(this.fetchInitialPropertiesAsync(payload));
  }

  // Observable wrapper for getPropertiesFromIndexedDB (for backward compatibility)
  getPropertiesFromIndexedDB(payload: any): Observable<any> {
    return from(this.getPropertiesFromIndexedDBAsync(payload));
  }

  // Observable wrapper for clearPropertyDataFromIndexedDB (for backward compatibility)
  clearPropertyDataFromIndexedDB(payload: any = {}): Observable<void> {
    return from(this.clearPropertyDataFromIndexedDBAsync(payload));
  }

  // Observable wrapper for storePropertiesInIndexedDB (for backward compatibility)
  storePropertiesInIndexedDB(properties: any[]): Observable<void> {
    return from(this.storePropertiesInIndexedDBAsync(properties));
  }

  // Observable wrapper for storeMetadataInIndexedDB (for backward compatibility)
  storeMetadataInIndexedDB(key: string, value: any): Observable<void> {
    return from(this.storeMetadataInIndexedDBAsync(key, value));
  }

  // Observable wrapper for getMetadataFromIndexedDB (for backward compatibility)
  getMetadataFromIndexedDB(key: string): Observable<any> {
    return from(this.getMetadataFromIndexedDBAsync(key));
  }

  // Observable wrapper for checkAndUpdateData (for backward compatibility)
  checkAndUpdateData(payload: any = {}): Observable<any> {
    return from(this.checkAndUpdateDataAsync(payload));
  }

  // Observable wrapper for getPropertyCount (for backward compatibility)
  getPropertyCount(payload: any): Observable<any> {
    return from(this.getPropertyCountAsync(payload));
  }

  // Observable wrapper for fetchPropertyCountAndStore (for backward compatibility)
  fetchPropertyCountAndStore(payload: any): Observable<any> {
    return from(this.fetchPropertyCountAndStoreAsync(payload));
  }

  // Observable wrapper for getBatchProcessingStatus (for backward compatibility)
  getBatchProcessingStatus(): Observable<any> {
    return from(this.getBatchProcessingStatusAsync());
  }

  // Observable wrapper for checkIfPropertiesStoreIsEmpty (for backward compatibility)
  checkIfPropertiesStoreIsEmpty(): Observable<boolean> {
    return from(this.checkIfPropertiesStoreIsEmptyAsync());
  }

  // Observable wrapper for getPropertyPermissionValue (for backward compatibility)
  getPropertyPermissionValue(): Observable<number | null> {
    return from(this.getPropertyPermissionValueAsync());
  }

  // Observable wrapper for storePropertyPermissionValue (for backward compatibility)
  storePropertyPermissionValue(permissionValue: number): Observable<void> {
    return from(this.storePropertyPermissionValueAsync(permissionValue));
  }
}
