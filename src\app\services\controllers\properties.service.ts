import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { buildHttpParams } from 'src/app/core/utils/common.util';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';
@Injectable({
  providedIn: 'root',
})
export class PropertyService extends BaseService<any> {
  public page: number;
  public count: number;
  serviceBaseUrl: string;
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'Property';
  }

  updateStatus(id: string) {
    return this.http.put(`${this.serviceBaseUrl}/status/${id}`, '');
  }

  increaseShareCount(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/shareCount`, payload);
  }

  getLocationList() {
    return this.http.get(`${this.serviceBaseUrl}/filterdata`);
  }

  reassignProperty(resource: any) {
    return this.http.put(`${this.serviceBaseUrl}/updatepropertyassignedto/${resource.propertyId}`, resource);
  }

  bulkReassignProperty(resource: any) {
    return this.http.put(`${this.serviceBaseUrl}/assigntomultipleproperties`, resource);
  }

  archive(id: string) {
    return this.http.put(`${this.serviceBaseUrl}/archive/${id}`, '');
  }

  uploadPropertyBrochures(id: string, payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/brochures/${id}`, payload);
  }

  getPropertyBrochures(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/brochures/${id}`);
  }

  getGalleryDropdownData() {
    return this.http.get(`${this.serviceBaseUrl}/gallerydropdowndata`);
  }

  updateGallery(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/gallery${payload.propertyId}`, payload);
  }

  uploadExcel(selectedFile: File) {
    let formData = new FormData();
    formData.append('file', selectedFile);
    return this.http.post(`${this.serviceBaseUrl}/excel`, formData);
  }

  uploadMappedColumns(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/batch`, payload);
  }

  getExcelUploadedList(pageNumber: number, pageSize: number) {
    return this.http.get(`${this.serviceBaseUrl}/bulk/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`);
  }

  exportProperty(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/export/batch`, payload);
  }

  getExportPropertyStatus(pageNumber: number, pageSize: number) {
    return this.http.get(`${this.serviceBaseUrl}/export/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`);
  }

  getOwnerNames() {
    return this.http.get(`${this.serviceBaseUrl}/ownernames`);
  }

  getMicrositeProperty(payload: any) {
    return this.http.get(`${this.serviceBaseUrl}/microsite?serialNo=${payload}`);
  }

  getMicrositeListing(payload: any) {
    return this.http.get(`${this.serviceBaseUrl}/listing/microsite?serialNo=${payload}`);
  }

  getMicrositeSimilarProperties(payload: any) {
    return this.http.get(`${this.serviceBaseUrl}/microsite/similar-properties/new?serialNo=${payload.serialNo}&pageNumber=${payload.pageNumber}&pageSize=${payload.pageSize}`);
  }

  addMicrositeLead(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/microsite/lead`, payload);
  }

  getMicrositeUser(payload: any) {
    return this.http.get(`${this.serviceBaseUrl}/microsite/user?userName=${payload}`);
  }

  bulkDeleteProperties(ids: string[]) {
    const httpOptions = {
      body: {
        ids: ids
      }
    };
    return this.http.delete(`${this.serviceBaseUrl}/softdeleteproperties`, httpOptions);
  }

  bulkRestoreProperties(ids: string[]) {
    const httpOptions = {
      ids: ids
    };
    return this.http.put(`${this.serviceBaseUrl}/restoredeletedproperties`, httpOptions);
  }

  getCurrency() {
    return this.http.get(`${this.serviceBaseUrl}/currency`);
  }

  addWaterMark(payload: any) {
    const formData = new FormData();
    const headers = new HttpHeaders();
    for (let file1 of payload.files) {
      formData.append('Files', file1);
    }
    let params = new HttpParams();
    for (let key in payload) {
      if (payload.hasOwnProperty(key)) {
        if (key === 'files') {
          continue;
        }
        params = params.append(key, payload[key]);
      }
    }
    return this.http.post(`${this.serviceBaseUrl}/image/upload?${params.toString()}`, formData, { headers });
  }

  getPropertyWithIdNameList() {
    return this.http.get(`${this.serviceBaseUrl}/idwithname`);
  }

  fetchPropertyCount(payload: any) {
    const params = buildHttpParams(payload);
    return this.http.get(`${this.serviceBaseUrl}/count?${params.toString()}`);
  }
  getAllListing(payload: any) {
    const params = buildHttpParams(payload);
    return this.http.get(`${this.serviceBaseUrl}/all/listing?${params.toString()}`);
  }

  getAllListingTopCount(payload: any) {
    const params = buildHttpParams(payload);
    return this.http.get(`${this.serviceBaseUrl}/listing/top-count?${params.toString()}`);
  }

  getAllListingBaseCount(payload: any) {
    const params = buildHttpParams(payload);
    return this.http.get(`${this.serviceBaseUrl}/listing/base-count?${params.toString()}`);
  }

  list(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/publish`, payload)
  }

  delist(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/delist`, payload)
  }

  listingExport(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/listing/export/batch`, payload)
  }

  deletePropertyPermanently(id: any) {
    const ids = {
      body: {
        ids: id,
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/delete`, ids);
  }

  getPropertyAssignments(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/assignments?iD=${id}`);
  }

  cloneProperty(id: any) {
    return this.http.post(`${this.serviceBaseUrl}/cloneproperty`, id)
  }

  getModifiedProperties(payload: any) {
    return this.http.get(
      `${this.serviceBaseUrl}/modifiedon?lastModifiedOn=${encodeURIComponent(
        payload?.lastModifiedDate
      )}&pageNumber=${payload?.pageNumber}&pageSize=${payload?.pageSize}&pemission=${payload?.permission}`
    );
  }

  getPropertiesNewAll(params: any = {}): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/new/all`, { params });
  }

  getGoogleLocationProperties() {
    return this.http.get(`${this.serviceBaseUrl}/all/properties`);
  }

  getPropertiesByIds(ids: string[]) {
    return this.http.post(`${this.serviceBaseUrl}/multiplepropertiesbyids`, { ids: ids });
  }

  addListing(payload: any) {
    return this.http.post(`${env.baseURL}api/v2/property`, payload);
  }

  updateListing(payload: any, id: string) {
    return this.http.put(`${env.baseURL}api/v2/property/${id}`, payload);
  }

  getListing(id: string) {
    return this.http.get(`${env.baseURL}api/v2/property/${id}`);
  }

  getpropertyfinderLocationList(payload: any) {
    const params = buildHttpParams(payload);
    return this.http.get(`${env.baseURL}api/v2/property/location?${params.toString()}`);
  }
}
