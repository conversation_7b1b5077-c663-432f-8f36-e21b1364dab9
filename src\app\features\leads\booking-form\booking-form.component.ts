import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { NavigationEnd, Router } from '@angular/router';
import { NgSelectComponent } from '@ng-select/ng-select';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, take, takeUntil } from 'rxjs/operators';

import {
  EMPTY_GUID,
  PAYMENT_MODES,
  PAYMENT_TYPE,
} from 'src/app/app.constants';
import { BHKType, BookingPaymentMode, EnquiryType, PaymentType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  formatBudget,
  getAWSImagePath,
  getBHKDisplayString,
  getBRDisplayString,
  getLocationDetailsByObj,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDateWithTime,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  AddInvoice,
  FetchInvoiceById,
} from 'src/app/reducers/invoice/invoice.actions';
import { getBookingData } from 'src/app/reducers/invoice/invoice.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'booking-form',
  templateUrl: './booking-form.component.html',
})
export class BookingFormComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() canShowStatusPopupInPreview: boolean = false;
  @Input() canEditLead: boolean = false;
  @Input() isLeadPreview: boolean = false;
  @Input() isLastLead: boolean = false;
  @Input() closeLeadPreviewModal: any;
  @ViewChild('fullBookingForm') fullBookingForm: TemplateRef<any>;
  @ViewChild('imageUploadLimit') imageUploadLimit: TemplateRef<any>;
  @ViewChild('referralNoInput') referralNoInput: any;
  @ViewChild('preventNumericValue') preventNumericValue: NgSelectComponent;
  @ViewChild('confirmModal') confirmModal: TemplateRef<any>;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  completeBookingForm: FormGroup;
  brokerageInfoForm: FormGroup;
  formatBudget = formatBudget;
  EnquiryType = EnquiryType;
  BHKType = BHKType;
  leadInfo: any;
  selectedProject: any;
  miniBookDate: any;
  miniBookingData: any;
  bookingData: any;
  selectedImage: any;
  bookingFormData: any;
  areaUnit: any;
  allUsers: any[];
  allSecondaryUsers: any[];
  userDetails: any;
  currencyList: any[] = [];
  currentDate: Date = new Date();
  bookingDetailsImageArray: any;
  bookingFormFields1: Array<any> = [];
  agreementValueInWords: string = '';
  carParkingChargesInWords: string = '';
  addOnChargesInWords: string = '';
  totalSoldPriceInWords: string = '';
  tokenAmountPaidInWords: string = '';
  discountInWords: string = '';
  balanceAmountInWords: string = '';
  miniagreementValueInWords: string = '';
  agreementValueFromBrokerageinfoInWords: string = '';
  totalSoldPriceFromBrokerageinfoInWords: string = '';
  brokerageChargesInWords: string = '';
  netbrokerageAmountInWords: string = '';
  brokerageEarnedInWords: string = '';
  gstInWords: string = '';
  totalBrokerageInWords: string = '';
  referralcommissionInWords: string = '';
  ImageUrl: any;
  defaultCurrency: string = '';
  canViewBrokerageInfo: boolean = false;
  canViewInvoice: boolean = false;
  selectedFormTab: boolean = true;
  isShowBookingFormBtn: boolean = false;
  isDiscountPercentage: boolean = false;
  isCommissionPercentage: boolean = false;
  isbrokerageChargesInPercentage: boolean = false;
  isUploading: boolean = false;
  isTypingInGst: boolean = true;
  hideRuppesIcons: boolean = false;
  syncingCurrency: boolean = false;
  hasInternationalSupport: boolean = false;
  showSelectedImgPopup: boolean = false;
  isSaveBrokerageForm: boolean = false;
  isChangenetBrokerageAmountValue: boolean = false;
  isChangetotalBrokerageAmountValue: boolean = false;
  isShowDocumentError: boolean = false;
  isShowFilneExist: boolean = false;
  currentPath: string;
  gstValue: number;
  netBrokerageAmountValue: any;
  totalBrokerageAmountValue: any;
  preferredCountries = ['in'];
  getBHKDisplayString = getBHKDisplayString;
  getBRDisplayString = getBRDisplayString;
  onPickerOpened = onPickerOpened;
  EMPTY_GUID = EMPTY_GUID;
  PaymentModeList = PAYMENT_MODES;
  PaymentTypeList = PAYMENT_TYPE;
  documentList: any[] = [];
  selectedDocument: string = ''
  userData: any;
  globalSettingsData: any;

  get addresses(): string {
    return (
      this.leadInfo?.enquiry?.addresses
        ?.map((address: any) => getLocationDetailsByObj(address))
        ?.join('; ') || '--'
    );
  }

  constructor(
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private formBuilder: FormBuilder,
    private _store: Store<AppState>,
    private imgService: BlobStorageService,
    public router: Router,
    public shareDataService: ShareDataService,
    private _notificationService: NotificationsService,
    private sanitizer: DomSanitizer
  ) {
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data;
        this.hasInternationalSupport = data?.hasInternationalSupport;
        this.currencyList =
          data.countries && data.countries.length > 0
            ? data.countries[0].currencies
            : null;
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
        this.preferredCountries = data?.hasInternationalSupport
          ? data?.countries?.length
            ? [data.countries[0].code?.toLowerCase()]
            : ['in']
          : ['in'];
      });
  }
  ngOnInit() {
    if (this.leadInfo?.status?.status === 'invoiced') {
      this.goToBookingFormBack();
      this.patchBrokerageForm();
    }

    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions) => {
        const permissionsSet = new Set(permissions);
        this.canViewBrokerageInfo = permissionsSet.has(
          'Permissions.Invoice.ViewBrokerageInfo'
        );
        this.canViewInvoice = permissionsSet.has('Permissions.Invoice.View');
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });

    this._store.dispatch(new FetchUsersListForReassignment());
    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUsers = data.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allUsers = assignToSort(this.allUsers, '');
      });

    this.completeBookingFormInitializer();
    this.brokerageInfoFormInitializer();

    this.completeBookingForm.get('currency').valueChanges.subscribe((value) => {
      if (!this.syncingCurrency && value !== '%') {
        this.isDiscountPercentage = false;
        this.syncingCurrency = true;
        this.completeBookingForm.get('discountUnit')?.patchValue(this.isDiscountPercentage);
        let isInPercent = this.completeBookingForm.get('discountUnit').value;
        this.completeBookingForm.get('discountCurrency').setValue(isInPercent ? '%' : value || this.leadInfo?.enquiry?.currency || this.defaultCurrency);
        this.completeBookingForm.get('currency').setValue(value || this.leadInfo?.enquiry?.currency || this.defaultCurrency);
        this.brokerageInfoForm.get('currency').setValue(value || this.leadInfo?.enquiry?.currency || this.defaultCurrency);
        this.syncingCurrency = false;
      }

      let referalComCurrency = this.brokerageInfoForm.get('referalComCurrency').value;
      if (value) {
        this.defaultCurrency = value;
        this.agreementValueInWords = formatBudget(this.completeBookingForm.value.agreementValue, value);
        this.carParkingChargesInWords = formatBudget(this.completeBookingForm.value.carParkingCharges, value);
        this.addOnChargesInWords = formatBudget(this.completeBookingForm.value.addOnCharges, value);
        this.totalSoldPriceInWords = formatBudget(this.completeBookingForm.value.totalSoldPrice, value);
        this.tokenAmountPaidInWords = formatBudget(this.completeBookingForm.value.tokenAmountPaid, value);
        this.discountInWords = formatBudget(this.completeBookingForm.value.discount, this.isDiscountPercentage ? '%' : value);
        this.balanceAmountInWords = formatBudget(this.completeBookingForm.value.balanceAmount, value);
        if (referalComCurrency === '%') {
          this.brokerageInfoForm.get('referalComCurrency')?.setValue('%');
          this.referralcommissionInWords = formatBudget(this.brokerageInfoForm.value.referralcommission, this.isCommissionPercentage ? '%' : value);
        } else {
          this.brokerageInfoForm.get('referalComCurrency')?.setValue(value);
          this.referralcommissionInWords = formatBudget(this.brokerageInfoForm.value.referralcommission, this.isCommissionPercentage ? '%' : value);
        }
      }
    });

    this.completeBookingForm
      .get('discountCurrency')
      .valueChanges.subscribe((value) => {
        if (value === '%') {
          const discountAmount =
            this.completeBookingForm.get('discount')?.value;
          if (discountAmount > 99) {
            this.completeBookingForm
              .get('discount')
              .setErrors({ invalid: 'discount must be less than 99' });
          }
          this.isDiscountPercentage = true;
          this.completeBookingForm
            .get('discountUnit')
            ?.setValue(this.isDiscountPercentage);
        } else {
          this.isDiscountPercentage = false;
          this.completeBookingForm.get('currency').setValue(value);
          this.completeBookingForm.get('discount')?.setErrors(null);
          this.completeBookingForm
            .get('discountUnit')
            ?.setValue(this.isDiscountPercentage);
        }
      });

    this.brokerageInfoForm
      .get('referalComCurrency')
      .valueChanges.subscribe((value) => {
        if (value === '%') {
          this.isCommissionPercentage = true;
        } else {
          this.isCommissionPercentage = false;
          this.brokerageInfoForm.get('currency').setValue(value);
        }
      });
    this.brokerageInfoForm
      .get('totalBrokerage')
      .valueChanges.subscribe((value) => {
        let refferalCommision =
          this.brokerageInfoForm.get('referralcommission').value;
        if (refferalCommision) {
          let result = value - refferalCommision;
          if (result < 0) {
            this.brokerageInfoForm
              .get('brokerageEarned')
              .setErrors({ invalid: 'balance amount must be greater than 1' });
            this.brokerageInfoForm.get('brokerageEarned').markAsTouched();
          } else {
            this.brokerageInfoForm
              .get('brokerageEarned')
              .setValue(Number((value - refferalCommision)?.toFixed(2).replace(/\.00$/, '')));
          }
        } else {
          if (refferalCommision != null) {
            this.brokerageInfoForm
              .get('brokerageEarned')
              .setValue(Number(refferalCommision?.toFixed(2).replace(/\.00$/, '')));
          }
        }
      });

    this.completeBookingForm
      .get('primaryOwner')
      .valueChanges.subscribe((id) => {
        this.allSecondaryUsers = this.allUsers.filter(
          (user: any) => user.id !== id
        );
      });
    this._store.select(getBookingData).pipe(take(1)).subscribe((bookingData: any) => {
      this.miniBookingData = bookingData;
      if (this.leadInfo?.status?.status !== 'invoiced') {
        if (this.selectedProject?.brokerageCurrency === '%') {
          this.isbrokerageChargesInPercentage = true;
          this.brokerageInfoForm.get('brokerageChargesCurrency').setValue('%');
        } else {
          this.brokerageInfoForm.get('brokerageChargesCurrency')
            .setValue(bookingData?.brokerageInfo?.commissionUnit || this.brokerageInfoForm.value.currency || this.defaultCurrency);
        }
      } else {
        if (this.selectedProject?.brokerageCurrency === '%' && bookingData?.brokerageInfo?.brokerageUnit === '%') {
          this.isbrokerageChargesInPercentage = true;
          this.brokerageInfoForm.get('brokerageChargesCurrency').setValue('%');
        } else {
          this.brokerageInfoForm.get('brokerageChargesCurrency')
            .setValue(bookingData?.brokerageInfo?.commissionUnit || this.brokerageInfoForm.value.currency || this.defaultCurrency);
        }
      }
    });

  }

  completeBookingFormInitializer() {
    this.allSecondaryUsers = this.allUsers.filter(
      (user: any) => user.id !== this.userDetails?.userId
    );
    this.completeBookingForm = this.formBuilder.group({
      bookingDate: [this.miniBookDate ?? new Date(this.currentDate)],
      teamHead: [this.userDetails?.reportsTo?.id ?? null],
      documentName: [null],
      documents: [null],
      primaryOwner: [this.selectedProject?.assignedToUserId ? this.selectedProject?.assignedToUserId : this.userDetails?.userId ?? null],
      secondaryOwner: [
        ((this.selectedProject.secondaryAssignTo && this.selectedProject.secondaryAssignTo !== EMPTY_GUID)
          ? this.selectedProject.secondaryAssignTo
          : ((this.leadInfo?.secondaryUserId && this.leadInfo?.secondaryUserId !== EMPTY_GUID)
            ? this.leadInfo?.secondaryUserId
            : null)),
        [],
      ],
      agreementValue: [this.selectedProject?.agreementValue, Validators.required],
      carParkingCharges: [null],
      addOnCharges: [null],
      totalSoldPrice: [null],
      tokenAmountPaid: [null],
      paymentMode: [null],
      discount: [null],
      discountUnit: [this.isDiscountPercentage],
      cashback: [null],
      balanceAmount: [null],
      typeOfPaymentSelected: [null],
      notes: [this.selectedProject?.notes],
      currency: [this.selectedProject?.selectedCurrency || this.leadInfo.enquiry.currency || this.defaultCurrency],
      discountCurrency: [
        this.isDiscountPercentage
          ? '%'
          : this.selectedProject?.selectedCurrency || this.leadInfo.enquiry.currency || this.defaultCurrency,
      ],
    });

    this.completeBookingForm
      .get('agreementValue')
      .valueChanges.subscribe((val) => {
        this.agreementValueInWords = formatBudget(
          val,
          this.completeBookingForm.value.currency
        );
        if(val){
          this.updateTotalSoldPrice();
        }
      });
    this.completeBookingForm
      .get('carParkingCharges')
      .valueChanges.subscribe((val) => {
        this.carParkingChargesInWords = formatBudget(
          val,
          this.completeBookingForm.value.currency
        );
      });
    this.completeBookingForm
      .get('addOnCharges')
      .valueChanges.subscribe((val) => {
        this.addOnChargesInWords = formatBudget(
          val,
          this.completeBookingForm.value.currency
        );
      });
    this.completeBookingForm
      .get('totalSoldPrice')
      .valueChanges.subscribe((val) => {
        this.totalSoldPriceInWords = formatBudget(
          val,
          this.completeBookingForm.value.currency
        );
      });
    this.completeBookingForm
      .get('tokenAmountPaid')
      .valueChanges.subscribe((val) => {
        this.tokenAmountPaidInWords = formatBudget(
          val,
          this.completeBookingForm.value.currency
        );
      });
    this.completeBookingForm.get('discount').valueChanges.subscribe((val) => {
      let isInPercent = this.completeBookingForm.get('discountUnit').value;
      this.discountInWords = formatBudget(
        val,
        isInPercent ? '%' : this.completeBookingForm.value.currency
      );
    });
    this.completeBookingForm
      .get('balanceAmount')
      .valueChanges.subscribe((val) => {
        this.balanceAmountInWords = formatBudget(
          val,
          this.completeBookingForm.value.currency
        );
      });
    this.completeBookingForm.get("documentName")?.valueChanges.subscribe((val) => {
      this.isShowFilneExist = false;
    });
    this.completeBookingForm.get('agreementValue').updateValueAndValidity();
  }

  brokerageInfoFormInitializer() {
    this.brokerageInfoForm = this.formBuilder.group({
      agreementValueFromBrokerageinfo: [null],
      totalSoldPriceFromBrokerageinfo: [null],
      brokerageCharges: [this.selectedProject.brockragecharge || this.miniBookingData?.brokerageInfo?.brokerageCharges],
      brokerageChargesUnit: [this.isbrokerageChargesInPercentage],
      brokerageChargesCurrency: [
        this.completeBookingForm.value.currency || this.defaultCurrency,
      ],
      netbrokerageAmount: [
        this.selectedProject.brokerageCurrency === '%'
          ? null
          : this.selectedProject.brockragecharge,
      ],
      brokerageAmountValue: [null],
      gst: [null],
      totalBrokerage: [null],
      referralname: [null],
      referralnumber: ['', this.contactNumberValidator('referral')],
      referralcommission: [null],
      referralCommissionUnit: [this.isCommissionPercentage],
      brokerageEarned: [null],
      currency: [this.leadInfo?.enquiry?.currency || this.defaultCurrency],
      referalComCurrency: [
        this.leadInfo?.enquiry?.currency || this.defaultCurrency,
      ],
    });

    this.brokerageInfoForm.get('currency').valueChanges.subscribe((val) => {
      const currency = val || this.brokerageInfoForm.value.currency || this.defaultCurrency;
      if (this.isbrokerageChargesInPercentage) {
        this.brokerageInfoForm.get('brokerageChargesCurrency').setValue('%');
      } else {
        this.brokerageInfoForm.get('brokerageChargesCurrency').setValue(currency);
      }
      this.agreementValueFromBrokerageinfoInWords = formatBudget(this.brokerageInfoForm.value.agreementValueFromBrokerageinfo, currency);
      this.totalSoldPriceFromBrokerageinfoInWords = formatBudget(this.brokerageInfoForm.value.totalSoldPriceFromBrokerageinfo, currency);
      this.brokerageChargesInWords = formatBudget(this.brokerageInfoForm.value.brokerageCharges,
        this.isbrokerageChargesInPercentage ? '%' : currency
      );
      this.netbrokerageAmountInWords = formatBudget(this.brokerageInfoForm.value.netbrokerageAmount, currency);
      this.gstInWords = formatBudget(this.brokerageInfoForm.value.gst, '%');
      this.totalBrokerageInWords = formatBudget(this.brokerageInfoForm.value.totalBrokerage, currency);
      this.referralcommissionInWords = formatBudget(
        this.brokerageInfoForm.value.referralcommission,
        this.isCommissionPercentage ? '%' : currency
      );
      this.brokerageEarnedInWords = formatBudget(
        this.brokerageInfoForm.value.brokerageEarned,
        currency
      );

      this.brokerageInfoForm.get('agreementValueFromBrokerageinfo').valueChanges.subscribe((value) => {
        this.agreementValueFromBrokerageinfoInWords = formatBudget(value, currency);
      });
      this.brokerageInfoForm.get('totalSoldPriceFromBrokerageinfo').valueChanges.subscribe((value) => {
        this.totalSoldPriceFromBrokerageinfoInWords = formatBudget(value, currency);
      });
      this.brokerageInfoForm.get('brokerageCharges').valueChanges.subscribe((value) => {
        this.brokerageChargesInWords = formatBudget(value, this.isbrokerageChargesInPercentage ? '%' : currency);
      });
      this.brokerageInfoForm.get('netbrokerageAmount').valueChanges.subscribe((value) => {
        this.netbrokerageAmountInWords = formatBudget(value, currency);
      });
      this.brokerageInfoForm.get('gst').valueChanges.subscribe((value) => {
        this.gstInWords = formatBudget(value, '%');
      });
      this.brokerageInfoForm.get('totalBrokerage').valueChanges.subscribe((value) => {
        this.totalBrokerageInWords = formatBudget(value, currency);
      });
      this.brokerageInfoForm.get('referralcommission').valueChanges.subscribe((value) => {
        this.referralcommissionInWords = formatBudget(
          value,
          this.isCommissionPercentage ? '%' : currency
        );
      });
      this.brokerageInfoForm.get('brokerageEarned').valueChanges.subscribe((value) => {
        this.brokerageEarnedInWords = formatBudget(value, currency);
      });
    });
  }
  changeCurrency(currency: any) {
    this.completeBookingForm.get('currency').setValue(currency);
  }

  saveAndMoveToInvoice(isGoToInvoice: boolean = false) {
    if (this.selectedFormTab === false) {
      this.brokerageInfoSaveClose(isGoToInvoice);
      return;
    }
    if (this.completeBookingForm.invalid) {
      validateAllFormFields(this.completeBookingForm);
      return;
    }
    let bookingData = this.completeBookingForm.value;
    let bookingFormPayload: any = {
      leadId: this.leadInfo?.id,
      bookedDate: setTimeZoneDateWithTime(bookingData?.bookingDate, this.userData?.timeZoneInfo?.baseUTcOffset),
      bookedBy: bookingData?.primaryOwner ?? null,
      bookedByUser: this.leadInfo?.bookedUnderName ?? null,
      soldPrice: bookingData?.totalSoldPrice?.toString() ?? null,
      notes: bookingData?.notes ?? null,
      teamHead: bookingData?.teamHead,
      agreementValue: bookingData?.agreementValue,
      documents: [] as Document[],
      carParkingCharges: bookingData?.carParkingCharges,
      additionalCharges: bookingData?.addOnCharges,
      tokenAmount: bookingData?.tokenAmountPaid,
      paymentMode: BookingPaymentMode[bookingData.paymentMode] || 0, // enum
      discount: bookingData?.discount,
      discountUnit: bookingData?.discountUnit
        ? '%'
        : this.defaultCurrency ?? '',
      discountMode: bookingData?.cashback ?? 0, // enum cashback
      remainingAmount: Number(bookingData?.balanceAmount) ?? 0,
      paymentType: PaymentType[bookingData?.typeOfPaymentSelected] || 0, // enum
      secondaryOwner: bookingData?.secondaryOwner,
      brokerageInfo: this.bookingData?.brokerageInfo,
      currency: bookingData?.currency,
      IsBookingCompleted: true,
    };
    this.documentList.forEach((fields: any) => {
      const Documents = {
        documentName: `${fields.photoName}`,
        filePath: fields?.filePath,
        uploadedOn: fields?.uploadedOn,
        createdOn: fields?.createdOn,
        type: 0,
        bookedDocumentType: 0,
      };
      bookingFormPayload.documents =
        bookingFormPayload.documents.concat(Documents);
    });
    this._store.dispatch(
      new AddInvoice(
        bookingFormPayload,
        this.leadInfo.id,
        (isGoToInvoice = !this.canViewBrokerageInfo && this.canViewInvoice)
      )
    );
    let aggrementValue = this.completeBookingForm.get('balanceAmount').value;
    let totalSoldPrice = this.completeBookingForm.get('totalSoldPrice').value;
    this.brokerageInfoForm.get('agreementValueFromBrokerageinfo').patchValue(aggrementValue ?? this.completeBookingForm.get('agreementValue').value);
    this.brokerageInfoForm.get('totalSoldPriceFromBrokerageinfo').patchValue(
      !isNaN(parseFloat(totalSoldPrice)) ? parseFloat(totalSoldPrice).toFixed(2).replace(/\.00$/, '') : totalSoldPrice
    );
    setTimeout(() => {
      this.checkBrokerageCharges();
    });
    this.bookingFormData = bookingFormPayload;
    let selectedCurrency = this.completeBookingForm.get('currency').value;
    this.completeBookingForm.reset();
    this.completeBookingForm.get('currency').setValue(selectedCurrency);
    if (!this.canViewBrokerageInfo) {
      this.modalService.hide();
    }
  }

  uploadImage(event: any): void {
    const MAX_FILE_SIZE = 5000000;
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      const fileSize = file.size;
      const extensionName = this.getFileExtension(file.name).toLowerCase();
      if (fileSize > MAX_FILE_SIZE) {
        this._notificationService.warn(`File size should be less than 5 MB.`);
        return;
      }
      const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
      const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'];

      if (!allowedExtensions.includes(extensionName)) {
        this._notificationService.warn(`Only JPG, PNG, GIF, and PDF files are allowed.`);
        return;
      }
      if (videoExtensions.includes(extensionName)) {
        this._notificationService.warn(`Video files are not allowed.`);
        return;
      }
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (eventOnload: any) => {
        if (reader.result) {
          this.imgService
            .uploadBookingForm([eventOnload.target.result])
            .pipe(takeUntil(this.stopper))
            .subscribe((res: any) => {
              if (res?.data) {
                const pathArr = res?.data;
                pathArr?.map((path: string) => {
                  const ImagePathUrl = getAWSImagePath(path);
                  if (ImagePathUrl) {
                    const uploadedOn = new Date().toISOString();
                    this.documentList.push({
                      filePath: ImagePathUrl,
                      extensionName: extensionName,
                      photoName: this.completeBookingForm.get("documentName").value,
                      uploadedOn: uploadedOn,
                      createdOn: uploadedOn
                    });
                    this.completeBookingForm.get("documents").patchValue(this.documentList);
                    this.completeBookingForm.get("documentName").reset();
                  }
                });
              }
            });
        }
      };
    }
  }

  getFileExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex !== -1) {
      return fileName.substring(lastDotIndex + 1);
    }
    return '';
  }

  removeImage(doc: any, index: number) {
    this.documentList = this.documentList.filter((data: any, ind) => ind !== index);
  }

  updateDiscount(value: any) {
    const totalSoldPrice =
      this.completeBookingForm.controls['totalSoldPrice'].value || 0;
    const tokenAmountPaid =
      this.completeBookingForm.controls['tokenAmountPaid'].value || 0;
    const discountAmount =
      this.completeBookingForm.controls['discount'].value || 0;
    const discountUnit =
      this.completeBookingForm.controls['cashback'].value || 0;
    const agreementValue =
      this.completeBookingForm.controls['agreementValue'].value || 0;
    const balanceAmount = totalSoldPrice - tokenAmountPaid;

    if (discountUnit === 2) {
      const copyBalanceAmount = balanceAmount;
      if (balanceAmount) {
        if (this.isDiscountPercentage) {
          var percentageDecimal = discountAmount / 100;
          var amount = agreementValue * percentageDecimal;
          let balanceAmnt = balanceAmount - amount;
          this.completeBookingForm
            .get('balanceAmount')
            .setValue(balanceAmnt?.toFixed(2).replace(/\.00$/, ''));
          this.isBalanceAmountLessThanZero();
        } else {
          let balanceAmnt = copyBalanceAmount - discountAmount;
          this.completeBookingForm.get('balanceAmount').setValue(balanceAmnt.toFixed(2).replace(/\.00$/, ''));
          this.isBalanceAmountLessThanZero();
        }
      }
    } else {
      this.completeBookingForm.get('balanceAmount').setValue(balanceAmount.toFixed(2).replace(/\.00$/, ''));
    }
    const discountControl = this.completeBookingForm.get('discount').value;
    const checkbox = this.completeBookingForm.get('discountUnit').value;
    if (discountControl > 99 && checkbox) {
      this.completeBookingForm
        .get('discount')
        .setErrors({ invalid: 'discount must be less than 99' });
    } else if (discountControl && !checkbox) {
      if (discountControl > totalSoldPrice) {
        this.completeBookingForm
          .get('discount')
          .setErrors({ invalid: 'must be less than sold price' });
      }
      if (discountControl > agreementValue) {
        this.completeBookingForm
          .get('discount')
          .setErrors({ invalid: 'must be less than agreement value' });
      }
    } else {
      this.completeBookingForm.get('discount').setErrors(null);
    }
  }

  isBalanceAmountLessThanZero() {
    let balanceAmount = this.completeBookingForm.get('balanceAmount').value;
    if (balanceAmount < 0) {
      this.completeBookingForm
        .get('balanceAmount')
        .setErrors({ invalid: 'balance amount cannot less than 0' });
      this.completeBookingForm.get('balanceAmount').markAsTouched();
    }
  }

  updateDiscountType() {
    this.isDiscountPercentage = !this.isDiscountPercentage;
    this.completeBookingForm.get('discountUnit')
      ?.setValue(this.isDiscountPercentage);
    const discountAmount = this.completeBookingForm.get('discount')?.value;
    const totalSoldPrice =
      this.completeBookingForm.controls['totalSoldPrice'].value || 0;
    const tokenAmountPaid =
      this.completeBookingForm.controls['tokenAmountPaid'].value || 0;
    const discountUnit =
      this.completeBookingForm.controls['cashback'].value || 0;
    const agreementValue =
      this.completeBookingForm.controls['agreementValue'].value || 0;
    let balanceAmount = totalSoldPrice - tokenAmountPaid;
    if (this.isDiscountPercentage) {
      if (discountAmount > 99) {
        this.completeBookingForm
          .get('discount')
          .setErrors({ invalid: 'discount must be less than 99' });
      } else {
        if (discountAmount && discountUnit === 2) {
          var percentageDecimal = discountAmount / 100;
          var amount = agreementValue * percentageDecimal;
          let balanceAmnt = balanceAmount - amount;
          this.completeBookingForm
            .get('balanceAmount')
            .setValue(balanceAmnt?.toFixed(2).replace(/\.00$/, ''));
          this.isBalanceAmountLessThanZero();
        } else if (discountAmount && discountUnit === 1) {
          this.completeBookingForm.get('balanceAmount').setValue(balanceAmount.toFixed(2).replace(/\.00$/, ''));
          this.isBalanceAmountLessThanZero();
        }
        this.completeBookingForm.get('discount')?.setErrors(null);
      }
      this.completeBookingForm.get('discountCurrency')?.setValue('%');
    } else {
      if (
        (discountAmount && discountUnit === 0) ||
        (discountAmount && discountUnit === 1)
      ) {
        this.completeBookingForm.get('balanceAmount').setValue(balanceAmount.toFixed(2).replace(/\.00$/, ''));
        this.isBalanceAmountLessThanZero();
      } else {
        let balanceAmnt = balanceAmount - discountAmount;
        this.completeBookingForm.get('balanceAmount').setValue(balanceAmnt.toFixed(2).replace(/\.00$/, ''));
        this.isBalanceAmountLessThanZero();
      }
      this.completeBookingForm.get('discountCurrency')
        ?.setValue(this.defaultCurrency);
      this.completeBookingForm.get('discount')?.setErrors(null);
    }
    if (this.isDiscountPercentage) {
      this.discountInWords = formatBudget(discountAmount, '%');
    } else {
      this.discountInWords = formatBudget(
        discountAmount,
        this.completeBookingForm.value.currency
      );
    }
  }

  onChangeDiscount() {
    const discountUnit = +this.completeBookingForm.get('cashback').value || 0;
    const totalSoldPrice =
      +this.completeBookingForm.controls['totalSoldPrice'].value || 0;
    const agreementValue =
      this.completeBookingForm.controls['agreementValue'].value || 0;
    const tokenAmountPaid =
      +this.completeBookingForm.controls['tokenAmountPaid'].value || 0;
    const discount = +this.completeBookingForm.controls['discount'].value || 0;

    let balanceAmount = totalSoldPrice - tokenAmountPaid;

    if (discountUnit === 2) {
      if (this.isDiscountPercentage) {
        const percentageDecimal = discount / 100;
        const discountAmount = agreementValue * percentageDecimal;
        balanceAmount -= discountAmount;
      } else {
        balanceAmount -= discount;
      }
    }
    this.completeBookingForm
      .get('balanceAmount')
      .setValue(balanceAmount.toFixed(2).replace(/\.00$/, ''));
    this.isBalanceAmountLessThanZero();
  }

  brokerageInfoSaveClose(isGoToInvoice: boolean = false) {
    if (this.brokerageInfoForm.invalid) {
      validateAllFormFields(this.brokerageInfoForm);
      return false;
    }
    if (
      typeof this.netBrokerageAmountValue === 'number' &&
      typeof this.brokerageInfoForm.get('netbrokerageAmount').value === 'number' &&
      this.brokerageInfoForm.get('netbrokerageAmount').value !== null &&
      this.netBrokerageAmountValue !== this.brokerageInfoForm.get('netbrokerageAmount').value
    ) {
      this.isChangenetBrokerageAmountValue = true;
      this.confirmsModal();
      if (!this.isSaveBrokerageForm) {
        return false;
      }
    } else if (
      typeof this.totalBrokerageAmountValue === 'number' &&
      typeof this.brokerageInfoForm.get('totalBrokerage').value === 'number' &&
      this.totalBrokerageAmountValue !== this.brokerageInfoForm.get('totalBrokerage').value
    ) {
      this.isChangetotalBrokerageAmountValue = true;
      this.confirmsModal();
      if (!this.isSaveBrokerageForm) {
        return false;
      }
    }

    let brokerageInfoData = this.brokerageInfoForm.value;
    let brockrageInfoPayload = {
      leadId: this.leadInfo?.id,
      ...this.bookingFormData,
      brokerageInfo: {
        soldPrice: brokerageInfoData?.totalSoldPriceFromBrokerageinfo,
        agreementValue: Number(brokerageInfoData?.agreementValueFromBrokerageinfo),
        brokerageCharges: brokerageInfoData?.brokerageCharges,
        brokerageUnit: this.isbrokerageChargesInPercentage
          ? '%'
          : this.defaultCurrency || '',
        netBrokerageAmount: Number(brokerageInfoData?.netbrokerageAmount),
        gst: brokerageInfoData?.gst,
        totalBrokerage: Number(brokerageInfoData?.totalBrokerage),
        referralName: brokerageInfoData?.referralname,
        referralNumber: brokerageInfoData?.referralnumber,
        commission: brokerageInfoData?.referralcommission,
        commissionUnit: this.isCommissionPercentage
          ? '%'
          : this.defaultCurrency,
        brokerageType:
          brokerageInfoData?.brokerageAmountValue > 0
            ? brokerageInfoData?.brokerageAmountValue
            : 0,
        earnedBrokerage: Number(brokerageInfoData?.brokerageEarned),
      },
    };
    this._store.dispatch(
      new AddInvoice(
        brockrageInfoPayload,
        this.leadInfo.id,
        (isGoToInvoice = true)
      )
    );
    this.modalService.hide();
    if (this.leadInfo?.status?.status === 'invoiced') {
      this.patchBrokerageForm();
    }
    return true;
  }

  saveAndMoveBrockrage(): void {
    Object.keys(this.completeBookingForm.controls).forEach((field) => {
      const control = this.completeBookingForm.get(field);
      if (control && control.invalid) {
        console.log(`Invalid field: ${field}`);
      }
    });
    if (this.completeBookingForm.invalid) {
      validateAllFormFields(this.completeBookingForm);
      return;
    }
    this.saveAndMoveToInvoice();
    this.switchFormTab('Brokerage info');
    this._store.select(getBookingData).subscribe((data: any) => {
      this.bookingData = data;
      if (data) {
        this.brokerageInfoForm.patchValue({
          brokerageAmountValue: 'Agreement Value',
        });
        if (this.leadInfo?.status?.status !== 'invoiced') {
          this.onAmountValueChange('Agreement Value');
        }
      }
    });
  }

  onAmountValueChange(value: any) {
    const selectedValue = this.brokerageInfoForm.get(
      'brokerageAmountValue'
    )?.value;
    const agreementValue =
      this.brokerageInfoForm.get('agreementValueFromBrokerageinfo').value || 0;
    const soldPrice = parseInt(
      this.brokerageInfoForm.get('totalSoldPriceFromBrokerageinfo').value || ''
    );
    const brokerage =
      this.brokerageInfoForm.get('brokerageCharges')?.value || 0;
    const refferalCommission =
      this.brokerageInfoForm.get('referralcommission')?.value || 0;
    const gstValue = this.brokerageInfoForm.get('gst')?.value || 0;
    const referralCommissionUnit =
      this.brokerageInfoForm.get('referralCommissionUnit')?.value || 0;

    this.checkBrokerageCharges();
    let netBrokerageAmount = 0;
    if (this.isbrokerageChargesInPercentage) {
      if (
        (selectedValue === 'Agreement Value' || selectedValue === 1) &&
        agreementValue !== undefined
      ) {
        netBrokerageAmount = agreementValue * (brokerage / 100);
      } else if (soldPrice !== undefined) {
        netBrokerageAmount = soldPrice * (brokerage / 100);
      }
      this.brokerageInfoForm
        .get('netbrokerageAmount')
        ?.setValue(
          netBrokerageAmount
            ? netBrokerageAmount.toFixed(2).replace(/\.0+$/, '')
            : ''
        );
      this.netBrokerageAmountValue = netBrokerageAmount
        .toFixed(2)
        .replace(/\.0+$/, '');
      if (gstValue) {
        const totalAmount =
          netBrokerageAmount + (netBrokerageAmount * gstValue) / 100;
        this.brokerageInfoForm
          .get('totalBrokerage')
          ?.setValue(totalAmount.toFixed(2).replace(/\.0+$/, ''));
        this.totalBrokerageAmountValue = totalAmount
          .toFixed(2)
          .replace(/\.0+$/, '');
      }
      let brokerageEarned = 0;
      if (refferalCommission) {
        const totalBrokerage = parseFloat(
          this.brokerageInfoForm.get('totalBrokerage')?.value || ''
        );
        const result = totalBrokerage * (refferalCommission / 100);
        brokerageEarned =
          referralCommissionUnit === 1
            ? totalBrokerage - result
            : totalBrokerage - refferalCommission;
      } else {
        brokerageEarned = parseFloat(
          this.brokerageInfoForm.get('totalBrokerage')?.value || ''
        );
      }
      this.brokerageInfoForm
        .get('brokerageEarned')
        ?.setValue(
          brokerageEarned ? brokerageEarned.toFixed(2).replace(/\.0+$/, '') : ''
        );
    } else {
      if (brokerage) {
        this.brokerageInfoForm.get('netbrokerageAmount')
          ?.setValue(brokerage ? brokerage.toFixed(2).replace(/\.0+$/, '') : '');
        this.netBrokerageAmountValue = brokerage.toFixed(2).replace(/\.0+$/, '');
        if (gstValue) {
          const totalAmount = brokerage + (brokerage * gstValue) / 100;
          this.brokerageInfoForm
            .get('totalBrokerage')
            ?.setValue(totalAmount.toFixed(2).replace(/\.0+$/, ''));
          this.totalBrokerageAmountValue = totalAmount
            .toFixed(2)
            .replace(/\.0+$/, '');
        }
        let brokerageEarned = 0;
        if (refferalCommission) {
          const totalBrokerage = parseFloat(
            this.brokerageInfoForm.get('totalBrokerage')?.value || ''
          );
          const result = totalBrokerage * (refferalCommission / 100);
          brokerageEarned =
            referralCommissionUnit === 1
              ? totalBrokerage - result
              : totalBrokerage - refferalCommission;
        } else {
          brokerageEarned = parseFloat(
            this.brokerageInfoForm.get('totalBrokerage')?.value || ''
          );
        }
        this.brokerageInfoForm
          .get('brokerageEarned')
          ?.setValue(
            brokerageEarned ? brokerageEarned.toFixed(2).replace(/\.0+$/, '') : ''
          );
      } else {
        this.netBrokerageAmountValue = this.brokerageInfoForm.get('netbrokerageAmount').value;
      }
    }
  }

  checkBrokerageCharges(): void {
    const agreementValue = parseFloat(
      this.brokerageInfoForm.get('agreementValueFromBrokerageinfo')?.value
    );
    const soldPrice = parseFloat(
      this.brokerageInfoForm.get('totalSoldPriceFromBrokerageinfo')?.value
    );
    const brokerage = parseFloat(
      this.brokerageInfoForm.get('brokerageCharges')?.value
    );
    if (
      this.selectedProject?.brokerageCurrency !== '%' &&
      brokerage > agreementValue
    ) {
      this.brokerageInfoForm
        .get('brokerageCharges')
        ?.setErrors({ brokerageChargeExceedsAgreement: true });
    } else if (
      this.selectedProject?.brokerageCurrency !== '%' &&
      brokerage > soldPrice
    ) {
      this.brokerageInfoForm
        .get('brokerageCharges')
        ?.setErrors({ brokerageChargeExceedsSoldPrice: true });
    } else {
      this.brokerageInfoForm.get('brokerageCharges')?.setErrors(null);
    }
    this.brokerageInfoForm.get('brokerageCharges')?.markAsTouched();
  }

  onTypingInAgreement(): void {
    const brokerageCharge =
      this.brokerageInfoForm.get('brokerageCharges')?.value;
    const agreementValue =
      this.brokerageInfoForm.get('agreementValueFromBrokerageinfo')?.value || 0;
    const gst = this.brokerageInfoForm.get('gst')?.value || 0;
    const referralCommission =
      this.brokerageInfoForm.get('referralcommission')?.value || 0;
    const referralCommissionUnit =
      this.brokerageInfoForm.get('referralCommissionUnit')?.value || 0;
    this.brokerageInfoForm.get('brokerageAmountValue')?.setValue(1);

    let netBrokerageAmount = 0;
    if (brokerageCharge !== undefined && this.isbrokerageChargesInPercentage) {
      netBrokerageAmount = agreementValue * (brokerageCharge / 100) || 0;
      this.brokerageInfoForm
        .get('netbrokerageAmount')
        ?.setValue(netBrokerageAmount.toFixed(2).replace(/\.0+$/, ''));
      this.netBrokerageAmountValue = netBrokerageAmount
        .toFixed(2)
        .replace(/\.0+$/, '');
      if (gst) {
        const gstAmount = (netBrokerageAmount * gst) / 100;
        const totalAmount = netBrokerageAmount + gstAmount;
        this.brokerageInfoForm
          .get('totalBrokerage')
          ?.setValue(totalAmount.toFixed(2).replace(/\.0+$/, ''));
        this.totalBrokerageAmountValue = totalAmount
          .toFixed(2)
          .replace(/\.0+$/, '');
      }
    } else {
      this.brokerageInfoForm
        .get('netbrokerageAmount')
        ?.setValue(
          this.selectedProject?.brokerageCharge || brokerageCharge || 0
        );
      this.netBrokerageAmountValue =
        this.selectedProject?.brokerageCharge || brokerageCharge || 0;
      netBrokerageAmount =
        this.selectedProject?.brokerageCharge || brokerageCharge || 0;
      if (gst) {
        const gstAmount = (netBrokerageAmount * gst) / 100;
        const totalAmount = netBrokerageAmount + gstAmount;
        this.brokerageInfoForm
          .get('totalBrokerage')
          ?.setValue(totalAmount.toFixed(2).replace(/\.0+$/, ''));
        this.totalBrokerageAmountValue = totalAmount
          .toFixed(2)
          .replace(/\.0+$/, '');
      }
    }
    let brokerageEarned = 0;
    if (referralCommission) {
      let totalBrokerage = parseFloat(
        this.brokerageInfoForm.get('totalBrokerage')?.value || ''
      );
      if (referralCommissionUnit) {
        const result = totalBrokerage * (referralCommission / 100);
        brokerageEarned = totalBrokerage - result;
      } else {
        brokerageEarned = totalBrokerage - referralCommission;
      }
    } else {
      brokerageEarned = parseFloat(
        this.brokerageInfoForm.get('totalBrokerage')?.value || ''
      );
    }
    this.brokerageInfoForm
      .get('brokerageEarned')
      ?.setValue(
        brokerageEarned ? brokerageEarned.toFixed(2).replace(/\.0+$/, '') : ''
      );
  }

  onTypingInSoldPrice(event: any): void {
    const brokerageCharge =
      this.brokerageInfoForm.get('brokerageCharges')?.value;
    const soldPrice =
      this.brokerageInfoForm.get('totalSoldPriceFromBrokerageinfo')?.value || 0;
    const gst = this.brokerageInfoForm.get('gst')?.value || 0;
    const referralCommission =
      this.brokerageInfoForm.get('referralcommission')?.value || 0;
    const referralCommissionUnit =
      this.brokerageInfoForm.get('referralCommissionUnit')?.value || 0;
    this.brokerageInfoForm.get('brokerageAmountValue')?.setValue(2);

    let netBrokerageAmount = 0;
    if (brokerageCharge !== undefined && this.isbrokerageChargesInPercentage) {
      netBrokerageAmount = soldPrice * (brokerageCharge / 100) || 0;
      this.brokerageInfoForm
        .get('netbrokerageAmount')
        ?.setValue(netBrokerageAmount.toFixed(2).replace(/\.0+$/, ''));
      this.netBrokerageAmountValue = netBrokerageAmount
        .toFixed(2)
        .replace(/\.0+$/, '');
      if (gst) {
        const totalBrokerage = netBrokerageAmount * (1 + gst / 100);
        this.brokerageInfoForm
          .get('totalBrokerage')
          ?.setValue(totalBrokerage.toFixed(2).replace(/\.0+$/, ''));
        this.totalBrokerageAmountValue = totalBrokerage
          .toFixed(2)
          .replace(/\.0+$/, '');
      }
    } else {
      this.brokerageInfoForm
        .get('netbrokerageAmount')
        ?.setValue(this.selectedProject?.brokerageCharge || 0);
      this.netBrokerageAmountValue = this.selectedProject?.brokerageCharge || 0;
      netBrokerageAmount = this.selectedProject?.brokerageCharge || 0;
      if (gst) {
        const totalBrokerage = netBrokerageAmount * (1 + gst / 100);
        this.brokerageInfoForm
          .get('totalBrokerage')
          ?.setValue(totalBrokerage.toFixed(2).replace(/\.0+$/, ''));
        this.totalBrokerageAmountValue = totalBrokerage
          .toFixed(2)
          .replace(/\.0+$/, '');
      }
    }
    let brokerageEarned = 0;
    if (referralCommission) {
      let totalBrokerage = parseFloat(
        this.brokerageInfoForm.get('totalBrokerage')?.value || ''
      );
      if (referralCommissionUnit) {
        const result = totalBrokerage * (referralCommission / 100);
        brokerageEarned = totalBrokerage - result;
      } else {
        brokerageEarned = totalBrokerage - referralCommission;
      }
    } else {
      brokerageEarned = parseFloat(
        this.brokerageInfoForm.get('totalBrokerage')?.value || ''
      );
    }
    this.brokerageInfoForm
      .get('brokerageEarned')
      ?.setValue(
        brokerageEarned ? brokerageEarned.toFixed(2).replace(/\.0+$/, '') : ''
      );
  }

  onTypingInBrokerage(event: any): void {
    const brokerageCharge = parseFloat(event.target.value);
    const selectedValue = this.brokerageInfoForm.get(
      'brokerageAmountValue'
    )?.value;
    const agreementValue =
      this.brokerageInfoForm.get('agreementValueFromBrokerageinfo')?.value || 0;
    const soldPrice = parseInt(
      this.brokerageInfoForm.get('totalSoldPriceFromBrokerageinfo')?.value || ''
    );
    const gst = this.brokerageInfoForm.get('gst')?.value || 0;
    const referralCommission =
      this.brokerageInfoForm.get('referralcommission')?.value || 0;
    const referralCommissionUnit =
      this.brokerageInfoForm.get('referralCommissionUnit')?.value || false;
    this.checkBrokerageCharges();

    let netBrokerage = 0;
    if (this.isbrokerageChargesInPercentage) {
      if (brokerageCharge > 99) {
        this.brokerageInfoForm
          .get('brokerageCharges')
          .setErrors({ invalid: 'brokerage charges must be ≤ 99' });
      } else {
        this.brokerageInfoForm.get('brokerageCharges').setErrors(null);
        netBrokerage =
          selectedValue === 'Agreement Value' || selectedValue === 1
            ? (agreementValue * brokerageCharge) / 100
            : (soldPrice * brokerageCharge) / 100;
      }
    } else {
      netBrokerage = this.selectedProject?.brokerageCharge || brokerageCharge;
    }
    this.brokerageInfoForm
      .get('netbrokerageAmount')
      ?.setValue(
        netBrokerage ? netBrokerage.toFixed(2).replace(/\.0+$/, '') : ''
      );
    this.netBrokerageAmountValue = netBrokerage.toFixed(2).replace(/\.0+$/, '');
    if (gst) {
      const totalBrokerage = netBrokerage * (1 + gst / 100);
      this.brokerageInfoForm
        .get('totalBrokerage')
        ?.setValue(totalBrokerage.toFixed(2).replace(/\.0+$/, ''));
      this.totalBrokerageAmountValue = totalBrokerage
        .toFixed(2)
        .replace(/\.0+$/, '');
    }
    let brokerageEarned = 0;
    if (referralCommission) {
      const totalBrokerage = parseFloat(
        this.brokerageInfoForm.get('totalBrokerage')?.value || ''
      );
      const result = totalBrokerage * (referralCommission / 100);
      brokerageEarned = referralCommissionUnit
        ? totalBrokerage - result
        : totalBrokerage - referralCommission;
    } else {
      brokerageEarned = parseFloat(
        this.brokerageInfoForm.get('totalBrokerage')?.value || ''
      );
    }
    this.brokerageInfoForm
      .get('brokerageEarned')
      ?.setValue(
        brokerageEarned ? brokerageEarned.toFixed(2).replace(/\.0+$/, '') : ''
      );
  }

  onTypingInGST(event: any) {
    let gst: number = parseFloat(event.target.value);
    if (isNaN(gst) || gst < 0) {
      return;
    }
    const maxGST = 99;
    if (gst > maxGST) {
      event.target.value = maxGST.toString();
      gst = maxGST;
      this.brokerageInfoForm.controls['gst'].setValue(maxGST);
      this.brokerageInfoForm
        .get('gst')
        .setErrors({ invalid: 'GST must be less than 99' });
      setTimeout(() => {
        this.brokerageInfoForm.get('gst').setErrors(null);
      }, 1500);
    } else {
      this.brokerageInfoForm.controls['gst'].setValue(gst);
    }
    const netBrokerageAmount = parseFloat(
      this.brokerageInfoForm.get('netbrokerageAmount')?.value || ''
    );
    if (!isNaN(netBrokerageAmount)) {
      const gstAmount = (netBrokerageAmount * gst) / 100;
      const totalAmount = netBrokerageAmount + gstAmount;
      this.brokerageInfoForm.controls['totalBrokerage'].setValue(
        totalAmount.toFixed(2).replace(/\.0+$/, '')
      );
      this.totalBrokerageAmountValue = totalAmount
        .toFixed(2)
        .replace(/\.0+$/, '');
    }
    const refferalCommission =
      this.brokerageInfoForm.get('referralcommission')?.value || 0;
    const referralCommissionUnit =
      this.brokerageInfoForm.get('referralCommissionUnit')?.value || false;
    let brokerageEarned = 0;
    if (refferalCommission) {
      const totalBrokerage = parseFloat(
        this.brokerageInfoForm.get('totalBrokerage')?.value || ''
      );
      const result = totalBrokerage * (refferalCommission / 100);
      brokerageEarned = referralCommissionUnit
        ? totalBrokerage - result
        : totalBrokerage - refferalCommission;
    } else {
      brokerageEarned = parseFloat(
        this.brokerageInfoForm.get('totalBrokerage')?.value || ''
      );
    }
    this.brokerageInfoForm
      .get('brokerageEarned')
      ?.setValue(brokerageEarned.toFixed(2).replace(/\.0+$/, ''));
  }

  getTotalComission(value: number) {
    const totalBrockrageAmount =
      this.brokerageInfoForm.get('totalBrokerage')?.value;
    const totalBrockrageNumber = Number(totalBrockrageAmount);
    let valueNumber = Number(value);
    if (!isNaN(totalBrockrageNumber) && !isNaN(valueNumber)) {
      if (this.isCommissionPercentage) {
        valueNumber = Math.min(valueNumber, 99);
        if (valueNumber > 99) {
          this.brokerageInfoForm.get('referralcommission')?.setValue(99);
        }
        const percentage = (valueNumber / 100) * totalBrockrageNumber;
        const totalCommission = totalBrockrageNumber - percentage;
        this.brokerageInfoForm
          .get('brokerageEarned')
          ?.setValue(Number(totalCommission?.toFixed(2).replace(/\.0+$/, '')));
      } else {
        const totalCommission = totalBrockrageNumber - valueNumber;
        this.brokerageInfoForm
          .get('brokerageEarned')
          ?.setValue(Number(totalCommission?.toFixed(2).replace(/\.0+$/, '')));
        if (totalCommission < 1) {
          this.brokerageInfoForm
            .get('brokerageEarned')
            .setErrors({ invalid: 'balance amount must be greater than 1' });
          this.brokerageInfoForm.get('brokerageEarned').markAsTouched();
        }
      }
    }
  }

  updateCommissionType() {
    this.isCommissionPercentage = !this.isCommissionPercentage;
    this.brokerageInfoForm
      .get('referralCommissionUnit')
      ?.setValue(this.isCommissionPercentage);
    const referralCommission =
      this.brokerageInfoForm.get('referralcommission')?.value;
    if (this.isCommissionPercentage) {
      if (referralCommission > 99) {
        this.brokerageInfoForm
          .get('referralcommission')
          .setErrors({ invalid: 'commission must be ≤ 99' });
      } else {
        this.brokerageInfoForm.get('referralcommission')?.setErrors(null);
      }
      this.brokerageInfoForm.get('referalComCurrency')?.setValue('%');
    } else {
      this.brokerageInfoForm
        .get('referalComCurrency')
        ?.setValue(this.defaultCurrency);
    }
    this.brokerageInfoForm.get('referralcommission')?.markAsTouched();
    this.getTotalComission(referralCommission);
    if (this.isCommissionPercentage) {
      this.referralcommissionInWords = formatBudget(referralCommission, '%');
    } else {
      this.referralcommissionInWords = formatBudget(
        referralCommission,
        this.defaultCurrency
      );
    }
  }

  updateBrockerageChargesType() {
    let brockerageAmount = this.brokerageInfoForm.get('brokerageCharges').value;
    this.isbrokerageChargesInPercentage = !this.isbrokerageChargesInPercentage;
    this.brokerageInfoForm
      .get('brokerageChargesUnit')
      ?.setValue(this.isbrokerageChargesInPercentage);
    const brockerageChargest =
      this.brokerageInfoForm.get('brokerageCharges')?.value;

    if (this.isbrokerageChargesInPercentage) {
      if (brockerageChargest > 99) {
        this.brokerageInfoForm
          .get('brokerageCharges')
          .setErrors({ invalid: 'it must be ≤ 99' });
      } else {
        this.brokerageInfoForm.get('brokerageCharges')?.setErrors(null);
      }
      this.onTypingInAgreement();
      this.brokerageInfoForm.get('brokerageChargesCurrency')?.setValue('%');
    } else {
      this.brokerageInfoForm
        .get('netbrokerageAmount')
        ?.setValue(brockerageAmount?.toFixed(2).replace(/\.00$/, ''));
      this.brokerageInfoForm
        .get('brokerageChargesCurrency')
        ?.setValue(this.brokerageInfoForm.value.currency || this.defaultCurrency);
      this.onTypingInAgreement();
    }
    this.brokerageInfoForm.get('brokerageCharges')?.markAsTouched();
    if (this.isbrokerageChargesInPercentage) {
      this.brokerageChargesInWords = formatBudget(brockerageChargest, '%');
    } else {
      this.brokerageChargesInWords = formatBudget(
        brockerageChargest,
        this.leadInfo?.enquiry?.currency || this.defaultCurrency
      );
    }
  }

  updateTotalSoldPrice() {
    const agreementValue =
      +this.completeBookingForm.controls['agreementValue'].value || null;
    const carParkingCharges =
      +this.completeBookingForm.controls['carParkingCharges'].value || null;
    const addOnCharges =
      +this.completeBookingForm.controls['addOnCharges'].value || null;
    const tokenAmountPaid =
      +this.completeBookingForm.controls['tokenAmountPaid'].value || null;
    const discountAmount =
      +this.completeBookingForm.controls['discount'].value || null;
    const discountUnit =
      +this.completeBookingForm.controls['cashback'].value || null;

    const totalSoldPrice = agreementValue + carParkingCharges + addOnCharges;
    this.completeBookingForm.controls['totalSoldPrice'].setValue(
      totalSoldPrice ? totalSoldPrice.toFixed(2).replace(/\.00$/, '') : null
    );

    if (carParkingCharges > agreementValue) {
      this.completeBookingForm.get('carParkingCharges')
        .setErrors({ invalid: 'must be less than agreement value' });
      this.completeBookingForm.get('carParkingCharges').markAsTouched();
    } else {
      this.completeBookingForm.get('carParkingCharges').setErrors(null);
    }

    if (addOnCharges > agreementValue) {
      this.completeBookingForm
        .get('addOnCharges')
        .setErrors({ invalid: 'must be less than agreement value' });
      this.completeBookingForm.get('addOnCharges').markAsTouched();
    } else {
      this.completeBookingForm.get('addOnCharges').setErrors(null);
    }

    let balanceAmount = totalSoldPrice - tokenAmountPaid;

    if (tokenAmountPaid > totalSoldPrice) {
      this.completeBookingForm
        .get('tokenAmountPaid')
        .setErrors({ invalid: 'must be less than total sold price' });
      this.completeBookingForm.get('tokenAmountPaid').markAsTouched();
    } else {
      this.completeBookingForm
        .get('balanceAmount')
        .setValue(balanceAmount.toFixed(2).replace(/\.00$/, ''));
    }

    if (discountAmount) {
      if (discountUnit === 0 || discountUnit === 1) {
        balanceAmount -= discountAmount;
      } else if (discountUnit === 2) {
        if (this.isDiscountPercentage) {
          const percentageDiscount = agreementValue * (discountAmount / 100);
          balanceAmount = totalSoldPrice - percentageDiscount;
        } else {
          balanceAmount -= discountAmount;
        }
      }
      this.completeBookingForm.get('balanceAmount')
        .setValue(balanceAmount.toFixed(2).replace(/\.00$/, ''));
    }
    this.isBalanceAmountLessThanZero();
  }

  updateBalanceAmount() {
    const totalSoldPrice =
      parseFloat(this.completeBookingForm.controls['totalSoldPrice'].value) ||
      0;
    const tokenAmountPaid =
      parseFloat(this.completeBookingForm.controls['tokenAmountPaid'].value) ||
      0;
    const discountValue =
      parseFloat(this.completeBookingForm.controls['discount'].value) || 0;
    const discountUnit =
      parseInt(this.completeBookingForm.controls['cashback'].value) || 0;
    const agreementValue =
      parseFloat(this.completeBookingForm.controls['agreementValue'].value) ||
      0;

    if (tokenAmountPaid > totalSoldPrice) {
      this.completeBookingForm
        .get('tokenAmountPaid')
        .setErrors({ invalid: 'must be less than total sold price' });
      this.completeBookingForm.get('tokenAmountPaid').markAsTouched();
      return;
    }
    let balanceAmount = totalSoldPrice - tokenAmountPaid;

    if (discountValue) {
      if (discountUnit === 1) {
        balanceAmount -= discountValue;
      } else if (discountUnit === 2) {
        if (this.isDiscountPercentage) {
          balanceAmount -= (balanceAmount * discountValue) / 100;
        } else {
          balanceAmount -= discountValue;
        }
      }
    }
    this.completeBookingForm.controls['balanceAmount'].setValue(
      balanceAmount ? balanceAmount.toFixed(2).replace(/\.0+$/, '') : null
    );
    this.isBalanceAmountLessThanZero();
  }

  switchFormTab(tab: string) {
    this.selectedFormTab = tab === 'Booking details' ? true : false;
  }

  goToBookingFormBack() {
    this._store.dispatch(new FetchInvoiceById(this.leadInfo.id));
    this._store.select(getBookingData)?.subscribe((data: any) => {
      let documents = data?.documents;
      if (documents) {
        this.documentList = documents.map((doc: any) => {
          return {
            photoName: doc.documentName,
            id: doc.id,
            filePath: doc.filePath,
            extensionName: this.getFileExtension(doc.filePath),
            createdOn: doc?.createdOn,
            uploadedOn: doc?.uploadedOn,
          };
        });

      }
      this.completeBookingForm?.patchValue({
        bookingDate: patchTimeZoneDate(this.miniBookDate || data?.bookedDate, this.userData?.timeZoneInfo?.baseUTcOffset),
        teamHead: data?.teamHead,
        primaryOwner: data?.bookedBy,
        secondaryOwner: (data?.secondaryOwner && data?.secondaryOwner !== EMPTY_GUID) ? data?.secondaryOwner : null,
        agreementValue: data?.agreementValue,
        carParkingCharges: data?.carParkingCharges,
        addOnCharges: data?.additionalCharges,
        totalSoldPrice:
          data?.soldPrice !== 'undefined' &&
            data?.soldPrice !== '' &&
            data?.soldPrice !== undefined &&
            data?.soldPrice !== null
            ? data?.soldPrice
            : data?.brokerageInfo?.soldPrice,
        tokenAmountPaid: data?.tokenAmount,
        paymentMode: data?.paymentMode ? BookingPaymentMode[data?.paymentMode] : null,
        discount: data?.discount,
        discountUnit: data?.discountUnit,
        cashback: data?.discountMode || null,
        balanceAmount: data?.remainingAmount || null,
        typeOfPaymentSelected: data?.paymentType ? PaymentType[data?.paymentType] : null,
        notes: data?.notes,
        currency: data?.currency,
      });
      this.switchFormTab('Booking details');
      if (data?.discountUnit === '%') {
        this.isDiscountPercentage = true;
        this.completeBookingForm?.get('discountUnit')?.setValue(true);
        this.completeBookingForm?.get('discountCurrency')?.setValue('%');
        this.syncingCurrency = true;
      } else {
        this.isDiscountPercentage = false;
        this.completeBookingForm?.get('discountUnit')?.setValue(false);
        this.completeBookingForm
          ?.get('discountCurrency')
          ?.setValue(data?.currency);
      }
      this.completeBookingForm?.get('currency')?.setValue(data?.currency);
    });
  }

  patchBrokerageForm() {
    this._store.dispatch(new FetchInvoiceById(this.leadInfo.id));
    this._store.select(getBookingData).subscribe((bookingData: any) => {
      if (bookingData) {
        this.netBrokerageAmountValue =
          bookingData?.brokerageInfo?.netBrokerageAmount;
        this.totalBrokerageAmountValue =
          bookingData?.brokerageInfo?.totalBrokerage;
        this.isCommissionPercentage =
          bookingData?.brokerageInfo?.commissionUnit === '%';
        this.bookingData = bookingData;
        this.brokerageInfoForm?.patchValue({
          agreementValueFromBrokerageinfo:
            bookingData?.brokerageInfo?.agreementValue ? bookingData?.brokerageInfo?.agreementValue : null,
          totalSoldPriceFromBrokerageinfo: bookingData?.brokerageInfo?.soldPrice ? bookingData?.brokerageInfo?.soldPrice : null,
          brokerageCharges: bookingData?.brokerageInfo?.brokerageCharges ? bookingData?.brokerageInfo?.brokerageCharges : null,
          brokerageChargesUnit: bookingData?.brokerageInfo?.brokerageUnit ? bookingData?.brokerageInfo?.brokerageUnit : null,
          netbrokerageAmount: bookingData?.brokerageInfo?.netBrokerageAmount ? bookingData?.brokerageInfo?.netBrokerageAmount : null,
          brokerageAmountValue: bookingData?.brokerageInfo?.brokerageType ? bookingData?.brokerageInfo?.brokerageType : null,
          gst: bookingData?.brokerageInfo?.gst ? bookingData?.brokerageInfo?.gst : null,
          totalBrokerage: bookingData?.brokerageInfo?.totalBrokerage ? bookingData?.brokerageInfo?.totalBrokerage : null,
          referralname: bookingData?.brokerageInfo?.referralName,
          referralnumber: bookingData?.brokerageInfo?.referralNumber,
          referralcommission: bookingData?.brokerageInfo?.commission ? bookingData?.brokerageInfo?.commission : null,
          referralCommissionUnit:
            bookingData?.brokerageInfo?.commissionUnit === '%',
          brokerageEarned: bookingData?.brokerageInfo?.earnedBrokerage ? Number(bookingData?.brokerageInfo?.earnedBrokerage) : null,
          currency: bookingData?.brokerageInfo?.currency,
          referalComCurrency:
            bookingData?.brokerageInfo?.commissionUnit === '%'
              ? '%'
              : this.defaultCurrency,
        });
        this.brokerageInfoForm?.get('currency')?.setValue(this.brokerageInfoForm.value.currency || this.defaultCurrency);
      }
    });
  }

  getSelectedCountryCodeReferralNo(): any {
    return this.referralNoInput?.selectedCountry;
  }

  contactNumberValidator(numType: string): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      if (numType == 'referral') {
        const input = document.querySelector(
          '.contactNoInput > div > input'
        ) as HTMLInputElement;
        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodeReferralNo()?.dialCode;
      }
      try {
        const validNumber = isPossiblePhoneNumber(
          (numType == 'referral' ? this.referralNoInput?.value : '') ||
          control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  changeBrockerageCurrency(value: any) {
    if (value === '%') {
      this.isbrokerageChargesInPercentage = true;
      this.onTypingInAgreement();
    } else {
      this.isbrokerageChargesInPercentage = false;
      this.onTypingInAgreement();
    }
  }

  toCamelCaseWithCapitals(input: string): string {
    if (!input) {
      return '--';
    }
    return input
      .toLowerCase()
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  onKeyDown(event: KeyboardEvent) {
    const keyCode = event.keyCode;
    if (keyCode >= 48 && keyCode <= 57) {
      event.preventDefault();
    }
  }

  ischangeNetBorkerage($event: any) {
    this.isChangenetBrokerageAmountValue = true;
  }
  ischangetotalBrokerage($event: any) {
    this.isChangetotalBrokerageAmountValue = true;
  }

  confirmsModal() {
    this.modalRef = this.modalService.show(this.confirmModal, {
      class: 'modal-500 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
    });
    return;
  }

  isClickYesOrNo(isClick: boolean) {
    if (isClick) {
      this.isSaveBrokerageForm = true;
      this.brokerageInfoSaveClose(false);
    } else {
      this.modalRef.hide();
    }
  }

  isPdfFile(filePath: any): boolean {
    return filePath?.changingThisBreaksApplicationSecurity.toLowerCase().endsWith('.pdf');
  }

  onMouseEnter(img: any) {
    this.showSelectedImgPopup = true;
    this.ImageUrl = { ...img, filePath: this.sanitizer.bypassSecurityTrustResourceUrl(img.filePath) };
  }

  onMouseLeave(img: any) {
    this.showSelectedImgPopup = false;
  }

  triggerFileInput(): void {
    const documentName = this.completeBookingForm.get("documentName")?.value;
    if (!documentName) {
      this.isShowDocumentError = true;
      return;
    }
    const isFileNameExists = this.documentList.some((doc: any) => doc.photoName === documentName);
    if (isFileNameExists) {
      this.isShowFilneExist = true;
      return;
    }
    this.isShowDocumentError = false;
    this.isShowFilneExist = false;
    this.fileInput.nativeElement.click();
  }

  setDocumentName($event: any) {
    this.completeBookingForm.get('documentName').setValue($event.trim());
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
