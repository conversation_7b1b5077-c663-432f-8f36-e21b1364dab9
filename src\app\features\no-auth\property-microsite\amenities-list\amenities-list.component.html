<ng-container *ngIf="selectedSection === 'Amenities'; else attributes">
  <ng-container *ngIf="presentAmenities?.length > 0; else noAmenitiesFound">
    <div *ngFor="let amenityType of amenitiesArr" [ngClass]="{'gray-scale': isPropertySoldOut}">
      <h4 *ngIf="amenityType[1]?.length > 0"
        class="px-20 py-20 br-50px cursor-pointer fw-600 text-mud text-decoration-underline">
        {{ amenityType[0] }}
      </h4>
      <div class="d-flex flex-wrap">
        <ng-container *ngFor="let amenity of amenityType[1]">
          <div class="text-center w-60 mr-20 mt-10">
            <img *ngIf="amenity.imageURL; else dummy" [src]="amenity.imageURL" alt="amenity" class="p-10">
            <div class="amenity-name text-sm text-truncate-1 break-all" [title]="amenity.amenityDisplayName">{{ amenity.amenityDisplayName }}</div>
          </div>
          <ng-template #dummy>
            <span class="icon ic-black ic-lg">{{ getFirstCharacter(amenity?.amenityDisplayName) }}</span>
          </ng-template>
        </ng-container>
      </div>
    </div>
  </ng-container>
</ng-container>

<ng-template #noAmenitiesFound>
  <img [src]="image?.noAmenitiesFound" alt="No Amenities Found" width="704px" height="199px">
  <h5 class="flex-center text-coal">No amenities found</h5>
</ng-template>

<ng-template #attributes>
  <ng-container *ngIf="AdditionalAttrData?.length > 0; else noAttributeFound">
    <div class="d-flex flex-wrap mt-20">
      <ng-container *ngFor="let attr of AdditionalAttrData">
        <div class="text-center w-60 mr-20 mt-10">
          <img *ngIf="attr.activeImageURL; else dummyAttr" [src]="attr?.activeImageURL" alt="attr" class="p-10">
          <div class="amenity-name text-sm text-truncate-1 break-all" [title]="attr.attributeDisplayName">{{ attr.attributeDisplayName }}</div>
        </div>
        <ng-template #dummyAttr>
          <span class="icon ic-black ic-lg">{{ getFirstCharacter(attr?.attributeDisplayName) }}</span>
        </ng-template>
      </ng-container>
    </div>
  </ng-container>
</ng-template>

<ng-template #noAttributeFound>
  <img [src]="image?.noAmenitiesFound" alt="No Attributes Found" width="704px" height="199px">
  <h5 class="flex-center text-coal">No attributes found</h5>
</ng-template>