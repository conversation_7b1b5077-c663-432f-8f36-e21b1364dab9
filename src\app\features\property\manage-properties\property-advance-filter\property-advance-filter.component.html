<div class="property-adv-filter px-30 bg-white brbl-15 brbr-15">
  <div class="h-100-100 scrollbar position-relative">
    <div class="flex-between ip-flex-start ip-flex-col ip-col-reverse">
      <div>
        <div class="field-label">{{'LABEL.property'| translate}} {{'LABEL.type'| translate}}</div>
        <div class="d-flex flex-wrap">
          <div (click)="appliedFilter.propertyType = null;updatePropertySubType()"
            class="px-20 py-6 br-4 mr-8 mb-4 ip-mb-10 align-center cursor-pointer"
            [ngClass]="appliedFilter.propertyType == null ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'">
            {{ 'GLOBAL.all' | translate }}</div>
          <div *ngFor="let propertyType of propertyTypeList">
            <div name="propertyType" (click)="appliedFilter.propertyType = propertyType.id;updatePropertySubType()"
              id="clkProp{{propertyType}}" data-automate-id="clkProp{{propertyType}}"
              class="px-20 py-6 br-4 mr-8 mb-4 ip-mb-10 align-center cursor-pointer"
              [ngClass]="appliedFilter.propertyType == propertyType.id ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'">
              {{propertyType.displayName}}</div>
          </div>
        </div>
      </div>
      <div class="filters-grid d-flex pl-0 pb-0">
        <div class="dropdown-date-picker d-flex rounded">
          <div class="bg-white rounded-start manage-select">
            <ng-select [virtualScroll]="true" placeholder="{{'GLOBAL.all'| translate}}" class="w-100px lead-date"
              [(ngModel)]="appliedFilter.dateType" ResizableDropdown>
              <ng-option name="dateType" *ngFor="let dType of dateTypeList" [value]="dType">{{dType}}
              </ng-option>
            </ng-select>
          </div>
          <div class="date-picker align-center py-4 rounded-end" id="propertyAppointmentDate"
            data-automate-id="propertyAppointmentDate">
            <span class="ic-appointment icon ic-xxs ic-black" [owlDateTimeTrigger]="dt1"></span>
            <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [selectMode]="'range'"
              class="fw-400 pl-30 text-large" [disabled]="!appliedFilter.dateType"
              (ngModelChange)="appliedFilter.date = $event" [ngModel]="appliedFilter.date"
              placeholder="ex. 19-06-2025 - 29-06-2025" />
            <owl-date-time [pickerType]="'calendar'" #dt1
              (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
          </div>
        </div>
        <div *ngIf="appliedFilter?.date?.[0]" class="bg-coal px-8 cursor-pointer align-center br-6 ml-10"
          (click)="onResetDateFilter()">
          <span class="ic-refresh ic-white"></span>
        </div>
      </div>
    </div>
    <div class="field-label">{{'LEADS.filter-by' | translate}}</div>
    <div class="d-flex">
      <div (click)="appliedFilter.enquiredFor = null"
        class="px-20 py-6 br-4 mr-8 mb-4 ip-mb-10 align-center cursor-pointer"
        [ngClass]="appliedFilter.enquiredFor == null ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'">
        {{ 'GLOBAL.all' | translate }}</div>
      <div *ngFor="let enquiry of enquiredFor">
        <div name="enquiry" (click)="appliedFilter.enquiredFor = enquiry.type"
          class="px-20 py-6 br-4 mr-8 mb-4 ip-mb-10 align-center cursor-pointer"
          [ngClass]="appliedFilter.enquiredFor == enquiry.type ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'"
          id="clkProp{{enquiry.type}}" data-automate-id="clkProp{{enquiry.type}}">{{enquiry.type}}</div>
      </div>
    </div>
    <!-- <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100 pr-20 focus-unset">
      <div class="field-label">{{'LABEL.budget'| translate}}</div>
      <div>
        <ngx-slider [(value)]="appliedFilter.minBudget" [(highValue)]="appliedFilter.maxBudget"
          [options]="options"></ngx-slider>
      </div>
    </div> -->
    <div class="d-flex w-100 flex-wrap ng-select-sm">
      <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">{{'LABEL.property'| translate}} {{'LABEL.sub-type'| translate}}</div>
        <div class="mr-20">
          <ng-select [virtualScroll]="true" [items]="propertySubTypes" [multiple]="true" [closeOnSelect]="false"
            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="id"
            [(ngModel)]="appliedFilter.propertySubType" ResizableDropdown>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                  class="text-truncate-1 break-all">{{item.displayName}}</span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">{{'PROPERTY.bhk'| translate}}</div>
        <div class="mr-20">
          <ng-select [virtualScroll]="true" [items]="noOfBhk" [multiple]="true" [closeOnSelect]="false"
            placeholder="{{'GLOBAL.select' | translate}}" [(ngModel)]="appliedFilter.BHKs" ResizableDropdown>
            <ng-template ng-label-tmp let-item="item" let-clear="clear">
              <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
              <span class="ng-value-label"> {{getBHKDisplayString(item)}}</span>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                  class="checkmark"></span>{{getBHKDisplayString(item)}}
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">{{'PROPERTY.bhk' | translate}} {{'LABEL.type'| translate}}</div>
        <div class="mr-20">
          <ng-select [virtualScroll]="true" [items]="bhkType" [multiple]="true" [closeOnSelect]="false"
            placeholder="{{'GLOBAL.select' | translate}}" [(ngModel)]="appliedFilter.bhkType" ResizableDropdown>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                  class="text-truncate-1 break-all">{{item}}</span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">{{'SIDEBAR.project'| translate}}</div>
        <div class="mr-20">
          <ng-select [virtualScroll]="true" [items]="projectList" [multiple]="true" [closeOnSelect]="false"
            [ngClass]="{'pe-none blinking': isProjectsListLoading}" placeholder="{{'GLOBAL.select' | translate}}"
            bindLabel="id" [(ngModel)]="appliedFilter.projects" ResizableDropdown>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                  class="text-truncate-1 break-all">{{item}}</span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <!-- <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100 pr-20">
        <div class="field-label">Possession</div>
        <form-errors-wrapper label="Possession">
          <input type="text" [owlDateTime]="dt2" [owlDateTimeTrigger]="dt2" readonly bsDatepicker
            [(ngModel)]="appliedFilter.possessionDate" id="inpPossessionDate" placeholder="ex. 5/3/2025"
            data-automate-id="inpPossessionDate" [selectMode]="'range'">
          <span class="icon ic-calendar ic-sm ic-coal position-absolute right-20 top-8"></span>
          <owl-date-time [pickerType]="'calendar'" #dt2 (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
        </form-errors-wrapper>
      </div> -->
      <div *ngIf="canView" class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">{{'LEADS.assign-to'| translate}}</div>
        <div class="mr-20">
          <ng-select [virtualScroll]="true" [items]="allUserList" [multiple]="true" [closeOnSelect]="false"
            placeholder="{{'GLOBAL.select' | translate}}" bindValue="id" bindLabel="fullName"
            [ngClass]="{'pe-none blinking': allUserListIsLoading}" [(ngModel)]="appliedFilter.assignedTo"
            ResizableDropdown>
            <ng-template ng-label-tmp let-item="item" let-clear="clear">
              <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
              <span class="ng-value-label">
                {{item.firstName + ' ' + item.lastName}}
              </span>
            </ng-template>
            <ng-template ng-label-tmp let-item="item" let-clear="clear">
              <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
              <span class="ng-value-label">
                {{item.firstName + ' ' + item.lastName}}
              </span>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="flex-between">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item.firstName}}
                    {{item.lastName}}</span></div> <span class="text-disabled" *ngIf="!item.isActive">( Disabled
                  )</span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100" *ngIf="canViewOwner">
        <div class="field-label">Owner Name</div>
        <ng-select [virtualScroll]="true" [items]="ownerNames" [multiple]="true" [closeOnSelect]="false"
          placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="id"
          [(ngModel)]="appliedFilter.ownerDetails" class="mr-20" ResizableDropdown>
          <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                [checked]="item$.selected"><span class="checkmark"></span><span
                class="text-truncate-1 break-all">{{item}}</span>
            </div>
          </ng-template>
        </ng-select>
      </div>
      <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">Property Status</div>
        <div class="mr-20">
          <ng-select [virtualScroll]="true" [multiple]="false" [closeOnSelect]="true" ResizableDropdown
            placeholder="{{'GLOBAL.select' | translate}}" [(ngModel)]="appliedFilter.PropertyStatus">
            <ng-option *ngFor="let status of propertyStatus" [value]="status.value">
              {{status?.name}}</ng-option>
          </ng-select>
        </div>
      </div>
      <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">Property Title</div>
        <div class="no-validation mr-20">
          <form-errors-wrapper label="Property Title">
            <div class="border-gray br-6">
              <input id="inpPropertyTitle" data-automate-id="inpPropertyTitle" placeholder="enter property title"
                [(ngModel)]="appliedFilter.title" class="border-0">
            </div>
          </form-errors-wrapper>
        </div>
      </div>
      <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">Serial No</div>
        <div class="no-validation mr-20">
          <form-errors-wrapper>
            <div class="border-gray br-6">
              <input placeholder="enter serial no" [(ngModel)]="appliedFilter.SerialNo" class="border-0">
            </div>
          </form-errors-wrapper>
        </div>
      </div>

      <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">Lead Count</div>
        <div class="mr-20">
          <div class="w-100 align-center">
            <div class="w-100 no-input-validation">
              <form-errors-wrapper>
                <div class="w-100 d-flex">
                  <div class="w-50">
                    <input type="number" (keydown)="onlyNumbers($event)" (input)="minMaxLeadCheck()"
                      [max]="appliedFilter.MaxLeadCount" placeholder="ex. 10" maxlength="10"
                      [(ngModel)]="appliedFilter.MinLeadCount">
                  </div>
                  <h6 class="text-sm text-mud align-center m-4">To</h6>
                  <div class="w-50">
                    <input type="number" (keydown)="onlyNumbers($event)" (input)="minMaxLeadCheck()"
                      [min]="appliedFilter.MinLeadCount" placeholder="ex. 1000" maxlength="10"
                      [(ngModel)]="appliedFilter.MaxLeadCount">
                  </div>
                </div>
              </form-errors-wrapper>
              <div class="text-xs text-red fw-semi-bold position-absolute"
                *ngIf="(appliedFilter.MinLeadCount && appliedFilter.MaxLeadCount) && !minMaxLeadValidation">
                {{'Min lead cannot be greater than max lead'}}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">Data Count</div>
        <div class="mr-20">
          <div class="w-100 align-center">
            <div class="w-100 no-input-validation">
              <form-errors-wrapper>
                <div class="w-100 d-flex">
                  <div class="w-50">
                    <input type="number" (keydown)="onlyNumbers($event)" (input)="minMaxDataCheck()"
                      [max]="appliedFilter.MaxProspectCount" placeholder="ex. 10" maxlength="10"
                      [(ngModel)]="appliedFilter.MinProspectCount">
                  </div>
                  <h6 class="text-sm text-mud align-center m-4">To</h6>
                  <div class="w-50">
                    <input type="number" (keydown)="onlyNumbers($event)" (input)="minMaxDataCheck()"
                      [min]="appliedFilter.MinProspectCount" placeholder="ex. 1000" maxlength="10"
                      [(ngModel)]="appliedFilter.MaxProspectCount">
                  </div>
                </div>
              </form-errors-wrapper>
              <div class="text-xs text-red fw-semi-bold position-absolute"
                *ngIf="(appliedFilter.MinProspectCount && appliedFilter.MaxProspectCount) && !minMaxDataValidation">
                {{'Min data cannot be greater than max lead'}}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">Possession</div>
        <div class="mr-20">
          <app-possession-filter
            #possessionFilter
            [initialPossessionType]="appliedFilter.PossesionType"
            [initialFromPossessionDate]="appliedFilter.FromPossesionDate"
            [initialToPossessionDate]="appliedFilter.ToPossesionDate"
            [userTimeZoneOffset]="userData?.timeZoneInfo?.baseUTcOffset"
            (possessionFilterChange)="onPossessionFilterChange($event)">
          </app-possession-filter>
        </div>
      </div>

    </div>
    <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
      <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Area:</legend>
      <div class="d-flex w-100 flex-wrap ng-select-sm">
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Property Area</div>
          <div class="w-100 align-center">
            <div class="w-60pr no-input-validation input-sm">
              <form-errors-wrapper>
                <div class="w-100 d-flex">
                  <div class="w-50">
                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                      (input)="propertyAreaValidation()" [(ngModel)]="appliedFilter.MinPropertyArea" min="0"
                      id="inpMinPropertyArea" data-automate-id="inpMinPropertyArea" placeholder="ex. 123">
                  </div>
                  <h6 class="text-sm text-mud align-center m-4">To</h6>
                  <div class="w-50">
                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                      (input)="propertyAreaValidation()" [(ngModel)]="appliedFilter.MaxPropertyArea" min="0"
                      id="inpMaxPropertyArea" data-automate-id="inpMaxPropertyArea" placeholder="ex. 123">
                  </div>
                </div>
              </form-errors-wrapper>
            </div>
            <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
              *ngIf="appliedFilter.MinPropertyArea && appliedFilter.MaxPropertyArea && !areaValidation">
              {{'PROPERTY.area-validation' | translate}}</div>
            <div class="w-40pr ml-8 mr-20">
              <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                <ng-select [virtualScroll]="true" tabindex="4" placeholder="ex. sq. feet." [items]="areaSizeUnits"
                  [ngClass]="{'pe-none blinking': isAreaUnitsLoading}" bindValue="id"
                  [(ngModel)]="appliedFilter.PropertyAreaUnitId" bindLabel="unit" ResizableDropdown></ng-select>
              </form-errors-wrapper>
            </div>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Carpet Area</div>
          <div class="w-100 align-center">
            <div class="w-60pr no-input-validation input-sm">
              <form-errors-wrapper>
                <div class="w-100 d-flex">
                  <div class="w-50">
                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                      (input)="validateCarpetArea()" [(ngModel)]="appliedFilter.MinCarpetArea" min="0"
                      id="inpMinCarpetArea" data-automate-id="inpMinCarpetArea" placeholder="ex. 123">
                  </div>
                  <h6 class="text-sm text-mud align-center m-4">To</h6>
                  <div class="w-50">
                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                      [(ngModel)]="appliedFilter.MaxCarpetArea" (input)="validateCarpetArea()" min="0"
                      id="inpMaxCarpetArea" data-automate-id="inpMaxCarpetArea" placeholder="ex. 123">
                  </div>
                </div>
              </form-errors-wrapper>
            </div>
            <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
              *ngIf="appliedFilter.MinCarpetArea && appliedFilter.MaxCarpetArea && !carpetAreaValidations ">
              {{'PROPERTY.area-validation' | translate}}</div>
            <div class="w-40pr ml-8 mr-20">
              <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                <ng-select [virtualScroll]="true" tabindex="4" placeholder="ex. sq. feet." [items]="areaSizeUnits"
                  [ngClass]="{'pe-none blinking': isAreaUnitsLoading}" bindValue="id"
                  [(ngModel)]="appliedFilter.carpetAreaId" bindLabel="unit" ResizableDropdown></ng-select>
              </form-errors-wrapper>
            </div>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Built-Up Area</div>
          <div class="w-100 align-center">
            <div class="w-60pr no-input-validation input-sm">
              <form-errors-wrapper>
                <div class="w-100 d-flex">
                  <div class="w-50">
                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                      (input)="validateBuildUpArea()" [(ngModel)]="appliedFilter.MinBuildUpArea" min="0"
                      id="inpMinBuitUpArea" data-automate-id="inpPropSize" placeholder="ex. 123">
                  </div>
                  <h6 class="text-sm text-mud align-center m-4">To</h6>
                  <div class="w-50">
                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                      (input)="validateBuildUpArea()" [(ngModel)]="appliedFilter.MaxBuildUpArea" min="0"
                      id="inpPropSize" data-automate-id="inpPropSize" placeholder="ex. 123">
                  </div>
                </div>
              </form-errors-wrapper>
            </div>
            <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
              *ngIf="appliedFilter.MinBuildUpArea && appliedFilter.MaxBuildUpArea && !buildUpAreaValidations ">
              {{'PROPERTY.area-validation' | translate}}</div>
            <div class="w-40pr ml-8 mr-20">
              <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                <ng-select [virtualScroll]="true" tabindex="4" placeholder="ex. sq. feet." [items]="areaSizeUnits"
                  [ngClass]="{'pe-none blinking': isAreaUnitsLoading}" bindValue="id"
                  [(ngModel)]="appliedFilter.buildUpAreaId" bindLabel="unit" ResizableDropdown></ng-select>
              </form-errors-wrapper>
            </div>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Saleable Area</div>
          <div class="w-100 align-center">
            <div class="w-60pr no-input-validation input-sm">
              <form-errors-wrapper>
                <div class="w-100 d-flex">
                  <div class="w-50">
                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                      (input)="saleableAreaValidation()" [(ngModel)]="appliedFilter.MinSaleableArea" min="0"
                      id="inpMinSaleableArea" data-automate-id="inpMinSaleableArea" placeholder="ex. 123">
                  </div>
                  <h6 class="text-sm text-mud align-center m-4">To</h6>
                  <div class="w-50">
                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                      (input)="saleableAreaValidation()" [(ngModel)]="appliedFilter.MaxSaleableArea" min="0"
                      id="inpMaxSaleableArea" data-automate-id="inpMaxSaleableArea" placeholder="ex. 123">
                  </div>
                </div>
              </form-errors-wrapper>
            </div>
            <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
              *ngIf="appliedFilter.MinSaleableArea && appliedFilter.MaxSaleableArea && !saleableValidation">
              {{'PROPERTY.area-validation' | translate}}</div>
            <div class="w-40pr ml-8 mr-20">
              <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                <ng-select [virtualScroll]="true" tabindex="4" placeholder="ex. sq. feet." [items]="areaSizeUnits"
                  [ngClass]="{'pe-none blinking': isAreaUnitsLoading}" bindValue="id"
                  [(ngModel)]="appliedFilter.saleableAreaId" bindLabel="unit" ResizableDropdown></ng-select>
              </form-errors-wrapper>
            </div>
          </div>
        </div>
      </div>
    </fieldset>
    <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
      <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Budget:</legend>
      <div class="d-flex w-100 flex-wrap ng-select-sm">
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100 field-rupees-tag">
          <div class="field-label">{{'GLOBAL.min' | translate}} {{'LABEL.budget' | translate}}</div>
          <div class="position-relative mt-4 mr-20 lead adv-dropdown"
            [ngClass]="{'pe-none blinking': isGlobalSettingsLoading}">
            <form-errors-wrapper>
              <input type="number" (input)="budgetCheck()" [min]="0" (keydown)="onlyNumbers($event)"
                [max]="appliedFilter.maxPrice" id="inpMinBudget" data-automate-id="inpMinBudget" placeholder="ex. 10"
                maxlength="10" [(ngModel)]="appliedFilter.minPrice">
              <div class="no-validation">
                <ng-container *ngIf="propertyCurrency?.length > 1; else showCurrencySymbol">
                  <ng-select [(ngModel)]="appliedFilter.currency" ResizableDropdown
                    class="mt-2 ml-2  position-absolute top-0 manage-dropdown">
                    <ng-option *ngFor="let curr of propertyCurrency" [value]="curr">{{curr}}
                    </ng-option>
                  </ng-select>
                </ng-container>
                <ng-template #showCurrencySymbol>
                  <h5 class="rupees px-12 py-4 fw-600 m-4">{{ defaultCurrency }}</h5>
                </ng-template>
              </div>
            </form-errors-wrapper>
            <div *ngIf="appliedFilter.minPrice" class="position-absolute right-10 top-15">
              <span class="text-nowrap text-xs text-accent-green fw-semi-bold">{{
                formatBudget(appliedFilter.minPrice,appliedFilter.currency || defaultCurrency) }}</span>
            </div>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100 field-rupees-tag">
          <div class="field-label">{{'GLOBAL.max' | translate}} {{'LABEL.budget' | translate}}</div>
          <div class="position-relative mr-20 lead adv-dropdown"
            [ngClass]="{'pe-none blinking': isGlobalSettingsLoading}">
            <form-errors-wrapper>
              <input type="number" class="pl-40" (input)="budgetCheck()" (keydown)="onlyNumbers($event)" [min]="0"
                [min]="appliedFilter.minPrice" id="inpMaxBudget" data-automate-id="inpMaxBudget"
                placeholder="ex. 1000000" maxlength="10" [(ngModel)]="appliedFilter.maxPrice">
              <div class="no-validation">
                <ng-container *ngIf="propertyCurrency?.length > 1; else showCurrencySymbol">
                  <ng-select [(ngModel)]="appliedFilter.currency" ResizableDropdown
                    class="mt-2 ml-2 position-absolute top-0 manage-dropdown">
                    <ng-option *ngFor="let curr of propertyCurrency" [value]="curr">{{curr}}
                    </ng-option>
                  </ng-select>
                </ng-container>
              </div>
            </form-errors-wrapper>
            <div class="text-xs text-red fw-semi-bold position-absolute"
              *ngIf="(appliedFilter.minPrice && appliedFilter.maxPrice) && !budgetValidation">
              {{'LEADS.budget-validation' | translate}}</div>
            <div *ngIf="appliedFilter.maxPrice" class="position-absolute right-10 top-15">
              <span class="text-nowrap text-xs text-accent-green fw-semi-bold">{{
                formatBudget(appliedFilter.maxPrice,appliedFilter.currency || defaultCurrency) }}</span>
            </div>
          </div>
        </div>
      </div>
    </fieldset>
    <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
      <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Location:</legend>
      <div class="d-flex w-100 flex-wrap ng-select-sm">
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">{{'LOCATION.locality'| translate}} / {{ 'PROJECTS.area' | translate }}</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [items]="localityList" [multiple]="true" [closeOnSelect]="false"
              [ngClass]="{'pe-none blinking': isLocationListLoading}" placeholder="{{'GLOBAL.select' | translate}}"
              [(ngModel)]="appliedFilter.locations" ResizableDropdown>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item}}</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">{{'LOCATION.city'| translate}}</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [items]="cityList" [multiple]="true" [closeOnSelect]="false"
              [ngClass]="{'pe-none blinking': isLocationListLoading}" placeholder="{{'GLOBAL.select' | translate}}"
              [(ngModel)]="appliedFilter.cities" ResizableDropdown>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item}}</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">{{'LOCATION.state' | translate}}</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [items]="stateList" [multiple]="true" [closeOnSelect]="false"
              [ngClass]="{'pe-none blinking': isLocationListLoading}" placeholder="{{'GLOBAL.select' | translate}}"
              [(ngModel)]="appliedFilter.states" ResizableDropdown>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item}}</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
      </div>
    </fieldset>
    <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
      <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Attributes & Amenities:</legend>
      <div class="d-flex w-100 flex-wrap ng-select-sm">
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Furnish Status</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [dropdownPosition]="'top'" [items]="furnishStatus" [multiple]="true"
              [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
              [(ngModel)]="appliedFilter.furnishStatus" bindLabel="value" bindValue="dispName" ResizableDropdown>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item.dispName}}</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Facing</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [dropdownPosition]="'top'" [items]="facing" [multiple]="false"
              [closeOnSelect]="true" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="id"
              bindLabel="displayName" [(ngModel)]="appliedFilter.facing" ResizableDropdown>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">No. of Floors</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [dropdownPosition]="'top'" [items]="numbers" [multiple]="true"
              [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="value" bindValue="value"
              [(ngModel)]="appliedFilter.noOfFloors" ResizableDropdown>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container">
                  <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                    [checked]="item$.selected">
                  <span class="checkmark"></span>{{item.display}}
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">No. of Bathrooms</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [dropdownPosition]="'top'" [items]="numbers" [multiple]="true"
              [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="value" bindValue="value"
              [(ngModel)]="appliedFilter.noOfBathrooms" ResizableDropdown>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                    class="checkmark"></span>{{item.display}}
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">No. of Living rooms</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [dropdownPosition]="'top'" [items]="numbers" [multiple]="true"
              [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="value" bindValue="value"
              [(ngModel)]="appliedFilter.noOfLivingrooms" ResizableDropdown>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                    class="checkmark"></span>{{item.display}}
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">No. of Bedrooms</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [dropdownPosition]="'top'" [items]="numbers" [multiple]="true"
              [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="value" bindValue="value"
              [(ngModel)]="appliedFilter.noOfBedrooms" ResizableDropdown>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                    class="checkmark"></span>{{item.display}}
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">No. of Utilites</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [dropdownPosition]="'top'" [items]="numbers" [multiple]="true"
              [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="value" bindValue="value"
              [(ngModel)]="appliedFilter.noOfUtilites" ResizableDropdown>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                    class="checkmark"></span>{{item.display}}
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">No. of Kitchens</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [dropdownPosition]="'top'" [items]="numbers" [multiple]="true"
              [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="value" bindValue="value"
              [(ngModel)]="appliedFilter.noOfKitchens" ResizableDropdown>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                    class="checkmark"></span>{{item.display}}
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">No. of Balconies</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [dropdownPosition]="'top'" [items]="numbers" [multiple]="true"
              [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="value" bindValue="value"
              [(ngModel)]="appliedFilter.noOfBalconies" ResizableDropdown>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                    class="checkmark"></span>{{item.display}}
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">{{'PROPERTY.amenity'| translate}}</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [dropdownPosition]="'top'" [items]="amenities" [multiple]="true"
              [closeOnSelect]="false" [ngClass]="{'pe-none blinking': isAmenitiesLoading}"
              placeholder="{{'GLOBAL.select' | translate}}" bindLabel="amenityDisplayName" bindValue="id"
              [(ngModel)]="appliedFilter.amenities" ResizableDropdown>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item.amenityDisplayName}}</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
      </div>
    </fieldset>
  </div>
  <div class="flex-end py-20">
    <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="modalRef.hide()">{{'BUTTONS.cancel' |
      translate }}</u>
    <button class="btn-gray" (click)="reset()">{{ 'GLOBAL.reset' | translate }}</button>
    <button (click)="validateAndApplyFilter()" class="btn-coal ml-20">{{ 'GLOBAL.search' | translate }}</button>
  </div>
</div>