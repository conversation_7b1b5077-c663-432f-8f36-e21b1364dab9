import { CommonModule } from "@angular/common";
import { HttpClient } from "@angular/common/http";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateLoader, TranslateModule, TranslateService } from "@ngx-translate/core";
import { AgGridModule } from "ag-grid-angular";
import { LottieModule } from "ngx-lottie";

import { HttpLoaderFactory, playerFactory } from "src/app/app.imports";
import { SharedModule } from "src/app/shared/shared.module";
import { reportsRoutingModule, REPORTS_DECLARATIONS } from "./reports-routing.module";
import { CanvasJSAngularChartsModule } from "@canvasjs/angular-charts";

@NgModule({
  declarations: [...REPORTS_DECLARATIONS],
  imports: [
    CommonModule,
    reportsRoutingModule,
    SharedModule,
    AgGridModule,
    ReactiveFormsModule,
    FormsModule,
    CanvasJSAngularChartsModule,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      // isolate: true,
      // to extend the I18n to use global en.json
      extend: true,
    }),
    LottieModule.forRoot({ player: playerFactory }),
  ],
  providers: [TranslateService],
})
export class reportModule { }