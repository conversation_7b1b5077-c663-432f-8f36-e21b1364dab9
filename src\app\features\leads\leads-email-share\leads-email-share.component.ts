import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { LEAD_TEMPLATE_VARIABLES, PROJECT_TEMPLATE_VARIABLES, PROPERTY_TEMPLATE_VARIABLES } from 'src/app/app.constants';
import { ContactType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  getTenantName,
  LeadTemplateMsg,
  ProjectTemplateMsg,
  PropertyTemplateMsg,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import {
  CommunicationBulkDataCount,
  CommunicationBulkDataMessage,
  CommunicationDataCount,
  CommunicationDataMessage,
} from 'src/app/reducers/data/data-management.actions';
import { SendTestEmailBulk } from 'src/app/reducers/email/email-settings.action';
import { getEmailSMTPByUserId } from 'src/app/reducers/email/email-settings.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  CommunicationBulkCount,
  CommunicationBulkMessage,
  CommunicationCount,
  CommunicationMessage,
} from 'src/app/reducers/lead/lead.actions';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import { FetchProjectById, FetchProjectIdWithName } from 'src/app/reducers/project/project.action';
import { getProjectsIDWithName, getProjectsIDWithNameIsLoading, getSelectedProjectById } from 'src/app/reducers/project/project.reducer';
import { FetchPropertyById, FetchPropertyWithIdNameList } from 'src/app/reducers/property/property.actions';
import { getPropertyListDetails, getPropertyWithIdLoading, getPropertyWithIdNameList } from 'src/app/reducers/property/property.reducer';
import {
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { FetchTemplateModule } from 'src/app/reducers/template/template.actions';
import { getTemplatesModule } from 'src/app/reducers/template/template.reducer';
import { ProjectsService } from 'src/app/services/controllers/projects.service';
import { PropertyService } from 'src/app/services/controllers/properties.service';
import { TenantService } from 'src/app/services/controllers/tenant.service';

@Component({
  selector: 'leads-email-share',
  templateUrl: './leads-email-share.component.html',
})
export class LeadsEmailShareComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  data: any;
  templates: [];
  isTemplatesLoading: boolean = true;
  tenantName: string = '';
  selectedCallType: any;
  selectedTemplate: any;
  message: string = '';
  defaultCurrency: string;
  // showTemplates: boolean = false;
  EmailList: any;
  fromList: any;
  ccEnabled: boolean = false;
  bccEnabled: boolean = false;
  ccMail: string = '';
  bccMail: string = '';
  emailForm: FormGroup;
  isShowVariablePopup: boolean = false;
  variables: string[] = [];
  bulkData: any[];
  template: any = '';
  displayEmail: string = '';
  shareType: any;
  isData: boolean;
  fileFormat: any = 'Supported format: JPG, PNG, MP4, GIF and PDF';
  fileMessage: any = true;
  selectedFile: File;
  files: any = [];
  isLoading: boolean;
  toList: string[] = [];
  ccList: string[] = [];
  bccList: string[] = [];
  isShowLeadPreview: boolean = false;
  allUserList: any[] = [];
  userData: any;
  templatesList: any = ['Lead', 'Project', 'Property']

  public Editor = ClassicEditor;
  currentDate: Date = new Date();
  allProjectList: any;
  projectListIsLoading: boolean;
  allPropertyList: any;
  propertyListIsLoading: boolean;
  propertyData: any;
  projectData: any;
  globalSettings: any;
  areaSizeUnits: any;

  constructor(
    public modalRef: BsModalRef,
    private _store: Store<AppState>,
    private tenantService: TenantService,
    private _notificationService: NotificationsService,
    private fb: FormBuilder,
    private projectService: ProjectsService,
    private propertyService: PropertyService,
  ) { }

  ngOnInit(): void {
    this.toList.push(this.data?.email);
    this.bulkData = this.data?.bulkData;
    this.shareType = this.data?.shareType;
    this.isData = this.data?.isData;
    this.emailForm = this.fb.group({
      from: [null, Validators.required],
      to: [this.data.email, Validators.required],
      cc: ['', ValidationUtil.emailValidatorMinLength],
      bcc: ['', ValidationUtil.emailValidatorMinLength],
      subject: ['', Validators.required],
      selectedOption: ['Lead'],
      selectedProject: [[]],  // Changed to array for multiple selection
      selectedProperty: [[]], // Changed to array for multiple selection
    });
    this.emailForm.get('selectedOption').valueChanges.subscribe((value: string) => {
      this.selectedTemplate = null;
      this.template = '';
      this.emailForm.get('selectedProject').reset();
      this.emailForm.get('selectedProperty').reset();
      this.emailForm.get('selectedProject').clearValidators();
      this.emailForm.get('selectedProperty').clearValidators();

      if (value === 'Lead') {
        this._store.dispatch(new FetchTemplateModule(0));
        this.variables = this.globalSettings?.isDualOwnershipEnabled
          ? LEAD_TEMPLATE_VARIABLES.filter((variable) => variable !== '#Assign To#').sort()
          : LEAD_TEMPLATE_VARIABLES.filter(
            (variable) => variable !== '#Primary Owner#' && variable !== '#Secondary Owner#'
          ).sort();

      } else if (value === 'Project') {
        this.emailForm.get('selectedProject').setValidators(Validators.required);
        this.emailForm.get('selectedProject').markAsUntouched();
        this._store.dispatch(new FetchProjectIdWithName());
        this._store.dispatch(new FetchTemplateModule(5));
        this.variables = PROJECT_TEMPLATE_VARIABLES.sort();

      } else if (value === 'Property') {
        this.emailForm.get('selectedProperty').setValidators(Validators.required);
        this.emailForm.get('selectedProperty').markAsUntouched();
        this._store.dispatch(new FetchPropertyWithIdNameList());
        this._store.dispatch(new FetchTemplateModule(6));
        this.variables = PROPERTY_TEMPLATE_VARIABLES.sort();
      }

      // Update form controls to reflect new validators
      this.emailForm.get('selectedProject').updateValueAndValidity();
      this.emailForm.get('selectedProperty').updateValueAndValidity();
    });

    this._store.dispatch(new FetchTemplateModule(0));
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettings = data;
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
        const excludeVariables = new Set<string>();

        if (data?.isDualOwnershipEnabled) {
          excludeVariables.add('#Assign To#');
        } else {
          excludeVariables.add('#Primary Owner#');
          excludeVariables.add('#Secondary Owner#');
        }

        if (data?.isCustomLeadFormEnabled) {
          excludeVariables.add('#No Of BHK#');
          excludeVariables.add('#BHK Type#');
        } else {
          excludeVariables.add('#BR#');
          excludeVariables.add('#Beds#');
          excludeVariables.add('#Baths#');
          excludeVariables.add('#Furnish Status#');
          excludeVariables.add('#Preferred Floor#');
          excludeVariables.add('#Offering Type#');
          excludeVariables.add('#Secondary Owner#');
          excludeVariables.add('#Property Area#');
          excludeVariables.add('#Net Area#');
          excludeVariables.add('#Unit Number or Name#');
          excludeVariables.add('#Cluster Name#');
          excludeVariables.add('#Nationality#');
        }

        this.variables = LEAD_TEMPLATE_VARIABLES.filter(variable => !excludeVariables.has(variable)).sort();
      });
    this._store.dispatch(new FetchAreaUnitList());
    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });
    this._store
      .select(getEmailSMTPByUserId)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.EmailList = data;
        this.fromList = data;
      });
    this._store
      .select(getTemplatesModule)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        this.isTemplatesLoading = res?.isTemplatesLoading;
        if (res != '' && res != undefined) {
          this.templates = res.templates
            .filter((data: any) => data)
            .slice()
            .sort((a: any, b: any) => a.title.localeCompare(b.title));
        }
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((user: any) => {
        this.allUserList = user;
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.tenantName = this.userData?.organizationName;
      });

    this._store
      .select(getProjectsIDWithName)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allProjectList = data
          ?.filter((data: any) => data.name)
          .slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      })

    this._store
      .select(getProjectsIDWithNameIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.projectListIsLoading = data;
      });

    this._store
      .select(getPropertyWithIdNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allPropertyList = data.slice().sort((a: any, b: any) => {
          const nameA = a.name || '';
          const nameB = b.name || '';
          return nameA.localeCompare(nameB);
        });
      });

    this._store
      .select(getPropertyWithIdLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.propertyListIsLoading = data;
      });

  }

  sendMessage() {
    if (!this.emailForm.valid) {
      validateAllFormFields(this.emailForm);
      return;
    }
    if (this.template.trim() === '') {  
      this._notificationService.warn('Email Body Required');
      return;
    }
    if (this.ccEnabled && this.emailForm.get('cc').valid) {
      this.ccList.push(this.emailForm.get('cc').value);
    }
    if (this.bccEnabled && this.emailForm.get('bcc').valid) {
      this.bccList.push(this.emailForm.get('bcc').value);
    }

    let fileSize = this.files.reduce(
      (totalSize: number, file: any) => totalSize + file.size,
      0
    );

    if (fileSize > 25 * 1024 * 1024) {
      this._notificationService.warn('File size should not exceed 25MB');
      return;
    }

    let userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    let emailForm = this.emailForm.value;

    let email: any = [];
    const ccMails = this.ccList.filter((cc) => cc);
    const bccMails = this.bccList.filter((bcc) => bcc);
    email = {
      CurrentUserId: userId,
      From: emailForm.from.from,
      ServerName: emailForm.from.serverName,
      Port: emailForm.from.port,
      UserName: emailForm.from.userName,
      Password: emailForm.from.password,
      Priority: 1,
      To: [this.data?.email],
      Body: this.messagePreview.replace(/\n/g, '<br>'),
      Subject: emailForm.subject,
      CC: [...ccMails],
      BCC: [...bccMails],
    };

    this._store.dispatch(new SendTestEmailBulk([email], this.files));
    const payload: any = {
      contactType: ContactType[this.shareType],
      message: this.message,
    };

    const ids: string[] = this.bulkData?.map((item: any) => item?.id);

    if (this.bulkData && this.isData) {
      payload.prospectIds = ids;
    } else if (!this.bulkData && this.isData) {
      payload.prospectId = this.data.id;
    } else if (this.bulkData && !this.isData) {
      payload.leadIds = ids;
    } else if (!this.bulkData && !this.isData) {
      payload.leadId = this.data.id;
    }

    if (this.bulkData && this.isData) {
      this._store.dispatch(new CommunicationBulkDataMessage(payload));
    } else if (!this.bulkData && this.isData) {
      this._store.dispatch(new CommunicationDataMessage(payload));
    } else if (this.bulkData && !this.isData) {
      this._store.dispatch(new CommunicationBulkMessage(payload));
    } else if (!this.bulkData && !this.isData) {
      this._store.dispatch(new CommunicationMessage(payload));
    }

    let payloadCount: any = {
      contactType: ContactType[this.shareType],
    };

    if (this.bulkData) {
      payloadCount.ids = ids;
    } else {
      payloadCount.id = this.data.id;
    }
    if (this.bulkData && this.isData) {
      this._store.dispatch(new CommunicationBulkDataCount(payloadCount));
    } else if (!this.bulkData && this.isData) {
      this._store.dispatch(
        new CommunicationDataCount(payloadCount.id, payloadCount)
      );
    } else if (this.bulkData && !this.isData) {
      this._store.dispatch(new CommunicationBulkCount(payloadCount));
    } else if (!this.bulkData && !this.isData) {
      this._store.dispatch(
        new CommunicationCount(payloadCount.id, payloadCount)
      );
    }
    this.modalRef.hide();
  }

  addVariable(variable: string) {
    if (this.isShowLeadPreview) {
      this.isShowVariablePopup = false;
      return;
    }
    const textToInsert = ' ' + variable;

    const textarea = document.getElementById(
      'txtLeadMsg'
    ) as HTMLTextAreaElement;

    const cursorPosition = textarea.selectionStart;

    let textBeforeCursor = textarea.value.substring(0, cursorPosition);
    let textAfterCursor = textarea.value.substring(cursorPosition);

    if (cursorPosition === undefined || cursorPosition === null) {
      textBeforeCursor = textarea.value;
      textAfterCursor = ' ';
    }

    textarea.value = textBeforeCursor + textToInsert + textAfterCursor;

    textarea.selectionStart = cursorPosition + textToInsert.length;
    textarea.selectionEnd = cursorPosition + textToInsert.length;

    this.template = textarea.value;

    this.isShowVariablePopup = false;
  }

  templateChange() {
    if (!this.selectedTemplate) {
      this.emailForm.get('subject').patchValue(null);
      this.template = '';

      return;
    }
    this.emailForm.get('subject').patchValue(this.selectedTemplate.title);
    this.template =
      (this.selectedTemplate.header
        ? this.selectedTemplate.header + '\n'
        : '') +
      `${this.selectedTemplate.message}` +
      (this.selectedTemplate.footer ? '\n' + this.selectedTemplate.footer : '');
  }



  get messagePreview() {
    if (this.emailForm.get('selectedOption').value === 'Project') {
      return this.projectData.map((project: any) => {
        const projectData = { ...project, leadName: this.data?.name };
        return ProjectTemplateMsg(
          this.template,
          projectData,
          this.areaSizeUnits,
          this.tenantName,
          '',
          '',
          this.allUserList,
          'share-project',
          this.userData
        );
      }).join('\n\n')?.replace(/\\n/g, '\n');
    }
    if (this.emailForm.get('selectedOption').value === 'Property') {
      return this.propertyData.map((property: any) => {
        const propertyData = { ...property, leadName: this.data?.name };
        return PropertyTemplateMsg(
          this.template,
          propertyData,
          this.areaSizeUnits,
          this.tenantName,
          '',
          '',
          true,
          'share-property',
          this.userData,
          this.currentDate
        );
      }).join('\n\n')?.replace(/\\n/g, '\n');
    }
    return LeadTemplateMsg(
      this.template,
      this.data,
      this.tenantName,
      this.defaultCurrency,
      '',
      '',
      this.allUserList,
      this.userData,
      this.currentDate
    )?.replace(/\\n/g, '\n');
  }

  toggleLeadPreview() {
    this.isShowLeadPreview = !this.isShowLeadPreview;
  }

  onFileSelection(event: any) {
    let file = event.target.files;
    this.selectedFile = file;
    this.files = [...this.files, ...file];
  }

  deleteFiles(startIndex: number, count: number): void {
    this.files.splice(startIndex, count);
  }

  addCCToList() {
    const ccValue = this.emailForm.get('cc').value;
    if (ccValue && this.emailForm.get('cc').valid) {
      this.ccList = [...this.ccList, ccValue];
      this.emailForm.get('cc').setValue(null);
    }
  }

  addBCCToList() {
    const bccValue = this.emailForm.get('bcc').value;
    if (bccValue && this.emailForm.get('bcc').valid) {
      this.bccList = [...this.bccList, bccValue];
      this.emailForm.get('bcc').setValue(null);
    }
  }

  removeCCFromList(index: number): void {
    if (index >= 0 && index < this.ccList?.length) {
      this.ccList.splice(index, 1);
    }
  }

  removeBCCFromList(index: number): void {
    if (index >= 0 && index < this.bccList?.length) {
      this.bccList.splice(index, 1);
    }
  }

  projectPropertyChange(type: string) {
    if (type === 'Project' && this.emailForm.get('selectedProject').value?.length) {
      let ids:any = this.emailForm.get('selectedProject').value;
      this.projectService.getProjectsByIds(ids).subscribe((res: any) => {
        if(res.succeeded)
        this.projectData = res.data?.map((project: any) => ({
          ...project,
          leadName: this.data?.name,
        }));
      });
    } else if (type === 'Property' && this.emailForm.get('selectedProperty').value?.length) {
      let ids:any = this.emailForm.get('selectedProperty').value;
      this.propertyService.getPropertiesByIds(ids).subscribe((res: any) => {
        if(res.succeeded)
        this.propertyData = res.data?.map((property: any) => ({
          ...property,
          leadName: this.data?.name,
        }));
      });
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
