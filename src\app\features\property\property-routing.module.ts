import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { AddPropertiesComponent } from 'src/app/features/property/add-property/add-property.component';
import { PropertyPreviewComponent } from 'src/app/features/property/add-property/property-preview/property-preview.component';
import { AddressBulkUploadComponent } from 'src/app/features/property/address-bulk-upload/address-bulk-upload.component';
import { BulkUploadComponent } from 'src/app/features/property/bulk-upload/bulk-upload.component';
import { ListingAdvanceFilterComponent } from 'src/app/features/property/listing-management/listing-advance-filter/listing-advance-filter.component';
import { ListingManagementComponent } from 'src/app/features/property/listing-management/listing-management.component';
import { ListingSyncTrackerComponent } from 'src/app/features/property/listing-management/listing-sync-tracker/listing-sync-tracker.component';
import { PropertyListingComponent } from 'src/app/features/property/listing-management/property-listing/property-listing.component';
import { PropertiesActionGridComponent } from 'src/app/features/property/manage-properties/action-grid-child/action-grid-child.component';
import { ManagePropertiesComponent } from 'src/app/features/property/manage-properties/manage-properties.component';
import { PropertyAdvanceFilterComponent } from 'src/app/features/property/manage-properties/property-advance-filter/property-advance-filter.component';
import { LeadsShareDataComponent } from 'src/app/features/property/matching-leads/leads-share-data/leads-share-data.component';
import { MatchingLeadsComponent } from 'src/app/features/property/matching-leads/matching-leads.component';
import { PropertyBulkUpdateComponent } from 'src/app/features/property/property-bulk-update/property-bulk-update.component';
import { PropertyRootLayoutComponent } from 'src/app/features/property/property-root.component';
import { PropertyStatusComponent } from 'src/app/features/property/property-status/property-status.component';
import { ReferenceIdManagementComponent } from './reference-id-management/reference-id-management.component';

export const routes: Routes = [
  {
    path: '',
    component: PropertyRootLayoutComponent,
    children: [
      { path: '', redirectTo: 'manage-properties', pathMatch: 'full' },
      { path: 'manage-properties', component: ManagePropertiesComponent },
      { path: 'add-property', component: AddPropertiesComponent },
      { path: 'edit-property/:id', component: AddPropertiesComponent },
      { path: 'bulk-upload', component: BulkUploadComponent },
      { path: 'manage-listing', component: ListingManagementComponent },
      { path: 'add-listing', component: AddPropertiesComponent },
      { path: 'manage-reference-id', component: ReferenceIdManagementComponent },
      { path: 'edit-listing/:id', component: AddPropertiesComponent },
      { path: 'listing-bulk-upload', component: BulkUploadComponent },
      { path: 'bulk-address', component: AddressBulkUploadComponent },
      { path: 'reference-bulk-upload', component: BulkUploadComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PropertyRoutingModule { }

export const PROPERTY_DECLARATIONS = [
  PropertyRootLayoutComponent,
  ManagePropertiesComponent,
  AddPropertiesComponent,
  PropertiesActionGridComponent,
  PropertyStatusComponent,
  MatchingLeadsComponent,
  LeadsShareDataComponent,
  PropertyPreviewComponent,
  BulkUploadComponent,
  PropertyAdvanceFilterComponent,
  ListingManagementComponent,
  ListingSyncTrackerComponent,
  ListingAdvanceFilterComponent,
  PropertyListingComponent,
  PropertyAdvanceFilterComponent,
  PropertyBulkUpdateComponent,
  AddressBulkUploadComponent,
];
