import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { select, Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { catchError, EMPTY, map, mergeMap, of, switchMap, throwError, withLatestFrom } from 'rxjs';
import { CloseModal, OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import { ListingSiteService } from 'src/app/services/controllers/listingSite.service';
import { PropertyService } from 'src/app/services/controllers/properties.service';
import { getPermissions } from '../permissions/permissions.reducers';
import { FetchPropertyLeadsCountByIds } from '../property/property.actions';
import {
  AddListingAcounts,
  AddListingAcountsSuccess,
  AddToList,
  DeleteListingAcounts,
  DeList,
  ExcelUpload,
  ExcelUploadSuccess,
  FetchAddress,
  FetchAddressSuccess,
  FetchAllListing,
  FetchAllListingSuccess,
  FetchCommunities,
  FetchCommunitiesSuccess,
  FetchExcelTrackerList,
  FetchExcelTrackerListSuccess,
  FetchListingAcounts,
  FetchListingAcountsSuccess,
  FetchListingBaseCount,
  FetchListingBaseCountSuccess,
  FetchListingSource,
  FetchListingSourceSuccess,
  FetchListingSourceWithId,
  FetchListingSourceWithIdSuccess,
  FetchListingTopCount,
  FetchListingTopCountSuccess,
  FetchSubCommunities,
  FetchSubCommunitiesSuccess,
  FetchSyncListingList,
  FetchSyncListingListSuccess,
  FetchSyncListingSource,
  FetchSyncListingSourceSuccess,
  ListingExport,
  ListingExportSuccess,
  ListingSiteActionTypes,
  syncListing,
  UpdateListingAcounts,
  UpdateListingAcountsSuccess,
  UpdateListingPayload,
  UploadAddressMappedColumns,
} from './listing-site.actions';

@Injectable()
export class ListingSiteEffects {
  getListingSiteSources$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.FETCH_LISTING_SOURCE),
      switchMap((action: FetchListingSource) => {
        return this.listingSiteService.getListingSources().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchListingSourceSuccess(resp?.items);
            }
            return new FetchListingSourceSuccess([]);
          }),
          catchError((err: Error) => of(new OnError(err)))
        );
      })
    )
  );

  getListingSiteSourcesWithId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.FETCH_LISTING_SOURCE_WITH_ID),
      switchMap((action: FetchListingSourceWithId) => {
        return this.listingSiteService.getListingSourcesWithId().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchListingSourceWithIdSuccess(resp?.data);
            }
            return new FetchListingSourceWithIdSuccess([]);
          }),
          catchError((err: Error) => of(new OnError(err)))
        );
      })
    )
  );

  fetchAllListing$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.FETCH_ALL_LISTING),
      withLatestFrom(
        this.store.pipe(
          select((state: any) => state?.listingSite.filtersPayload)
        ),
        this.store.select(getPermissions)
      ),
      switchMap(([action, filtersFromState, permissions]: any) => {
        let filterPayload: any = { ...filtersFromState };
        if (!filterPayload?.permission) {
          const permissionSet = new Set(permissions)
          filterPayload = {
            ...filterPayload,
            permission: permissionSet.has('Permissions.Properties.View') ? 2 : permissionSet.has(
              'Permissions.Properties.ViewAssigned'
            ) ? 1 : 0
          }
          this.store.dispatch(new UpdateListingPayload(filterPayload))
          return EMPTY;
        }
        this.store.dispatch(new FetchListingTopCount());
        this.store.dispatch(new FetchListingBaseCount());
        return this.propertyService.getAllListing(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              const propertyIds: string[] | undefined = resp?.items?.map(
                (property: any) => property?.id
              );
              this.store.dispatch(
                new FetchPropertyLeadsCountByIds(propertyIds)
              );
              return new FetchAllListingSuccess(resp);
            }
            return new FetchAllListingSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  fetchAllListingTopCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.FETCH_LISTING_TOP_COUNT),
      withLatestFrom(
        this.store.pipe(
          select((state: any) => state?.listingSite.filtersPayload)
        )
      ),
      switchMap(([action, filtersFromState]: any) => {
        const filterPayload: any = { ...filtersFromState };
        return this.propertyService.getAllListingTopCount(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchListingTopCountSuccess(resp?.data);
            }
            return new FetchListingTopCountSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  fetchAllListingBaseCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.FETCH_LISTING_BASE_COUNT),
      withLatestFrom(
        this.store.pipe(
          select((state: any) => state?.listingSite.filtersPayload)
        )
      ),
      switchMap(([action, filtersFromState]: any) => {
        const filterPayload: any = { ...filtersFromState };
        return this.propertyService.getAllListingBaseCount(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchListingBaseCountSuccess(resp?.data);
            }
            return new FetchListingBaseCountSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateListingPayload$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.UPDATE_LISTING_PAYLOAD),
      switchMap((action: UpdateListingPayload) => {
        return of(new FetchAllListing());
      })
    )
  );

  getAllListedAcounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.FETCH_LISTING_ACOUNTS),
      switchMap((action: FetchListingAcounts) => {
        return this.listingSiteService.getAllListingAcounts(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchListingAcountsSuccess(resp?.items);
            }
            return new FetchListingAcountsSuccess([]);
          }),
          catchError((err: Error) => of(new OnError(err)))
        );
      })
    )
  );

  createlistingAcounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.ADD_LISTING_ACOUNTS),
      map((action: AddListingAcounts) => action.payload),
      switchMap((data: any) => {
        return this.listingSiteService.createListingAcounts(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Acount added Successfully');
              this.store.dispatch(new AddListingAcountsSuccess());
              return new FetchListingAcounts(data?.listingSourceId);
            }
            return new FetchListingAcountsSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updatelistingAcounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.UPDATE_LISTING_ACOUNTS),
      map((action: UpdateListingAcounts) => action.payload),
      switchMap((data: any) => {
        return this.listingSiteService.updateListingAcounts(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Acount Updated Successfully');
              this.store.dispatch(new UpdateListingAcountsSuccess());
              return new FetchListingAcounts(data?.listingSourceId);
            }
            return new FetchListingAcountsSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deletelistingAcounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.DELETE_LISTING_ACOUNTS),
      switchMap((data: DeleteListingAcounts) => {
        return this.listingSiteService
          .deleteListingAcounts(data.payload.id)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                this._notificationService.success(
                  'Acount Deleted Successfully'
                );
                return new FetchListingAcounts(data?.payload?.sourceId);
              }
              return new FetchListingAcountsSuccess({});
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  syncListing$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.SYNC_LISTING),
      map((action: syncListing) => action.payload),
      switchMap((data: any) =>
        this.listingSiteService.syncListing(data).pipe(
          mergeMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Sync Successful');
              return EMPTY;
            }
            return of(new OnError(resp?.errors));
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );


  getSyncListingList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.FETCH_SYNC_LISTING_LIST),
      map((action: FetchSyncListingList) => action),
      switchMap((data: any) => {
        return this.listingSiteService
          .syncListingTracker(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchSyncListingListSuccess(resp);
              }
              return new FetchSyncListingListSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  list$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.ADD_TO_LIST),
      switchMap((action: AddToList) => {
        return this.propertyService.list(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Property Listed Successfully');
              return new FetchAllListing();
            }
            return null;
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  delist$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.DELIST),
      switchMap((action: DeList) => {
        return this.propertyService.delist(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Property Delisted Successfully');
              return new FetchAllListing();
            }
            return null;
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addExcel$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.EXCEL_UPLOAD),
      switchMap((action: ExcelUpload) => {
        return this.listingSiteService.uploadExcel(action.file).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Excel uploaded Successfully'
              );
              return new ExcelUploadSuccess(resp.data);
            } else {
              this.store.dispatch(new CloseModal());
              this._notificationService.warn(`${resp.message}`);
              return new FetchListingAcounts(resp);
            }
          }),
          catchError((err: any) => {
            throwError(err);
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  uploadMappedColumns$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.UPLOAD_MAPPED_COLUMNS),
      switchMap((action: UploadAddressMappedColumns) => {
        return this.listingSiteService.uploadMappedColumns(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (resp.data) {
                if (resp.data?.excelUrl) {
                  const dataCount = resp.message?.DataCount || '';
                  this._notificationService.success(
                    `${dataCount} Invalid Data Not Uploaded`
                  );
                  return new ExcelUploadSuccess(resp.data);
                } else {
                  this._notificationService.success(
                    'Excel Uploaded Successfully'
                  );
                  return new ExcelUploadSuccess(resp.data);
                }
              } else {
                this._notificationService.error(resp.message);
              }
              return new FetchListingAcounts({});
            }
            return new FetchListingAcountsSuccess({});
          }),
          catchError((err: any) => {
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getCommunities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.FETCH_COMMUNITIES),
      switchMap((action: FetchCommunities) => {
        return this.listingSiteService.getCommunities().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCommunitiesSuccess(resp?.data);
            }
            return new FetchCommunitiesSuccess([]);
          }),
          catchError((err: Error) => of(new OnError(err)))
        );
      })
    )
  );

  getSubCommunities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.FETCH_SUB_COMMUNITIES),
      switchMap((action: FetchSubCommunities) => {
        return this.listingSiteService.getSubCommunities().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchSubCommunitiesSuccess(resp?.data);
            }
            return new FetchSubCommunitiesSuccess([]);
          }),
          catchError((err: Error) => of(new OnError(err)))
        );
      })
    )
  );

  getTrackerList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.FETCH_EXCEL_TRACKER_LIST),
      map((action: FetchExcelTrackerList) => action),
      switchMap((data: any) => {
        return this.listingSiteService.getTracker(data.pageNumber, data.pageSize).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchExcelTrackerListSuccess(resp);
            }
            return new FetchExcelTrackerListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAddressWithId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.FETCH_ADDRESS_WITH_ID),
      switchMap((action: FetchAddress) => {
        return this.listingSiteService.getAddress().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAddressSuccess(resp?.items);
            }
            return new FetchAddressSuccess([]);
          }),
          catchError((err: Error) => of(new OnError(err)))
        );
      })
    )
  );

  exportListing$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.LISTING_EXPORT),
      switchMap((action: ListingExport) => {
        return this.propertyService.listingExport(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Listing property(s) are being exported in excel format`
              );
              return new ListingExportSuccess(resp);
            }
            return new ListingExportSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getSyncListingSiteSources$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ListingSiteActionTypes.FETCH_SYNC_LISTING_SOURCE),
      switchMap((action: FetchSyncListingSource) => {
        return this.listingSiteService.getSyncListingSources().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchSyncListingSourceSuccess(resp?.data);
            }
            return new FetchSyncListingSourceSuccess([]);
          }),
          catchError((err: Error) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private store: Store<AppState>,
    private _notificationService: NotificationsService,
    private listingSiteService: ListingSiteService,
    private propertyService: PropertyService
  ) { }
}
