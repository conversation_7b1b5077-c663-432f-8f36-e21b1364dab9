import { Component, EventEmitter, Input, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { takeUntil } from 'rxjs';
import { ATTRIBUTES_ITEM, ATTRIBUTES_MAP } from 'src/app/app.constants';
import { Facing, FurnishStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  Attribute,
  Property,
} from 'src/app/core/interfaces/property-microsite.interface';
import { changeCalendar, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'property-attributes',
  templateUrl: './property-attributes.component.html',
})
export class PropertyAttributesComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() propertyInfo: Property = {} as Property;
  @Input() attributesSelection: any;
  @Input() isPropertySoldOut: boolean;
  // currentDate = moment(new Date().toISOString());
  moment = moment;
  FurnishStatus = FurnishStatus;
  Facing = Facing;
  attrMap: any[] = ATTRIBUTES_MAP;
  attributesItems: any = ATTRIBUTES_ITEM;
  AdditionalAttrData: Attribute[];
  userData: any;
  currentDate: Date = new Date();
  getTimeZoneDate = getTimeZoneDate;

  constructor(private store: Store<AppState>,
  ) { }

  ngOnInit() {
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });

    this.AdditionalAttrData = this.propertyInfo?.attributes.filter(
      (item: any) => item.attributeType === 'Additional'
    );
  }

  nth(d: any) {
    // Handle string values for basement and ground floors
    if (typeof d === 'string') {
      if (d === 'B1' || d === 'B2') return '';
      if (d === '0') return '';
      // Convert string numbers to numbers for processing
      const num = parseInt(d, 10);
      if (isNaN(num)) return '';
      d = num;
    }

    if (d > 3 && d < 21) return 'th';
    switch (d % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }
}
