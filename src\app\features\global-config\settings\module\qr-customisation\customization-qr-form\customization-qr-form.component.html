<div routerLink='/global-config/manage-qr' [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
    class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
</div>
<div class="d-flex w-100">
    <div class="w-35 tb-position-absolute z-index-2 tb-w-360px" *ngIf="showEvents || screen?.innerWidth > 1279">
        <div class="w-100 flex-between bg-black-100 p-16">
            <div class="text-white">Form Templates</div>
            <div class="icon ic-close-secondary ic-sm cursor-pointer d-none tb-d-block mr-10 mt-10"
                (click)="showEvents = !showEvents"></div>
        </div>
        <form class="p-16 bg-white h-100-97 scrollbar scroll-hide tb-h-100-108" [formGroup]="qrCustomForm">
            <div class="d-flex">
                <div class="border br-20 bg-white align-center user">
                    <div>
                        <div class="activation" [ngClass]="{'active' : selectedSection == 'Contents'}"
                            (click)="selectedSection = 'Contents'">
                            <span class="icon ic-book ic-sm mr-8 ip-mr-4"
                                [ngClass]="{'active' : selectedSection !== 'Contents'}"></span>
                            <span class="ph-d-none"> Contents</span>
                        </div>
                    </div>
                    <div class="activation" [ngClass]="{'active' : (selectedSection == 'Header')}"
                        (click)="selectedSection = 'Header'">
                        <span class="icon ic-header ic-sm mr-8 ip-mr-4"
                            [ngClass]="{'active' : selectedSection !== 'Header'}"></span>
                        <span class="ph-d-none">Header</span>
                    </div>
                    <div class="activation" [ngClass]="{'active' : selectedSection == 'Footer'}"
                        (click)="selectedSection = 'Footer'">
                        <span class="icon ic-footer ic-sm mr-8 ip-mr-4"
                            [ngClass]="{'active' : selectedSection !== 'Footer'}"></span>
                        <span class="ph-d-none">Footer</span>
                    </div>
                </div>
            </div>
            <!-- contents -->
            <div class="mt-24" *ngIf="selectedSection == 'Contents'">
                <h6>Form Fields</h6>
                <div class="text-dark-gray text-sm  mt-4"> Customize your form fields to your requirement.</div>
                <div class="mt-20 p-10 align-center">
                    <div class="align-center cursor-pointer" (click)="isHideBasicInfo = !isHideBasicInfo">
                        <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                            [ngClass]="{'rotate-270' : isHideBasicInfo}"></span>
                        <h6 class="text-black-200">Basic Info</h6>
                    </div>
                </div>
                <ng-container *ngIf="!isHideBasicInfo">
                    <div class="border-bottom mb-6"></div>
                    <div class="d-flex flex-wrap w-100 position-relative">
                        <div class="w-50 mt-10">
                            <div class="mr-10 border br-4">
                                <div class="py-8 px-10 flex-between">
                                    <div class="fw-600 text-sm text-mud position-relative">Name
                                        <div class="position-absolute text-red text-xl nright-8 ntop-8">*</div>
                                    </div>
                                    <div>
                                        <input type="checkbox" name="darkTheme" formControlName="name"
                                            class="toggle-switch toggle-active-sold pe-none">
                                        <div for="chkToggle" class="switch-label pe-none"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w-50 mt-10">
                            <div class="border br-4">
                                <div class="py-8 px-10 flex-between">
                                    <div class="fw-600 text-sm text-mud position-relative">Primary Number
                                        <div class="position-absolute text-red text-xl nright-8 ntop-8">*</div>
                                    </div>
                                    <div>
                                        <input type="checkbox" name="darkTheme" formControlName="phoneNo"
                                            class="toggle-switch toggle-active-sold pe-none">
                                        <div for="chkToggle" class="switch-label pe-none"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w-50 mt-10">
                            <div class="mr-10 border br-4">
                                <div class="py-8 px-10 flex-between">
                                    <div class="fw-600 text-sm text-mud">Email</div>
                                    <div>
                                        <input type="checkbox" name="darkTheme" formControlName="email"
                                            class="toggle-switch toggle-active-sold">
                                        <div for="chkToggle" class="switch-label"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w-50 mt-10">
                            <div class="border br-4">
                                <div class="py-8 px-10 flex-between">
                                    <div class="fw-600 text-sm text-mud">Alternate Number</div>
                                    <div>
                                        <input type="checkbox" name="darkTheme" formControlName="altPhoneNo"
                                            class="toggle-switch toggle-active-sold">
                                        <div for="chkToggle" class="switch-label"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ng-container>
                <div class="mt-20 p-10 flex-between">
                    <div class="cursor-pointer align-center" (click)="isHideEnquiryInfo = !isHideEnquiryInfo">
                        <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                            [ngClass]="{'rotate-270' : isHideEnquiryInfo}"></span>
                        <h6 class="text-black-200">More Information</h6>
                    </div>
                    <div class="d-flex">
                        <div class="text-dark-gray text-sm align-center me-2"> Select all</div>
                        <div class="position-relative flex-between" (change)="toggleAllCheckboxes()">
                            <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold"
                                [checked]="allCheckboxesChecked()">
                            <div for="chkToggle" class="switch-label"></div>
                        </div>
                    </div>

                </div>
                <ng-container *ngIf="!isHideEnquiryInfo">
                    <div class="border-bottom mb-6"></div>
                    <div class="d-flex flex-wrap w-100 position-relative">
                        <ng-container *ngFor="let field of filteredFields; let i = index">
                            <div class="w-50 mt-10">
                                <div class="border br-4" [ngClass]="{'mr-10': i % 2 == 0}">
                                    <div class="py-8 px-10 flex-between">
                                        <div class="fw-600 text-sm text-mud">{{field.displayName}}</div>
                                        <div>
                                            <input type="checkbox" name="darkTheme"
                                                class="toggle-switch toggle-active-sold"
                                                [formControlName]="field.controlName"
                                                (change)="updatePropertyType(field.controlName)">
                                            <div for="chkToggle" class="switch-label"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </ng-container>
            </div>
            <!-- header -->
            <div class="mt-24" *ngIf="selectedSection == 'Header'">
                <div *ngIf="qrCustomForm.controls['headerDesign'].value == 0">
                    <h6 class="fw-semi-bold text-black-100">Header Template</h6>
                    <div class="text-dark-gray text-xs mt-4">Preview and pick a template. Don't worry, you can
                        change it
                        anytime.</div>
                </div>
                <div class="justify-center cursor-pointer" *ngIf="isAddHeaderClicked">
                    <div class="mt-20 br-18 bg-accent-green align-center p-6 border-green-160"
                        (click)="isAddHeaderClicked = !isAddHeaderClicked"><span
                            class="icon ic-plus  bg-green-140 br-50 ic-xxxs p-10 box-shadow-4"></span><span
                            class="ml-6 fw-semi-bold text-sm text-white">Add Header Section</span></div>
                </div>
                <div *ngIf="qrCustomForm.controls['headerDesign'].value == 0">
                    <div class="w-100 d-flex flex-wrap mt-20">
                        <div *ngFor="let header of [1, 2, 3, 4, 5, 6]; let i = index" class="w-50"
                            (click)="toggleColor(i + 1)"
                            [ngClass]="{'filter': isFiltered[i + 1], 'selected': qrCustomForm.controls['headerDesign'].value === i + 1}">
                            <div class="mt-10 mr-10">
                                <img [ngStyle]="{'filter': isAddHeaderClicked ? 'blur(5px)' : 'none'}"
                                    [type]="'leadrat'"
                                    [appImage]="'../../../../../../assets/images/qr-form/header-' + (i + 1) + '.svg'"
                                    class="cursor-pointer w-100">
                            </div>
                        </div>
                    </div>
                    <div class="justify-center cursor-pointer" *ngIf="!isAddHeaderClicked">
                        <div class="mt-20 br-18 bg-red-350 align-center p-6 border-red" (click)="removeHeaderSection()">
                            <span class="icon ic-plus  bg-red-70 br-50 ic-xxxs p-10 box-shadow-4"></span>
                            <span class="ml-6 fw-semi-bold text-sm text-white"> Remove Header Section</span>
                        </div>
                    </div>
                </div>
                <div class="fw-600 text-large header-5" *ngIf="qrCustomForm.controls['headerDesign'].value !== 0">
                    <div>Selected Template</div>
                    <div class="mt-16">
                        <img [type]="'leadrat'"
                            [appImage]="'../../../../../../assets/images/qr-form/header-' + qrCustomForm.controls['headerDesign'].value + '.svg'"
                            class="w-100 h-100px">
                    </div>
                    <div class="justify-center text-accent-green fw-semi-bold text-sm cursor-pointer text-decoration-underline mt-16"
                        (click)="qrCustomForm.patchValue({ headerDesign: 0 })">Change Template </div>
                    <div class="flex-between mt-20">
                        <div>Edit Header Details</div>
                    </div>
                    <h6 class="text-mud fw-semi-bold mx-10 mt-20">Company Logo</h6>
                    <label *ngIf="!headerPic"
                        class="mt-16 py-8 px-10 br-2 bg-light-pearl cursor-pointer w-100 justify-center text-black-200 fw-semi-bold header-6 align-center">
                        <input type="file" (change)="onFileSelected($event, 'HeaderLogo')"
                            [accept]="'image/x-png,image/gif,image/jpeg,image/tiff'">
                        Upload Logo <span class="icon ic-sm ic-black-200 ic-upload ml-6"></span>
                    </label>
                    <div class="align-center mt-12" *ngIf="headerPic">
                        <div class="mr-20"><img
                                [appImage]="headerPic ? s3BucketUrl+headerPic : (selectedTemplateInfo.header?.logoUrl ? s3BucketUrl+selectedTemplateInfo.header?.logoUrl : '')"
                                [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border" width="80"
                                height="80" class="h-60 w-60 rounded-circle obj-cover border">
                        </div>
                        <div>
                            <label
                                class="text-accent-green fw-semi-bold text-sm cursor-pointer text-decoration-underline">
                                <input type="file" (change)="onFileSelected($event, 'HeaderLogo')"
                                    [accept]="'image/x-png,image/gif,image/jpeg,image/tiff'"> Change Logo
                            </label>
                            <div class="text-red-350 fw-semi-bold text-sm cursor-pointer text-decoration-underline mt-10"
                                (click)="headerPic = null">Remove</div>
                        </div>
                    </div>
                    <div class="flex-between mt-20">
                        <h6 class="fw-semi-bold text-mud">Background Color</h6>
                    </div>
                    <div for="inpHeaderBgColor"
                        class="align-center no-validation border px-10 py-8 br-2 mt-4 position-relative cursor-pointer">
                        <input id="inpHeaderBgColor" type="color" class="w-16 h-16 border-0 p-0"
                            formControlName="headerBgColor">
                        <h6 class="flex-grow-1 fw-semi-bold text-mud ml-10">{{
                            qrCustomForm.controls['headerBgColor'].value }}</h6>
                    </div>
                    <div class="flex-between mt-20">
                        <h6 class="fw-semi-bold text-mud">Text Colour</h6>
                    </div>
                    <div for="inpHeaderTextColor"
                        class="align-center no-validation border px-10 py-8 br-2 mt-4 position-relative cursor-pointer">
                        <input id="inpHeaderTextColor" type="color" class="w-16 h-16 border-0 p-0"
                            formControlName="headerTextColor">
                        <h6 class="flex-grow-1 fw-semi-bold text-mud ml-10">{{
                            qrCustomForm.controls['headerTextColor'].value }}</h6>
                    </div>
                    <h6 class="fw-semi-bold mt-20 text-mud mb-4">Add Company Name</h6>
                    <form-errors-wrapper>
                        <input formControlName="orgCompanyName" type="text" id="inpAddCompany"
                            data-automate-id="inpAddCompany"
                            placeholder="ex. easy gharoffice solutions private limited">
                    </form-errors-wrapper>
                </div>
            </div>
            <!-- footer -->
            <div class="mt-24" *ngIf="selectedSection == 'Footer'">
                <div *ngIf="qrCustomForm.controls['footerDesign'].value == 0">
                    <h6 class="fw-semi-bold text-black-100">Footer Template</h6>
                    <div class="text-dark-gray text-xs mt-4">Preview and pick a template. Don't worry, you can
                        change it
                        anytime.</div>
                </div>
                <div class="justify-center cursor-pointer" *ngIf="isAddFooterClicked">
                    <div class="mt-20 br-18 bg-accent-green align-center p-6 border-green-160"
                        (click)="isAddFooterClicked = !isAddFooterClicked"><span
                            class="icon ic-plus  bg-green-140 br-50 ic-xxxs p-10 box-shadow-4"></span><span
                            class="ml-6 fw-semi-bold text-sm text-white">Add Footer Section</span></div>
                </div>
                <div *ngIf="qrCustomForm.controls['footerDesign'].value == 0">
                    <div class="w-100 d-flex flex-wrap mt-20">
                        <div *ngFor="let footer of [1, 2, 3, 4, 5, 6]; let i = index" class="w-50"
                            (click)="toggleImage(i + 1)"
                            [ngClass]="{'filter': isData[i + 1], 'selected': qrCustomForm.controls['footerDesign'].value === i + 1}">
                            <div class="mt-10 mr-10">
                                <img [ngStyle]="{'filter': isAddFooterClicked ? 'blur(5px)' : 'none'}"
                                    [type]="'leadrat'"
                                    [appImage]="'../../../../../../assets/images/qr-form/footer-' + (i + 1) + '.svg'"
                                    class="cursor-pointer w-100">
                            </div>
                        </div>
                    </div>
                    <div class="justify-center cursor-pointer" *ngIf="!isAddFooterClicked">
                        <div class="mt-20 br-18 bg-red-350 align-center p-6 border-red" (click)="removeFooterSection()">
                            <span class="icon ic-plus  bg-red-70 br-50 ic-xxxs p-10 box-shadow-4"></span><span
                                class="ml-6 fw-semi-bold text-sm text-white"> Remove Footer Section</span>
                        </div>
                    </div>
                </div>
                <div class="fw-600 text-large header-5" *ngIf="qrCustomForm.controls['footerDesign'].value !== 0">
                    <div>Selected Template</div>
                    <div class="mt-16">
                        <img [type]="'leadrat'"
                            [appImage]="'../../../../../../assets/images/qr-form/footer-' + qrCustomForm.controls['footerDesign'].value + '.svg'"
                            class="w-100 h-100px">
                    </div>
                    <div class="justify-center text-accent-green fw-semi-bold text-sm cursor-pointer text-decoration-underline mt-16"
                        (click)="qrCustomForm.patchValue({ footerDesign: 0 })">Change
                        Template </div>
                    <div class="flex-between mt-20">
                        <div>Edit Footer Details</div>
                    </div>
                    <h6 class="text-mud fw-semi-bold mx-10 mt-20">Company Logo</h6>
                    <label *ngIf="!footerPic"
                        class="mt-16 py-8 px-10 br-2 bg-light-pearl cursor-pointer w-100 justify-center text-black-200 fw-semi-bold header-6 align-center">
                        <input type="file" (change)="onFileSelected($event, 'FooterLogo')"
                            [accept]="'image/x-png,image/gif,image/jpeg,image/tiff'">
                        Upload Logo <span class="icon ic-sm ic-black-200 ic-upload ml-6"></span>
                    </label>
                    <div class="align-center mt-12" *ngIf="footerPic">
                        <div class="mr-20"><img [appImage]="footerPic ? s3BucketUrl+footerPic : ''"
                                [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border" width="80"
                                height="80" class="h-60 w-60 rounded-circle obj-cover border"></div>
                        <div>
                            <label
                                class="text-accent-green fw-semi-bold text-sm cursor-pointer text-decoration-underline">
                                <input type="file" (change)="onFileSelected($event, 'FooterLogo')"
                                    [accept]="'image/x-png,image/gif,image/jpeg,image/tiff'"> Change Logo
                            </label>
                            <div class="text-red-350 fw-semi-bold text-sm cursor-pointer text-decoration-underline mt-10"
                                (click)="footerPic = null">
                                Remove</div>
                        </div>
                    </div>
                    <div class="flex-between mt-20">
                        <h6 class="fw-semi-bold text-mud">Background Color</h6>
                    </div>
                    <div for="inpFooterBgColor"
                        class="align-center no-validation border px-10 py-8 br-2 mt-4 position-relative cursor-pointer">
                        <input id="inpFooterBgColor" type="color" class="w-16 h-16 border-0 p-0"
                            formControlName="footerBgColor">
                        <h6 class="flex-grow-1 fw-semi-bold text-mud ml-10">{{
                            qrCustomForm.get('footerBgColor').value }}</h6>
                    </div>
                    <div class="flex-between mt-20">
                        <h6 class="fw-semi-bold text-mud">Text Colour</h6>
                    </div>
                    <div for="inpFooterTextColor"
                        class="align-center no-validation border px-10 py-8 br-2 mt-4 position-relative cursor-pointer">
                        <input id="inpFooterTextColor" type="color" class="w-16 h-16 border-0 p-0"
                            formControlName="footerTextColor">
                        <h6 class="flex-grow-1 fw-semi-bold text-mud ml-10">{{
                            qrCustomForm.controls['footerTextColor'].value }}</h6>
                    </div>
                    <h6 class="fw-semi-bold text-mud mt-20">Add Company Address</h6>
                    <div class="form-group mt-4">
                        <textarea rows="3" name="orgAddress" id="orgAddress" data-automate-id="orgAddress"
                            class="scrollbar" formControlName="orgAddress"
                            placeholder="ex. 1585/1 level, 2, 20th Main Rd, 1st Sector, HSR Layout, Bengaluru, Karnataka 560102"></textarea>
                    </div>
                    <h6 class="fw-semi-bold mt-20 text-mud">Add Phone No</h6>
                    <form-errors-wrapper label="phone" [control]="qrCustomForm.controls['orgPhoneNo']">
                        <input type="tel" placeholder='ex. 9133XXXXXX' class="mt-4" formControlName="orgPhoneNo"
                            [min]="10">
                    </form-errors-wrapper>
                    <h6 class="fw-semi-bold mt-20 text-mud mb-4">Add Email</h6>
                    <form-errors-wrapper [control]="qrCustomForm.controls['orgEmail']">
                        <input formControlName="orgEmail" type="email" id="inpAddEmail" data-automate-id="inpAddEmail"
                            placeholder="ex. <EMAIL>">
                    </form-errors-wrapper>
                    <ng-container
                        *ngIf="qrCustomForm.controls['footerDesign'].value == 1 || qrCustomForm.controls['footerDesign'].value == 3 || qrCustomForm.controls['footerDesign'].value == 4">
                        <h6 class="fw-semi-bold mt-20 text-mud">Social Profiles</h6>
                        <div class="mt-10">
                            <div *ngFor="let media of socialMedia; let i = index">
                                <div class="align-center mt-6">
                                    <img [type]="'leadrat'" [appImage]="media.image" class="cursor-pointer br-50 mr-10"
                                        height="24" width="24" />
                                    <div class="form-group w-100">
                                        <input [formControlName]="media.controlName" type="text"
                                            [placeholder]="media.placeholder">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </div>
        </form>
    </div>
    <div class="w-65 tb-w-100 position-relative">
        <div class="py-12 px-20 bg-gray-dark flex-between">
            <div class="align-center fw-600 text-dark-gray">
                <h5 class="cursor-pointer d-none tb-d-block" (click)="showEvents = !showEvents"> Form Templates
                </h5>
                <h5 class="cursor-pointer text-dark-red ml-20 fw-600" routerLink='/global-config/manage-qr'>
                    Cancel
                </h5>
            </div>
            <div class="align-center">
                <div class="icon ic-large ic-desktop cursor-pointer" [ngClass]="showWidth ?'ic-coal':'ic-slate-90'"
                    (click)="showWidth = true"></div>
                <div class="border-gray-right h-16 mx-20 ph-mx-10"></div>
                <div class="icon ic-large ic-mobile cursor-pointer" [ngClass]="showWidth ?'ic-slate-90':'ic-coal'"
                    (click)="showWidth = false"></div>
            </div>
            <h5 class="align-center fw-600"
                [ngClass]="(qrCustomForm.dirty || isNameTouched) && !isTemplateNameExists && templateName !=='' ? 'text-coal' : 'text-dark-gray'"
                (click)="(qrCustomForm.dirty || isNameTouched) && !isTemplateNameExists && templateName !=='' ? saveData() : ''">
                <div class="cursor-pointer">Save</div>
            </h5>
        </div>
        <div class="bg-dot-pattern px-40 pt-36 pb-20 ip-px-20">
            <div class="pb-10">
                <div class="field-label-req mt-0">
                    <div class="align-center position-relative">
                        <input type="text" class="br-4 pl-10 pr-20 py-8 w-120 border outline-0 mr-4"
                            placeholder="Untitled Form" [(ngModel)]="templateName"
                            (input)="onTemplateNameChange();isNameTouched = true;">
                        <div class="icon ic-pen ic-coal ic-xxs position-absolute right-5 top-6 mx-8 my-6">
                        </div>
                    </div>
                </div>
                <div class="h-10 m-0 mb-4">
                    <div *ngIf="isTemplateNameExists" class="ms-3 text-error-red">Template Name already Exists</div>
                </div>

            </div>
            <div class="justify-center">
                <div class="h-100-190 scrollbar scroll-hide" [ngClass]="showWidth ?'w-100':' w-50 ip-w-100'">
                    <!-- header -->
                    <div *ngIf="selectedSection === 'Contents' && qrCustomForm.controls['headerDesign'].value == 0"
                        class="hover border-mud-bottom bg-white">
                        <div class="hover-div cursor-pointer" (click)="onEditSectionHeader()">
                            <div class="flex-center"
                                [ngStyle]="{ 'background-color': qrCustomForm.controls['headerBgColor'].value, 'color': qrCustomForm.controls['headerTextColor'].value }">
                                <div class="w-150 br-18 bg-dark-700 p-12 justify-center my-40">
                                    <div class="icon ic-pen ic-xxs  mr-6"></div>
                                    <div class="text-white">Edit Section</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ng-container *ngIf="(!isAddHeaderClicked)">
                        <div [ngStyle]="{ 'background-color': qrCustomForm.controls['headerBgColor'].value, 'color': qrCustomForm.controls['headerTextColor'].value }"
                            class="border-green-2 justify-center-col p-30"
                            *ngIf="(qrCustomForm.controls['headerDesign'].value == 0 && selectedSection === 'Header')">
                            <ng-container *ngIf="(showChooseTemplate && selectedSection === 'Header') || selectedSection ===
                                    'Header'">
                                <h2 class="fw-semi-bold text-center">Choose template!</h2>
                                <div class="fw-300 text-sm mt-10">Please choose the header from the
                                    selection of
                                    templates on the left panel. Your chosen header will set the visual tone
                                    for
                                    your
                                    document and make it stand out.</div>
                            </ng-container>
                        </div>
                        <div *ngIf="qrCustomForm.controls['headerDesign'].value !== 0" class="p-30 border-bottom"
                            [ngStyle]="{ 'background-color': qrCustomForm.controls['headerBgColor'].value, 'color': qrCustomForm.controls['headerTextColor'].value }">
                            <ng-container *ngIf="qrCustomForm.controls['headerDesign'].value === 1">
                                <div class="justify-center">
                                    <img [appImage]="headerPic ? s3BucketUrl+headerPic : ''" [type]="'orgProfileLogo'"
                                        alt="logo" class="br-50 obj-cover border" width="80" height="80">
                                </div>
                                <h2 class="text-center mt-16 text-truncate-1 break-all">
                                    {{ qrCustomForm.controls['orgCompanyName'].value }}
                                </h2>
                            </ng-container>
                            <ng-container *ngIf="qrCustomForm.controls['headerDesign'].value === 2">
                                <h2 class="text-center">{{ qrCustomForm.controls['orgCompanyName'].value ||
                                    orgProfileData?.displayName }}
                                </h2>
                                <div class="justify-center mt-16">
                                    <img [appImage]="headerPic ? s3BucketUrl+headerPic : ''" [type]="'orgProfileLogo'"
                                        alt="logo" class="br-50 obj-cover border" width="80" height="80">
                                </div>
                            </ng-container>
                            <ng-container *ngIf="qrCustomForm.controls['headerDesign'].value === 3">
                                <div class="flex-between">
                                    <h2 class="text-center text-truncate-1 break-all">{{
                                        qrCustomForm.controls['orgCompanyName'].value
                                        || orgProfileData?.displayName
                                        }}
                                    </h2>
                                    <div class="justify-center">
                                        <img [appImage]="headerPic ? s3BucketUrl+headerPic : ''"
                                            [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border"
                                            width="80" height="80">
                                    </div>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="qrCustomForm.controls['headerDesign'].value === 4">
                                <div class="flex-between">
                                    <div class="justify-center">
                                        <img [appImage]="headerPic ? s3BucketUrl+headerPic : ''"
                                            [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border"
                                            width="80" height="80">
                                    </div>
                                    <h2 class="text-center text-truncate-1 break-all">{{
                                        qrCustomForm.controls['orgCompanyName'].value
                                        || orgProfileData?.displayName }}</h2>
                                    <div></div>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="qrCustomForm.controls['headerDesign'].value === 5">
                                <div class="flex-between">
                                    <div class="justify-center">
                                        <img [appImage]="headerPic ? s3BucketUrl+headerPic : ''"
                                            [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border"
                                            width="80" height="80">
                                    </div>
                                    <h2 class="text-center">{{ qrCustomForm.controls['orgCompanyName'].value
                                        || orgProfileData?.displayName
                                        }}
                                    </h2>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="qrCustomForm.controls['headerDesign'].value === 6">
                                <h2 class="text-center">{{ qrCustomForm.controls['orgCompanyName'].value ||
                                    orgProfileData?.displayName }}
                                </h2>
                            </ng-container>
                        </div>
                    </ng-container>
                    <!-- form -->
                    <div class="bg-white flex-center-col pe-none py-40">
                        <div class="w-75 ip-w-100 ip-px-20">
                            <div>
                                <h3 class="mb-30 fw-600 text-black-200 text-center">Enquiry Form</h3>
                            </div>
                            <div>
                                <h5 class="fw-600 py-10 border-bottom">Basic Info</h5>
                                <div class="flex-wrap" [ngClass]="showWidth ?'d-flex':' flex-column'">
                                    <div [ngClass]="showWidth ?'w-50':' w-100'"
                                        *ngIf="qrCustomForm.controls['name'].value">
                                        <div class="field-label-req">{{ 'GLOBAL.name' | translate }}</div>
                                        <div [ngClass]="showWidth ?'mr-20 ph-mr-10':'mr-0'">
                                            <form-errors-wrapper label="{{ 'GLOBAL.name' | translate }}">
                                                <input type="text" placeholder="ex. Manasa Pampana">
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                    <div [ngClass]="showWidth ?'w-50':' w-100'"
                                        *ngIf="qrCustomForm.controls['phoneNo'].value">
                                        <div class="field-label-req text-nowrap">Primary Number</div>
                                        <div [ngClass]="showWidth ?'mr-20 ph-mr-10':'mr-0'">
                                            <form-errors-wrapper label="Phone Number">
                                                <input type="number" placeholder="ex. 7569xxxxx">
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                    <div [ngClass]="showWidth ?'w-50':' w-100'"
                                        *ngIf="qrCustomForm.controls['email'].value">
                                        <div class="field-label">{{ 'USER.email' | translate }}</div>
                                        <div [ngClass]="showWidth ?'mr-20 ph-mr-10':'mr-0'">
                                            <form-errors-wrapper label="{{ 'USER.email' | translate }}">
                                                <input type="email" placeholder="ex. <EMAIL>">
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                    <div [ngClass]="showWidth ?'w-50':' w-100'"
                                        *ngIf="qrCustomForm.controls['altPhoneNo'].value">
                                        <div class="field-label text-nowrap text-truncate">Alternate Number</div>
                                        <div [ngClass]="showWidth ?'mr-20 ph-mr-10':'mr-0'">
                                            <form-errors-wrapper label="Alternate Phone Number">
                                                <input type="tel" placeholder="ex. 7036xxxxx">
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-30" *ngIf="showEnquiryInfo">
                                <h5 class="fw-600 py-10 border-bottom">More Information</h5>
                                <div class="flex-wrap" [ngClass]="showWidth ?'d-flex':' flex-column'">
                                    <ng-container *ngFor="let field of filteredFields; let i = index">
                                        <div [ngClass]="showWidth ? 'w-50' : 'w-100'"
                                            *ngIf="qrCustomForm.controls[field.controlName]?.value">
                                            <div class="field-label">{{field.displayName}}</div>
                                            <div *ngIf="field.type === 'range'" class="position-relative">
                                                <div class="align-center mt-4"
                                                    [ngClass]="showWidth ?'mr-20 ph-mr-10':'mr-0'">
                                                    <div class="w-50">
                                                        <form-errors-wrapper>
                                                            <input type="number" [id]="'inp' + field.controlName"
                                                                data-automate-id="'inp' + field.controlName"
                                                                [placeholder]="field.placeholder" maxlength="10">
                                                        </form-errors-wrapper>
                                                    </div>
                                                    <div class="mx-10">to</div>
                                                    <div class="w-50">
                                                        <form-errors-wrapper>
                                                            <input type="number" id="'inp' + field.controlName"
                                                                data-automate-id="'inp' + field.controlName"
                                                                [placeholder]="field.placeholder" maxlength="10">
                                                        </form-errors-wrapper>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Number Input -->
                                            <div *ngIf="field.type === 'number'" class="align-center"
                                                [ngClass]="showWidth ? 'mr-20 ph-mr-10' : 'mr-0'">
                                                <div class="w-60pr no-validation">
                                                    <form-errors-wrapper label="{{ field.displayName }}">
                                                        <div class="border-gray br-6">
                                                            <input type="number" [id]="'inp' + field.controlName"
                                                                [placeholder]="field.placeholder" class="border-0">
                                                        </div>
                                                    </form-errors-wrapper>
                                                </div>
                                                <div class="w-40pr ml-8">
                                                    <form-errors-wrapper label="{{ 'PROJECTS.size-unit' | translate }}">
                                                        <ng-select tabindex="4" placeholder="ex. sq. feet."
                                                            class="bg-white" ResizableDropdown bindValue="id"
                                                            bindLabel="unit"></ng-select>
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>

                                            <!-- Dropdown -->
                                            <div *ngIf="field.type === 'dropdown'" class="mt-4"
                                                [ngClass]="showWidth ? 'mr-20 ph-mr-10' : 'mr-0'">
                                                <ng-select class="bg-white" [placeholder]="field.placeholder"
                                                    ResizableDropdown></ng-select>
                                            </div>

                                            <!-- Text Input -->
                                            <div *ngIf="field.type === 'text'" class="form-group mt-4"
                                                [ngClass]="showWidth ? 'mr-20 ph-mr-10' : 'mr-0'">
                                                <input type="text" [id]="'inp' + field.controlName"
                                                    [placeholder]="field.placeholder">
                                                <span *ngIf="field.controlName == 'possessionAvailability' || field.controlName == 'dateOfBirth'"
                                                    class="icon ic-calendar ic-sm ic-coal position-absolute right-20 top-12 cursor-pointer"></span>
                                            </div>

                                            <div *ngIf="field.type === 'notetext'" class="form-group mt-4"
                                                [ngClass]="showWidth ? 'mr-20 ph-mr-10' : 'mr-0'">

                                                <div>
                                                    <textarea rows="4" id="txtQrNotes" data-automate-id="txtQrNotes"
                                                        placeholder="Describe your requirement "></textarea>
                                                </div>

                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- footer -->
                    <div *ngIf="selectedSection === 'Contents' && qrCustomForm.controls['footerDesign'].value == 0"
                        class="hover border-mud-top bg-white">
                        <div class="hover-footer cursor-pointer" (click)="onEditSectionFooter()">
                            <div class="flex-center"
                                [ngStyle]="{ 'background-color': qrCustomForm.controls['footerBgColor'].value, 'color': qrCustomForm.controls['footerTextColor'].value }">
                                <div class="w-150 br-18 bg-dark-700 p-12 justify-center my-40">
                                    <div class="icon ic-pen ic-xxs  mr-6"></div>
                                    <div class="text-white">Edit Section</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ng-container *ngIf="(!isAddFooterClicked)">
                        <div [ngStyle]="{ 'background-color': qrCustomForm.controls['footerBgColor'].value, 'color': qrCustomForm.controls['footerTextColor'].value }"
                            class="border-green-2 p-40 justify-center-col"
                            *ngIf="(qrCustomForm.controls['footerDesign'].value == 0 && selectedSection === 'Footer')">
                            <ng-container *ngIf="(showChooseTemplate && selectedSection === 'Footer')|| selectedSection ===
                                    'Footer'">
                                <h2 class="fw-semi-bold text-center">Choose template!</h2>
                                <div class="fw-300 text-sm mt-10">Please choose the footer from the
                                    selection of
                                    templates on the left panel. Your chosen footer will set the visual tone
                                    for
                                    your
                                    document and make it stand out.</div>
                            </ng-container>
                        </div>
                        <div *ngIf="qrCustomForm.controls['footerDesign'].value !== 0" class="p-30 border-top"
                            [ngStyle]="{ 'background-color': qrCustomForm.controls['footerBgColor'].value, 'color': qrCustomForm.controls['footerTextColor'].value }">
                            <ng-container *ngIf="qrCustomForm.controls['footerDesign'].value === 1 && showWidth">
                                <div class="px-20 justify-between">
                                    <div class="w-25">
                                        <div [ngClass]="showWidth ?'d-flex ph-flex-col':' flex-col'">
                                            <span class="fw-600 mr-8 text-xxs">Address</span>
                                            <span class="text-xs word-break fw-semi-bold">
                                                {{qrCustomForm.controls['orgAddress'].value}}</span>
                                        </div>
                                        <div class="mt-20"
                                            *ngIf="qrCustomForm.controls['orgPhoneNo'].value && qrCustomForm.controls['orgPhoneNo'].value.trim().length > 0"
                                            [ngClass]="showWidth ?'ph-flex-col align-center':' flex-col'">
                                            <span class="fw-600 mr-8 text-xxs">Phone</span><span
                                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgPhoneNo'].value}}</span>
                                        </div>
                                        <div class="mt-20"
                                            *ngIf="qrCustomForm.controls['orgEmail'].value && qrCustomForm.controls['orgEmail'].value.length > 0"
                                            [ngClass]="showWidth ?'ph-flex-col align-center':' flex-col'">
                                            <span class="fw-600 mr-8 text-nowrap text-xxs">Email</span><span
                                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgEmail'].value}}</span>
                                        </div>
                                    </div>
                                    <div class="justify-center-col align-center-col w-50">
                                        <img [appImage]="footerPic ? s3BucketUrl+footerPic : ''"
                                            [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border"
                                            width="80" height="80">
                                        <div class="text-center text-xxs fw-300 mt-10"> © {{currentYear}} {{subDomain}}
                                            All
                                            Rights
                                            Reserved </div>
                                    </div>
                                    <div class="w-25 flex-end">
                                        <div [ngClass]="showWidth ?'d-flex ph-flex-col':' flex-col'">
                                            <ng-container *ngFor="let media of socialMedia">
                                                <a *ngIf="qrCustomForm.controls[media.controlName].value"
                                                    [href]="media?.baseUrl + getSocialMediaLink(media.controlName)"
                                                    target="_blank">
                                                    <img [type]="'leadrat'" [appImage]="media.image" alt="social media"
                                                        class="br-50 mr-10 mb-10" height="24" width="24"
                                                        *ngIf="qrCustomForm.controls[media.controlName].value" />
                                                </a>
                                            </ng-container>
                                        </div>
                                    </div>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="qrCustomForm.controls['footerDesign'].value === 2 && showWidth">
                                <div class="px-20 justify-between">
                                    <div class="w-25 align-end">
                                        <div class="text-center text-xxs fw-300"> © {{currentYear}} {{subDomain}} All
                                            Rights
                                            Reserved </div>
                                    </div>
                                    <div class="justify-center-col align-center-col w-50">
                                        <img [appImage]="footerPic ? s3BucketUrl+footerPic : ''"
                                            [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border"
                                            width="80" height="80">
                                    </div>
                                    <div class="w-25">
                                        <div [ngClass]="showWidth ?'d-flex ph-flex-col':' flex-col'"><span
                                                class="fw-600 mr-8 text-xxs">Address</span><span
                                                class="text-xs word-break fw-semi-bold">{{qrCustomForm.controls['orgAddress'].value}}</span>
                                        </div>
                                        <div class="mt-20"
                                            *ngIf="qrCustomForm.controls['orgPhoneNo'].value && qrCustomForm.controls['orgPhoneNo'].value.trim().length > 0"
                                            [ngClass]="showWidth ?'ph-flex-col align-center':' flex-col'">
                                            <span class="fw-600 mr-8 text-xxs">Phone</span><span
                                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgPhoneNo'].value}}</span>
                                        </div>
                                        <div class="mt-20"
                                            *ngIf="qrCustomForm.controls['orgEmail'].value && qrCustomForm.controls['orgEmail'].value.length > 0"
                                            [ngClass]="showWidth ?'ph-flex-col align-center':' flex-col'">
                                            <span class="fw-600 mr-8 text-nowrap text-xxs">Email</span><span
                                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgEmail'].value}}</span>
                                        </div>
                                    </div>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="qrCustomForm.controls['footerDesign'].value === 3 && showWidth">
                                <div class="px-20 justify-between">
                                    <div class="w-25">
                                        <div [ngClass]="showWidth ?'d-flex ph-flex-col':' flex-col'"><span
                                                class="fw-600 mr-8 text-xxs">Address</span><span
                                                class="text-xs word-break fw-semi-bold">{{qrCustomForm.controls['orgAddress'].value}}</span>
                                        </div>
                                        <div class="mt-20"
                                            *ngIf="qrCustomForm.controls['orgPhoneNo'].value && qrCustomForm.controls['orgPhoneNo'].value.trim().length > 0"
                                            [ngClass]="showWidth ?'ph-flex-col align-center':' flex-col'">
                                            <span class="fw-600 mr-8 text-xxs">Phone</span><span
                                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgPhoneNo'].value}}</span>
                                        </div>
                                        <div class="mt-20"
                                            *ngIf="qrCustomForm.controls['orgEmail'].value && qrCustomForm.controls['orgEmail'].value.length > 0"
                                            [ngClass]="showWidth ?'ph-flex-col align-center':' flex-col'">
                                            <span class="fw-600 mr-8 text-nowrap text-xxs">Email</span><span
                                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgEmail'].value}}</span>
                                        </div>
                                    </div>
                                    <div class="justify-center-col align-center-col w-50">
                                        <div [ngClass]="showWidth ?'d-flex ph-flex-col':' flex-col'">
                                            <ng-container *ngFor="let media of socialMedia">
                                                <a *ngIf="qrCustomForm.controls[media.controlName].value"
                                                    [href]="media?.baseUrl + getSocialMediaLink(media.controlName)"
                                                    target="_blank">
                                                    <img [type]="'leadrat'" [appImage]="media.image" alt="social media"
                                                        class="br-50 mr-10 mb-10" height="24" width="24"
                                                        *ngIf="qrCustomForm.controls[media.controlName].value" />
                                                </a>
                                            </ng-container>
                                        </div>
                                        <div class="text-center text-xxs fw-300 mt-10"> © {{currentYear}} {{subDomain}}
                                            All
                                            Rights
                                            Reserved </div>
                                    </div>
                                    <div class="w-25"> <img [appImage]="footerPic ? s3BucketUrl+footerPic : ''"
                                            [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border"
                                            width="80" height="80"></div>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="qrCustomForm.controls['footerDesign'].value === 4 && showWidth">
                                <div class="px-20 justify-between">
                                    <div class="w-25">
                                        <div [ngClass]="showWidth ?'d-flex ph-flex-col':' flex-col'"><span
                                                class="fw-600 mr-8 text-xxs">Address</span><span
                                                class="text-xxs word-break">{{qrCustomForm.controls['orgAddress'].value}}</span>
                                        </div>
                                        <div class="mt-20"
                                            *ngIf="qrCustomForm.controls['orgPhoneNo'].value && qrCustomForm.controls['orgPhoneNo'].value.trim().length > 0"
                                            [ngClass]="showWidth ?'ph-flex-col align-center':' flex-col'">
                                            <span class="fw-600 mr-8 text-xxs">Phone</span><span
                                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgPhoneNo'].value}}</span>
                                        </div>
                                        <div class="mt-20"
                                            *ngIf="qrCustomForm.controls['orgEmail'].value && qrCustomForm.controls['orgEmail'].value.length > 0"
                                            [ngClass]="showWidth ?'ph-flex-col align-center':' flex-col'">
                                            <span class="fw-600 mr-8 text-nowrap text-xxs">Email</span><span
                                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgEmail'].value}}</span>
                                        </div>
                                    </div>
                                    <div class="justify-center-col align-center-col w-50">
                                        <div [ngClass]="showWidth ?'d-flex':' flex-col'">
                                            <ng-container *ngFor="let media of socialMedia">
                                                <a *ngIf="qrCustomForm.controls[media.controlName].value"
                                                    [href]="media?.baseUrl + getSocialMediaLink(media.controlName)"
                                                    target="_blank">
                                                    <img [type]="'leadrat'" [appImage]="media.image" alt="social media"
                                                        class="br-50 mr-10 mb-10" height="24" width="24"
                                                        *ngIf="qrCustomForm.controls[media.controlName].value" />
                                                </a>
                                            </ng-container>
                                        </div> <img [appImage]="footerPic ? s3BucketUrl+footerPic : ''"
                                            [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border"
                                            width="80" height="80">
                                    </div>
                                    <div class="w-25 align-items-end justify-center text-center text-xxs fw-300">
                                        ©
                                        {{currentYear}}
                                        {{subDomain}} All Rights
                                        Reserved </div>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="qrCustomForm.controls['footerDesign'].value === 5 && showWidth">
                                <div class="px-20 justify-between">
                                    <div class="justify-center-col align-center-col w-25">
                                        <img [appImage]="footerPic ? s3BucketUrl+footerPic : ''"
                                            [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border"
                                            width="80" height="80">
                                    </div>
                                    <div class="w-50 align-items-end justify-center">
                                        <div class="text-center text-xxs fw-300"> © {{currentYear}} {{subDomain}} All
                                            Rights
                                            Reserved </div>
                                    </div>
                                    <div class="w-25">
                                        <div [ngClass]="showWidth ?'d-flex ph-flex-col':' flex-col'"><span
                                                class="fw-600 mr-8 text-xxs">Address</span><span
                                                class="text-xs word-break fw-semi-bold">{{qrCustomForm.controls['orgAddress'].value}}</span>
                                        </div>
                                        <div class="mt-20"
                                            *ngIf="qrCustomForm.controls['orgPhoneNo'].value && qrCustomForm.controls['orgPhoneNo'].value.trim().length > 0"
                                            [ngClass]="showWidth ?'ph-flex-col align-center' :' flex-col'">
                                            <span class="fw-600 mr-8 text-xxs">Phone</span><span
                                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgPhoneNo'].value}}</span>
                                        </div>
                                        <div class="mt-20"
                                            *ngIf="qrCustomForm.controls['orgEmail'].value && qrCustomForm.controls['orgEmail'].value.length > 0"
                                            [ngClass]="showWidth ?'ph-flex-col align-center':' flex-col'">
                                            <span class="fw-600 mr-8 text-nowrap text-xxs">Email</span><span
                                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgEmail'].value}}</span>
                                        </div>
                                    </div>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="qrCustomForm.controls['footerDesign'].value === 6 && showWidth">
                                <div class="px-20 flex-between">
                                    <div class="w-25">
                                        <div [ngClass]="showWidth ?'d-flex ph-flex-col':' flex-col'"><span
                                                class="fw-600 mr-8 text-xxs">Address</span><span
                                                class="text-xs word-break fw-semi-bold">{{qrCustomForm.controls['orgAddress'].value}}</span>
                                        </div>
                                        <div *ngIf="qrCustomForm.controls['orgPhoneNo'].value && qrCustomForm.controls['orgPhoneNo'].value.trim().length > 0"
                                            class="mt-20"
                                            [ngClass]="showWidth ? 'ph-flex-col align-center' : 'flex-col'">
                                            <span class="fw-600 mr-8 text-xxs">Phone</span>
                                            <span class="text-xs text-truncate-1 break-all fw-semi-bold">
                                                {{ qrCustomForm.controls['orgPhoneNo'].value }}
                                            </span>
                                        </div>
                                        <div class="mt-20"
                                            *ngIf="qrCustomForm.controls['orgEmail'].value && qrCustomForm.controls['orgEmail'].value.length > 0"
                                            [ngClass]="showWidth ?'ph-flex-col align-center':' flex-col'">
                                            <span class="fw-600 mr-8 text-nowrap text-xxs">Email</span><span
                                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgEmail'].value}}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="w-50">
                                        <div class="text-center text-xxs fw-300"> © {{currentYear}} {{subDomain}} All
                                            Rights
                                            Reserved </div>
                                    </div>
                                    <div></div>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="!showWidth">
                                <div class="px-20 justify-center-col align-center-col">
                                    <ng-container *ngIf="qrCustomForm.controls['footerDesign'].value !== 6">
                                        <img [appImage]="footerPic ? s3BucketUrl+footerPic : ''"
                                            [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border"
                                            width="80" height="80">
                                    </ng-container>
                                    <div class="d-flex mt-8">
                                        <span class="fw-600 mr-8 text-xxs">Address:</span>
                                        <span class="text-xs word-break fw-semi-bold">
                                            {{qrCustomForm.controls['orgAddress'].value}}</span>
                                    </div>
                                    <div *ngIf="qrCustomForm.controls['orgPhoneNo'].value && qrCustomForm.controls['orgPhoneNo'].value.trim().length > 0"
                                        class="align-center mt-6">
                                        <span class="fw-600 mr-8 text-xxs">Phone:</span><span
                                            class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgPhoneNo'].value}}</span>
                                    </div>
                                    <div *ngIf="qrCustomForm.controls['orgEmail'].value && qrCustomForm.controls['orgEmail'].value.length > 0"
                                        class="align-center mt-6">
                                        <span class="fw-600 mr-8 text-nowrap text-xxs">Email:</span><span
                                            class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrCustomForm.controls['orgEmail'].value}}</span>
                                    </div>
                                    <div class="align-center"
                                        *ngIf="qrCustomForm.controls['footerDesign'].value !== 2 && qrCustomForm.controls['footerDesign'].value !== 5 && qrCustomForm.controls['footerDesign'].value !== 6">
                                        <ng-container *ngFor="let media of socialMedia">
                                            <a *ngIf="qrCustomForm.controls[media.controlName].value"
                                                [href]="media?.baseUrl + getSocialMediaLink(media.controlName)"
                                                target="_blank">
                                                <img [type]="'leadrat'" [appImage]="media.image" alt="social media"
                                                    class="br-50 mr-10 mt-6" height="24" width="24"
                                                    *ngIf="qrCustomForm.controls[media.controlName].value" />
                                            </a>
                                        </ng-container>
                                    </div>
                                    <div class="text-center text-xxs fw-300 mt-6"> © {{currentYear}} {{subDomain}}
                                        All Rights Reserved </div>
                                </div>
                            </ng-container>
                        </div>
                    </ng-container>
                </div>
            </div>
        </div>
    </div>
</div>