<div class="px-20 mb-20" #basicDetailsForm>
    <form [formGroup]="projectDetailForm" class="bg-white p-20 br-6">
        <div class="d-flex">
            <div class="field-label-underline mt-0">Project Details</div>
        </div>
        <div class="d-flex flex-wrap">
            <div class="w-33 tb-w-50 ip-w-100 position-relative">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label-req">Project Name</div>
                    <form-errors-wrapper [control]="checkDuplicacy ? null : projectDetailForm.controls['name'] "
                        label="Name">
                        <input type="text" id="inpPropTitle" data-automate-id="inpPropTitle" placeholder="ex. Utpal"
                            autocomplete="off" formControlName="name">
                    </form-errors-wrapper>
                    <div *ngIf="checkDuplicacy" class="mt-4 text-xs text-red position-absolute right-20 fw-semi-bold">
                        Project name already exist
                    </div>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0">
                    <div class="box-radio black">
                        <div class="field-label-req">Project Type</div>
                        <div class="d-flex flex-wrap">
                            <form-errors-wrapper [control]="projectDetailForm.controls['projectType']"
                                label="Project type">
                                <ng-container *ngFor="let type of projectTypeList">
                                    <input type="radio" class="btn-check" name="projectType" [id]="type.displayName"
                                        autocomplete="off" [value]="type.displayName" formControlName="projectType">
                                    <label class="btn-outline h-40 py-10"
                                        [for]="type.displayName">{{type.displayName}}</label>
                                </ng-container>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-w-100">
                    <div class="field-label-req">Project Sub-Type</div>
                    <form-errors-wrapper [control]="projectDetailForm.controls['projectSubType']" label="Sub type">
                        <ng-select [virtualScroll]="true" ResizableDropdown [items]="projectSubType"
                            formControlName="projectSubType" bindLabel="displayName" bindValue="id" name="displayName"
                            [readonly]="projectDetailForm.controls['projectType'].value ? false : true"
                            placeholder="ex. Plot">
                        </ng-select>
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-w-100">
                    <div class="field-label">Status</div>
                    <form-errors-wrapper [control]="projectDetailForm.controls['status']" label="Status">
                        <ng-select [virtualScroll]="true" class="bg-white" [items]="statusList" [closeOnSelect]="true"
                            ResizableDropdown bindLabel="displayName" bindValue="id" name="displayName"
                            placeholder="ex. Upcoming" formControlName="status" addTagText="Create New Project">
                        </ng-select>
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100">
                <div [ngClass]="projectDetailForm.controls['area'].value ? 'field-label-req' : 'field-label'">Land Area
                </div>
                <div class="align-center mr-10 ip-mr-0">
                    <div class="w-60pr mr-10">
                        <form-errors-wrapper label="Land Area" [control]="projectDetailForm.controls['area']">
                            <input type="number" min="0" placeholder="ex. 4758" formControlName="area"
                                autocomplete="off">
                        </form-errors-wrapper>
                    </div>
                    <div class="w-40pr">
                        <form-errors-wrapper label="Area unit"
                            [control]="projectDetailForm.controls['carpetAreaUnitId']">
                            <ng-select [virtualScroll]="true" formControlName="carpetAreaUnitId" [items]="areaSizeUnits"
                                ResizableDropdown placeholder="ex. Sq. Mt." bindValue="id" bindLabel="unit"
                                [readonly]="projectDetailForm.controls['area']?.value ? false : true"></ng-select>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">Certificate</div>
                    <form-errors-wrapper>
                        <input type="text" id="inpPropTitle" data-automate-id="inpPropTitle" autocomplete="off"
                            placeholder="ex. Govt. Approved" formControlName="certificates">
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0">
                    <div class="box-radio black">
                        <div class="field-label">{{'PROPERTY.PROPERTY_DETAIL.facing' | translate }}</div>
                        <div class="d-flex flex-wrap">
                            <ng-container *ngFor="let facing of facingList">
                                <!-- <input type="radio" class="btn-check" name="facing" [id]="facing.displayName"
                                    autocomplete="off" [value]="facing.displayName" formControlName="facing"> -->
                                <input type="checkbox" class="btn-check" name="facing" [id]="facing.displayName"
                                    (change)="selectFacing($event.target.checked, facing)"
                                    [checked]="facingArray?.includes(facing.value)" autocomplete="off"
                                    [value]="facing.displayName">
                                <label class="btn-outline" [for]="facing.displayName">{{facing.displayName}}</label>
                            </ng-container>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100 d-flex flex-wrap">
                <form-errors-wrapper [control]="projectDetailForm.controls['totalBlocks']" label="Total Blocks"
                    class="position-relative mr-20 error"
                    *ngIf="projectDetailForm.controls['projectType'].value !== 'Agricultural'">
                    <div class="field-label text-nowrap ">Total Blocks</div>
                    <div class="spin-btn-container-gray align-start mt-6 mr-10">
                        <div class="spin-btn" (click)="decrement('totalBlocks')"><span class="spin-ic ic-minus"></span>
                        </div>
                        <div class="no-validation px-6">
                            <input type="number" placeholder="ex. 3" formControlName="totalBlocks" autocomplete="off"
                                (keydown)="onlyNumbers($event)" min="0" />
                        </div>
                        <div class="spin-btn" (click)="increment('totalBlocks')"><span class="spin-ic ic-plus"></span>
                        </div>
                    </div>
                </form-errors-wrapper>
                <form-errors-wrapper [control]="projectDetailForm.controls['totalFloors']" label="Total Floors"
                    class="position-relative mr-20 error"
                    *ngIf="projectDetailForm.controls['projectType'].value !== 'Agricultural'">
                    <div class="field-label text-nowrap ">Total Floors</div>
                    <div class="spin-btn-container-gray align-start mt-6">
                        <div class="spin-btn" (click)="decrement('totalFloors')"><span class="spin-ic ic-minus"></span>
                        </div>
                        <div class="no-validation px-6">
                            <input type="number" placeholder="ex. 3" formControlName="totalFloors" min="0" max="999"
                                autocomplete="off" (keydown)="onlyNumbers($event)" />
                        </div>
                        <div class="spin-btn" (click)="increment('totalFloors')"><span class="spin-ic ic-plus"></span>
                        </div>
                    </div>
                </form-errors-wrapper>
                <form-errors-wrapper [control]="projectDetailForm.controls['totalFlats']" label="Total Flats"
                    class="position-relative mr-20 error"
                    *ngIf="projectDetailForm.controls['projectType'].value == 'Residential' ">
                    <div class="field-label text-nowrap ">Total Units</div>
                    <div class="spin-btn-container-gray align-start">
                        <div class="spin-btn" (click)="decrement('totalFlats')"><span class="spin-ic ic-minus"></span>
                        </div>
                        <div class="no-validation px-6">
                            <input type="number" placeholder="ex. 3" formControlName="totalFlats" autocomplete="off"
                                (keydown)="onlyNumbers($event)" min="0" />
                        </div>
                        <div class="spin-btn" (click)="increment('totalFlats')"><span class="spin-ic ic-plus"></span>
                        </div>
                    </div>
                </form-errors-wrapper>
            </div>
            <div class="w-66 tb-w-100">
                <div class="flex-between">
                    <div class="field-label">Project Description</div>
                    <div *ngIf="projectDetailForm.controls['projectDescription'].value" class="cursor-pointer mt-16"
                        (click)="openPreview()">
                        <h6 class="flex-center text-green-250 fw-400"><span
                                class="ic-eye-solid ic-xxs ic-accent-green mr-4"></span>Preview
                        </h6>
                    </div>
                </div>
                <form-errors-wrapper>
                    <textarea id="inpPropTitle" data-automate-id="inpPropTitle"
                        placeholder="ex. Utpal by Aashish Group has a pleasing appearance that would charm most buyers. The units of this property are Under Construction. The units offered are Flat, which have been developed to provide complete satisfaction."
                        formControlName="projectDescription"></textarea>
                </form-errors-wrapper>
            </div>
            <div class="w-66 tb-w-100">
                <div class="flex-between">
                    <div class="field-label">Notes</div>
                    <div *ngIf="projectDetailForm.controls['notes'].value" class="cursor-pointer mt-16"
                        (click)="openPreview(true)">
                        <h6 class="flex-center text-green-250 fw-400"><span
                                class="ic-eye-solid ic-xxs ic-accent-green mr-4"></span>Preview
                        </h6>
                    </div>
                </div>
                <form-errors-wrapper>
                    <textarea rows="2" id="txtUpdateStatusNotes" data-automate-id="txtUpdateStatusNotes"
                        formControlName="notes" placeholder="ex. I want to say "></textarea>
                </form-errors-wrapper>
            </div>
        </div>
        <div class="d-flex">
            <div class="field-label-underline mt-30">Builder details</div>
        </div>
        <div class="d-flex flex-wrap">
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">Builder/Developer Name</div>
                    <form-errors-wrapper [control]="projectDetailForm.controls['builderName']">
                        <input type="text" id="inpPropTitle" data-automate-id="inpPropTitle" autocomplete="off"
                            placeholder="ex. Prestige Group" formControlName="builderName" min="5" #reraNumberField>
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">Builder Contact Number</div>
                    <form-errors-wrapper [control]="projectDetailForm.controls['builderNumber']" label="Phone number">
                        <ngx-mat-intl-tel-input *ngIf="hasInternationalSupport"
                            [preferredCountries]="preferredCountries" [enablePlaceholder]="true" [enableSearch]="true"
                            formControlName="builderNumber" class="no-validation builderNumber">
                        </ngx-mat-intl-tel-input>
                        <ngx-mat-intl-tel-input *ngIf="!hasInternationalSupport"
                            [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                            [enablePlaceholder]="true" [enableSearch]="true" formControlName="builderNumber"
                            class="no-validation builderNumber">
                        </ngx-mat-intl-tel-input>
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100 ">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">Point of Contact</div>
                    <form-errors-wrapper [control]="projectDetailForm.controls['pointOfContact']"
                        label="Point Of Contact">
                        <ngx-mat-intl-tel-input *ngIf="hasInternationalSupport"
                            [preferredCountries]="preferredCountries" [enablePlaceholder]="true" [enableSearch]="true"
                            formControlName="pointOfContact" class="no-validation pointOfContact">
                        </ngx-mat-intl-tel-input>
                        <ngx-mat-intl-tel-input *ngIf="!hasInternationalSupport"
                            [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                            [enablePlaceholder]="true" [enableSearch]="true" formControlName="pointOfContact"
                            class="no-validation pointOfContact">
                        </ngx-mat-intl-tel-input>
                    </form-errors-wrapper>
                </div>
            </div>
        </div>

        <div class="d-flex">
            <div class="field-label-underline mt-30">RERA Details</div>
        </div>
        <div class="w-33 tb-w-50 ip-w-100 ">
            <div class="mr-10 ip-mr-0">
                <div class="field-label ">RERA Registration numbers</div>
                <form-errors-wrapper>
                    <div class="align-center">
                        <input type="text" formControlName="reraNumberList" placeholder="ex. P02919062000"
                            autocomplete="off" [(ngModel)]="newUrl" class="outline-0 w-50 br-4"
                            (keyup.enter)="addInputField()">
                        <div title="Add rera numbers" class="bg-accent-green icon-badge p-20" (click)="addInputField()">
                            <span class="icon ic-add ic-sm"></span>
                        </div>
                    </div>
                    <small class="text-muted text-nowrap ml-10 ph-d-none">
                        ({{ "Press Enter to add Rera numbers" }})</small>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="links?.length">
                    <div class="align-center border br-12 p-6 mr-8 mb-4 bg-white" *ngFor="let link of links">
                        <a class="fw-600 text-sm mr-4 text-black-100 cursor-pointer" target="_blank">{{link}}</a>
                        <span class="icon ic-xxs ic-close-secondary ic-gray cursor-pointer"
                            (click)="removeInputField(link)"></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex">
            <div class="field-label-underline mt-30">Project price</div>
        </div>
        <div class="align-center flex-wrap">
            <div class="field-rupees-tag w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">{{'GLOBAL.min' | translate}} {{'PROPERTY.PROPERTY_DETAIL.price' |
                        translate}}
                    </div>
                    <div class="position-relative budget-dropdown">
                        <form-errors-wrapper [control]="projectDetailForm.controls['minimumPrice']"
                            label="{{'LABEL.budget' | translate}}">
                            <input type="number" placeholder="ex. 3210000" maxlength="10" autocomplete="off"
                                formControlName="minimumPrice" min="1" (keydown)="onlyNumbers($event)">
                            <div class="no-validation">
                                <ng-container *ngIf="currencyList?.length > 1; else showCurrencySymbol">
                                    <ng-select formControlName="currency" ResizableDropdown
                                        class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                                        <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                                            <span [title]="curr.currency">{{curr.currency}}</span>
                                        </ng-option>
                                    </ng-select>
                                </ng-container>
                                <ng-template #showCurrencySymbol>
                                    <h5 class="rupees px-12 py-8 fw-600 m-4">{{ selectedProject?.monetaryInfo?.currency
                                        || defaultCurrency }}</h5>
                                </ng-template>
                            </div>
                        </form-errors-wrapper>
                        <div *ngIf="projectDetailForm.controls['minimumPrice'].value"
                            class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                            {{lowerBudgetInWords}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="field-rupees-tag w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">{{'GLOBAL.max' | translate}} {{'PROPERTY.PROPERTY_DETAIL.price' |
                        translate}}
                    </div>
                    <div class="position-relative budget-dropdown">
                        <form-errors-wrapper label="{{'LABEL.budget' | translate}}"
                            [control]="projectDetailForm.controls['maximumPrice']">
                            <input formControlName="maximumPrice" type="number" placeholder="ex. 5210000"
                                autocomplete="off" maxlength="10" min="1" (keydown)="onlyNumbers($event)">
                            <div class="no-validation">
                                <ng-container *ngIf="currencyList?.length > 1; else showCurrencySymbol">
                                    <ng-select formControlName="currency" ResizableDropdown
                                        class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                                        <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                                            <span [title]="curr.currency">{{curr.currency}}</span>
                                        </ng-option>
                                    </ng-select>
                                </ng-container>
                            </div>
                        </form-errors-wrapper>
                        <div *ngIf="projectDetailForm.controls['maximumPrice'].value"
                            class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                            {{upperBudgetInWords}}
                            <div *ngIf="budgetValidation"
                                class="mt-6 text-xs text-red position-absolute right-0 fw-semi-bold text-nowrap">
                                {{'Max price cannot be less than Min price'}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">
                        {{'PROPERTY.PROPERTY_DETAIL.brokerage' | translate}}</div>
                    <div class="mr-10 w-100 align-center">
                        <div class="flex-grow-1">
                            <form-errors-wrapper [control]="projectDetailForm.controls['BrokerageAmount']"
                                label="{{ 'PROPERTY.PROPERTY_DETAIL.brokerage' | translate }}">
                                <input type="number" id="inpPropBrokerage" data-automate-id="inpPropBrokerage"
                                    placeholder="ex. 29.5" formControlName="BrokerageAmount" min="0.01" step="0.01"
                                    (input)="updateBrokerageUnit($event.target.value)" (keydown)="onlyNumbersWithDecimal($event, $event.target.value)"
                                    [ngClass]="{'cursor-not-allow' : !projectDetailForm.controls['minimumPrice'].value}"
                                    [readonly]="projectDetailForm.controls['minimumPrice'].value ? false : true">
                            </form-errors-wrapper>
                        </div>
                        <div class="w-120 pl-8">
                            <form-errors-wrapper [control]="projectDetailForm.controls['BrokerageAmountUnit']"
                                label="Unit">
                                <ng-select placeholder="ex. %" ResizableDropdown formControlName="BrokerageAmountUnit"
                                    [items]="brokerageUnits"
                                    [readonly]="(projectDetailForm.controls['minimumPrice'].value && projectDetailForm.controls['BrokerageAmount'].value) ? false : true">
                                </ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex">
            <div class="field-label-underline mt-30">Date Status</div>
        </div>
        <div class="d-flex flex-wrap justify-between">
            <div class="w-33 tb-w-50 ip-w-100 field-rupees-tag">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">{{'PROJECTS.start-date' | translate}}</div>
                    <div class="position-relative">
                        <form-errors-wrapper [control]="projectDetailForm.controls['startDate']" label="Start date">
                            <div class="rupees icon ic-calendar ic-xxs ic-coal cursor-pointer"
                                [owlDateTimeTrigger]="dt2"></div>
                            <input type="text" [owlDateTime]="dt2" [owlDateTimeTrigger]="dt2" readonly bsDatepicker
                                [max]="projectDetailForm.value.endDate" autocomplete="off" formControlName="startDate"
                                id="inpStartDate" placeholder="ex. 05/03/2025" data-automate-id="inpStartDate">
                            <owl-date-time [pickerType]="'calendar'" #dt2
                                (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100 field-rupees-tag">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">{{'PROJECTS.end-date' | translate}}</div>
                    <div class="position-relative"
                        [ngClass]="{'pe-none disabled' : !projectDetailForm.value.startDate}">
                        <form-errors-wrapper [control]="projectDetailForm.controls['endDate']" label="End date">
                            <div class="rupees icon ic-calendar ic-xxs ic-coal cursor-pointer"
                                [owlDateTimeTrigger]="dt3"></div>
                            <input type="text" [owlDateTime]="dt3" [owlDateTimeTrigger]="dt3" readonly bsDatepicker
                                [min]="projectDetailForm.value.startDate?projectDetailForm.value.startDate:currentDate"
                                formControlName="endDate" id="inpEndDate" placeholder="ex. 19/06/2025"
                                data-automate-id="inpEndDate" autocomplete="off">
                            <owl-date-time [pickerType]="'calendar'" #dt3
                                (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">Possession</div>
                    <div class="position-relative mt-4 lead field-rupees-tag">
                        <form-errors-wrapper>
                            <div class="rupees icon ic-calendar ic-xxs ic-coal cursor-pointer"
                                (click)="isOpenPossessionModal = !isOpenPossessionModal"></div>
                            <input type="text" id="inpPossessionDate" placeholder="ex. 29/06/2025" readonly
                                [value]="selectedPossession ? selectedPossession : projectDetailForm.value.globalRange"
                                (click)="isOpenPossessionModal = !isOpenPossessionModal" autocomplete="off">
                        </form-errors-wrapper>
                    </div>
                    <div class="position-relative">
                        <div class="position-absolute top-0 w-100 bg-white z-index-1001 box-shadow-10"
                            *ngIf="isOpenPossessionModal">
                            <div class="d-flex">
                                <div class="w-100 bg-white">
                                    <ng-container *ngFor="let type of dateFilterList">
                                        <div class="form-check form-check-inline w-fit-content">
                                            <input type="radio" id="inpShowData{{type.value}}" name="globalRange"
                                                formControlName="globalRange" [value]="type.value"
                                                 class="radio-check-input w-10 h-10">
                                            <label class="text-dark-gray text-large ml-8"
                                                for="inpShowData{{type.value}}">{{type.displayName}}</label>
                                        </div>
                                    </ng-container>
                                    <div class="position-relative dashboard-filter form-group m-6 border py-6"
                                        [ngClass]="{'pe-none disabled' : projectDetailForm.controls['globalRange'].value !== 'Custom Date', 'border-red-30': isValidPossDate, 'border': !isValidPossDate}">
                                        <form-errors-wrapper [control]="projectDetailForm.controls['globalDate']"
                                            label="Date">
                                            <span *ngIf="selectedMonth"
                                                class="fw-700 text-large text-black-200 px-12 w-90pr cursor-pointer"
                                                [owlDateTimeTrigger]="dt5">
                                                {{selectedMonth}} {{selectedYear}}
                                            </span>
                                            <span *ngIf="!selectedMonth"
                                                class="text-dark-gray px-12 w-90pr cursor-pointer"
                                                [owlDateTimeTrigger]="dt5">
                                                select month and year
                                            </span>
                                            <input type="text" [value]="selectedMonthAndYear" [owlDateTimeTrigger]="dt5"
                                                [owlDateTime]="dt5" placeholder="Select date"
                                                class="p-0 border-0 border-remove"
                                                style="height: 0 !important; opacity: 0; position: absolute; top: 0; left: 0; pointer-events: none;" />
                                            <owl-date-time #dt5 startView="year" [yearOnly]="true"
                                                [pickerType]="'calendar'"
                                                (afterPickerOpen)="onPickerOpened(currentDate, 'month')"
                                                (monthSelected)="monthChanged($event)"></owl-date-time>
                                            <div *ngIf="isValidPossDate"
                                                class="mt-8 text-xs text-red position-absolute right-16 fw-semi-bold">
                                                Please select possession date
                                            </div>
                                        </form-errors-wrapper>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-end p-6 border-top">
                                <div class="btn-coal" (click)="closeModal()">Close</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex">
            <div class="field-label-underline mt-30">Associated Banks</div>
        </div>
        <div class="flex-wrap justify-between">
            <div class="w-33 tb-w-50 ip-w-100 field-rupees-tag">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">Select Bank(s)</div>
                    <form-errors-wrapper [control]="projectDetailForm.controls['bankList']" label="bankList">
                        <ng-select [virtualScroll]="true" class="bg-white" [items]="associatedBanks" [multiple]="true"
                            ResizableDropdown [closeOnSelect]="false" bindLabel="name" bindValue="id" name="bankList"
                            formControlName="bankList" placeholder="Select banks" addTagText="Create New Project">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark" autocomplete="off"></span>{{item.name}}
                                </div>
                            </ng-template>
                        </ng-select>
                    </form-errors-wrapper>
                </div>
            </div>
        </div>

        <div class="d-flex">
            <div class="field-label-underline mt-30">Project location details</div>
        </div>
        <div class="d-flex flex-wrap" *ngIf="isManualLocation; else googleLocation">
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0">
                    <div [ngClass]="isGeoFenceEnabled ? 'field-label-req' : 'field-label'">Locality
                    </div>
                    <form-errors-wrapper [control]="projectDetailForm.controls['locality']" label="locality">
                        <input type="text" id="inpPropTitle" data-automate-id="inpPropTitle" autocomplete="off"
                            placeholder="ex. sector 1 HSR Layout" formControlName="locality">
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">City</div>
                    <form-errors-wrapper [control]="projectDetailForm.controls['city']">
                        <input type="text" id="inpPropTitle" data-automate-id="inpPropTitle" autocomplete="off"
                            placeholder="ex. Bangalore" formControlName="city">
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0">
                    <div class="field-label">State</div>
                    <form-errors-wrapper [control]="projectDetailForm.controls['state']">
                        <input type="text" id="inpPropTitle" data-automate-id="inpPropTitle" autocomplete="off"
                            placeholder="ex. Karnataka" formControlName="state">
                    </form-errors-wrapper>
                </div>
            </div>

            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0 ip-mr-0">
                    <div class="field-label">Country</div>
                    <form-errors-wrapper [control]="projectDetailForm.controls['country']" label="country">
                        <input type="text" id="inpPropTitle" data-automate-id="inpPropTitle" autocomplete="off"
                            placeholder="ex. India" formControlName="country">
                    </form-errors-wrapper>
                </div>
            </div>

            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-10 ip-mr-0 ip-mr-0">
                    <div class="field-label">Pin code</div>
                    <form-errors-wrapper [control]="projectDetailForm.controls['pinCode']" label="Pin code">
                        <input type="number" id="inpPropTitle" data-automate-id="inpPropTitle" autocomplete="off"
                            placeholder="ex. 228118" formControlName="pinCode" (keydown)="onlyNumbers($event)"
                            onWheel="event.preventDefault()">
                    </form-errors-wrapper>
                </div>
                <div class="d-flex">
                    <div class="cursor-pointer align-center fw-semi-bold text-accent-green mt-6"
                        (click)="addManualLocation()">
                        <span class="icon ic-xs ic-accent-green"
                            [ngClass]="isManualLocation ? 'ic-search' : 'ic-add'"></span>
                        <span>{{isManualLocation ? 'Location List' : 'Manually Enter Location'}}</span>
                    </div>
                </div>
            </div>

        </div>
        <!-- <div class="w-100 h-400 br-8 ph-w-100 responsive-map mt-20">
            <google-map [center]="mapCenter" [zoom]="zoom">
                <map-marker [position]="mapCenter?.lat ? { lat: mapCenter?.lat, lng: mapCenter?.lng } : ''"
                    [label]="10">
                </map-marker>
                <map-info-window>{{ selectedAddress }}</map-info-window>
            </google-map>
        </div> -->
        <ng-template #googleLocation>
            <div class="w-33 tb-w-50 ip-w-100" [formGroup]="projectDetailForm">
                <div [ngClass]="isGeoFenceEnabled ? 'field-label-req' :'field-label'">{{'LOCATION.location' |
                    translate}}</div>
                <form-errors-wrapper [control]="projectDetailForm.controls['locationId']"
                    label="{{ 'LOCATION.location' | translate }}">
                    <div class="field-tag">
                        <ng-select [virtualScroll]="true" formControlName="locationId"
                            (search)="searchPlaceTerm$.next($event.term)" [editableSearchTerm]="true"
                            placeholder="Select Location" class="bg-white" [items]="placesList" bindLabel="location"
                            (clear)="removeLocation('location')" (change)="removeLocation('changeLocation')">
                            <ng-template ng-option-tmp let-item="item">
                                <div title="{{item?.location}}">{{item?.location}}</div>
                            </ng-template>
                        </ng-select>
                        <div class="position-absolute top-5 right-40" *ngIf="isPlacesListLoading">
                            <img src="assets/images/loader-rat.svg" class="rat-loader h-30px w-30px" alt="loader">
                        </div>
                        <div class="search icon ic-search ic-sm ic-coal"></div>
                    </div>

                </form-errors-wrapper>
                <div class="d-flex">
                    <div class="cursor-pointer align-center fw-semi-bold text-accent-green mt-6"
                        (click)="isManualLocation = !isManualLocation">
                        <span class="icon ic-xs ic-accent-green"
                            [ngClass]="isManualLocation ? 'ic-search' : 'ic-add'"></span>
                        <span>{{isManualLocation ? 'Location List' : 'Manually Enter Location'}}</span>
                    </div>
                </div>
            </div>
        </ng-template>
    </form>
</div>
<div class="flex-end px-20 py-16 bg-white position-sticky box-shadow-10 bottom-0 z-index-1001">
    <u class="mr-20 text-large fw-bold cursor-pointer mr-10" (click)="manageProject()">Cancel</u>
    <button class="btn-coal" (click)="onSubmit()">Save & Go To Next</button>
</div>
<ng-template #previewModal>
    <div class="w-100 br-10">
        <div class="p-20">
            <div class="flex-between">
                <h5 class="fw-600">{{isForNotes ? 'Notes' : 'Project Description'}}
                </h5>
                <div class="cursor-pointer flex-center header-5 fw-400 text-red-350" (click)="modalRef.hide()"><span
                        class="ic-close mb-2 ic-xxs ic-red-350 mr-4"></span>Close
                </div>
            </div>
            <div class="scrollbar max-h-400 border br-6 mt-8 p-16">
                <h6 *ngIf="isForNotes" [innerHTML]="convertUrlsToLinks(getPreviewContent(),true)"
                    class="text-sm text-black-20 pre-whitespace"></h6>
                <h6 *ngIf="!isForNotes" [innerHTML]="convertUrlsToLinks(getPreviewContent(),true)"
                    class="text-sm text-black-20 pre-whitespace"></h6>
            </div>
        </div>
    </div>
</ng-template>