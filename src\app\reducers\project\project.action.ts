import { Action } from '@ngrx/store';
import { LeadExcel, MapColumnsExcel } from 'src/app/core/interfaces/leads.interface';
import { UnitInfoFilter } from 'src/app/core/interfaces/project.interface';

export enum ProjectActionTypes {
  FETCH_ALL_ASSIGNED_PROJECTS_LIST = '[ASSIGNEDPROJECT] Fetch All ASSIGNED Project List',
  FETCH_ALL_ASSIGNED_PROJECTS_SUCCESS = '[ASSIGNEDPROJECT] Fetch All ASSIGNED Project List SUCCESS',
  UPDATE_ASSIGNED_PROJECTS = '[ASSIGNEDPROJECT] Update Template Assigned Projects',
  UPDATE_ASSIGNED_PROJECTS_SUCCESS = '[ASSIGNEDPROJECT] Update Template Assigned Projects Success',
  FETCH_TEMP_PROJECT_LIST = '[TEMPPROJECT] Fetch Project List',
  FETCH_TEMP_PROJECT_LIST_SUCCESS = '[TEMPPROJECT] Fetch Project List Success',
  FETCH_TEMP_PROJECT_BY_ID = '[TEMPPROJECT] Fetch Project By Id',
  FETCH_TEMP_PROJECT_BY_ID_SUCCESS = '[TEMPPROJECT] Fetch Project By Id Success',
  FETCH_PROJECT_ID_WITH_NAME = '[PROJECT] Fetch Project Id With Name',
  FETCH_PROJECT_ID_WITH_NAME_SUCCESS = '[PROJECT] Fetch Project ID With Name Success',
  FETCH_PROJECT_LEADS_COUNT_BY_IDS = '[TEMPPROJECT] Fetch Project Leads Count By Ids',
  FETCH_PROJECT_LEADS_COUNT_BY_IDS_SUCCESS = '[TEMPPROJECT] Fetch Project Leads Count By Ids Success',
  DELETE_TEMP_PROJECT = '[TEMPPROJECT] Delete Project',
  ADD_TEMP_PROJECT = '[TEMPPROJECT] Add Project',
  UPDATE_PROJECT = '[TEMPPROJECT] Update Project',
  FETCH_PROJECT_ASSIGNMENT_DETAILS = '[TEMPROJECT] Get all assigned users for Project',
  FETCH_PROJECT_ASSIGNMENT_DETAILS_SUCCESS = '[TEMPROJECT] Get all assigned users for Project Success',
  UPDATE_FILTERS_PAYLOAD = '[TEMPROJECT] Update Filters Payload',
  FETCH_PROJECT_COUNT = '[PROJECT] Get Project Count',
  FETCH_PROJECT_COUNT_SUCCESS = '[PROJECT] Get Project Count Success',
  ADD_UNIT_TYPE = '[PROJECT] Add Unit Type',
  FETCH_UNIT_INFO_BY_IDS = '[PROJECT] Fetch Unit Info By Ids',
  FETCH_UNIT_INFO_BY_IDS_SUCCESS = '[PROJECT] Fetch Unit Info By Ids Success',
  ADD_PROJECT_BLOCKS = '[PROJECT] Add Project Blocks',
  UPDATE_PROJECT_BLOCK = '[PROJECT] Update Project Block',
  DELETE_PROJECT_BLOCK = '[PROJECT] Delete Project Block',
  UPDATE_PROJECT_UNIT_INFO = '[PROJECT] Update Project Unit Info',
  DELETE_PROJECT_UNIT_INFO = '[PROJECT] Delete Project Unit Info',
  ADD_PROJECT_AMENITIES = '[PROJECT] Add Project Amenities',
  UPDATE_PROJECT_AMINITY = '[PROJECT] Update Project Aminity',
  UPDATE_PROJECT_GALLERY = '[PROJECT] Update Project Gallery',
  UPDATE_PROJECT_TOGGLE_STATUS = '[PROJECT] Update Project Toggle Status',
  FETCH_PROJECT_TYPE = '[MASTER_DATA] Fetch Project Type',
  FETCH_PROJECT_TYPE_SUCCESS = '[MASTER_DATA] Fetch Project Type Success',
  UPDATE_UNIT_TYPE_STATUS = '[PROJECT] Update Unit Type toggle Status',
  FETCH_MICROSITE_PROJECT = '[PROJECT] Fetch Microsite Project Details',
  FETCH_MICROSITE_PROJECT_SUCCESS = '[PROJECT] Fetch Microsite Project Details Success',
  FETCH_PROJECT_UNIT = '[PROJECT] Fetch Microsite Project Unit',
  FETCH_PROJECT_UNIT_SUCCESS = '[PROJECT] Fetch Microsite Project Unit Success',
  FETCH_UNIT_TYPE_BY_ID = '[PROJECT] Fetch Unit Type By Id',
  FETCH_UNIT_TYPE_BY_ID_SUCCESS = '[PROJECT] Fetch Unit Type By Id Success',
  FETCH_BLOCK_BY_ID = '[PROJECT] Fetch Block By Id',
  FETCH_BLOCK_BY_ID_SUCCESS = '[PROJECT] Fetch Block By Id Success',
  FETCH_BUILDER_DETAILS = '[PROJECT] Fetch Builder Details',
  FETCH_BUILDER_DETAILS_SUCCESS = '[PROJECT] Fetch Builder Details Success',
  BULK_DELETE_PROJECT_BLOCK = '[TEMPPROJECT] BUlk  Delete Project Blocks',
  FETCH_LOCATIONS = '[PROJECT] Fetch Project Locations',
  FETCH_LOCATIONS_SUCCESS = '[PROJECT] Fetch Project Locations Success',
  FETCH_PROJECT_AMENITIES_BY_IDS = '[PROJECT] Fetch Project Amenities By Ids',
  FETCH_PROJECT_AMENITIES_BY_IDS_SUCCESS = '[PROJECT] Fetch Project Amenities By Ids Success',
  CLEAR_PROJECTS_FILTERSPAYLOAD = '[PROJECT] Clear Filters Payload',
  HAS_PROJECT_NAME = '[PROJECT] Has project name',
  HAS_PROJECT_NAME_SUCCESS = '[PROJECT] Has project name success',
  FETCH_PROJECT_CURRENCY_LIST = '[PROJECT] Fetch Project Currency List',
  FETCH_PROJECT_CURRENCY_LIST_SUCCESS = '[PROJECT] Fetch Project List Success',
  UNIT_INFO_DELETE_SUCCESS = '[PROJECT] Unit info deleted Success',
  FETCH_UNIT_INFO_BY_ID = '[TEMPPROJECT] Fetch Unit info By Id',
  FETCH_UNIT_INFO_BY_ID_SUCCESS = '[TEMPPROJECT] Fetch Unit info By Id Success',
  INCREASE_PROJECT_SHARE_COUNT = '[PROJECT] Project Share Count',
  FETCH_MICROSITE_UNIT = '[PROJECT] Fetch Microsite Unit Details',
  FETCH_MICROSITE_UNIT_SUCCESS = '[PROJECT] Fetch Microsite Unit Details Success',
  FETCH_MICROSITE_AMENITIES = '[PROJECT] Fetch Microsite Amenities Details',
  FETCH_MICROSITE_AMENITIES_SUCCESS = '[PROJECT] Fetch Microsite Amenities Details Success',
  FETCH_IMAGE_DROPDOWN = '[PROJECT] Fetch Project Image Dropdown',
  FETCH_IMAGE_DROPDOWN_SUCCESS = '[PROJECT] Fetch Project Image Dropdown Success',
  PROJECT_UNIT_EXCEL_UPLOAD = '[PROJECT] Upload Project Unit Excel File',
  PROJECT_UNIT_EXCEL_UPLOAD_SUCCESS = '[PROJECT] Upload Project Unit Excel File Success',
  UPLOAD_MAPPED_COLUMNS = '[PROJECT] Upload Mapped Column Data',
  FETCH_EXCEL_UPLOADED_LIST = '[PROJECT] Fetch Excel Uploaded List',
  FETCH_EXCEL_UPLOADED_LIST_SUCCESS = '[PROJECT] Fetch Excel Uploaded List Success',
  UPDATE_UNITINFO_FILTER_PAYLOAD = '[USERS] Update Unit Info Filter Payload',
  FETCH_PROJECT_BASICDETAILS_BY_IDS = '[PROJECT] Fetch Project Basic Details By Ids',
  FETCH_PROJECT_BASICDETAILS_BY_IDS_SUCCESS = '[PROJECT] Fetch Project Basic Details By Ids Success',
  FETCH_PROJECT_GALLERY_BY_IDS = '[PROJECT] Fetch Project Gallery By Ids',
  FETCH_PROJECT_GALLERY_BY_IDS_SUCCESS = '[PROJECT] Fetch Project Gallery By Ids Success',
  INCREASE_PROJECT_UNIT_SHARE_COUNT = '[PROJECT] Project Unit Share Count',
  FETCH_UNIT_BY_IDS = '[PROJECT] Fetch Project Unit By Ids',
  FETCH_UNIT_BY_IDS_SUCCESS = '[PROJECT] Fetch Project Unit By Ids Success',
  FETCH_PROJECT_EXCEL_UPLOADED_LIST = '[PROJECT] Fetch Project Excel Uploaded List',
  FETCH_PROJECT_EXCEL_UPLOADED_LIST_SUCCESS = '[PROJECT] Fetch Project Excel Uploaded List Success',
  FETCH_EXPORT_PROJECT_STATUS = '[PROJECT] Fetch Project Export Status List',
  FETCH_EXPORT_PROJECT_STATUS_SUCCESS = '[PROJECT] Fetch Project Export Status List Success',
  EXPORT_PROJECT = '[PROJECT] Export Project through excel',
  EXPORT_PROJECT_SUCCESS = '[PROJECT] Export Project through excel Success',
  UPLOAD_PROJECT_MAPPED_COLUMNS = '[PROJECT] Upload Project Mapped Column Data',
  PROJECT_EXCEL_UPLOAD = '[PROJECT] Upload Project Excel File',
  PROJECT_EXCEL_UPLOAD_SUCCESS = '[PROJECT] Upload Project Excel File Success',
  FETCH_PROJECT_WITH_GOOGLE_LOCATION = '[PROJECT] Fetch Project With Google Location',
  FETCH_PROJECT_WITH_GOOGLE_LOCATION_SUCCESS = '[PROJECT] Fetch Project With Google Location Success',
  FETCH_PROJECT_ASSIGNMENTS = '[PROJECT] Fetch Project Assignments',
  FETCH_PROJECT_ASSIGNMENTS_SUCCESS = '[PROJECT] Fetch Project Assignments Success',
}
export class FetchAssignedProjectsList implements Action {
  readonly type: string = ProjectActionTypes.FETCH_ALL_ASSIGNED_PROJECTS_LIST;
  constructor(public id: string) { }
}
export class FetchAssignedProjectsListSuccess implements Action {
  readonly type: string =
    ProjectActionTypes.FETCH_ALL_ASSIGNED_PROJECTS_SUCCESS;
  constructor(public response: any) { }
}

export class updateAssignedProjects implements Action {
  readonly type: string = ProjectActionTypes.UPDATE_ASSIGNED_PROJECTS;
  constructor(public id: any, public projectId: any[]) { }
}
export class updateAssignedProjectsSuccess implements Action {
  readonly type: string = ProjectActionTypes.UPDATE_ASSIGNED_PROJECTS_SUCCESS;
  constructor(public response: any) { }
}

export class FetchProjectList implements Action {
  readonly type: string = ProjectActionTypes.FETCH_TEMP_PROJECT_LIST;
  constructor(
    public payload = {
      path: 'project',
    },
    public canFetchProjectsCount: boolean = false
  ) { }
}

export class FetchProjectListSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_TEMP_PROJECT_LIST_SUCCESS;
  constructor(public response: any = []) { }
}

export class AddProject implements Action {
  readonly type: string = ProjectActionTypes.ADD_TEMP_PROJECT;
  constructor(public payload: any) { }
}

export class AddUnitType implements Action {
  readonly type: string = ProjectActionTypes.ADD_UNIT_TYPE;
  constructor(public payload: any) { }
}
export class AddProjectBlocks implements Action {
  readonly type: string = ProjectActionTypes.ADD_PROJECT_BLOCKS;
  constructor(public payload: any) { }
}
export class UpdateProject implements Action {
  readonly type: string = ProjectActionTypes.UPDATE_PROJECT;
  constructor(public id: string, public payload: any) { }
}
export class FetchProjectById implements Action {
  readonly type: string = ProjectActionTypes.FETCH_TEMP_PROJECT_BY_ID;
  constructor(public id: string) { }
}
export class FetchProjectByIdSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_TEMP_PROJECT_BY_ID_SUCCESS;
  constructor(public selectedProject: any) { }
}
export class FetchProjectLeadsCountByIds implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_LEADS_COUNT_BY_IDS;
  constructor(public ids: string[]) { }
}
export class FetchProjectLeadsCountByIdsSuccess implements Action {
  readonly type: string =
    ProjectActionTypes.FETCH_PROJECT_LEADS_COUNT_BY_IDS_SUCCESS;
  constructor(public counts: any) { }
}
export class DeleteProject implements Action {
  readonly type: string = ProjectActionTypes.DELETE_TEMP_PROJECT;
  constructor(public id: string) { }
}

export class BulkDeleteProjectBlock implements Action {
  readonly type: string = ProjectActionTypes.BULK_DELETE_PROJECT_BLOCK;
  constructor(public ids: string[], public payload: any) { }
}

export class FetchProjectAssignmentDetails implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_ASSIGNMENT_DETAILS;
  constructor(public resource: any) { }
}
export class FetchProjectAssignmentDetailsSuccess implements Action {
  readonly type: string =
    ProjectActionTypes.FETCH_PROJECT_ASSIGNMENT_DETAILS_SUCCESS;
  constructor(public response: any) { }
}
export class UpdateProjectsFiltersPayload implements Action {
  readonly type: string = ProjectActionTypes.UPDATE_FILTERS_PAYLOAD;
  constructor(public payload: any) { }
}
export class FetchProjectCount implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_COUNT;
  constructor(
    public payload = {
      path: 'project/count',
    },
    public canFetchProjectsCount: boolean = false
  ) { }
}
export class FetchProjectCountSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_COUNT_SUCCESS;
  constructor(public response: any = []) { }
}

export class FetchProjectDataById implements Action {
  readonly type: string = ProjectActionTypes.FETCH_UNIT_INFO_BY_IDS;
  constructor(public ids: any) { }
}

export class FetchProjectDataByIdSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_UNIT_INFO_BY_IDS_SUCCESS;
  constructor(public response: any) { }
}

export class FetchProjectAmenitiesByIds implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_AMENITIES_BY_IDS;
  constructor(public ids: any) { }
}

export class FetchProjectAmenitiesByIdsSuccess implements Action {
  readonly type: string =
    ProjectActionTypes.FETCH_PROJECT_AMENITIES_BY_IDS_SUCCESS;
  constructor(public response: any) { }
}

export class FetchUnitInfo implements Action {
  readonly type: string = ProjectActionTypes.FETCH_UNIT_TYPE_BY_ID;
  constructor(public payload: any) { }
}

export class FetchUnitInfoSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_UNIT_TYPE_BY_ID_SUCCESS;
  constructor(public response: any) { }
}

export class FetchBuilderDetails implements Action {
  readonly type: string = ProjectActionTypes.FETCH_BUILDER_DETAILS;
  constructor() { }
}

export class FetchBuilderDetailsSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_BUILDER_DETAILS_SUCCESS;
  constructor(public response: any) { }
}

export class FetchBlockById implements Action {
  readonly type: string = ProjectActionTypes.FETCH_BLOCK_BY_ID;
  constructor(public id: any) { }
}

export class FetchBlockByIdSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_BLOCK_BY_ID_SUCCESS;
  constructor(public response: any) { }
}

export class UpdateProjectBlock implements Action {
  readonly type: string = ProjectActionTypes.UPDATE_PROJECT_BLOCK;
  constructor(public payload: any) { }
}

export class UpdateProjectAminity implements Action {
  readonly type: string = ProjectActionTypes.UPDATE_PROJECT_AMINITY;
  constructor(public payload: any) { }
}

export class UpdateProjectGallery implements Action {
  readonly type: string = ProjectActionTypes.UPDATE_PROJECT_GALLERY;
  constructor(public payload: any) { }
}

export class UpdateProjectToggleStatus implements Action {
  readonly type: string = ProjectActionTypes.UPDATE_PROJECT_TOGGLE_STATUS;
  constructor(public payload: any) { }
}

export class UpdateUnitTypeToggleStatus implements Action {
  readonly type: string = ProjectActionTypes.UPDATE_UNIT_TYPE_STATUS;
  constructor(public payload: any) { }
}

export class DeleteProjectBlock implements Action {
  readonly type: string = ProjectActionTypes.DELETE_PROJECT_BLOCK;
  constructor(public id: string, public projId: string) { }
}

export class UpdateProjectUnitInfo implements Action {
  readonly type: string = ProjectActionTypes.UPDATE_PROJECT_UNIT_INFO;
  constructor(public payload: any) { }
}

export class DeleteProjectUnitInfo implements Action {
  readonly type: string = ProjectActionTypes.DELETE_PROJECT_UNIT_INFO;
  constructor(public payload: any) { }
}

export class AddProjectAmenities implements Action {
  readonly type: string = ProjectActionTypes.ADD_PROJECT_AMENITIES;
  constructor(public payload: any) { }
}

export class FetchProjectType implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_TYPE;
  constructor() { }
}

export class FetchProjectTypeSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_TYPE_SUCCESS;
  constructor(public data: any) { }
}

export class FetchMicrositeProject implements Action {
  readonly type: string = ProjectActionTypes.FETCH_MICROSITE_PROJECT;
  constructor(public payload: any) { }
}
export class FetchMicrositeProjectSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_MICROSITE_PROJECT_SUCCESS;
  constructor(public project: any[] = []) { }
}

export class FetchMicrositeUnit implements Action {
  readonly type: string = ProjectActionTypes.FETCH_MICROSITE_UNIT;
  constructor(public payload: any) { }
}
export class FetchMicrositeUnitSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_MICROSITE_UNIT_SUCCESS;
  constructor(public project: any[] = []) { }
}
export class FetchMicrositeAmenities implements Action {
  readonly type: string = ProjectActionTypes.FETCH_MICROSITE_AMENITIES;
  constructor(public payload: any) { }
}
export class FetchMicrositeAmenitiesSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_MICROSITE_AMENITIES_SUCCESS;
  constructor(public project: any[] = []) { }
}

export class FetchProjectUnit implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_UNIT;
  constructor(public payload: any) { }
}
export class FetchProjectUnitSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_UNIT_SUCCESS;
  constructor(public response: any[] = []) { }
}
export class FetchProjectLocations implements Action {
  readonly type: string = ProjectActionTypes.FETCH_LOCATIONS;
  constructor() { }
}

export class FetchProjectLocationsSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_LOCATIONS_SUCCESS;
  constructor(public response: string[] = []) { }
}

export class ClearFiltersPayload implements Action {
  readonly type: string = ProjectActionTypes.CLEAR_PROJECTS_FILTERSPAYLOAD;
  constructor() { }
}

export class HasProjectName implements Action {
  readonly type: string = ProjectActionTypes.HAS_PROJECT_NAME;
  constructor(public data: any) { }
}

export class HasProjectNameSuccess implements Action {
  readonly type: string = ProjectActionTypes.HAS_PROJECT_NAME_SUCCESS;
  constructor(public resp: string = '') { }
}

export class FetchProjectCurrency implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_CURRENCY_LIST;
  constructor() { }
}
export class FetchProjectCurrencySuccess implements Action {
  readonly type: string =
    ProjectActionTypes.FETCH_PROJECT_CURRENCY_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class fetchCurrOffSetSuccess implements Action {
  readonly type: string = ProjectActionTypes.UNIT_INFO_DELETE_SUCCESS;
  constructor(public response: any) { }
}

export class ProjectUnitExcelUpload implements Action {
  readonly type: string = ProjectActionTypes.PROJECT_UNIT_EXCEL_UPLOAD;
  constructor(public file: File) { }
}

export class ProjectUnitExcelUploadSuccess implements Action {
  readonly type: string = ProjectActionTypes.PROJECT_UNIT_EXCEL_UPLOAD_SUCCESS;
  constructor(public resp: LeadExcel) { }
}

export class UploadMappedColumns implements Action {
  readonly type: string = ProjectActionTypes.UPLOAD_MAPPED_COLUMNS;
  constructor(public payload: MapColumnsExcel) { }
}
export class FetchProjectUnitExcelUploadedList implements Action {
  readonly type: string = ProjectActionTypes.FETCH_EXCEL_UPLOADED_LIST;
  constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchProjectUnitExcelUploadedSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_EXCEL_UPLOADED_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchUnitInfoById implements Action {
  readonly type: string = ProjectActionTypes.FETCH_UNIT_INFO_BY_ID;
  constructor(public id: string) { }
}
export class FetchUnitInfoByIdSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_UNIT_INFO_BY_ID_SUCCESS;
  constructor(public unitInf: any) { }
}

export class IncreaseProjectShareCount implements Action {
  readonly type: string = ProjectActionTypes.INCREASE_PROJECT_SHARE_COUNT;
  constructor(public id: string) { }
}

export class fetchImageDropDown implements Action {
  readonly type: string = ProjectActionTypes.FETCH_IMAGE_DROPDOWN;
  constructor() { }
}

export class fetchImageDropDownSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_IMAGE_DROPDOWN_SUCCESS;
  constructor(public response: string[] = []) { }
}
export class FetchProjectIdWithName implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_ID_WITH_NAME;
  constructor() { }
}
export class FetchProjectIDWithNameSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_ID_WITH_NAME_SUCCESS;
  constructor(public projectList: any) { }
}
export class UpdateUnitInfoFilterPayload implements Action {
  readonly type: string = ProjectActionTypes.UPDATE_UNITINFO_FILTER_PAYLOAD;
  constructor(public filter: UnitInfoFilter) { }
}

export class FetchProjectBasicDetailsById implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_BASICDETAILS_BY_IDS;
  constructor(public ids: any) { }
}

export class FetchProjectBasicDetailsByIdSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_BASICDETAILS_BY_IDS_SUCCESS;
  constructor(public response: any) { }
}

export class FetchProjectGalleryById implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_GALLERY_BY_IDS;
  constructor(public ids: any) { }
}

export class FetchProjectGalleryByIdSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_GALLERY_BY_IDS_SUCCESS;
  constructor(public response: any) { }
}

export class IncreaseProjectUnitShareCount implements Action {
  readonly type: string = ProjectActionTypes.INCREASE_PROJECT_UNIT_SHARE_COUNT;
  constructor(public id: string) { }
}

export class FetchUnitById implements Action {
  readonly type: string = ProjectActionTypes.FETCH_UNIT_BY_IDS;
  constructor(public id: any) { }
}

export class FetchUnitByIdSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_UNIT_BY_IDS_SUCCESS;
  constructor(public response: any) { }
}

export class FetchProjectExcelUploadedList implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_EXCEL_UPLOADED_LIST;
  constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchProjectExcelUploadedSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_EXCEL_UPLOADED_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchExportProjectStatus implements Action {
  readonly type: string = ProjectActionTypes.FETCH_EXPORT_PROJECT_STATUS;
  constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchExportProjectStatusSuccess implements Action {
  readonly type: string =
    ProjectActionTypes.FETCH_EXPORT_PROJECT_STATUS_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class ExportProject implements Action {
  readonly type: string = ProjectActionTypes.EXPORT_PROJECT;
  constructor(public payload: any) { }
}

export class ExportProjectSuccess implements Action {
  readonly type: string = ProjectActionTypes.EXPORT_PROJECT_SUCCESS;
  constructor(public resp: string = '') { }
}

export class UploadProjectMappedColumns implements Action {
  readonly type: string = ProjectActionTypes.UPLOAD_PROJECT_MAPPED_COLUMNS;
  constructor(public payload: any) { }
}

export class ProjectExcelUpload implements Action {
  readonly type: string = ProjectActionTypes.PROJECT_EXCEL_UPLOAD;
  constructor(public file: File) { }
}
export class ProjectExcelUploadSuccess implements Action {
  readonly type: string = ProjectActionTypes.PROJECT_EXCEL_UPLOAD_SUCCESS;
  constructor(public resp: LeadExcel) { }
}

export class FetchProjectWithGoogleLocation implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_WITH_GOOGLE_LOCATION;
  constructor() { }
}
export class FetchProjectWithGoogleLocationSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_WITH_GOOGLE_LOCATION_SUCCESS;
  constructor(public responce: any[]) { }
}
export class FetchProjectAssignments implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_ASSIGNMENTS;
  constructor(public id: string) { }
}

export class FetchProjectAssignmentsSuccess implements Action {
  readonly type: string = ProjectActionTypes.FETCH_PROJECT_ASSIGNMENTS_SUCCESS;
  constructor(public response: any) { }
}