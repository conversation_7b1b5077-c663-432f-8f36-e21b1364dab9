<a (click)="openMatchPropertyModal(matchingPropertiesModal)"
    class="text-accent-green border-bottom-green">{{'GLOBAL.match' | translate}}
</a>
<ng-template #matchingPropertiesModal>
    <div class=" text-coal">
        <div class="flex-between bg-coal w-100 px-16 py-12 text-white">
            <h3 class="fw-semi-bold">{{ paramsValue?.value === 'MatchingProperties' ? ('LEADS.matching-properties'
                | translate) : ('LEADS.matching-projects' | translate) }}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
        </div>
        <div class="px-16 pb-20 h-100-30">
            <div class="flex-wrap w-100 align-center text-sm fw-600 text-dark-gray-350">
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.name">
                    <div class="text-gray-90 text-nowrap">{{'GLOBAL.name' | translate}}:</div>
                    <div class="ml-10 text-truncate-1 break-all">{{params.name}}</div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.enquiredFor">
                    <div class="text-gray-90 text-nowrap">{{'LEAD_FORM.enquired-for' | translate}}:</div>
                    <div class="ml-10 text-truncate-1 break-all">{{params.enquiredFor}}</div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.assignTo">
                    <div class="text-gray-90 text-nowrap">{{'LEADS.assign-to' | translate}}:</div>
                    <div class="ml-10 text-truncate-1 break-all">{{params.assignTo}}</div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.lowerBudget">
                    <div class="text-gray-90 text-nowrap">Min. {{'LEAD_FORM.budget' |
                        translate}}:</div>
                    <div class="ml-10 text-truncate-1 break-all">{{params.lowerBudget}}</div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.upperBudget">
                    <div class="text-gray-90 text-nowrap">Max. {{'LEAD_FORM.budget' |
                        translate}}:</div>
                    <div class="ml-10 text-truncate-1 break-all">{{params.upperBudget}}</div>
                </div>
                <ng-container *ngIf="params?.propertyType == 'Residential' && params?.propertySubType !== 'Plot'">
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.noOfBHK">
                        <div class="text-gray-90 text-nowrap">{{'PROPERTY.bhk' | translate}}:</div>
                        <div class="ml-10 text-truncate-1 break-all">{{params.noOfBHK}}</div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.bhkType">
                        <div class="text-gray-90 text-nowrap">{{'PROPERTY.bhk' | translate}} {{'LABEL.type' |
                            translate}}:</div>
                        <div class="ml-10 text-truncate-1 break-all">{{params.bhkType}}</div>
                    </div>
                </ng-container>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.propertyType">
                    <div class="text-gray-90 text-nowrap">{{'LABEL.property' | translate}} {{'LABEL.type' | translate}}:
                    </div>
                    <div class="ml-10 text-truncate-1 break-all">{{params.propertyType}}</div>
                </div>
                <div class="w-100 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.propertySubType?.length">
                    <div class="text-gray-90 text-nowrap">{{'PROPERTY.sub-type' | translate}}:</div>
                    <div class="ml-10 text-truncate-1 break-all">{{params.propertySubType}}</div>
                </div>
                <div class="w-100 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.location">
                    <div class="text-gray-90 text-nowrap">{{'INTEGRATION.enquired-location' | translate}}:</div>
                    <div [title]="params.location" class="ml-10 text-truncate-1 break-all">{{params.location}}</div>
                </div>
            </div>
            <div>
                <div class="d-flex ip-flex-col bg-white w-100 border-gray mt-20">
                    <div class="align-center px-10 flex-grow-1 border-end no-validation py-12 ip-br-0">
                        <ng-container>
                            <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
                            <input type="text" (keydown.enter)="searchTermSubject.next($event.target.value)"
                                (input)="isEmptyInput($event)" placeholder="type to search" [(ngModel)]="searchTerm"
                                class="border-0 outline-0 w-100">
                            <small class="text-muted text-nowrap">({{ 'LEADS.lead-search-prompt' | translate }})</small>
                        </ng-container>
                    </div>

                    <!-- manage colums -->
                    <div class="d-flex ph-flex-wrap ip-br-top">
                        <div class="align-center position-relative cursor-pointer d-flex border-end bg-white">
                            <span class="position-absolute left-15 z-index-2 fw-600 text-sm" *ngIf="!matchingRadius">
                                Matching Radius</span>
                            <div class=" w-130 no-validation">
                                <ng-select [virtualScroll]="true" [items]="matchingRadiusList" bindLabel="label"
                                    bindValue="value" (change)="filterFunction()" [clearable]="true"
                                    [(ngModel)]="matchingRadius" class="bg-white border-0 no-validation"
                                    [searchable]="false">
                                </ng-select>
                            </div>
                        </div>
                        <div class="align-center position-relative cursor-pointer d-flex border-end">
                            <span class="position-absolute left-15 z-index-2 fw-600 text-sm">
                                {{ 'BUTTONS.manage-columns' | translate }}</span>
                            <div class="show-hide-gray w-140">
                                <ng-select [virtualScroll]="true" class="bg-white" [items]="columns" [multiple]="true" appSelectAll
                                    [searchable]="false" [closeOnSelect]="false" [ngModel]="defaultColumns"
                                    ResizableDropdown (change)="onColumnsSelected($event)">
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span>{{item.label}}</div>
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                        <div class="bg-coal text-white px-10 py-12 ip-w-30px  align-center cursor-pointer"
                            (click)="onSetColumnDefault()">
                            <span class="ip-d-none">{{ 'GLOBAL.default' | translate }}</span> <span
                                class="ic-refresh d-none ip-d-block"></span>
                        </div>
                        <div class="show-dropdown-white align-center position-relative">
                            <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
                                    {{ 'GLOBAL.show' | translate }}</span> {{ 'GLOBAL.entries' | translate }}</span>
                            <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" ResizableDropdown
                                class="w-150 tb-w-120px" (change)="assignCount()" [(ngModel)]="selectedPageSize"
                                [searchable]="false">
                                <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize"
                                    [value]="pageSize">{{pageSize}}</ng-option>
                            </ng-select>
                        </div>
                    </div>
                </div>
                <div class="py-4 border-left border-right" *ngIf="!getIsMatchingLeadLoading && rowData?.length"></div>
                <div class="matching checkbox-align" *ngIf="!getIsMatchingLeadLoading && rowData?.length">
                    <ag-grid-angular #agGrid class="ag-theme-alpine" [gridOptions]="gridOptions"
                        (gridReady)="onGridReady($event)" [defaultColDef]="gridOptions.defaultColDef"
                        [pagination]="true" [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true"
                        (filterChanged)="onFilterChanged($event)" [paginationPageSize]="pageSize" [rowData]="rowData"
                        [suppressPaginationPanel]="true">
                    </ag-grid-angular>
                </div>
                <div class="flex-end mt-20" *ngIf="totalMatchCount && !getIsMatchingLeadLoading">
                    <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{(currOffset * pageSize) + 1}}
                        {{ 'GLOBAL.to-small' | translate }} {{(currOffset * pageSize) + pageSize > totalMatchCount ?
                        gridApi?.getDisplayedRowCount() : (currOffset * pageSize) + gridApi?.getDisplayedRowCount()}}
                        {{'GLOBAL.of-small' | translate }}
                        {{totalMatchCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
                    <pagination [offset]="currOffset" [limit]="1" [range]="1"
                        [size]="getPages(totalMatchCount,pageSize)" (pageChange)="onPageChange($event)">
                    </pagination>
                </div>
            </div>
            <ng-container *ngIf="!getIsMatchingLeadLoading && !rowData?.length">
                <div class="flex-center-col h-100-114">
                    <img src="assets/images/layered-cards.svg" alt="No Data Found">
                    <div class="header-3 fw-600 text-center">{{'PROFILE.no-data-found' | translate }}</div>
                </div>
            </ng-container>
            <ng-container *ngIf="getIsMatchingLeadLoading">
                <div class="flex-center h-100">
                    <application-loader></application-loader>
                </div>
            </ng-container>

        </div>
        <div class="justify-center">
            <div class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 z-index-2"
                [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}">
                <div class="align-center tb-mb-10">
                    <div class="fw-600 text-coal mr-20 text-xl">{{gridApi?.getSelectedNodes()?.length}}
                        {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' | translate}}
                    </div>
                </div>
                <div class="flex-center flex-wrap">
                    <button class="btn-bulk" (click)="openShareExternalModal()">
                        Bulk Share
                    </button>
                </div>
            </div>
        </div>
    </div>
</ng-template>