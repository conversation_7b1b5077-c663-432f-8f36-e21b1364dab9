import {
  Directive,
  AfterV<PERSON>wInit,
  On<PERSON><PERSON>roy,
  ChangeDetectorRef,
  OnInit,
  ElementRef,
  Renderer2,
  Input
} from '@angular/core';
import { NgSelectComponent } from '@ng-select/ng-select';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Directive({
  selector: '[appSelectAll]'
})
export class SelectAllDirective implements OnInit, AfterViewInit, OnD<PERSON>roy {
  private destroy$ = new Subject<void>();
  private isInitialized = false;
  private selectAllCheckbox: HTMLElement | null = null;
  private checkboxContainer: HTMLElement | null = null;
  private checkmarkElement: HTMLElement | null = null;
  private selectAllCheckboxState: 'checked' | 'unchecked' | 'indeterminate' = 'unchecked';

  // Configuration
  private readonly MAX_ITEMS_THRESHOLD = 3000; // Hide select-all for datasets larger than this

  @Input() appSelectAllUseItemsList: boolean = false;

  constructor(
    private ngSelect: NgSelectComponent,
    private cdRef: ChangeDetectorRef,
    private renderer: Renderer2,
    private elementRef: ElementRef
  ) {}

  ngOnInit() {
    this.ngSelect.changeEvent
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.isInitialized) {
          this.updateSelectAllState();
        }
      });
  }

  ngAfterViewInit() {
    setTimeout(() => this.initializeSelectAll(), 0);
  }

  initializeSelectAll() {
    const originalOpen = this.ngSelect.open;
    this.ngSelect.open = () => {
      const result = originalOpen.call(this.ngSelect);
      setTimeout(() => {
        this.addSelectAllCheckbox();
        this.updateSelectAllState();
      }, 0);
      return result;
    };

    this.ngSelect.changeEvent
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        setTimeout(() => {
          this.addSelectAllCheckbox();
          this.updateSelectAllState();
        }, 0);
      });

    this.isInitialized = true;
  }

  addSelectAllCheckbox() {
    const dropdownPanel = this.elementRef.nativeElement.querySelector('.ng-dropdown-panel-items');
    if (!dropdownPanel) return;

    let selectAllContainer = dropdownPanel.querySelector('.select-all-container');
    const allItems = this.ngSelect.items || [];

    // Check if we should hide select-all for large datasets
    const shouldHideForLargeDataset = allItems.length > this.MAX_ITEMS_THRESHOLD;

    // Handle existing container
    if (selectAllContainer) {
      if (allItems.length === 0 || shouldHideForLargeDataset) {
        this.renderer.setStyle(selectAllContainer, 'display', 'none');
      } else {
        this.renderer.removeStyle(selectAllContainer, 'display');
      }
      return;
    }

    // Don't create select-all for large datasets
    if (shouldHideForLargeDataset) {
      return;
    }

    // Create new container
    selectAllContainer = this.createSelectAllContainer();

    if (allItems.length === 0) {
      this.renderer.setStyle(selectAllContainer, 'display', 'none');
    }

    const scrollContainer = dropdownPanel.querySelector('.ng-dropdown-panel-items .ng-option');

    if (scrollContainer && scrollContainer.parentNode) {
      this.renderer.insertBefore(scrollContainer.parentNode, selectAllContainer, scrollContainer);
    } else {
      const firstChild = dropdownPanel.firstChild;
      if (firstChild) {
        this.renderer.insertBefore(dropdownPanel, selectAllContainer, firstChild);
      } else {
        this.renderer.appendChild(dropdownPanel, selectAllContainer);
      }
    }
  }

  createSelectAllContainer(): HTMLElement {
    const selectAllContainer = this.renderer.createElement('div');
    this.renderer.addClass(selectAllContainer, 'select-all-container');

    const checkboxContainer = this.createCheckboxContainer();
    this.renderer.appendChild(selectAllContainer, checkboxContainer);

    this.renderer.listen(selectAllContainer, 'click', (event) => {
      event.preventDefault();
      event.stopPropagation();
      this.toggleSelectAll();
    });

    return selectAllContainer;
  }

  createCheckboxContainer(): HTMLElement {
    const checkboxContainer = this.renderer.createElement('div');
    this.renderer.addClass(checkboxContainer, 'checkbox-container');
    this.checkboxContainer = checkboxContainer;

    const checkbox = this.createCheckboxIcon();
    this.selectAllCheckbox = checkbox;

    const checkmark = this.renderer.createElement('span');
    this.renderer.addClass(checkmark, 'checkmark');
    this.renderer.addClass(checkmark, 'select-all-checkmark');
    this.checkmarkElement = checkmark;

    const labelText = this.renderer.createElement('span');
    this.renderer.addClass(labelText, 'text-truncate-1');
    this.renderer.addClass(labelText, 'break-all');
    labelText.textContent = 'Select All';

    this.renderer.appendChild(checkboxContainer, checkbox);
    this.renderer.appendChild(checkboxContainer, checkmark);
    this.renderer.appendChild(checkboxContainer, labelText);

    [checkbox, checkmark, labelText].forEach((element) => {
      this.renderer.listen(element, 'click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        this.toggleSelectAll();
      });
    });

    return checkboxContainer;
  }

  createCheckboxIcon(): HTMLElement {
    const checkbox = this.renderer.createElement('span');
    this.renderer.addClass(checkbox, 'icon');
    this.renderer.addClass(checkbox, 'position-absolute');
    this.renderer.addClass(checkbox, 'ic-accent-green');
    this.renderer.addClass(checkbox, 'select-all-checkbox');
    this.renderer.setAttribute(checkbox, 'id', 'select-all-checkbox');
    this.renderer.setAttribute(checkbox, 'data-automate-id', 'select-all-checkbox');
    return checkbox;
  }

  updateSelectAllState() {
    if (!this.selectAllCheckbox || !this.checkboxContainer || !this.checkmarkElement) return;

    const selectedItems = this.ngSelect.selectedItems || [];
    const allItems = this.appSelectAllUseItemsList
      ? (this.ngSelect.itemsList?.items || []).filter((item: any) => item?.value?.isActive !== false)
      : this.ngSelect.items || [];

    const selectAllContainer = this.elementRef.nativeElement.querySelector('.select-all-container');
    if (selectAllContainer) {
      // Hide for empty or very large datasets
      if (allItems.length === 0 || allItems.length > this.MAX_ITEMS_THRESHOLD) {
        this.renderer.setStyle(selectAllContainer, 'display', 'none');
        return;
      } else {
        this.renderer.removeStyle(selectAllContainer, 'display');
      }
    }

    this.resetCheckboxStyles();

    if (selectedItems.length === 0) {
      // No items selected
      this.setUncheckedState();
    } else if (
      (this.appSelectAllUseItemsList &&
       allItems.every(s => selectedItems.some(a => this.equals(a.value, s.value || s)))) ||
      (!this.appSelectAllUseItemsList && selectedItems.length === allItems.length)
    ) {
      // All items are selected
      this.setCheckedState();
    } else {
      // Some items are selected
      this.setIndeterminateState();
    }
  }

  resetCheckboxStyles() {
    ['ic-square-check', 'ic-square-minus', 'unchecked', 'checked', 'indeterminate', 'empty'].forEach(cls =>
      this.renderer.removeClass(this.selectAllCheckbox, cls)
    );
    ['checked', 'indeterminate'].forEach(cls =>
      this.renderer.removeClass(this.checkboxContainer, cls)
    );
    this.renderer.removeClass(this.checkmarkElement, 'green-border');
  }

  setUncheckedState() {
    this.renderer.addClass(this.selectAllCheckbox, 'unchecked');
    this.selectAllCheckboxState = 'unchecked';
  }

  setCheckedState() {
    this.renderer.removeClass(this.selectAllCheckbox, 'unchecked');
    this.renderer.addClass(this.selectAllCheckbox, 'checked');
    this.renderer.addClass(this.selectAllCheckbox, 'ic-square-check');
    this.renderer.addClass(this.checkboxContainer, 'checked');
    this.renderer.addClass(this.checkmarkElement, 'green-border');
    this.selectAllCheckboxState = 'checked';
  }

  setIndeterminateState() {
    this.renderer.removeClass(this.selectAllCheckbox, 'unchecked');
    this.renderer.addClass(this.selectAllCheckbox, 'indeterminate');
    this.renderer.addClass(this.selectAllCheckbox, 'ic-square-minus');
    this.renderer.addClass(this.checkboxContainer, 'indeterminate');
    this.renderer.addClass(this.checkmarkElement, 'green-border');
    this.selectAllCheckboxState = 'indeterminate';
  }

  toggleSelectAll() {
    // Get filtered items
    const filteredItems = this.appSelectAllUseItemsList
      ? (this.ngSelect.itemsList?.filteredItems || []).filter((item: any) => item?.value?.isActive !== false)
      : (this.ngSelect.itemsList?.filteredItems || []);

    // Safety check for large datasets
    if (filteredItems.length > this.MAX_ITEMS_THRESHOLD) {
      console.warn(`Select-all operation cancelled: dataset too large (${filteredItems.length} items)`);
      return;
    }

    // Map values based on appSelectAllUseItemsList
    const filteredValues = this.appSelectAllUseItemsList
      ? filteredItems?.map((item: any) => item?.value ?? item)
      : filteredItems?.map((item: any) => {
          const val = item?.value ?? item;
          return this.ngSelect.bindValue && val && typeof val === 'object'
            ? val[this.ngSelect.bindValue]
            : val;
        });

    if (this.selectAllCheckboxState === 'checked') {
      // If already checked, clear selection
      this.ngSelect.clearModel();
      this.triggerChangeEvent([]);
      this.setUncheckedState();
    } else if (this.ngSelect.multiple) {
      // For multiple selection, add filtered items to existing selection
      const currentValues = this.appSelectAllUseItemsList
        ? this.ngSelect.selectedItems.map((item: any) => item.value ?? item)
        : this.ngSelect.selectedItems.map((item: any) => {
            const val = item.value ?? item;
            return this.ngSelect.bindValue && val && typeof val === 'object'
              ? val[this.ngSelect.bindValue]
              : val;
          });

      // Add filtered values that aren't already selected
      const mergedValues = [...currentValues];
      filteredValues.forEach(value => {
        if (!currentValues.some(selected => this.equals(selected, value))) {
          mergedValues.push(value);
        }
      });

      this.setModelValue(mergedValues);
      this.setCheckedState();
    } else {
      // For single selection, just select the first filtered item
      this.setModelValue(filteredValues[0]);
      this.setCheckedState();
    }

    this.cdRef.detectChanges();
    setTimeout(() => this.updateSelectAllState(), 50);
  }

  setModelValue(value: any) {
    if ((this.ngSelect as any)._value !== undefined) {
      (this.ngSelect as any)._value = value;
    }
    this.ngSelect.writeValue(value);
    this.triggerChangeEvent(value);
    setTimeout(() => this.ngSelect.detectChanges(), 0);
  }

  triggerChangeEvent(value: any) {
    if (typeof (this.ngSelect as any)._onChange === 'function') {
      (this.ngSelect as any)._onChange(value);
    }
    this.ngSelect.changeEvent.emit(value);
    if ((this.ngSelect as any).ngModel !== undefined) {
      (this.ngSelect as any).ngModel = value;
    }
  }

  getItemValues(items: any[]): any[] {
    if (!items.length) return [];
    if (typeof items[0] !== 'object') return items;
    return this.ngSelect.bindValue ? items.map(i => i[this.ngSelect.bindValue]) : items;
  }

  equals(a: any, b: any): boolean {
    try {
      if (a == null && b == null) return true;
      if (a == null || b == null) return false;

      // Compare by bindValue if available
      if (this.ngSelect.bindValue && typeof a === 'object' && typeof b === 'object') {
        if (a[this.ngSelect.bindValue] !== undefined && b[this.ngSelect.bindValue] !== undefined) {
          return a[this.ngSelect.bindValue] === b[this.ngSelect.bindValue];
        }
      }

      return JSON.stringify(a) === JSON.stringify(b);
    } catch {
      return a === b;
    }
  }


  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
