<ng-container *ngIf="canView || canViewAssigned">
  <div class="px-24 py-8 bg-white tb-left-200 mq-top-navbar" [ngClass]="showLeftNav ? 'left-320' : 'left-220'">
    <div class="flex-between flex-grow-1">
      <ul class="align-center top-nav-bar text-nowrap ip-scrollbar ip-w-100-180 scroll-hide">
        <li (click)="isViewAllProperties = true; initializeGridSettings();filterPropertyList()" class="cursor-pointer"
          [ngClass]="{'gray-scale' : !isViewAllProperties}">
          <div class="align-center">
            <div class="bg-linear-green dot dot-lg"><span class="icon ic-corporation ic-xxs"></span></div>
            <span class="text-large mx-8" [ngClass]="!isViewAllProperties ? 'fw-semi-bold' : 'fw-700'">
              {{'GLOBAL.all' | translate}}</span>
          </div>
        </li>
        <li (click)="isViewAllProperties = false;fetchArchivedProperty();initializeGridSettings()"
          class="cursor-pointer" [ngClass]="{'gray-scale' : isViewAllProperties}">
          <div class="align-center">
            <div class="bg-linear-orange dot dot-lg"><span class="icon ic-delete ic-xxs"></span></div>
            <span class="text-large mx-8" [ngClass]="isViewAllProperties ? 'fw-semi-bold' : 'fw-700'">
              {{'DASHBOARD.deleted' | translate}}</span>
          </div>
        </li>
      </ul>
      <div class="align-center">
        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap">
          <app-clear-indexeddb></app-clear-indexeddb>
        </div>
        <div class="btn-full-dropdown btn-w-100" *ngIf="canBulkUpload || canExport">
          <div class="position-absolute top-9 left-9 ip-top-11 align-center z-index-2">
            <span class="ic-tracker icon ic-xxs"></span>
            <span class="ml-8 ip-d-none">Tracker</span>
          </div>
          <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" [(ngModel)]="selectedTrackerOption"
            (change)="onTrackerChange($event)">
            <ng-option (click)="selectedTrackerOption = null" value="bulkUpload" *ngIf="canBulkUpload">
              <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
              {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
            <ng-option (click)="selectedTrackerOption = null" value="export" *ngIf="canExport">
              <span class="ic-download icon ic-xxs ic-dark mr-8"></span>Export</ng-option>
          </ng-select>
        </div>
        <ng-container *ngIf="canBulkUpload || canAdd">
          <div class="btn-left-dropdown ml-10" (click)="canAdd ? navigateToAddProperty() : ''">
            <span class="ic-add icon ic-xxs"></span>
            <span class="ml-8 ip-d-none">{{ 'BUTTONS.add-property' | translate }}</span>
          </div>
          <div class="btn-right-dropdown btn-w-30 black-100">
            <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" [(ngModel)]="selectedOption"
              (click)="openPropertyBulkUpload()">
              <ng-option (click)="selectedOption = null" value="bulkUpload" *ngIf="canBulkUpload">
                <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
            </ng-select>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
  <ng-container *ngIf="isViewAllProperties else deletedProperties">
    <div class="d-flex bg-white py-8 px-24 border-top border-bottom">
      <div class="d-flex text-nowrap ph-w-100-40 scrollbar scroll-hide">
        <button (click)="currentVisibility(null)" class="text-sm mr-10 px-12 py-8 border-0 br-4"
          [ngClass]="appliedFilter.propertyType == null ? 'text-dark-250 fw-700 bg-orange-150-active' :'bg-orange-150'">
          {{ 'GLOBAL.all' | translate }}
          <span *ngIf="propertyCount?.allPropertiesCount">({{propertyCount.allPropertiesCount}})</span></button>
        <ul *ngFor="let propertyType of propertyTypeList" class="cursor-pointer ip-mb-4">
          <button (click)="currentVisibility(propertyType.id)" class="text-sm mr-10 px-12 py-8 border-0 br-4"
            id="clk{{propertyType}}" data-automate-id="clk{{propertyType}}"
            [ngClass]="appliedFilter.propertyType == propertyType.id ? 'text-dark-250 fw-700' + ' btn-'+propertyType.type+'-active': 'btn-'+propertyType.type">
            {{propertyType.displayName}} ({{propertyCount ? propertyCount[((propertyType.displayName |
            lowercase))+'PropertiesCount'] : 0}})</button>
        </ul>
      </div>
    </div>
    <div class="pt-12 px-24">
      <div class="align-center bg-white w-100 border-gray tb-align-center-unset tb-flex-col">
        <div class="align-center border-end flex-grow-1 no-validation tb-br-0">
          <ng-container *ngIf="canSearch">
            <div class="align-center w-100 px-10 py-12">
              <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
              <input (keydown)="onSearch($event)" (input)="isEmptyInput($event)" [(ngModel)]="searchTerm"
                autocomplete="off" placeholder="type to search" name="search" class="border-0 outline-0 w-100">
            </div>
            <small class="text-muted text-nowrap ph-d-none pr-8">({{ 'LEADS.lead-search-prompt' | translate }})</small>
          </ng-container>
          <div *ngIf="canExport  && globalSettingsData?.isPropertiesExportEnabled"
            class="bg-accent-green text-white ml-10 px-20 py-12 h-100 align-center cursor-pointer border-start w-70px tb-br-top"
            (click)="exportPropertyReport()">{{ 'REPORTS.export' | translate }}</div>
        </div>
        <div class="tb-br-top align-center ip-flex-col ip-align-center-unset">
          <div class="d-flex w-100">
            <div class="px-10 align-center cursor-pointer border-end tb-flex-grow-1 ph-w-40px ph-flex-grow-unset"
              (click)="setFilterPayload(); openAdvFiltersModal(AdvancedFilters)">
              <div class="icon ic-filter-solid ic-xxs ic-black mr-10">
              </div>
              <span class="fw-600 ph-d-none text-nowrap">{{'PROPERTY.advanced-filters' | translate}}</span>
            </div>
            <div class="flex-center">
              <div class="filters-grid clear-padding border-end h-100 ip-br-0">
                <div class="align-center h-100 ml-16 tb-ml-0">
                  <div class="bg-white manage-select">
                    <ng-select [virtualScroll]="true" placeholder="{{'GLOBAL.all'| translate}}"
                      class="min-w-60 ip-max-w-80 lead-date" [(ngModel)]="appliedFilter.dateType"
                      (change)="filterPropertyList()" ResizableDropdown>
                      <ng-option name="dateType" *ngFor="let dType of dateTypeList" [value]="dType">{{dType}}
                      </ng-option>
                    </ng-select>
                  </div>
                  <div class="date-picker border-start-0 align-center" id="propertyAppointmentDate"
                    data-automate-id="propertyAppointmentDate">
                    <span class="ic-appointment icon ic-xxs ic-black" [owlDateTimeTrigger]="dt1"></span>
                    <input
                      [max]="appliedFilter.dateType === 'Modified Date' || appliedFilter.dateType === 'Created Date' ? maxDate : ''"
                      type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [selectMode]="'range'"
                      class="pl-20 text-large" [disabled]="!appliedFilter.dateType"
                      (ngModelChange)="appliedFilter.date = $event; filterPropertyList()" [ngModel]="appliedFilter.date"
                      placeholder="ex. 19-06-2025 - 29-06-2025" />
                    <owl-date-time [pickerType]="'calendar'" #dt1
                      (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                  </div>
                  <div *ngIf="appliedFilter?.date?.[0]"
                    class="bg-coal text-white px-10 py-12 w-50px ip-w-30px h-100 cursor-pointer align-center"
                    (click)="onResetDateFilter()">
                    <span class="ip-d-none">{{ 'GLOBAL.reset' | translate }}</span> <span
                      class="ic-convert d-none ip-d-block"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="d-flex ip-br-top">
            <div class="align-center position-relative cursor-pointer d-flex border-end">
              <span class="position-absolute left-15 z-index-2 fw-600 text-sm">
                {{ 'BUTTONS.manage-columns' | translate }}</span>
              <div class="show-hide-gray w-140">
                <ng-select [virtualScroll]="true" class="bg-white" [items]="columns" [multiple]="true" appSelectAll
                  [searchable]="false" [closeOnSelect]="false" [ngModel]="defaultColumns"
                  (change)="onColumnsSelected($event)" ResizableDropdown>
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"> <input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"> <span
                        class="checkmark"></span>{{item.label}} </div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div class="bg-coal text-white px-10 py-12 ip-w-30px h-100 align-center cursor-pointer"
              (click)="onSetColumnDefault()">
              <span class="ip-d-none">{{ 'GLOBAL.default' | translate }}</span> <span
                class="ic-refresh d-none ip-d-block"></span>
            </div>
            <div class="show-dropdown-white align-center position-relative ip-br-0">
              <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
                  {{ 'GLOBAL.show' | translate}}</span> {{ 'GLOBAL.entries' | translate }}</span>
              <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" class="w-150 tb-w-120px"
                (change)="assignCount()" [(ngModel)]="selectedPageSize" [searchable]="false" ResizableDropdown>
                <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                  {{pageSize}}</ng-option>
              </ng-select>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-white px-4 py-12 tb-w-100-34" [ngClass]="showLeftNav ? 'w-100-190' : 'w-100-90'">
        <ng-container *ngIf="showFilters">
          <div class="bg-secondary flex-between">
            <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
              <div class="d-flex" *ngFor="let filter of appliedFilter | keyvalue">
                <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                  *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                  {{propertyFiltersKeyLabel[filter.key] || filter.key}}: {{
                  filter.key === 'BHKs'? getBHKDisplayString(value) :
                  filter.key === 'assignedTo' ? getAssignedToName(value):
                  filter.key === 'propertyType' ? getPropertyTypeName(value):
                  filter.key === 'propertySubType' ? getPropertySubTypeName(value) :
                  filter.key === 'MinPropertyArea' ? getPropertyArea(value) :
                  filter.key === 'MaxPropertyArea' ? getPropertyArea(value) :
                  filter.key === 'MinCarpetArea' ? getCarpetArea(value) :
                  filter.key === 'MaxCarpetArea' ? getCarpetArea(value) :
                  filter.key === 'MaxBuildUpArea' ? getBuildUpArea(value) :
                  filter.key === 'MinBuildUpArea' ? getBuildUpArea(value) :
                  filter.key === 'MaxSaleableArea' ? getSaleableArea(value) :
                  filter.key === 'MinSaleableArea' ? getSaleableArea(value) :
                  filter.key === 'PossesionType' ? getPossessionTypeDisplayName(value) :
                  filter.key === 'Possession' ? 'Custom Date' :
                  filter.key === 'FromPossesionDate' ? getPossessionDate(value) :
                  filter.key === 'ToPossesionDate' ? getPossessionDate(value) :
                  filter.key === 'FromPossessionDate' ? getPossessionDate(value) :
                  filter.key === 'ToPossessionDate' ? getPossessionDate(value) :
                  filter.key === 'amenities' ? getAmenity(value) :
                  filter.key === 'PropertyStatus' ? (value === '0'? 'Active': 'Sold') :
                  value }}
                  <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                    (click)="onRemoveFilter(filter.key, value)"></span>
                </div>
              </div>
            </drag-scroll>
            <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
              (click)="reset(true);filterPropertyList();">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}
            </div>
          </div>
        </ng-container>
      </div>
      <ng-container *ngIf="!rowData?.length && !isPropertyListLoading; else propertyData">
        <div class="flex-center h-100-210">
          <div class="flex-column">
            <ng-lottie [options]='noDataFound'></ng-lottie>
            <h3 class="header-5 fw-600 text-center">{{'PROFILE.no-property-found' | translate }}</h3>
          </div>
        </div>
      </ng-container>
      <ng-template #propertyData>
        <div *ngIf="isPropertyListLoading || isIndexedDBLoading" class="pt-40">
          <ng-container *ngTemplateOutlet="gridLoader"></ng-container>
        </div>
        <div class="manage-property" *ngIf="!isPropertyListLoading && !isIndexedDBLoading">
          <ag-grid-angular class="ag-theme-alpine" [gridOptions]="gridOptions" (gridReady)="onGridReady($event)"
            [rowData]="rowData" [paginationPageSize]="pageSize" [pagination]="true" [suppressPaginationPanel]="true"
            [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true" (cellClicked)="onCellClicked($event)"
            (filterChanged)="onFilterChanged($event)">
            <!-- (cellClicked)="onCellClicked($event)" -->
          </ag-grid-angular>
        </div>
        <div class="my-20 flex-end" *ngIf="!isPropertyListLoading && !isIndexedDBLoading">
          <div class="mr-10" *ngIf="propertiesTotalCount">{{ 'GLOBAL.showing' | translate }}
            {{propertiesTotalCount ? currOffset*pageSize + 1 : 0}}
            {{ 'GLOBAL.to-small' | translate }} {{currOffset*pageSize + gridApi?.getDisplayedRowCount()}}
            {{ 'GLOBAL.of-small' | translate }} {{propertiesTotalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
          <pagination *ngIf="propertiesTotalCount" [offset]=currOffset [limit]="1" [range]="1"
            [size]='getPages(propertiesTotalCount,pageSize)' (pageChange)="onPageChange($event)">
          </pagination>
        </div>
      </ng-template>
    </div>
    <property-bulk-update [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}" [gridApi]="gridApi"
      [allUserList]="allUserList || []" [isViewAllProperties]="isViewAllProperties" [pageSize]="pageSize"
      [filtersPayload]="filtersPayload"></property-bulk-update>
  </ng-container>
  <ng-template #deletedProperties>
    <div class="pt-12 px-24">
      <div class="align-center bg-white w-100 border-gray ip-align-center-unset ip-flex-col">
        <div class="align-center px-10 border-end flex-grow-1 no-validation ip-br-0"
          [ngClass]="canSearch ? 'py-12' : 'py-20'">
          <ng-container *ngIf="canSearch">
            <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
            <input (keydown.enter)="searchTermSubject.next($event.target.value)" [(ngModel)]="searchTerm"
              placeholder="type to search" name="search" class="border-0 outline-0 w-100">
            <small class="text-muted text-nowrap">({{ 'LEADS.lead-search-prompt' | translate }})</small>
          </ng-container>
        </div>
        <div class="show-dropdown-white align-center position-relative ip-br-top ip-br-0">
          <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
              {{ 'GLOBAL.show' | translate }}</span> {{ 'GLOBAL.entries' | translate }}</span>
          <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" class="w-150 tb-w-120px"
            (change)="assignCount()" [(ngModel)]="selectedPageSize" [searchable]="false" ResizableDropdown>
            <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
              {{pageSize}}</ng-option>
          </ng-select>
        </div>
      </div>
      <ng-container *ngIf="!archivedProperties?.length && !isArchivedPropertiesLoading; else archivedPropertyData">
        <div class="flex-center-col h-100-210">
          <ng-lottie [options]='noDataFound'></ng-lottie>
          <div class="header-3 fw-600 text-center">{{'PROFILE.no-property-found' | translate }}</div>
        </div>
      </ng-container>
      <ng-template #archivedPropertyData>
        <div *ngIf="isArchivedPropertiesLoading" class="pt-40">
          <ng-container *ngTemplateOutlet="gridLoader"></ng-container>
        </div>
        <div *ngIf="!isArchivedPropertiesLoading" class="locality">
          <ag-grid-angular class="ag-theme-alpine" [gridOptions]="gridOptions" (gridReady)="onGridReady($event)"
            [rowData]="archivedProperties" [paginationPageSize]="pageSize" [pagination]="true"
            [suppressPaginationPanel]="true" (filterChanged)="onFilterChanged($event)">
          </ag-grid-angular>
        </div>
        <div *ngIf="!isArchivedPropertiesLoading" class="my-20 flex-end">
          <div class="mr-10" *ngIf="archivedTotalCount">{{ 'GLOBAL.showing' | translate }}
            {{archivedTotalCount ? currArchiveOffset*pageSize + 1 : 0}}
            {{ 'GLOBAL.to-small' | translate }} {{currArchiveOffset*pageSize + gridApi?.getDisplayedRowCount()}}
            {{ 'GLOBAL.of-small' | translate }} {{archivedTotalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
          <pagination [offset]=currArchiveOffset [limit]="1" [range]="1" [size]='getPages(archivedTotalCount,pageSize)'
            (pageChange)="onPageChangeArchived($event)">
          </pagination>
        </div>
      </ng-template>
      <property-bulk-update [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}" [gridApi]="gridApi"
        [allUserList]="allUserList || []" [listing]="false" [isViewAllProperties]="isViewAllProperties"
        [pageSize]="pageSize" [filtersPayload]="filtersPayload"></property-bulk-update>
    </div>
  </ng-template>
  <ng-template #gridLoader>
    <div class="flex-center h-280">
      <application-loader></application-loader>
    </div>
  </ng-template>