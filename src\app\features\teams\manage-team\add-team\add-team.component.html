<div class="h-100vh d-flex flex-column">
    <div class="flex-between fw-400 bg-coal px-24 py-12 text-white">
        <h3>{{ (selectedTeam ? 'TEAM.edit-team' : 'TEAM.add-team' ) | translate }}</h3>
        <div class="icon ic-close  ic-sm cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 py-10 flex-between-col flex-grow-1 bg-light-pearl">
        <div class="w-100">
            <form [formGroup]="addTeamForm" (ngSubmit)="onSave()">
                <div>
                    <div class="field-label-req">{{ 'TEAM.team-name' | translate }}</div>
                    <form-errors-wrapper [control]="addTeamForm.controls['name']" label="name">
                        <input type="text" required id="inpTeamName" data-automate-id="inpTeamName"
                            formControlName="name" placeholder="ex. Leads Recovery Team">
                    </form-errors-wrapper>
                </div>
                <div>
                    <div class="field-label-req">Team Leader</div>
                    <form-errors-wrapper [control]="addTeamForm.controls['leader']" label="Team Leader">
                        <ng-select [virtualScroll]="true" class="bg-white" bindValue="id" bindLabel="userName"
                            placeholder="Select a leader" formControlName="leader"
                            [ngClass]="{'pe-none bg-secondary hide-arrow': !filteredLeaderList?.length}"
                            ResizableDropdown>
                            <ng-option *ngFor="let user of filteredLeaderList" [value]="user.id" class="flex-center">
                                {{user.firstName}}
                                {{user.lastName}}
                            </ng-option>
                            <ng-option *ngFor="let user of inactiveUsers" [value]="user.id" [disabled]="true">
                                <div class="flex-between">
                                    <div class="text-truncate-1 break-all"> {{
                                        user.firstName }} {{ user.lastName }}
                                    </div>
                                    <span class="d-none">{{user.fullName}}</span><span class="text-disabled"
                                        *ngIf="!user.usersList">
                                        (Disabled)</span>
                                </div>
                            </ng-option>
                        </ng-select>
                    </form-errors-wrapper>
                </div>
                <div>
                    <div class="field-label-req">{{ 'TEAM.select-team-members' | translate }}</div>
                    <form-errors-wrapper [control]="addTeamForm.controls['members']" label="Team Members">
                        <ng-select [virtualScroll]="true" [items]="filteredMembersList" [multiple]="true" appSelectAll [appSelectAllUseItemsList] = "true"
                            [closeOnSelect]="false"
                            [ngClass]="{'pe-none bg-secondary hide-arrow': !filteredMembersList?.length}"
                            ResizableDropdown bindLabel="fullName" bindValue="id" name="members"
                            formControlName="members" class="bg-white" (change)="onUserSelect($event)"
                            placeholder="{{ 'GLOBAL.select' | translate }}">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                        [checked]="item$.selected" [disabled]="!item.isActive">
                                    <span class="checkmark"></span>
                                    <div class="flex-between">
                                        <span class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}}</span>
                                        <span class="error-text" *ngIf="!item.isActive">( Disabled )</span>
                                    </div>
                                </div>
                            </ng-template>
                        </ng-select>
                    </form-errors-wrapper>
                </div>
            </form>
        </div>
        <div class="text-red ml-20 mb-10" *ngIf="!usersList?.length">No users left for assignment.</div>
        <div class="text-red ml-20 mb-10" *ngIf="usersList?.length && !filteredLeaderList?.length">No users available
            for leader position.</div>
        <div class="text-red ml-20 mb-10" *ngIf="usersList?.length && !filteredMembersList?.length">No users available
            for member positions.</div>
    </div>
    <div class="flex-end modal-footer bg-white box-shadow-3">
        <h6 class="text-black-10 fw-semi-bold text-decoration-underline cursor-pointer" (click)="modalService.hide()">{{
            'BUTTONS.cancel' | translate }}</h6>
        <button (click)="onSave()" class="btn-coal ml-20">{{ 'TEAM.save-team' | translate }}</button>
    </div>
</div>