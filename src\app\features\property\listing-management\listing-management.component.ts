import { AfterViewInit, Component, ElementRef, EventEmitter, HostListener, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { CellClickedEvent, GridApi, GridOptions } from 'ag-grid-community';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, firstValueFrom, takeUntil } from 'rxjs';

import { BHK_TYPE, COMPLETIONSTATUS, FINISHINGTYPE, LISTING_FIRSTLEVELFILTER, PROP_DATE_TYPE, PROPERTY_FILTERS_KEY_LABEL, SHOW_ENTRIES, UAE_EMIRATES } from 'src/app/app.constants';
import { CompletionStatus, DataDateType, EnquiryType, Facing, FinishingType, FurnishStatus, ListingLevel, ListingStatus, ListingVisibility, OfferingType, PossessionType, UaeEmirate } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, changeCalendar, formatBudget, generateEnumList, getAssignedToDetails, getBRDisplayString, getLocationDetailsByObj, getPages, getSystemTimeOffset, getSystemTimeZoneId, getTimeZoneDate, hexToRgba, onFilterChanged, onPickerOpened, patchTimeZoneDate, setTimeZoneDate } from 'src/app/core/utils/common.util';
import { getAllAmenities } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { ClearListingSearch, FetchAllListing, FetchListingSource, FetchSyncListingList, FetchSyncListingSource, syncListing, UpdateListingPayload } from 'src/app/reducers/listing-site/listing-site.actions';
import { getAllListing, getListingBaseCount, getListingFilters, getListingSiteLoaders, getListingTopCount, getListingTotalCount, getSyncListingSource } from 'src/app/reducers/listing-site/listing-site.reducer';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchExportPropertyStatus, FetchPropertyExcelUploadedList } from 'src/app/reducers/property/property.actions';
import { getPropertyLeadsCount } from 'src/app/reducers/property/property.reducer';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { ExportPropertyTrackerComponent } from 'src/app/shared/components/export-property-tracker/export-property-tracker.component';
import { environment as env } from 'src/environments/environment';
import { ExcelUploadedStatusComponent } from '../../leads/excel-uploaded-status/excel-uploaded-status.component';
import { PropertiesActionGridComponent } from '../../property/manage-properties/action-grid-child/action-grid-child.component';
import { MatchingLeadsComponent } from '../../property/matching-leads/matching-leads.component';
import { PropertyStatusComponent } from '../../property/property-status/property-status.component';
import { ListingAdvanceFilterComponent } from './listing-advance-filter/listing-advance-filter.component';
import { ListingSyncTrackerComponent } from './listing-sync-tracker/listing-sync-tracker.component';

@Component({
  selector: 'listing-management',
  templateUrl: './listing-management.component.html',
})
export class ListingManagementComponent implements OnInit, OnDestroy, AfterViewInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  s3BucketUrl: string = env.s3ImageBucketURL;
  gridOptions: GridOptions;
  gridColumnApi: any;
  columns: any[];
  gridApi: GridApi;
  rowData: any[] = [];
  showLeftNav: boolean = true;
  dateType: string;
  dateTypeList: Array<string> = PROP_DATE_TYPE?.filter(item => item !== 'Possession Date');
  filterDate: any;
  onPickerOpened = onPickerOpened;
  onFilterChanged = onFilterChanged;
  getPages = getPages;
  getBRDisplayString = getBRDisplayString;
  defaultColumns: any[];
  propertyFiltersKeyLabel = PROPERTY_FILTERS_KEY_LABEL;
  filters = LISTING_FIRSTLEVELFILTER;
  topFilters: { displayName: string; enumValue: number }[];
  userData: any;
  currentDate: Date;
  filtersForm: FormGroup;
  formFields: any = {
    PropertyVisiblity: [null],
    FirstLevelFilter: [null],
    BasePropertyTypeId: [null],
    PageNumber: [null],
    PageSize: [null],
    PropertySearch: [''],
    DateType: [null],
    FromDate: [null],
    ToDate: [null]
  };
  topCount: any;
  baseCount: any;
  loaders: any;
  allUserList: any;
  defaultCurrency: string = 'INR';
  areaSizeUnits: Array<any> = [];
  listingTotalCount: number = 0;
  filtersPayload: any;
  propertyTypeList: any;
  amenities: any;
  permissions: any;
  globalSettings: any;
  hexToRgba: Function = hexToRgba;
  listingSource: any[];
  syncDateForm: FormGroup = new FormGroup({});
  isDropdownOpen: boolean;
  selectedItem: any;
  selectedOption: string;
  selectedTrackerOption: string;
  completionStatusList: any = COMPLETIONSTATUS
  finishingTypeList: any = FINISHINGTYPE
  uaeEmirateList: any = UAE_EMIRATES
  userList: any;
  currentPath: string;
  canBulkReassign: boolean = false;
  canBulkDelete: boolean = false;
  canPermanentDelete: boolean = false;
  canBulkShare: boolean = false;
  canBulkRestore: boolean = false;
  canBulkList: boolean = false;
  canBulkDeList: boolean = false;
  canBulkModifyListing: boolean = false;
  // canViewOwner: boolean = false;

  get showFilters(): boolean {
    const filterKeys = [
      'UserIds', 'ListingOnBehalf', 'EnquiredFor', 'Projects', 'MinPrice', 'MaxPrice', 'NoOfBHK', 'BHKTypes',
      'Cities', 'States', 'Locations', 'NoOfBathrooms', 'NoOfLivingrooms', 'NoOfBedrooms',
      'NoOfUtilites', 'NoOfKitchens', 'NoOfBalconies', 'NoOfFloor', 'FloorNumber', 'OwnerNames',
      'Currency', 'PropertyTitle', 'FurnishStatuses', 'Amenities', 'PropertySubTypes', 'MinPropertySize', 'FinishingType', 'UaeEmirates',
      'MaxPropertySize', 'MaxCarpetArea', 'MinCarpetArea', 'MinBuiltUpArea', 'MaxBuiltUpArea',
      'MinSaleableArea', 'MaxSaleableArea', 'MinNetArea', 'MaxNetArea', 'Facing', 'FromPossesionDate', 'ToPossesionDate', 'CompletionStatus',
      'ListingLevel', 'Communities', 'SubCommunities', 'SerialNo', 'MinLeadCount', 'MaxLeadCount', 'MinProspectCount', 'MaxProspectCount', 'PossesionType'
    ];
    return filterKeys.some(key => {
      const value = this.filtersPayload?.[key];
      return Array.isArray(value) ? value.length > 0 : typeof value === 'string' ? value.trim().length > 0 : Number(value) > 0;
    });
  }

  constructor(
    public metaTitle: Title,
    private headerTitle: HeaderTitleService,
    private shareDataService: ShareDataService,
    private modalService: BsModalService,
    private _store: Store<AppState>,
    private gridOptionsService: GridOptionsService,
    public modalRef: BsModalRef,
    private fb: FormBuilder,
    private router: Router,
    private eRef: ElementRef

  ) { }

  ngOnInit(): void {
    this.router.events
      .pipe(filter((event: any) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canBulkList = permissionsSet.has('Permissions.Properties.BulkList');
        this.canBulkDeList = permissionsSet.has('Permissions.Properties.BulkDeList');
        this.canBulkModifyListing = permissionsSet.has('Permissions.Properties.BulkModifyListing');
        this.canBulkReassign = permissionsSet.has('Permissions.Properties.BulkReassign');
        this.canBulkShare = permissionsSet.has('Permissions.Properties.BulkShare');
        this.canBulkDelete = permissionsSet.has('Permissions.Properties.BulkDelete');
        this.canPermanentDelete = permissionsSet.has('Permissions.Properties.BulkPermanentDelete');
        this.canBulkRestore = permissionsSet.has('Permissions.Properties.BulkRestore');
        // this.canViewOwner = permissionsSet.has('Permissions.Properties.ViewOwnerInfo');
        this.initializeTitleAndFilters();
        this.initializeForms();
        this.initializeDispatch()
        this.initializeSubscriptions();
        this.initializeGridOptions();
      })
  }

  ngAfterViewInit(): void {
    const propertyTypeList = JSON.parse(localStorage.getItem('propertyType') || '[]');
    this.propertyTypeList = propertyTypeList.filter(
      (property: any) => property.displayName !== 'Agricultural'
    );
  }

  initializeDispatch() {
    this._store.dispatch(new FetchListingSource());
    this._store.dispatch(new FetchSyncListingSource());
    this._store.dispatch(new FetchAreaUnitList());
  }

  initializeTitleAndFilters(): void {
    this.metaTitle.setTitle('CRM | Listing-Management');
    this.headerTitle.setLangTitle('Listing Management');
    this.shareDataService.showLeftNav$.subscribe(show => this.showLeftNav = show);
    this.topFilters = generateEnumList(ListingVisibility);
  }

  initializeGridOptions(): void {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowData = this.rowData;
  }

  initializeForms() {
    this.filtersForm = this.fb.group(this.formFields);
  }

  async initializeSubscriptions() {
    const userDetails: any = await firstValueFrom(this._store.select(getUserBasicDetails));
    this.userData = userDetails;
    this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset);

    const subscriptions = [
      {
        selector: getListingFilters,
        handler: (payload: any) => {
          this.filtersPayload = payload;
          this.filtersForm.patchValue({ ...payload }, { emitEvent: false });
          this.dateType = DataDateType[payload?.DateType];
          this.filterDate = [
            patchTimeZoneDate(payload?.FromDate, this.userData?.timeZoneInfo?.baseUTcOffset),
            patchTimeZoneDate(payload?.ToDate, this.userData?.timeZoneInfo?.baseUTcOffset),
          ];
        },
      },
      {
        selector: getPermissions,
        handler: (permissions: any) => {
          if (permissions?.length) {
            this.permissions = new Set(permissions);
            this._store.dispatch(new FetchAllListing());
          }

        }
      },
      { selector: getListingTopCount, handler: (counts: any) => (this.topCount = counts) },
      { selector: getListingBaseCount, handler: (counts: any) => (this.baseCount = counts) },
      { selector: getListingSiteLoaders, handler: (loaders: any) => (this.loaders = loaders) },
      {
        selector: getAllListing, handler: (data: any) => {
          if (this.getFormValue('PageNumber') > 1 && !data.length) {
            this.filtersForm.patchValue({
              PageNumber: this.getFormValue('PageNumber') - 1
            });
            return
          }
          this.rowData = data;
        }
      },
      {
        selector: getUsersListForReassignment,
        handler: (data: any) => {
          this.userList = data?.map((user: any) => ({
            ...user,
            fullName: `${user.firstName} ${user.lastName}`,
          }));
          this.allUserList = data?.map((user: any) => ({
            ...user,
            fullName: `${user.firstName} ${user.lastName}`,
          }));
          this.allUserList = assignToSort(this.allUserList, '');
          this.gridApi?.refreshCells();
        },
      },
      {
        selector: getGlobalSettingsAnonymous,
        handler: (data: any) => {
          this.globalSettings = data;
          this.defaultCurrency = data.countries?.[0]?.defaultCurrency || null;
        },
      },
      {
        selector: getAllAmenities,
        handler: (data: any) => {
          const allAmenities = data?.flatMap((category: any) => category.amenities);
          this.amenities = allAmenities
          this.amenities?.sort((a: any, b: any) =>
            a.amenityDisplayName.localeCompare(b.amenityDisplayName)
          );
        },
      },
      {
        selector: (state: any) => ({
          areaUnits: getAreaUnits(state),
        }),
        handler: ({ areaUnits }: any) => {
          this.areaSizeUnits = areaUnits;
        },
      },
      {
        selector: getListingTotalCount,
        handler: (count: number) => (this.listingTotalCount = count),
      },
      {
        selector: getPropertyLeadsCount,
        handler: (data: any) => {
          this.rowData = this.rowData?.map?.((row: any) => {
            const matchingData = data.find(
              (item: any) => item.propertyId === row.id
            );
            const leadsCount = matchingData ? matchingData.leadCount : 0;
            const prospectCount = matchingData ? matchingData.prospectCount : 0;
            const meetingDoneCount = matchingData ? matchingData.meetingDoneCount : 0;
            const meetingDoneUniqueCount = matchingData ? matchingData.meetingDoneUniqueCount : 0;
            const siteVisitDoneCount = matchingData ? matchingData.siteVisitDoneCount : 0;
            const siteVisitDoneUniqueCount = matchingData ? matchingData.siteVisitDoneUniqueCount : 0;

            return { ...row, prospectCount, leadsCount, meetingDoneCount, meetingDoneUniqueCount, siteVisitDoneCount, siteVisitDoneUniqueCount };
          });
        }
      },
      {
        selector: getSyncListingSource,
        handler: (data: any) => {
          if (data?.length) {
            this.listingSource = [...data];
            this.listingSource.forEach((source: any) => {
              this.syncDateForm.addControl(
                source.displayName,
                new FormControl(null)
              );
            });
          }
        }
      }
    ];

    subscriptions.forEach(({ selector, handler }) => {
      this._store.select(selector).pipe(takeUntil(this.stopper)).subscribe(handler);
    });
    // This eliminates the need for a separate filter function to dispatch API
    this.filtersForm.valueChanges
      .pipe(takeUntil(this.stopper))
      .subscribe(() => {
        const filters = this.filtersForm.value;
        //If filter payload is updated automatically all related api's will be called
        this._store.dispatch(new UpdateListingPayload({ ...this.filtersPayload, ...filters }));
        this.initializeGridSettings()
      });
    this.initializeGridSettings()

  }


  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 50;
    this.gridOptions.rowStyle = null;
    this.gridOptions.getRowClass = (params: any) => {
      if (params.data) {
        if (params.data.status === 1 ||
          params.data.status === 'Sold') {
          return 'bg-red-410';
        }
      }
      return '';
    };
    this.gridOptions.columnDefs = [
      {
        headerName: 'Status',
        field: 'Status',
        filter: false,
        lockPinned: true,
        valueGetter: (params: any) => [params.data?.status],
        maxWidth: 75,
        pinned: window.innerWidth > 768 ? 'left' : null,
        cellClass: "lock-pinned",
        cellRenderer: PropertyStatusComponent,
      },
      {
        headerName: 'Property Title',
        field: 'Property Title',
        cellClass: 'cursor-pointer lock-pinned align-center h-100 ',
        pinned: window.innerWidth > 768 ? 'left' : null,
        lockPinned: true,
        valueGetter: (params: any) => [params.data?.title],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Lisiting By',
        hide: true,
        minWidth: 195,
        field: 'Lisiting By',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data.assignedTo ? params.data.assignedTo?.map((id: any) => getAssignedToDetails(id, this.userList, true) || '--') : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2 break-all">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Listing On Behalf',
        hide: true,
        minWidth: 195,
        field: 'Listing On Behalf',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data.listingOnBehalf ? params.data.listingOnBehalf?.map((id: any) => getAssignedToDetails(id, this.userList, true) || '--') : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2 break-all">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Leads Count',
        field: 'Leads Count',
        hide: true,
        valueGetter: (params: any) => [
          params?.data?.leadsCount],
        cellRenderer: (params: any) => {
          return `<p>${params?.value?.[0] || 0}</p>`;
        },
        onCellClicked: ({ data, event }: any) => {
          if (!data?.leadsCount) return;
          const navigateToLeads = (propertyName: string, isCtrlClick: boolean) => {
            const query = `isNavigatedFromProperties=true&Properties=${encodeURIComponent(JSON.stringify([propertyName]))}`;
            const url = `leads/manage-leads?${query}`;
            if (isCtrlClick) {
              window.open(url, '_blank');
            } else {
              this.router.navigate(['leads', 'manage-leads'], {
                queryParams: {
                  Properties: JSON.stringify([propertyName]),
                  isNavigatedFromProperties: true,
                },
              });
            }
          };
          navigateToLeads(data.title, event?.ctrlKey);
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Data Count',
        field: 'Data Count',
        hide: true,
        valueGetter: (params: any) => [
          params?.data?.prospectCount,
          params?.data?.title,
        ],
        cellRenderer: (params: any) => {
          return `<p>${params?.value?.[0] || 0}</p>`;
        },
        onCellClicked: ({ data, event }: any) => {
          event.preventDefault()
          if (!data?.prospectCount) return;
          const navigateToLeads = (propertyName: string, isCtrlClick: boolean) => {
            const query = `isNavigatedFromProperties=true&Properties=${encodeURIComponent(propertyName)}`;
            const url = `data/manage-data?${query}`;
            if (isCtrlClick) {
              window.open(url, '_blank');
            } else {
              this.router.navigate(['data', 'manage-data'], {
                queryParams: {
                  Properties: propertyName,
                  isNavigatedFromProperties: true,
                },
              });
            }
          };
          navigateToLeads(data.title, event?.ctrlKey);
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: !this.globalSettings?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
        field: !this.globalSettings?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
        filter: false,
        hide: true,
        valueGetter: (params: any) => [
          params.data?.siteVisitDoneCount,
          params.data?.siteVisitDoneUniqueCount,
          params?.data?.title,
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>`;
        }
      },
      {
        headerName: 'Meeting Done',
        field: 'Meeting Done',
        filter: false,
        hide: true,
        valueGetter: (params: any) => [
          params.data?.meetingDoneCount,
          params.data?.meetingDoneUniqueCount,
          params?.data?.name,
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>`;
        },
      },
      {
        headerName: 'Property Type',
        field: 'Property Type',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => {
          const displayName = params.data?.propertyType?.displayName || '';
          const childType = params.data?.propertyType?.childType?.displayName || '';
          if (displayName === 'Residential') {
            const bhkType = params.data?.bhkType || '';
            const noOfBHK = params.data?.noOfBHK || '';
            return [displayName, childType, bhkType, noOfBHK];
          }
          return [displayName, childType];
        },
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0]}${params.value[0] && params.value[1] ? ',' : ''
            } ${params.value[1]} </p>
          <p class="text-truncate-1 break-all">${params.value[2] > 0 ? BHK_TYPE[params.value[2] - 1] : ''
            }${params.value[2] && params.value[3] ? ',' : ''} ${params.value[3] ? getBRDisplayString(params.value[3]) : ''
            } </p>`;
        },
      },
      {
        headerName: 'Property Description',
        field: 'Property Description',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data?.monetaryInfo?.expectedPrice
            ? formatBudget(
              params.data?.monetaryInfo?.expectedPrice,
              params.data?.monetaryInfo?.currency || this.defaultCurrency
            )
            : '',
          params.data?.dimension?.area,
          params.data?.dimension?.unit,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all"> ${params.value[0] ? 'Budget: ' + params.value[0] : ''}</p>
                  <p class="text-truncate-1 break-all"> ${params.value[1] && params.value[2] ? 'Property Area: '
              + params.value[1] + params.value[2] : ''}</p>`;
        },
      },
      {
        headerName: 'Listing Expiry Date',
        field: 'Listing Expiry Date',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.listingExpireDate
            ? params.data?.listingExpireDate
            : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p> ${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Listing Status',
        field: 'Listing Status',
        minWidth: 120,
        valueGetter: (params: any) => params.data?.listingStatus !== 0 ? params.data?.listingStatus : '--',
        cellRenderer: (params: any) => {
          if (params.value === '--') {
            return `<span>--</span>`;
          }
          const color = this.getStatusColor(params.value);
          return `
            <span class="status-label-badge" style="background-color: ${this.hexToRgba(
            color,
            0.08
          )};">
              <p class="mr-6" style="color: ${color};">${this.getStatusLabel(params.value)}</p>
            </span>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Location',
        field: 'Location',
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          getLocationDetailsByObj(params.data?.address),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Source Location',
        field: 'Source Location',
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => {
          return params.data?.listingSourceAddresses
            ?.map((item: any) => {
              const source = item.listingSource?.displayName || '--';
              const address = getLocationDetailsByObj(item) || '--';
              return `${source} - ${address}`;
            })
            .join(', ');
        },
        cellRenderer: (params: any) => {
          if (!params.value) {
            return '<p class="text-truncate-2">--</p>';
          }
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Enquired For',
        field: 'Enquired For',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.enquiredFor ? EnquiryType[params.data?.enquiredFor] : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Offering Type',
        field: 'Offering Type',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.offeringType ? OfferingType[params.data?.offeringType] : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Completion Status',
        field: 'Completion Status',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.completionStatus ? CompletionStatus[params.data?.completionStatus] : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Uae Emirate',
        field: 'Uae Emirate',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.uaeEmirate ? UaeEmirate[params.data?.uaeEmirate] : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Finishing Type',
        field: 'Finishing Type',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.finishingType ? FinishingType[params.data?.finishingType] : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Property Age',
        field: 'Property Age',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.age > 0 ? params.data?.age : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Sub-Community',
        field: 'Sub-Community',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.address?.subCommunity ? params.data?.address?.subCommunity : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Community',
        field: 'Community',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.address?.community ? params.data?.address?.community : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Tower Name',
        field: 'Tower Name',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.address?.towerName ? params.data?.address?.towerName : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Share Count',
        field: 'Share Count',
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data?.whatsAppShareCount,
          params.data?.emailShareCount,
          params.data?.smsShareCount,
          params.data?.shareCount,
          params.data?.callShareCount
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate-2">
          ${params.value[3] ? 'Total: ' + params.value[3] + '; ' : ''}
          ${params.value[0] ? 'WhatsApp: ' + params.value[0] + '; ' : ''}
          ${params.value[1] ? 'Email: ' + params.value[1] + '; ' : ''}
          ${params.value[2] ? 'SMS: ' + params.value[2] + '; ' : ''}
          ${params.value[4] ? 'Call: ' + params.value[4] + '; ' : ''}
          </p>`;
        },
      },
      {
        headerName: 'Possession Type',
        field: 'Possession Type',
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data?.possesionType
            ? PossessionType[params.data?.possesionType]
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Furnish Status',
        field: 'Furnish Status',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          FurnishStatus[params.data.furnishStatus],
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value == 'Unknown' ? '' : [params.value]}</p>`;
        },
      },
      {
        headerName: 'Facing',
        field: 'Facing',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [Facing[params.data.facing]],
        cellRenderer: (params: any) => {
          return `<p>${params.value == 'Unknown' ? '' : [params.value]}</p>`;
        },
      },
      {
        headerName: 'Portal Name',
        field: 'Portal Name',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => {
          if (params?.data?.listingSources?.length) {
            return params.data.listingSources.map((item: any) => item?.displayName).join(', ');
          } else {
            return '--';
          }
        },
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Broker Number',
        field: 'Broker Number',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [this.getLicense(params?.data?.assignedTo) || '--'],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Permit Number',
        field: 'Permit Number',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [params?.data?.dldPermitNumber || params?.data?.dtcmPermit || '--'],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Number of Cheques',
        field: 'Number of Cheques',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [params?.data?.monetaryInfo?.noOfChequesAllowed || '--'],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Listing Level',
        field: 'Listing Level',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [ListingLevel[params?.data?.listingLevel] || '--'],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Reference No',
        field: 'Reference No',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [params.data.serialNo],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params.value}</p>`;
        },
      },
      {
        headerName: 'About Property',
        field: 'About Property',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [params.data.aboutProperty],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Notes',
        field: 'Notes',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [params.data.notes],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Possession Availability',
        field: 'Possession Availability',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [params.data.possessionDate],
        cellRenderer: (params: any) => {
          const possessionDate = params.value[0];
          if (possessionDate) {
            return `<p>${moment(possessionDate) <= moment(new Date().toISOString())
              ? 'Ready To Move'
              : getTimeZoneDate(possessionDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear')
              }</p>`;
          } else {
            return "";
          }
        },
      },
      {
        headerName: 'Matching Leads',
        field: 'Matching Leads',
        hide: true,
        filter: false,
        maxWidth: 110,
        minWidth: 110,
        valueGetter: (params: any) => ['Properties'],
        cellRenderer: MatchingLeadsComponent,
      },
      {
        headerName: 'Created',
        field: 'Created',
        minWidth: 200,
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [getAssignedToDetails(params.data.createdBy, this.userList, true) || '',
        params.data?.createdOn
          ? 'At ' +
          getTimeZoneDate(params.data?.createdOn, this.userData?.timeZoneInfo?.baseUTcOffset, 'fullDateTime')
          : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 text-truncate-1 break-all mb-4">${params.value[0]}</p>
            <p>${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName && this.userData?.shouldShowTimeZone && params.value
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Modified',
        field: 'Modified',
        minWidth: 200,
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [getAssignedToDetails(params.data.lastModifiedBy, this.userList, true) || '',
        params.data?.lastModifiedOn
          ? 'At ' +
          getTimeZoneDate(params.data?.lastModifiedOn, this.userData?.timeZoneInfo?.baseUTcOffset, 'fullDateTime')
          : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 text-truncate-1 break-all mb-4">${params.value[0]}</p>
            <p>${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName && this.userData?.shouldShowTimeZone && params.value
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
    ];

    // if (this.canViewOwner) {
    //   this.gridOptions.columnDefs.push({
    //     headerName: 'Owner Info',
    //     field: 'ownerInfo',
    //     hide: true,
    //     cellClass: 'cursor-pointer',
    //     valueGetter: (params: any) => {
    //       const ownerDetails = params.data?.propertyOwnerDetails;
    //       if (Array.isArray(ownerDetails) && ownerDetails.length > 0) {
    //         return ownerDetails;
    //       }
    //       return [];
    //     },
    //     cellRenderer: (params: any) => {
    //       const owners = params.value;
    //       if (!owners || owners.length === 0) {
    //         return '';
    //       }
    //       let ownerInfoHtml = '';
    //       owners.forEach((owner: any) => {
    //         ownerInfoHtml += `
    //           <p class="text-truncate-1 break-all">
    //               ${owner?.name ? owner.name + '; ' : ''}
    //               ${owner?.phone ? owner.phone + '; ' : ''}
    //               ${owner?.alternateContactNo ? owner.alternateContactNo + '; ' : ''}
    //               ${owner?.email ? owner.email + '; ' : ''}
    //           </p>
    //         `;
    //       });
    //       return ownerInfoHtml;
    //     },
    //     width: 300,
    //     minWidth: 250,
    //   });
    // }

    this.gridOptions.columnDefs.push({
      headerName: 'Actions',
      field: 'Actions',
      filter: false,
      cellRenderer: PropertiesActionGridComponent,
      maxWidth: 250,
      minWidth: 250,
      menuTabs: [],
    });

    if (this.currentPath === '/properties/manage-listing') {
      if (this.filtersPayload?.PropertyVisiblity !== 4) {
        if (
          (this.filtersPayload?.PropertyVisiblity !== 2 &&
            this.filtersPayload?.PropertyVisiblity !== 5 &&
            this.canBulkList) ||
          (this.filtersPayload?.PropertyVisiblity !== 5 && this.canBulkDeList) ||
          (this.filtersPayload?.PropertyVisiblity === 2 && this.canBulkModifyListing) || (this.canBulkReassign || this.canBulkShare || this.canBulkDelete)
        ) {
          this.gridOptions.columnDefs.unshift({

            showRowGroup: true,
            cellRenderer: 'agGroupCellRenderer',
            headerCheckboxSelection: true,
            headerCheckboxSelectionFilteredOnly: true,
            checkboxSelection: true,
            filter: false,
            pinned: window.innerWidth > 768 ? 'left' : null,
            lockPinned: true,
            cellClass: 'lock-pinned',
            maxWidth: 50,
            suppressMovable: true,
            lockPosition: 'left'
          });
        }
      } else if (
        this.filtersPayload?.PropertyVisiblity === 4 &&
        (this.canBulkRestore || this.canPermanentDelete)
      ) {
        this.gridOptions.columnDefs.unshift({

          showRowGroup: true,
          cellRenderer: 'agGroupCellRenderer',
          headerCheckboxSelection: true,
          headerCheckboxSelectionFilteredOnly: true,
          checkboxSelection: true,
          filter: false,
          pinned: window.innerWidth > 768 ? 'left' : null,
          lockPinned: true,
          cellClass: 'lock-pinned',
          maxWidth: 50,
          suppressMovable: true,
          lockPosition: 'left'
        });
      }
    }


    this.gridOptions.context = {
      componentParent: this,
    };
  }

  getStatusColor(status: ListingStatus): string {
    const statusColorMap: Record<any, string> = {
      [ListingStatus.None]: '',
      [ListingStatus.Draft]: '#6850BF',
      [ListingStatus.Approved]: '#3A6DAF',
      [ListingStatus.Refused]: '#FF0000',
      [ListingStatus.Sold]: '#ED5454',
      [ListingStatus.Archived]: '#50BFA8',
    };
    return statusColorMap[status] || '#000000';
  }

  getStatusLabel(status: ListingStatus): string {
    return ListingStatus[status] || '';
  }

  onColumnsSelected(columns: any): void {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('manage-listing-columns', colData?.toString());
    const cols = columns?.map((col: any) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col) => col.value),
      false
    );
    var columnState: any = this.gridColumnApi.getColumnState();
    localStorage.setItem('myListingColumnState', JSON.stringify(columnState));
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns.filter(
      (col) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
  }

  toggleColumns(params: any): void {
    const columnState = JSON.parse(localStorage.getItem('myListingColumnState'));
    const columnData = localStorage.getItem('manage-listing-columns')?.split(',');
    this.columns = (params?.columnApi?.getColumns() || [])
      .map((column: any) => ({
        label: column?.getColDef()?.headerName,
        value: column,
      }))
      .slice(3, -1)
      .sort((a: any, b: any) => a?.label?.localeCompare(b?.label));
    this.defaultColumns = this.columns.filter(
      (col: any) => col?.value?.getColDef()?.hide !== true
    );
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }
    if (columnData?.length) {
      this.defaultColumns = this.columns.filter((col: any) =>
        columnData.includes(col.label)
      );
      this.onColumnsSelected(this.defaultColumns);
    }
  }


  onGridReady(params: any) {
    this.gridApi = params?.api;
    this.gridColumnApi = params.columnApi;
    this.toggleColumns(params);
  }

  isEmptyInput(event: any) {
    const searchTerm = (event.target as HTMLInputElement).value
    if ((searchTerm === '' || searchTerm === null) && this.getFormValue('PropertySearch')) {
      this.filtersForm.get('PropertySearch')?.setValue('');
    }
  }

  exportListing() {
    this.openModal(ExportMailComponent, {
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
      initialState: {
        payload: {
          ...this.filtersPayload,
          path: 'Listing',
          timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
          baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset()
        },
      }
    });
  }

  openAdvFiltersModal() {
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal'
    };
    this.openModal(ListingAdvanceFilterComponent, initialState)
  }

  dateChange() {
    if (this.dateType && this.filterDate?.[0]) {
      this.filtersForm.patchValue({
        DateType: DataDateType[this.dateType as keyof typeof DataDateType],
        FromDate: setTimeZoneDate(this.filterDate?.[0], this.userData?.timeZoneInfo?.baseUTcOffset),
        ToDate: setTimeZoneDate(this.filterDate?.[1], this.userData?.timeZoneInfo?.baseUTcOffset),
      })
    }
  }

  openModal(component: any, data: any) {
    this.modalRef = this.modalService.show(component, {
      ...data
    });
  }

  getFormValue(controlName: string) {
    return this.filtersForm.get(controlName)?.value
  }

  onEnterKey(event: KeyboardEvent) {
    const searchValue = (event.target as HTMLInputElement).value;
    if (!searchValue && !this.getFormValue('PropertySearch')) return
    this.filtersForm.get('PropertySearch')?.setValue(searchValue);
  }

  onResetDateFilter() {
    if (this.getFormValue('DateType') !== null && this.getFormValue('FromDate')) {
      this.filtersForm.patchValue({
        DateType: null,
        FromDate: null,
        ToDate: null
      });
    }
  }

  getCount(countType: string, key: string): string {
    const counts = countType === 'top' ? this.topCount : this.baseCount;
    const countMapping: { [key: string]: keyof typeof counts } = {
      All: 'allCount',
      Draft: 'draftCount',
      Approved: 'approvedCount',
      Refused: 'refusedCount',
      Archived: 'archivedCount',
      Sold: 'soldCount',
      Residential: 'residentialCount',
      Commercial: 'commercialCount',
      Agricultural: 'agricultureCount',
    };
    return counts?.[countMapping[key]] ?? '0';
  }

  getAreaUnit(id: string) {
    let areaUnit = '';
    this.areaSizeUnits?.forEach((type: any) => {
      if (type.id === id) areaUnit = type.unit;
    });
    return areaUnit;
  }

  getAssignedToName(id: string) {
    let assignedTo = '';
    this.allUserList?.forEach((assign: any) => {
      if (assign.id === id) assignedTo = assign.fullName;
    });
    return assignedTo;
  }

  getAmenity(value: string) {
    return this.amenities?.filter((amenity: any) => amenity?.id == value)?.[0]
      ?.amenityDisplayName;
  }

  getPropertySubTypes(id: string) {
    let propertySubTypes = [...this.propertyTypeList.map((type: any) => type.childTypes).flat()];
    return propertySubTypes?.find((item: any) => item.id === id)?.displayName;
  }

  getEnquiredFor(value: any) {
    return EnquiryType[value as keyof typeof EnquiryType];
  }

  getCompletionStatus(value: any) {
    return this.completionStatusList.find((status: any) => status.enumValue === Number(value))?.displayName;
  }

  getFinishingType(value: any) {
    return this.finishingTypeList.find((status: any) => status.enumValue === Number(value))?.displayName;
  }

  getUaeEmirate(value: any) {
    return this.uaeEmirateList.find((status: any) => status.enumValue === Number(value))?.displayName;
  }

  getListingLevel(value: any) {
    return ListingLevel[value as keyof typeof ListingLevel];
  }

  getPropertyArea(propertyArea: number) {
    return (
      propertyArea +
      ' ' +
      (this.getAreaUnit(this.filtersPayload?.PropertySizeUnit) || '')
    );
  }

  getLicense(assignedTo: any[] | undefined): string | string[] {
    if (!assignedTo || assignedTo.length === 0) {
      return '--';
    }

    const licenses = assignedTo?.map(userId => {
      const user = this.allUserList?.find((user: any) => user.id === userId);
      return user ? user?.licenseNo : null;
    })
      .filter((license): license is string => license !== null);

    return licenses.length ? licenses : '--';
  }
  onFilterChange(selectedValue: number): void {
    if (this.getFormValue('PropertyVisiblity') !== selectedValue) {
      this.filtersForm.patchValue({ PropertyVisiblity: selectedValue, PageNumber: 1 });
    }
  }

  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = ['UserIds', 'ListingOnBehalf', 'Locations', 'Projects', 'States', 'Cities', 'OwnerNames', 'Amenities', 'Communities', 'SubCommunities'];
    if (
      ['PageSize', 'PageNumber', 'FirstLevelFilter', 'PropertySearch', 'PropertyVisiblity', 'permission',
        'BasePropertyTypeId', 'DateType', 'PropertySizeUnit', 'CarpetAreaUnit',
        'possessionDate', 'propertyType', 'SaleableAreaUnit', 'BuiltUpAreaUnit', 'NetAreaUnit']
        .includes(key) ||
      values?.length === 0 ||
      (key == 'maxPrice' && this.filtersPayload?.maxPrice == 0)
    )
      return [];
    else if (key === 'Currency') return null;
    else if (key === 'PossesionType' && values) {
      const numValue = Number(values);
      return [PossessionType[numValue] || values];
    }
    else if ((key === 'MaxPrice' || key === 'MinPrice') && values)
      return [' ' + this.filtersPayload?.Currency + values];
    else if ((key === 'MinCarpetArea' || key === 'MaxCarpetArea') && values)
      return [values + ' ' + this.getAreaUnit(this.filtersPayload?.CarpetAreaUnit)];
    else if ((key === 'MinBuiltUpArea' || key === 'MaxBuiltUpArea') && values)
      return [values + ' ' + this.getAreaUnit(this.filtersPayload?.BuiltUpAreaUnit)];
    else if ((key === 'MinSaleableArea' || key === 'MaxSaleableArea') && values)
      return [values + ' ' + this.getAreaUnit(this.filtersPayload?.SaleableAreaUnit)];
    else if ((key === 'MinNetArea' || key === 'MaxNetArea') && values)
      return [values + ' ' + this.getAreaUnit(this.filtersPayload?.NetAreaUnit)];
    else if (allowedKeys.includes(key)) return values;
    else if ((key === 'PropertyTitle' || key === 'SerialNo') && values) return [values];
    return values?.toString()?.split(',');
  }

  onRemoveFilter(key: string, value: string) {
    const resetCurrencyAndBudget = (): Record<string, any> => ({
      MaxPrice: null,
      MinPrice: null,
      Currency: null,
    });

    const resetPropertyArea = (): Record<string, any> => ({
      MaxPropertySize: null,
      MinPropertySize: null,
      PropertySizeUnit: null,
    });

    const resetCarpetArea = (): Record<string, any> => ({
      MinCarpetArea: null,
      CarpetAreaUnit: null,
      MaxCarpetArea: null,
    });

    const resetBuiltUpArea = (): Record<string, any> => ({
      MaxBuiltUpArea: null,
      MinBuiltUpArea: null,
      BuiltUpAreaUnit: null,
    });

    const resetSaleableArea = (): Record<string, any> => ({
      MinSaleableArea: null,
      MaxSaleableArea: null,
      SaleableAreaUnit: null,
    });

    const resetNetArea = (): Record<string, any> => ({
      MinNetArea: null,
      MaxNetArea: null,
      NetAreaUnit: null,
    });

    const resetPossessionFilters = (): Record<string, any> => ({
      PossesionType: null,
      FromPossesionDate: null,
      ToPossesionDate: null,
    });

    const resetFilter = (key: string): Record<string, any> => ({
      [key]: null,
    });

    const resetArrayFilter = (key: string, value: string): void => {
      const filteredValue = this.filtersPayload[key]?.filter((item: any) => item !== value);
      if (filteredValue) {
        this.filtersPayload = { ...this.filtersPayload, [key]: filteredValue };
      }
    };

    if (['PossesionType', 'FromPossesionDate', 'ToPossesionDate', 'Possession From Date', 'Possession To Date'].includes(key)) {
      this.filtersPayload = { ...this.filtersPayload, ...resetPossessionFilters() };
      this.filtersForm.patchValue(resetPossessionFilters());
      return;
    }

    if (key === 'MaxPrice') {
      this.filtersPayload = { ...this.filtersPayload, MaxPrice: null, ...(this.filtersPayload.MinPrice ? {} : resetCurrencyAndBudget()) };
    } else if (key === 'MinPrice') {
      this.filtersPayload = { ...this.filtersPayload, MinPrice: null, ...(this.filtersPayload.MaxPrice ? {} : resetCurrencyAndBudget()) };
    } else if (key === 'MaxCarpetArea' || key === 'MinCarpetArea') {
      this.filtersPayload = { ...this.filtersPayload, ...resetCarpetArea() };
    } else if (key === 'MaxBuiltUpArea' || key === 'MinBuiltUpArea') {
      this.filtersPayload = { ...this.filtersPayload, ...resetBuiltUpArea() };
    } else if (key === 'MinSaleableArea' || key === 'MaxSaleableArea') {
      this.filtersPayload = { ...this.filtersPayload, ...resetSaleableArea() };
    } else if (key === 'MinNetArea' || key === 'MaxNetArea') {
      this.filtersPayload = { ...this.filtersPayload, ...resetNetArea() };
    } else if (key === 'MinPropertySize' || key === 'MaxPropertySize') {
      this.filtersPayload = { ...this.filtersPayload, ...resetPropertyArea() };
    } else if (key === 'Profession') {
      const filteredProfession = this.filtersPayload?.Profession?.filter((item: any) => +item !== +value);
      this.filtersPayload = { ...this.filtersPayload, Profession: filteredProfession };
    }
    else if (['UserIds', 'ListingOnBehalf', 'PropertyTypes', 'PropertySubTypes', 'Projects', 'EnquiryTypes', 'NoOfBHK', 'BHKTypes', 'Locations', 'Cities', 'Amenities', 'Communities', 'SubCommunities'].includes(key)) {
      resetArrayFilter(key, value);
    } else if (Array.isArray(this.filtersPayload[key]) && value) {
      resetArrayFilter(key, value);
    } else {
      this.filtersPayload = { ...this.filtersPayload, ...resetFilter(key) };
    }

    if (key !== 'MaxPrice' && key !== 'MinPrice') {
      this.filtersPayload = { ...this.filtersPayload };
    }

    this.filtersForm.patchValue(this.filtersPayload);
  }

  clearAll() {
    this._store.dispatch(new UpdateListingPayload({
      PageNumber: 1,
      PageSize: 10,
      PropertyVisiblity: this.getFormValue('PropertyVisiblity'),
      FirstLevelFilter: this.getFormValue('FirstLevelFilter'),
      BasePropertyTypeId: this.getFormValue('BasePropertyTypeId'),
      PropertySearch: this.getFormValue('PropertySearch')
    },))
  }

  onCellClicked(event: CellClickedEvent): void {
    const userName = JSON.parse(localStorage.getItem('userDetails') || '{}')?.preferred_username;
    const excludedHeaders = ['Status', 'Matching Leads', 'Leads Count', 'Data Count', 'Actions', 'Site Visit Done', 'Meeting Done', undefined];
    if (!excludedHeaders.includes(event.colDef.headerName) && event.data?.serialNo) {
      window.open(`external/listing-preview/${userName}/${event.data.serialNo}`, '_blank');
    }
  }

  getPossessionDate(value: Date) {
    let date: any = setTimeZoneDate(value, this.userData?.timeZoneInfo?.baseUTcOffset)
    return getTimeZoneDate(date, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear');
  }

  navigateToAddListing() {
    this.router.navigate(['properties/add-listing']);
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  selectItem(item: any) {
    this.selectedItem = item;
    this.isDropdownOpen = false;
  }

  openPropertyBulkUpload() {
    if (this.selectedOption === 'bulkUpload') {
      this.router.navigate(['properties/listing-bulk-upload']);
    }
    this.selectedOption = '';
  }

  onSyncListing(source: any): void {
    const syncControl = this.syncDateForm.get(source?.displayName);
    const isValidSyncControl = syncControl?.value?.length === 2;
    const payload = {
      listingSourceId: source?.id,
      fromDate: isValidSyncControl ? setTimeZoneDate(syncControl.value[0], this.userData?.timeZoneInfo?.baseUTcOffset) : null,
      toDate: isValidSyncControl ? setTimeZoneDate(syncControl.value[1], this.userData?.timeZoneInfo?.baseUTcOffset) : null,
    };
    this._store.dispatch(new syncListing(payload));
    syncControl?.reset();
  }

  openPropertyTracker() {
    if (this.selectedTrackerOption === 'bulkUpload') {
      let initialState: any = {
        fieldType: 'property',
      };
      this._store.dispatch(new FetchPropertyExcelUploadedList(1, 10));
      this.modalService.show(ExcelUploadedStatusComponent, {
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      });
    } else if (this.selectedTrackerOption === 'export') {
      this._store.dispatch(new FetchExportPropertyStatus(1, 10));
      this.modalService.show(ExportPropertyTrackerComponent, {
        class: 'modal-900 modal-dialog-centered h-100 tb-modal-unset',
      });
    } else if (this.selectedTrackerOption === 'sync') {
      this._store.dispatch(new FetchSyncListingList(1, 10));
      this.modalService.show(ListingSyncTrackerComponent, {
        class: 'modal-900 modal-dialog-centered h-100 tb-modal-unset',
      });
    }
    this.selectedTrackerOption = '';
  }

  @HostListener('document:click', ['$event'])
  handleOutsideClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (
      Array.from(target?.classList).some((className) =>
        className.includes('owl-dt')
      ) ||
      Array.from(target?.parentElement?.classList).some((className) =>
        className.includes('owl-dt')
      ) ||
      (this.isDropdownOpen &&
        Array.from(target.classList).some((className) =>
          className.includes('cdk-overlay')
        ))
    ) {
      return;
    }

    if (!document.getElementById('dropdown-element')?.contains(target)) {
      this.isDropdownOpen = false;
    }
  }

  ngOnDestroy(): void {
    this._store.dispatch(new ClearListingSearch())
    this.stopper.next();
    this.stopper.complete();
  }
}
