<ng-container *ngIf="params.value[0] == 'Toggle Status' else actions">
  <div class="text-center align-center" [title]="status ? 'Available' : 'Unavailable'">
    <input type="checkbox" class="toggle-switch toggle-active-sold" [(ngModel)]="status"
      (click)="canEdit ? openConfirmModal(params.data) : ''" [ngClass]="{'pe-none' : !canEdit}">
    <label for="chkToggle" class="switch-label" [ngClass]="{'pe-none' : !canEdit}"></label>
  </div>
</ng-container>

<ng-template #actions>
  <div class="mt-4 justify-between">
    <div title="Edit" *ngIf="canEdit" class="bg-accent-green icon-badge" (click)="openCenterModal(params.data)"><span
        class="icon ic-pen m-auto ic-xxs" id="clkEditLead"></span></div>
    <div title="Delete" *ngIf="canDelete" class="bg-light-red icon-badge" (click)="deleteData(params.data)"><span
        class="icon ic-delete m-auto ic-xxs" id="clkDeleteLead" data-automate-id="clkDeleteProjects"></span></div>
    <div title="Clone" class="bg-dark-red-40 icon-badge" (click)="addSameUnit(params.data)"><span
        class="icon ic-split-arrows m-auto ic-xxxs"></span></div>
    <share-external [data]="getDataWithProject()" [moduleName]="'projectUnit'" [key]="'share-units'"
      [mailCount]="params.data?.contactRecords?.Email"
      [whatsAppCount]="params.data?.contactRecords?.WhatsApp"></share-external>
  </div>
</ng-template>