import {
  Component,
  EventEmitter,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { AddUnitInfoComponent } from 'src/app/features/projects/add-new-project/unit-info/add-unit-info/add-unit-info.component';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  AddUnitType,
  DeleteProjectUnitInfo,
  UpdateUnitTypeToggleStatus,
} from 'src/app/reducers/project/project.action';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'unit-info-actions',
  templateUrl: './unit-info-actions.component.html',
})
export class UnitInfoActionsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  canEdit: boolean = false;
  canDelete: boolean = false;
  params: any;
  status: boolean;

  constructor(
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    private store: Store<AppState>,
    public router: Router,
    public trackingService: TrackingService
  ) { }

  ngOnInit() {
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Projects.Update')) {
          this.canEdit = true;
        }
        if (permissions?.includes('Permissions.Projects.Delete')) {
          this.canDelete = true;
        }
      });

    this.status = this.params.data.unitTypeStatus === 0 ? true : false;
  }

  agInit(params: any): void {
    this.params = params;
  }

  openCenterModal(data: any) {
    this.trackingService.trackFeature(`Web.ProjectUnitInfo.Actions.Edit.Click`)
    let initialState: any = {
      selectedDataId: this.params.value[1],
      selectEditUnitData: data,
      pageNumber: this.params.value[2],
      pageSize: this.params.value[3]
    };
    this.modalService.show(AddUnitInfoComponent, {
      class: 'tb-modal-unset modal-1100',
      initialState
    });
  }

  deleteData(data: any) {
    this.trackingService.trackFeature(`Web.ProjectUnitInfo.Actions.Delete.Click`)
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.name,
      fieldType: 'Unit Info',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          let payload = {
            unitId: data?.id,
            projectId: this.params.value[1],
            pageNumber: this.params.value[2],
            pageSize: this.params.value[3]
          };
          this.trackingService.trackFeature(`Web.Project.UnitInfo.Actions.Deleted.Click`)
          this.store.dispatch(new DeleteProjectUnitInfo(payload));
        }
      });
    }
  }


  openConfirmModal(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'change the status of',
      title: data?.name,
      fieldType: 'Unit Info',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          let payload = {
            unitId: this.params.data.id,
            projectId: this.params.value[1],
            pageNumber: this.params.value[2],
            pageSize: this.params.value[3]
          };
          this.store.dispatch(new UpdateUnitTypeToggleStatus(payload));
        } else {
          this.status = this.params.data.unitTypeStatus === 0 ? true : false;
        }
      });
    }
  }

  addSameUnit(data: any) {
    let payload = {
      "name": data?.name,
      "furnishingStatus": data?.furnishingStatus,
      "noOfBHK": data?.noOfBHK,
      "bhkType": data?.bhkType,
      "facings": data?.facings,
      "area": data?.area,
      "areaUnitId": data?.areaUnitId,
      "carpetArea": data?.carpetArea,
      "carpetAreaUnitId": data?.carpetAreaUnitId,
      "buildUpArea": data?.buildUpArea,
      "buildUpAreaId": data?.buildUpAreaId,
      "superBuildUpArea": data?.superBuildUpArea,
      "superBuildUpAreaUnit": data?.superBuildUpAreaUnit,
      "pricePerUnit": data?.pricePerUnit,
      "price": data?.price,
      "unitTypeStatus": data?.unitTypeStatus,
      "currency": data?.currency,
      "projectId": this.params.value[1],
      "maintenanceCost": data?.maintenanceCost,
      unitTypeId: data?.unitType?.childType?.id,
      imageUrls: {
        additionalProp1: data?.images,
        additionalProp2: data?.videos,
        additionalProp3: data?.unitInfoGalleries,
      },
      unitAttributes: data?.attributes,
      pageNumber: this.params.value[2],
      pageSize: this.params.value[3]
    };
    this.store.dispatch(new AddUnitType(payload));
    this.trackingService.trackFeature(`Web.ProjectUnitInfo.Actions.Copy.Click`)
  }

  getDataWithProject() {
    const projectData = this.params.value[4];
    const serialNo = projectData?.serialNo
    return {
      ...this.params.data,
      serialNo: serialNo,
      projectData: projectData
    };
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
