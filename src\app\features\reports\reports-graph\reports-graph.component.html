<!-- <div class="bg-dark w-100 px-16 py-12 text-white flex-between">
  <h3 class="fw-semi-bold">Visualization Graph</h3>
  <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
</div> -->
<div class="bg-white">
  <!-- Controls Section -->
  <div class="align-center px-16">
    <!-- Parent Column Selector (only shown if parent columns exist) -->
    <div class="w-25 mr-16" *ngIf="parentColumns.length > 0">
      <div class="field-label" for="parentSelector">Select Category:</div>
      <ng-select id="parentSelector" [items]="parentColumns" bindLabel="headerName" [(ngModel)]="selectedParent"
        (change)="onParentChange($event)" placeholder="Select Categories" [multiple]="true" [closeOnSelect]="false"
        [clearable]="false" class="ng-select-gray">

        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
          <div class="checkbox-container">
            <input type="checkbox" id="parent-category-{{index}}" [checked]="item$.selected"
              (click)="$event.stopPropagation()">
            <span class="checkmark"></span>
            <span class="text-truncate-1 break-all">{{ item.headerName }}</span>
          </div>
        </ng-template>
      </ng-select>
    </div>

    <!-- Child Column Selector -->
    <div class="w-25 mr-16 tb-w-100 tb-mr-0 tb-mb-16 ph-w-100 ph-mr-0 ph-mb-12">
      <div class="field-label mb-8" for="childSelector">Select Data Field:</div>
      <ng-select [virtualScroll]="true" [items]="childColumns" [bindLabel]="getBindLabel()" [bindValue]="getBindValue()"
        [multiple]="true" [closeOnSelect]="false" placeholder="Select fields to display" [(ngModel)]="selectedChildren"
        (change)="onChildrenChange($event)" [compareWith]="getCompareFunction()" class="ng-select-gray">

        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
          <div class="checkbox-container">
            <input type="checkbox" id="child-field-{{index}}" [checked]="item$.selected"
              (click)="$event.stopPropagation()">
            <span class="checkmark"></span>
            <span class="text-truncate-1 break-all">{{ getDisplayLabel(item) }}</span>
          </div>
        </ng-template>
      </ng-select>
    </div>

    <div class="w-25 mr-16">
      <div class="field-label" for="chartTypeSelector">Chart Type:</div>
      <ng-select id="chartTypeSelector" [items]="chartTypes" bindLabel="label" bindValue="value"
        [(ngModel)]="selectedChartType" (change)="onChartTypeChange($event)" placeholder="Select Chart Type"
        [clearable]="false">
      </ng-select>
    </div>
  </div>

  <!-- <div class="flex-center h-100" *ngIf="!isChartReady && !showSelectionMessage && !noDataAvailable">
    <application-loader></application-loader>
  </div> -->

  <div *ngIf="showSelectionMessage" class="text-center">
    <div class="flex-center-col h-100-337">
      <div class="header-3 fw-600 text-center">Please Select Data Fields</div>
    </div>
  </div>

  <div [hidden]="!isChartReady" class="mt-16 h-100-160 w-100 justify-center">
    <canvas id="activityChart"></canvas>
  </div>

</div>