<div class="w-100 px-24 bg-white py-6">
  <div class="flex-between flex-grow-1">
    <ul class="align-center top-nav-bar text-nowrap scrollbar scroll-hide ph-w-100-80 tb-w-100-280 user-select-none">
      <ng-container>
        <li
          (click)="isViewAllProjects = true; currentVisibility(0);trackingService.trackFeature('Web.Project.Menu.All.Click');"
          class="cursor-pointer align-center" [ngClass]="{'gray-scale' : !isViewAllProjects}">
          <div class="bg-linear-green dot dot-lg"><span class="icon ic-buliding-secondary-solid ic-xxs"></span></div>
          <span class="text-large mx-8 d-flex" [ngClass]="isViewAllProjects ? 'fw-700' : 'fw-semi-bold'">
            All ({{projectTypeCount?.all}})
          </span>
        </li>
        <li
          (click)="isViewAllProjects = false; currentVisibility(4);trackingService.trackFeature('Web.Project.Menu.Deleted.Click')"
          class="cursor-pointer align-center ml-10" [ngClass]="{'gray-scale' : isViewAllProjects}">
          <div class="bg-linear-orange dot dot-lg"><span class="icon ic-delete ic-xxs"></span></div>
          <span class="text-large mx-8" [ngClass]="isViewAllProjects ? 'fw-semi-bold' : 'fw-700'">
            {{'DASHBOARD.deleted' | translate }} ({{projectTypeCount?.deleted}})</span>
        </li>
      </ng-container>
    </ul>
    <div class="align-center ph-mr-20">
      <ng-container *ngIf="canAdd">
        <div class="align-center">
          <div class="btn-full-dropdown btn-w-100" *ngIf="canBulkUpload || canExport">
            <div class="position-absolute top-9 left-9 ip-top-11 align-center z-index-2">
              <span class="ic-tracker icon ic-xxs"></span>
              <span class="ml-8 ip-d-none">Tracker</span>
            </div>
            <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
              [(ngModel)]="selectedTrackerOption" (change)="onTrackerChange($event)">
              <ng-option (click)="selectedTrackerOption = null" value="bulkUpload" *ngIf="canBulkUpload">
                <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
              <ng-option (click)="selectedTrackerOption = null" value="export" *ngIf="canExport">
                <span class="ic-download icon ic-xxs ic-dark mr-8"></span>Export</ng-option>
            </ng-select>
          </div>
          <ng-container *ngIf="canBulkUpload || canAdd">
            <div class="btn-left-dropdown ml-10"
              (click)="trackingService.trackFeature('Web.Project.Button.AddProject.Click')"
              routerLink="/projects/add-project">
              <span class="ic-add icon ic-xxs"></span>
              <span class="ml-8">Add Project</span>
            </div>
            <div class="btn-right-dropdown btn-w-30 black-100">
              <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" [(ngModel)]="selectedOption"
                (click)="openProjectBulkUpload()"
                (change)="trackingService.trackFeature('Web.Project.Button.BulkUpload.Click')">
                <ng-option (click)="selectedOption = null" value="bulkUpload" *ngIf="canBulkUpload">
                  <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                  {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
              </ng-select>
            </div>
          </ng-container>
        </div>
      </ng-container>
    </div>
  </div>
</div>

<div class="d-flex bg-white py-8 px-24 border-top border-bottom">
  <div class="d-flex text-nowrap ph-w-100-40 scrollbar scroll-hide">
    <ul *ngFor="let ProjectType of projectAllTypeList" class="cursor-pointer ip-mb-4">
      <button (click)="currentProjectType(ProjectType)" class="mr-10 px-12 bg-white header-5 py-8 border-0 br-4"
        id="clk{{ProjectType}}" data-automate-id="clk{{ProjectType}}"
        [ngClass]="appliedFilter.ProjectType == ProjectType ? 'text-dark-250 fw-700': 'btn-'+ProjectType.type">
        {{ProjectType}}
        ({{ProjectType==='All'?projectTypeCount?.all:ProjectType==='Residential'?projectTypeCount?.residential:ProjectType==='Commercial'?projectTypeCount?.commercial:ProjectType==='Agricultural'?projectTypeCount?.agriculture:projectTypeCount?.deleted}})</button>
    </ul>
  </div>
</div>
<ng-container *ngIf="canView">
  <div class="pt-12 px-24">
    <div class="align-center bg-white w-100 border-gray ip-align-center-unset ip-flex-col mt-10">
      <div class="align-center flex-grow-1 no-validation border-end tb-br-0">
        <div *ngIf="canSearch" class="position-relative flex-grow-1">
          <div class="align-center w-100 px-10 py-12">
            <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
            <input type="text" (keydown)="onSearch($event)" (input)="isEmptyInput($event)" placeholder="type to search"
              [(ngModel)]="searchTerm" [ngModelOptions]="{standalone: true}" class="border-0 outline-0 w-100" />
          </div>
          <small class="text-muted text-nowrap ph-d-none mr-10 position-absolute right-0 bottom-0">
            ({{ 'LEADS.lead-search-prompt' | translate }})</small>
        </div>
        <div class="flex-end">
          <div *ngIf="canExport && projectTotalCount && globalSettingsData?.isProjectsExportEnabled"
            (click)="exportProjectReport()"
            class="bg-accent-green text-white px-20 py-12 h-100 align-center cursor-pointer border-start w-70px tb-br-top">
            {{ 'REPORTS.export' | translate }}</div>
        </div>
      </div>
      <div class="align-center ip-br-top">
        <div class="px-10 align-center cursor-pointer tb-flex-grow-1 ph-w-40px ph-flex-grow-unset border-right"
          (click)="openAdvFiltersModal(AdvancedFilters)">
          <div class="icon ic-filter-solid ic-xxs ic-black mr-10">
          </div>
          <span class="fw-600 ph-d-none">{{'PROPERTY.advanced-filters' | translate}}</span>
        </div>
        <div class="align-center position-relative cursor-pointer d-flex border-end">
          <span class="position-absolute left-15 z-index-2 fw-600 text-sm">
            {{ 'BUTTONS.manage-columns' | translate }}</span>
          <div class="show-hide-gray w-140">
            <ng-select [virtualScroll]="true" class="bg-white" [items]="columns" [multiple]="true" appSelectAll [searchable]="false"
              ResizableDropdown [closeOnSelect]="false" [ngModel]="defaultColumns" (change)="onColumnsSelected($event)">
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                    class="checkmark"></span>{{item.label}}</div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="bg-coal text-white px-10 py-12 ip-w-30px h-100 align-center cursor-pointer"
          (click)="onSetColumnDefault();trackingService.trackFeature('Web.Project.Default.Column.Click')">
          <span class="ip-d-none">{{ 'GLOBAL.default' | translate }}</span> <span
            class="ic-refresh d-none ip-d-block"></span>
        </div>
        <div class="show-dropdown-white align-center position-relative">
          <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
              {{ 'GLOBAL.show' | translate }}</span> {{ 'GLOBAL.entries' | translate }}</span>
          <ng-select [virtualScroll]="true" [placeholder]="pageSize" class="w-150 tb-w-120px" (change)="assignCount()"
            ResizableDropdown [(ngModel)]="selectedPageSize" [searchable]="false">
            <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
              {{pageSize}}</ng-option>
          </ng-select>
        </div>
      </div>
    </div>
    <div class="bg-white px-4 py-12 tb-w-100-34" [ngClass]="showLeftNav ? 'w-100-190' : 'w-100-90'">
      <ng-container *ngIf="showFilters">
        <div class="bg-secondary flex-between">
          <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
            <div class="d-flex" *ngFor="let filter of appliedFilter | keyvalue">
              <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                {{projectFiltersKeyLabel[filter.key] || filter.key}}: {{
                filter.key === 'currentStatus'? value== 0 ? 'Available':'Unavailable':
                filter.key === 'FromDate'? DateFormat(value):
                filter.key === 'ToDate'? DateFormat(value):
                filter.key === 'FromPossesionDate'? changeMonthYearFormat(value):
                filter.key === 'ToPossesionDate'? changeMonthYearFormat(value):
                filter.key === 'projectSubType'? getProjectSubType(value):
                filter.key === 'AmenitesIds' ? getAmenity(value) :
                filter.key === 'MaxCarpetArea' || filter.key === 'MinCarpetArea' ? getCarpetArea(value) :
                value }}
                <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                  (click)="onRemoveFilter(filter.key, value)"></span>
              </div>
            </div>
          </drag-scroll>
          <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
            (click)="reset()">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}
          </div>
        </div>
      </ng-container>
    </div>
    <ng-container *ngIf="!rowData?.length && !isProjectsLoading; else projectData">
      <div class="flex-center-col h-100-250">
        <ng-lottie [options]='noDataFound'></ng-lottie>
        <div class="header-3 fw-600 text-center">{{'PROFILE.no-project-found' | translate }}</div>
      </div>
    </ng-container>
    <ng-template #projectData>
      <ng-container *ngIf="!isProjectsLoading">
        <div class="manage-project">
          <ag-grid-angular class="ag-theme-alpine" [gridOptions]="gridOptions" (gridReady)="onGridReady($event)"
            [rowData]="rowData" [paginationPageSize]="pageSize" [pagination]="true" [alwaysShowHorizontalScroll]="true"
            [alwaysShowVerticalScroll]="true" [suppressPaginationPanel]="true" (cellClicked)="onCellClicked($event)"
            (filterChanged)="onFilterChanged($event)">
          </ag-grid-angular>
        </div>
        <div class="my-20 flex-end" *ngIf="!isProjectsLoading">
          <div class="mr-10" *ngIf="projectTotalCount">{{ 'GLOBAL.showing' | translate }}
            {{projectTotalCount ? currOffset*pageSize + 1 : 0}}
            {{ 'GLOBAL.to-small' | translate }}
            {{currOffset*pageSize + gridApi?.getDisplayedRowCount()}}
            {{ 'GLOBAL.of-small' | translate }} {{projectTotalCount}} {{ 'GLOBAL.entries-small' | translate}}
          </div>
          <pagination [offset]=currOffset [limit]="1" [range]="1" [size]='getPages(projectTotalCount,pageSize)'
            (pageChange)="onPageChange($event)">
          </pagination>
        </div>
      </ng-container>
      <div *ngIf="isProjectsLoading" class="flex-center w-100 h-100-250 min-h-250">
        <ng-container [ngTemplateOutlet]="projectsLoader"></ng-container>
      </div>
    </ng-template>
    <div class="justify-center">

      <div class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 z-index-2"
        [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}">
        <drag-scroll class="d-flex scrollbar scroll-hide ip-max-w-100-70">
          <div class="align-center">
            <div class="fw-600 text-coal mr-20 text-xl">{{gridApi?.getSelectedNodes()?.length}}
              {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' | translate}}
            </div>
          </div>
          <div class="flex-center" *ngIf="appliedFilter?.ProjectVisibility !== 4 else bulkRestore">
            <button *ngIf="canBulkReassign" class="btn-bulk" id="btnBulkReassign" data-automate-id="btnBulkReassign"
              (click)="openBulkReassignModal(BulkReassignModal)">
              Bulk Reassign </button>
            <button class="btn-bulk" (click)="openShareExternalModal()" *ngIf="canBulkShare">Bulk Share</button>
            <button *ngIf="canBulkDelete" class="btn-bulk-red" (click)="openBulkDeleteModal(BulkDeleteModal)">
              Bulk Delete</button>
          </div>
          <ng-template #bulkRestore>
            <button *ngIf="canBulkRestore" class="btn-bulk-blue" (click)="openBulkRestoreModal(BulkRestoreModal)">
              Bulk Restore</button>
            <button *ngIf="canBulkPermanentDelete" title="Delete Permanently" class="btn-bulk-red"
              (click)="openBulkRestoreModal(BulkRestoreModal,true)">
              Bulk Delete
            </button>
          </ng-template>
        </drag-scroll>
      </div>
    </div>
    <ng-template #BulkReassignModal>
      <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
          <h3>{{ 'LEADS.bulk' | translate }} Reassign</h3>
          <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="bulkReassignModalRef.hide()">
          </div>
        </div>
        <div class="px-12 scrollbar h-100-108">
          <div class="field-label mb-10">Select Project(s) - {{selectedNodes?.length}}</div>
          <div class="table-scrollbar scrollbar scroll-hide ip-w-100-40 mb-16">
            <table class="table standard-table no-vertical-border">
              <thead>
                <tr class="w-100 text-nowrap">
                  <th class="w-150">
                    <span class="text-truncate-1 break-all">{{'GLOBAL.name' | translate}}</span>
                  </th>
                  <th class="w-150">
                    <span class="text-truncate-1 break-all">{{ 'LEADS.assign-to' | translate }}</span>
                  </th>
                  <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
                </tr>
              </thead>
              <tbody class="text-secondary fw-semi-bold scrollbar max-h-100-433">
                <ng-container *ngFor="let project of selectedNodes">
                  <tr>
                    <td class="w-150">
                      <div class="text-truncate-1 break-all" [title]="project?.name">
                        {{project?.name}}
                      </div>
                    </td>
                    <td class="w-150">
                      <div class="text-truncate-1 break-all" [title]="getAssignedToNames(project?.assignedUserIds)">
                        {{getAssignedToNames(project?.assignedUserIds)}}
                      </div>
                    </td>
                    <td class="w-70px">
                      <a (click)="openConfirmDeleteModal(project?.name, project?.id)" class="bg-light-red icon-badge"
                        id="clkDeleteBulkReassign" data-automate-id="clkDeleteBulkReassign">
                        <span class="icon ic-delete m-auto ic-xxs"></span></a>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
          <div>
            <div class="border br-20 bg-white align-center user w-max-content mt-10">
              <ng-container *ngFor="let option of leadAssignmentOptions">
                <div
                  *ngIf="option == 'Configuration' || (option != 'Configuration' && ((!canAllowSecondaryUsers && (assignedUser?.length || assignedDuplicateUser?.length)) || ((canAllowSecondaryUsers && canEnableAllowSecondaryUsers) && (assignedPrimaryUsers?.length || assignedSecondaryUsers?.length || assignedDuplicateUser?.length))))"
                  class="activation" [ngClass]="{'active' : selectedSectionLeadAssignment == option}"
                  (click)="selectedSectionLeadAssignment = option; setListSelection();">
                  <span>{{option}}</span>
                </div>
              </ng-container>
            </div>
            <ng-container *ngIf="selectedSectionLeadAssignment === 'Configuration' else selectedUsers">
              <div class="mt-10 mb-4 d-flex w-100 flex-wrap cursor-pointer">
                <div class="w-50"
                  title="This feature will assign a duplicate of the Incoming Lead to a block of  selected user(s) at once."
                  (click)="canEnableAllowDuplicates ? resetIntegrationForm() : openConfirmModal(changePopup, 'allowDuplicateLeads');toggleAssignedUserValidation()">
                  <label class="mb-4 checkbox-container text-gray" [ngClass]="{'pe-none': !canEnableAllowDuplicates}">
                    <input type="checkbox" [(ngModel)]="canAllowDuplicates" [ngModelOptions]="{standalone: true}"
                      (change)="toggleAssignedUserValidation()" />
                    <span class="checkmark"></span>
                    <span class="line-break" [ngClass]="{'text-coal': canEnableAllowDuplicates}">Assign
                      leads as duplicate(s)</span>
                  </label>
                </div>
                <div class="w-50"
                  title="This feature will assign a secondary Owner to a Incoming Lead to a block of  selected user(s) in Sequential Manner."
                  (click)="canEnableAllowSecondaryUsers ? resetIntegrationForm() : openConfirmModal(changePopup, 'allowSecondaryUsers');toggleAssignedUserValidation()">
                  <label class="mb-4 checkbox-container text-gray"
                    [ngClass]="{'pe-none': !canEnableAllowSecondaryUsers}">
                    <input type="checkbox" [(ngModel)]="canAllowSecondaryUsers" [ngModelOptions]="{standalone: true}" />
                    <span class="checkmark"></span>
                    <span class="line-break" [ngClass]="{'text-coal': canEnableAllowSecondaryUsers}">Assign
                      to secondary user</span>
                  </label>
                </div>
              </div>
              <form [formGroup]="integrationDuplicateForm" class="prevent-text-select" autocomplete="off">
                <div [ngClass]="canAllowDuplicates ? 'field-label-req' : 'field-label'" *ngIf="!canAllowSecondaryUsers">
                  {{'SETTINGS.select-user' | translate}}
                </div>
                <form-errors-wrapper [control]="integrationDuplicateForm?.controls?.['assignedUser']"
                  label="{{'SETTINGS.select-user' | translate}}">
                  <ng-select *ngIf="!canAllowSecondaryUsers" [items]="canAssignToAny ? allUserList : userList"
                    ResizableDropdown bindLabel="fullName" bindValue="id" [multiple]="true" appSelectAll [appSelectAllUseItemsList]="true"
                    [closeOnSelect]="false" [clearSearchOnAdd]="true" name="assignedUser"
                    (change)="onSelectionChange($event, 'assignedUser')" placeholder="ex. Mounika Pampana"
                    class="bg-white" [(ngModel)]="assignedUser" formControlName="assignedUser">
                    <ng-template ng-label-tmp let-item="item" let-clear="clear">
                      <span class="ic-cancel ic-dark icon ic-x-xs mr-4"
                          (click)="onClear(item)"></span>
                      <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                  </ng-template>
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                      <div class="checkbox-container">
                          <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                              [checked]="item$.selected" [disabled]="!item.isActive">
                          <span class="checkmark"></span>
                          <span class="text-truncate-1 break-all">{{item.firstName}}
                              {{item.lastName}}</span>
                          <span class="error-message-custom top-13" *ngIf="!item.isActive">( Disabled
                              )</span>
                      </div>
                  </ng-template>
                  </ng-select>
                </form-errors-wrapper>
                <ng-container *ngIf="canAllowDuplicates && canEnableAllowDuplicates && !canAllowSecondaryUsers">
                  <div class="field-label-req"> Select Lead Duplicate User(s)
                  </div>
                  <form-errors-wrapper [control]="integrationDuplicateForm.controls['assignedDuplicateUser']"
                    label="Select Lead Duplicate User(s)">
                    <ng-select [required]="true" [items]="canAssignToAny ? allUserList :  userList"
                      bindLabel="fullName" ResizableDropdown bindValue="id" [multiple]="true" appSelectAll [appSelectAllUseItemsList]="true"
                      [closeOnSelect]="false" [clearSearchOnAdd]="true"
                      (change)="onSelectionChange($event, 'assignedDuplicateUser')" name="assignedDuplicateUser"
                      placeholder="ex. Mounika Pampana" class="bg-white" [(ngModel)]="assignedDuplicateUser"
                      formControlName="assignedDuplicateUser">
                      <ng-template ng-label-tmp let-item="item" let-clear="clear">
                        <div class="flex-between">
                          <div class="align-center m-4">
                            <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                            <span class="ng-value-label text-truncate-1 break-all">{{item.firstName + ' ' +
                              item.lastName}}</span>
                          </div>
                          <span class="error-text" *ngIf="!item.isActive">( Disabled
                            )</span>
                        </div>
                      </ng-template>
                      <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container">
                          <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                            [checked]="item$.selected" [disabled]="!item.isActive">
                          <span class="checkmark"></span>
                          <div class="flex-between">
                            <span class="text-truncate-1 break-all">{{item.firstName}} {{item.lastName}}</span>
                            <span class="error-text" *ngIf="!item.isActive">( Disabled )</span>
                          </div>
                        </div>
                      </ng-template>
                    </ng-select>
                  </form-errors-wrapper>
                  <div class="d-flex">
                    <label class="checkbox-container mt-10" (click)="sameAsSelectedUsersClicked()">
                      <input type="checkbox" [(ngModel)]="sameAsSelectedUsers" [ngModelOptions]="{standalone: true}" />
                      <span class="checkmark"></span>
                      <span class="line-break text-sm ip-pl-10">Same as <span class="fw-600 text-coal">Selected
                          User(s)</span></span>
                    </label>
                  </div>
                  <div class="d-flex">
                    <label class="checkbox-container mt-10">
                      <input type="checkbox" [(ngModel)]="canAssignSequentially"
                        [ngModelOptions]="{standalone: true}" />
                      <span class="checkmark"></span>
                      <span class="line-break text-sm ip-pl-10">Assign duplicate leads sequentially</span>
                    </label>
                  </div>
                </ng-container>
              </form>

              <ng-container *ngIf="canAllowSecondaryUsers && canEnableAllowSecondaryUsers">
                <form [formGroup]="integrationDualOwnerForm" class="prevent-text-select" autocomplete="off">
                  <div class="field-label-req"> Select Primary Owner(s)
                  </div>
                  <form-errors-wrapper [control]="integrationDualOwnerForm?.controls?.['assignedPrimaryUsers']"
                    label="'Select Primary Owner(s)'">
                    <ng-select [required]="true" [items]="canAssignToAny ? allUserList : userList"
                      bindLabel="fullName" ResizableDropdown bindValue="id" [multiple]="true" appSelectAll [appSelectAllUseItemsList]="true"
                      [closeOnSelect]="false" [clearSearchOnAdd]="true"
                      name="assignedPrimaryUsers" formControlName="assignedPrimaryUsers"
                      placeholder="ex. Mounika Pampana" class="bg-white" [(ngModel)]="assignedPrimaryUsers"
                      (change)="onSelectionDualChange($event, 'assignedPrimaryUsers')">
                      <ng-template ng-label-tmp let-item="item" let-clear="clear">
                        <div class="flex-between">
                          <div class="align-center m-4">
                            <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                            <span class="ng-value-label text-truncate-1 break-all">{{item.firstName + ' ' +
                              item.lastName}}</span>
                          </div>
                          <span class="error-text" *ngIf="!item.isActive">( Disabled
                            )</span>
                        </div>
                      </ng-template>
                      <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container">
                          <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                            [checked]="item$.selected" [disabled]="!item.isActive">
                          <span class="checkmark"></span>
                          <div class="flex-between">
                            <span class="text-truncate-1 break-all">{{item.firstName}} {{item.lastName}}</span>
                            <span class="error-text" *ngIf="!item.isActive">( Disabled )</span>
                          </div>
                        </div>
                      </ng-template>
                    </ng-select>
                  </form-errors-wrapper>
                  <div class="field-label-req"> Select Secondary Owner(s)
                  </div>
                  <form-errors-wrapper [control]="integrationDualOwnerForm?.controls?.['assignedSecondaryUsers']"
                    label="Select Secondary Owner(s)">
                    <ng-select [required]="true" [items]="canAssignToAny ? allUserList : userList"
                      bindLabel="fullName" ResizableDropdown bindValue="id" [multiple]="true" appSelectAll [appSelectAllUseItemsList]="true"
                     [closeOnSelect]="false" [clearSearchOnAdd]="true"
                      name="assignedSecondaryUsers" formControlName="assignedSecondaryUsers"
                      placeholder="ex. Mounika Pampana" class="bg-white" [(ngModel)]="assignedSecondaryUsers"
                      (change)="onSelectionDualChange($event, 'assignedSecondaryUsers')">
                      <ng-template ng-label-tmp let-item="item" let-clear="clear">
                        <div class="flex-between">
                          <div class="align-center m-4">
                            <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                            <span class="ng-value-label text-truncate-1 break-all">{{item.firstName + ' ' +
                              item.lastName}}</span>
                          </div>
                          <span class="error-text" *ngIf="!item.isActive">( Disabled
                            )</span>
                        </div>
                      </ng-template>
                      <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container">
                          <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                            [checked]="item$.selected" [disabled]="!item.isActive">
                          <span class="checkmark"></span>
                          <div class="flex-between">
                            <span class="text-truncate-1 break-all">{{item.firstName}} {{item.lastName}}</span>
                            <span class="error-text" *ngIf="!item.isActive">( Disabled )</span>
                          </div>
                        </div>
                      </ng-template>
                    </ng-select>
                  </form-errors-wrapper>
                  <div class="d-flex">
                    <label class="mt-10 checkbox-container" (click)="sameAsSelectedUsersClicked(true)">
                      <input type="checkbox" [(ngModel)]="sameAsPrimaryUsers" [ngModelOptions]="{standalone: true}" />
                      <span class="checkmark"></span>
                      <span class="line-break text-sm ip-pl-10">Same as <span class="fw-600 text-coal">Primary
                          User(s)</span></span>
                    </label>
                  </div>
                  <ng-container *ngIf="canAllowDuplicates && canEnableAllowDuplicates">
                    <div class="field-label-req"> Select Lead Duplicate User(s)
                    </div>
                    <form-errors-wrapper [control]="integrationDualOwnerForm?.controls?.['assignedDuplicateUser']"
                      label="Select Lead Duplicate User(s)">
                      <ng-select [required]="true" [items]="canAssignToAny ? allUserList : userList"
                        bindLabel="fullName" ResizableDropdown bindValue="id" [multiple]="true" appSelectAll [appSelectAllUseItemsList]="true"
                       [closeOnSelect]="false"
                        [clearSearchOnAdd]="true" name="assignedDuplicateUser" placeholder="ex. Mounika Pampana"
                        class="bg-white" formControlName="assignedDuplicateUser" [(ngModel)]="assignedDuplicateUser"
                        (change)="onSelectionDualChange($event, 'assignedDuplicateUser')">
                        <ng-template ng-label-tmp let-item="item" let-clear="clear">
                          <div class="flex-between">
                            <div class="align-center m-4">
                              <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                              <span class="ng-value-label text-truncate-1 break-all">{{item.firstName + ' ' +
                                item.lastName}}</span>
                            </div>
                            <span class="error-text" *ngIf="!item.isActive">( Disabled
                              )</span>
                          </div>
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                          <div class="checkbox-container">
                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                              [checked]="item$.selected" [disabled]="!item.isActive">
                            <span class="checkmark"></span>
                            <div class="flex-between">
                              <span class="text-truncate-1 break-all">{{item.firstName}} {{item.lastName}}</span>
                              <span class="error-text" *ngIf="!item.isActive">( Disabled )</span>
                            </div>
                          </div>
                        </ng-template>
                      </ng-select>
                    </form-errors-wrapper>
                    <div class="d-flex">
                      <label class="mt-10 checkbox-container" (click)="sameAsPrimarySecondaryUsersClicked()">
                        <input type="checkbox" [(ngModel)]="sameAsAbove" [ngModelOptions]="{standalone: true}" />
                        <span class="checkmark"></span>
                        <span class="line-break text-sm ip-pl-10">Same as above</span>
                      </label>
                    </div>
                    <div class="flex-between">
                      <div class="align-center"
                        *ngFor="let userType of ['Primary User(s)', 'Secondary User(s)']; let i = index">
                        <div class="form-check form-check-inline align-center btn pl-0 py-0 mr-0">
                          <input type="radio" id="userType{{ i }}" data-automate-id="userType" name="userType"
                            [checked]="userType === selectedUserType" [value]="userType" (change)="sameAsAbove = false"
                            [(ngModel)]="selectedUserType" [ngModelOptions]="{standalone: true}"
                            class="radio-check-input mr-10" />
                          <label class="fw-600 text-secondary cursor-pointer text-large" for="userType{{ i }}">
                            {{ userType }}</label>
                        </div>
                      </div>
                    </div>
                    <div class="d-flex">
                      <label class="checkbox-container mt-10">
                        <input type="checkbox" [(ngModel)]="canAssignSequentially"
                          [ngModelOptions]="{standalone: true}" />
                        <span class="checkmark"></span>
                        <span class="line-break text-sm ip-pl-10">Assign duplicate leads sequentially</span>
                      </label>
                    </div>
                  </ng-container>
                </form>
              </ng-container>

            </ng-container>
            <ng-template #selectedUsers>
              <div class="mt-10" *ngIf="assignedUser?.length && !canAllowDuplicates && !canAllowSecondaryUsers">
                <div class="d-flex">
                  <div class="cursor-pointer mt-12 text-accent-green">Selected User(s)
                  </div>
                </div>
                <div class="mt-12">
                  <div class="flex-between mb-12" *ngFor="let user of assignedUser; let i = index">
                    <div class="align-center">
                      <div class="dot dot-xl bg-pearl-90 mr-6">
                        <span class="fw-semi-bold text-normal text-white text-uppercase">{{
                          user ?
                          getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.firstName[0] +
                          getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.lastName[0] :
                          '--'}}</span>
                      </div>
                      <div class="fw-semi-bold text-large text-coal">{{ getAssignedToDetails(user, canAssignToAny ?
                        allUserList : userList, true) || '--'}}</div>
                    </div>
                    <div>
                      <span (click)="removeUserFromSelection(user)" class="bg-light-red icon-badge">
                        <span class="icon ic-delete m-auto ic-xxs"></span></span>
                    </div>
                  </div>
                </div>
              </div>
              <div *ngIf="canAllowDuplicates && !canAllowSecondaryUsers">
                <div class="d-flex">
                  <div class="cursor-pointer mr-20 mt-12" *ngIf="assignedUser?.length"
                    (click)="originalDuplicateListToggle('original')"
                    [ngClass]="{'text-accent-green field-label-left-underline-green': listSelection == 'original'}">
                    Original
                    User(s)
                  </div>
                  <div class="cursor-pointer mt-12" *ngIf="assignedDuplicateUser?.length"
                    (click)="originalDuplicateListToggle('duplicate')"
                    [ngClass]="{'text-accent-green field-label-left-underline-green': listSelection == 'duplicate'}">
                    Duplicate User(s)
                  </div>
                </div>
                <div class="mt-12">
                  <div class="flex-between mb-12" *ngFor="let user of filteredUsers; let i = index">
                    <div class="align-center">
                      <div class="dot dot-xl bg-pearl-90 mr-6">
                        <span class="fw-semi-bold text-normal text-white text-uppercase">{{
                          user ?
                          getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.firstName[0] +
                          getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.lastName[0] :
                          '--'}}</span>
                      </div>
                      <div class="fw-semi-bold text-large text-coal">{{ getAssignedToDetails(user, canAssignToAny ?
                        allUserList : userList, true) || '--'}}</div>
                    </div>
                    <div>
                      <span (click)="removeUserFromSelection(user)" class="bg-light-red icon-badge">
                        <span class="icon ic-delete m-auto ic-xxs"></span></span>
                    </div>
                  </div>
                </div>
                <div class="d-flex">

                </div>
              </div>
              <div *ngIf="primarySeondaryUsers?.length && canAllowSecondaryUsers">
                <div class="d-flex">
                  <div class="cursor-pointer mr-20 mt-12" *ngIf="assignedPrimaryUsers?.length"
                    (click)="originalDuplicateListToggle('primary')"
                    [ngClass]="{'text-accent-green field-label-left-underline-green': listSelection == 'primary'}">
                    Primary
                  </div>
                  <div class="cursor-pointer mr-20 mt-12" *ngIf="assignedSecondaryUsers?.length"
                    (click)="originalDuplicateListToggle('secondary')"
                    [ngClass]="{'text-accent-green field-label-left-underline-green': listSelection == 'secondary'}">
                    Secondary
                  </div>
                  <div class="cursor-pointer mt-12" *ngIf="canAllowDuplicates && assignedDuplicateUser?.length"
                    (click)="originalDuplicateListToggle('duplicate')"
                    [ngClass]="{'text-accent-green field-label-left-underline-green': listSelection == 'duplicate'}">
                    Duplicate User(s)
                  </div>
                </div>
                <div class="mt-12">
                  <div class="flex-between mb-12" *ngFor="let user of primarySeondaryUsers; let i = index">
                    <div class="align-center">
                      <div class="dot dot-xl bg-pearl-90 mr-6">
                        <span class="fw-semi-bold text-normal text-white text-uppercase">{{
                          user ?
                          getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.firstName[0] +
                          getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.lastName[0] :
                          '--'}}</span>
                      </div>
                      <div class="fw-semi-bold text-large text-coal">{{ getAssignedToDetails(user, canAssignToAny ?
                        allUserList : userList, true) || '--'}}</div>
                    </div>
                    <div>
                      <span (click)="removeUserFromSelection(user)" class="bg-light-red icon-badge">
                        <span class="icon ic-delete m-auto ic-xxs"></span></span>
                    </div>
                  </div>
                </div>
                <div class="d-flex">

                </div>
              </div>
            </ng-template>
          </div>
          <div class="flex-end px-20 py-10 box-shadow-20 w-100 position-absolute left-0 bottom-0">
            <div class="fw-600 text-black-200 text-decoration-underline cursor-pointer"
              (click)="bulkReassignModalRef.hide()">{{
              'BUTTONS.cancel' | translate }}
            </div>
            <div class="border-right mx-12 h-16"></div>
            <div class="br-4 bg-coal py-10 px-20 text-white cursor-pointer" (click)="userAssignment()">{{ 'BUTTONS.save'
              |
              translate }}</div>
          </div>
        </div>
      </div>
    </ng-template>
    <ng-template #BulkDeleteModal>
      <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
          <h3>{{ 'LEADS.bulk' | translate }} {{ 'BUTTONS.delete' | translate }}</h3>
          <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="bulkDeleteModalRef.hide()">
          </div>
        </div>
        <div class="px-12">
          <div class="field-label mb-10">Selected Project(s) - {{selectedNodes?.length}}</div>
          <div class="flex-column scrollbar max-h-100-150">
            <ng-container *ngFor="let project of selectedNodes">
              <div class="flex-between p-12 fw-600 text-sm border-bottom text-secondary bg-white">
                <span class="text-truncate-1 break-all mr-6"> {{ project.name }}</span>
                <div (click)="openConfirmDeleteModal(project?.name, project?.id)" class="bg-light-red icon-badge"
                  id="clkBulkDelete" data-automate-id="clkBulkDelete">
                  <span class="icon ic-delete m-auto ic-xxs"></span>
                </div>
              </div>
            </ng-container>
          </div>
          <div class="flex-center" (click)="bulkDelete()">
            <button class="btn-coal mt-20 mr-8 px-10 min-w-fit-content" id="bulkdelete" data-automate-id="bulkdelete">
              <span *ngIf="!isBulkDeleting else buttonDots">{{ ('BUTTONS.delete' | translate) }}</span>
            </button>
          </div>
        </div>
      </div>
    </ng-template>
    <ng-template #BulkRestoreModal>
      <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
          <h3>Bulk {{ isPermanentDelete ? ('BUTTONS.delete' | translate) : ('BUTTONS.restore' |translate) }}</h3>
          <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="bulkRestoreModalRef.hide()">
          </div>
        </div>
        <div class="px-12">
          <div class="field-label mb-10">Selected Projects - {{selectedNodes?.length}}</div>
          <div class="flex-column scrollbar max-h-100-150">
            <div class="scrollbar table-scrollbar ph-w-100-60 h-100-150">
              <table class="table standard-table no-vertical-border">
                <thead>
                  <tr class="w-100">
                    <th class="w-120">Project name</th>
                    <th class="w-70px">Associated Leads</th>
                    <th class="w-70px">Associated Data</th>
                    <th class="w-70px">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let row of gridApi?.getSelectedNodes()">
                    <td class="w-120">{{ row?.data?.name? row?.data?.name: '--' }}</td>
                    <td class="w-70px">{{ row?.data?.leadsCount ? row?.data?.leadsCount : '--' }}</td>
                    <td class="w-70px">{{ row?.data?.prospectCount ? row?.data?.prospectCount : '--' }}</td>
                    <td class="w-70px" (click)="openConfirmDeleteModal(row?.data?.name, row?.data?.id)">
                      <a class="bg-light-red icon-badge br-50 ic-large">
                        <span class="icon ic-cancel m-auto ic-xx-xs"></span>
                      </a>
                    </td>
                  </tr>
              </table>
            </div>
          </div>
          <div class="flex-center" (click)="bulkRestoreProjects()">
            <button class="btn-coal mt-20 mr-8 px-10 min-w-fit-content" id="bulkdelete" data-automate-id="bulkdelete">
              <span *ngIf="!isBulkDeleting else buttonDots">{{isPermanentDelete ? 'Delete' : 'Restore'}}</span>
            </button>
          </div>
        </div>
      </div>
    </ng-template>
  </div>
</ng-container>

<ng-template #projectsLoader>
  <div class="flex-center h-100">
    <application-loader></application-loader>
  </div>
</ng-template>

<ng-template #buttonDots>
  <div class="container px-4">
    <ng-container *ngFor="let dot of [1,2,3]">
      <div class="dot-falling dot-white"></div>
    </ng-container>
  </div>
</ng-template>