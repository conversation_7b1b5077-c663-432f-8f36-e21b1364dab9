@media screen and (max-width: 850px) {
    .tbsm-d-none {
        display: none;
    }
}

@media screen and (max-width: 1120px) {
    .mq-top-navbar {
        border-top: 1px solid $dark-400;
        background-color: $white;
        padding: 12px 30px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        position: unset !important;
        top: unset !important;
        right: unset !important;
    }
}

@media screen and (min-width: 1290px) {
    .custom-bg {
        border-radius: 10px;
        margin-right: 40px;
    }
}

@media only screen and (min-width: 1280px) and (max-width: 1550px) {
    .mqd-flex-col {
        display: flex;
        flex-direction: column !important;
    }

    .mqd-align-center-unset {
        align-items: unset !important;
    }

    .mqd-pt-10 {
        padding-top: 10px !important;
    }

    .mqd-w-25 {
        width: 25% !important;
    }
}

@media only screen and (min-width: 1600px) {
    .tv-w-12 {
        width: 12% !important;
    }

    .tv-w-20 {
        width: 20% !important;
    }

    .tv-w-25 {
        width: 25% !important;
    }

    .tv-w-30 {
        width: 30% !important;
    }
}

@media screen and (min-height: 800px) {
    .hmq-h-100-405 {
        height: calc(100vh - 405px) !important;
    }

    .hmq-max-h-100-600 {
        max-height: calc(100vh - 600px) !important;
    }

    .hmq-h-100-665 {
        max-height: calc(100vh - 665px) !important;
    }
}

@media (max-width: 481px) {
    .hidden-web {
        display: none;
    }

    .hidden-phone {
        display: block;
    }
}