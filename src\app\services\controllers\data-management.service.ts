import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { getEnvDetails, getTenantName } from 'src/app/core/utils/common.util';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class DataManagementService extends BaseService<any> {
  serviceBaseUrl: string = '';
  tenant: string = getTenantName();
  headers: HttpHeaders = new HttpHeaders().set('tenant', this.tenant);

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'prospect';
  }

  getAllData() {
    return this.http.get(`${this.serviceBaseUrl}`);
  }

  getDataById(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/${id}`);
  }

  addNewData(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}`, payload, {
      headers: this.headers,
    });
  }

  updateData(id: string, payload: any) {
    return this.http.put(`${this.serviceBaseUrl}?id=${id}`, payload);
  }

  getSourceList() {
    return this.http.get(`${this.serviceBaseUrl}/all/source`, {
      headers: this.headers,
    });
  }

  getSubSourceList() {
    return this.http.get(`${this.serviceBaseUrl}/subsource`);
  }

  getDataIdWithContactNo(contactNo: any) {
    return this.http.get(`${this.serviceBaseUrl}/contactno?contactNo=${contactNo?.number}&countryCode=${contactNo?.countryCode}`, {
      headers: this.headers,
    });
  }

  communicationDataCount(id: string, payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/contactcount/${id}`, payload);
  }

  communicationBulkDataCount(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/bulk/contactcount`, payload);
  }

  bulkDeleteData(ids: string[]) {
    let headers: any = {
      body: {
        ids,
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/bulk/delete`, headers);
  }

  bulkReassign(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/bulk/assign`, payload);
  }

  getDataStatuses() {
    return this.http.get(`${this.serviceBaseUrl}/all/statuses`);
  }

  bulkUpdateStatus(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/status/bulk`, payload);
  }

  bulkSource(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/source/bulk`, payload);
  }

  bulkConvertToLead(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/bulk/convert`, payload);
  }

  convertToLead(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/convert`, payload);
  }

  checkDuplicateBeforeConvertToLead(payload: any, number: string) {
    return this.http.get(`${this.serviceBaseUrl}/leadduplicate?contactNo=${number}`);
  }

  updateStatus(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/update/status`, payload);
  }

  getQRCode() {
    let subDomain: string = getTenantName();
    let url = `https://${subDomain + getEnvDetails()}/external/add-data/qr-code`;
    let params = new HttpParams().set('url', url);
    return this.http.get(`${this.serviceBaseUrl}/qr-code?${params.toString()}`);
  }

  communicationDataMessage(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/message`, payload);
  }

  communicationBulkDataMessage(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/bulk/message`, payload);
  }

  getHistoryById(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/histories/${id}`);
  }

  updateDataNotes(payload: any) {
    return this.http.put(
      `${this.serviceBaseUrl}/notes?id=${payload?.prospectId}`,
      payload
    );
  }

  getAllDataCount() {
    return this.http.get(`${this.serviceBaseUrl}/count`);
  }

  getDataLocations() {
    return this.http.get(`${this.serviceBaseUrl}/addresses`);
  }

  getDataCities() {
    return this.http.get(`${this.serviceBaseUrl}/cities`);
  }

  getDataStates() {
    return this.http.get(`${this.serviceBaseUrl}/states`);
  }

  getDataCountries() {
    return this.http.get(`${this.serviceBaseUrl}/countries`);
  }

  getDataSubCommunities() {
    return this.http.get(`${this.serviceBaseUrl}/subcommunities`);
  }

  getDataCommunities() {
    return this.http.get(`${this.serviceBaseUrl}/communities`);
  }

  getDataTowerNames() {
    return this.http.get(`${this.serviceBaseUrl}/towername`);
  }

  getDataLocalities() {
    return this.http.get(`${this.serviceBaseUrl}/localites`);
  }

  //api no there
  getDataZones() {
    return this.http.get(`${this.serviceBaseUrl}/zones`);
  }

  getExcelUploadedList(pageNumber: number, pageSize: number) {
    return this.http.get(`${this.serviceBaseUrl}/tracker?PageNumber=${pageNumber}&PageSize=${pageSize}`);
  }

  uploadMappedColumns(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/bulk`, payload);
  }

  getMigrateExcelUploadedList(pageNumber: number, pageSize: number) {
    return this.http.get(`${this.serviceBaseUrl}/migrate/bulk/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`);
  }

  uploadMigrateMappedColumns(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/migrate/batch`, payload);
  }

  uploadExcel(selectedFile: File) {
    let formData = new FormData();
    formData.append('file', selectedFile);
    return this.http.post(`${this.serviceBaseUrl}/excel`, formData);
  }

  exportData(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/custom-filter-export`, payload);
  }

  getExportStatus(pageNumber: number, pageSize: Number) {
    return this.http.get(`${this.serviceBaseUrl}/export/tracker?PageNumber=${pageNumber}&PageSize=${pageSize}`);
  }

  restoreData(payload: {
    ids: string[]
  }) {
    return this.http.put(`${this.serviceBaseUrl}/restoreprospect`, payload);
  }

  getDataConversionStatus() {
    return this.http.get(`${this.serviceBaseUrl}/lead/statuses`);
  }

  getDataCurrency() {
    return this.http.get(`${this.serviceBaseUrl}/currency`);
  }

  communicationBulkCount(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/bulk/contactcount`, payload);
  }

  permanentdeleteData(ids: string[]) {
    let headers: any = {
      body: {
        ids: ids,
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/delete`, headers);
  }

  convertToLeadBulk(payload: any[]) {
    return this.http.post(`${this.serviceBaseUrl}/contactnocheck`, payload);
  }

  getDataNationality() {
    return this.http.get(`${this.serviceBaseUrl}/nationality`);
  }

  getDataClusterName() {
    return this.http.get(`${this.serviceBaseUrl}/clustername`);
  }

  getDataUnitName() {
    return this.http.get(`${this.serviceBaseUrl}/unitname`);
  }

  getDataPostalCode() {
    return this.http.get(`${this.serviceBaseUrl}/postalcode`);
  }

  getUploadTypeNameList() {
    return this.http.get(`${this.serviceBaseUrl}/uploadtypename`);
  }

  
  getDataCountryCode() {
    return this.http.get(`${this.serviceBaseUrl}/countrycode`);
  }

  getDataAltCountryCode() {
    return this.http.get(`${this.serviceBaseUrl}/altcountrycode`);
  }
}
