import {
  AfterViewChecked,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { Subscription, takeUntil } from 'rxjs';

import { WA_BUTTON_TYPE } from 'src/app/app.constants';
import { FolderNamesS3, WAButtonType, WhatsAppEvents } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { WAWrapperDto } from 'src/app/core/interfaces/leads.interface';
import {
  LeadTemplateMsg,
  ProjectTemplateMsg,
  PropertyTemplateMsg,
  WhatsAppTemplateMsg,
  changeCalendar,
  getAWSImagePath,
  getAssignedToDetails,
  getTenantName,
  getTimeZoneDate,
  isUrl
} from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import {
  FetchProjectById,
  FetchProjectIdWithName,
} from 'src/app/reducers/project/project.action';
import {
  getProjectsIDWithName,
  getProjectsIDWithNameIsLoading,
  getSelectedProjectById,
} from 'src/app/reducers/project/project.reducer';
import {
  FetchPropertyById,
  FetchPropertyWithIdNameList,
} from 'src/app/reducers/property/property.actions';
import {
  getPropertyListDetails,
  getPropertyWithIdLoading,
  getPropertyWithIdNameList,
} from 'src/app/reducers/property/property.reducer';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import {
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { FetchTemplateModule } from 'src/app/reducers/template/template.actions';
import { getTemplatesModule } from 'src/app/reducers/template/template.reducer';
import {
  Fetch24HrValidation,
  FetchConversation,
  FetchWhatsappTemplate,
} from 'src/app/reducers/whatsapp/whatsapp.actions';
import {
  get24HrValidation,
  getConversation,
  getConversationIsLoading,
  getWhatsAppTemplates,
} from 'src/app/reducers/whatsapp/whatsapp.reducer';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { TenantService } from 'src/app/services/controllers/tenant.service';
import { SignalRService } from 'src/app/services/shared/web-socket.service';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';

@Component({
  selector: 'whatsapp-chat',
  templateUrl: './whatsapp-chat.component.html',
})
export class WhatsappChatComponent
  implements OnInit, OnDestroy, AfterViewChecked {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  msgForm: FormGroup;
  templateForm: FormGroup;
  searchText = '';
  @Input() clickedData: any = null;
  @Input() whatsAppComp: boolean = false;
  data: any;
  globalSettingsData: any;
  noConversation: AnimationOptions = {
    path: 'assets/animations/whatsApp-chat.json',
  };
  showTempSelectionSec: boolean = false;
  selectedTempVariablesSec: boolean = false;
  selectedTempDataSec: boolean = false;
  templateType = ['WhatsApp Templates', 'CRM Templates'];
  selectedTemplateType = 'WhatsApp Templates';
  selectedTemplate: any;
  message: string;
  to: string;
  webSocketSubscription: Subscription;
  whatsAppTemplateList: Array<any> = [];
  CRMTemplateList: Array<any> = [];
  conversationList: Array<any> = [];
  isMsgSentIn24Hr: boolean;
  allUsers: Array<any> = [];
  getAssignedToDetails = getAssignedToDetails;
  getTimeZoneDate = getTimeZoneDate;
  WhatsAppEvents = WhatsAppEvents;
  @ViewChild('scrollToBottom') private chatContainer: ElementRef;
  originalWhatsAppTemplateList: any;
  originalCRMTemplateList: any;
  galleryImageArray: any;
  allImagePath: string;
  galleryS3Paths: any;
  allImagePathArr: any[];
  allVideoPath: string;
  galleryS3PathsDoc: any;
  allDocPathArr: any[];
  vidPathUrl: string;
  galleryS3PathsVid: any;
  allVidPathArr: any[];
  searchTerm = '';
  defaultCurrency: any;
  selectedFile: any;
  selectedWhatsAppNo: any;
  chatScrolled: boolean = false;
  selectedFileName: string;
  showImagePreview: boolean = false;
  uploadedImageURL: string;
  mediaType: string;
  showDocPreview: boolean = false;
  uploadedDoc: any;
  tenantId: string = getTenantName();
  headersList: any;
  moment = moment;
  isConversationLoading: boolean;
  userData: any;
  WAButtonType = WAButtonType;
  isLastMsgView: boolean = false;
  isReceiveNewMsg: boolean = false;
  currentDate: Date = new Date();
  selectedCRMTab: string = 'lead';
  isTemplatesLoading: boolean = true;
  propertyList: any[] = [];
  propertyListIsLoading: boolean = true;
  projectList: any[] = [];
  projectListIsLoading: boolean = true;
  projectData: any = {};
  propertyData: any = {};
  isSelectProjectProperty: boolean = false;
  areaSizeUnits: any;
  tenantName: string = '';
  allUserList: any[] = [];

  constructor(
    private SignalRService: SignalRService,
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    private formBuilder: FormBuilder,
    private _notificationService: NotificationsService,
    private s3UploadService: BlobStorageService,
    private sanitizer: DomSanitizer,
    private store: Store<AppState>,
    private tenantService: TenantService
  ) {
    this.msgForm = this.formBuilder.group({
      sendingMsg: [null, Validators.required],
    });
    this.templateForm = this.formBuilder.group({
      sendingTemplate: [null, Validators.required],
    });

    // let userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    this.store.dispatch(new FetchWhatsappTemplate());
    this.store.dispatch(new FetchTemplateModule(0));
    this.store.dispatch(new FetchUsersListForReassignment());
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.clickedData && changes.clickedData.currentValue) {
      this.data = this.clickedData;
      this.fetchConversation();
      this.check24HrsValidity();
    }
  }

  ngOnInit() {
    this.selectedWhatsAppNo = 'primary';
    if (!this.data?.alternateContactNo) {
      let payload: any = {
        CustomerId: this.data?.id,
        CustomerNo: this.data?.contactNo,
      };
      if (payload?.CustomerNo)
        this.store.dispatch(new FetchConversation(payload));
    }

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.tenantName = this.userData?.organizationName;
      });

    this.store
      .select(getConversationIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isConversationLoading = isLoading;
      });
    // this.connectWebSocket();
    this.check24HrsValidity();
    this.initGetters();
    // this.scrollToBottom();
    this.SignalRService.getMessageListener().subscribe(
      (message: WAWrapperDto) => {
        this.handleIncomingMessage(message);
      }
    );

    this.store.dispatch(new FetchAreaUnitList());
    this.store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });
    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((user: any) => {
        this.allUserList = user;
      });

  }

  fetchConversation() {
    let payload: any = {
      CustomerId: this.data?.id,
      CustomerNo:
        this.selectedWhatsAppNo == 'primary'
          ? this.data?.contactNo
          : this.data?.alternateContactNo,
    };
    if (payload?.CustomerNo)
      this.store.dispatch(new FetchConversation(payload));
  }

  handleIncomingMessage(message: any) {
    if (
      (typeof message === 'string' && message?.includes('joined in')) ||
      message?.data == true ||
      message?.data == false
    ) {
      return; // Do not add system messages to the list
    }

    const newDataArray = message.data?.filter(
      (msg: any) => msg.customerId === this.data?.id
    );
    if (!newDataArray || newDataArray.length === 0) {
      console.warn('Invalid message data format:', message);
      return; // Handle invalid data structure
    }

    const newData = newDataArray[0];
    if (
      this.selectedWhatsAppNo == 'primary'
        ? newData.customerNo !== this.data?.contactNo
        : newData.customerNo !== this.data?.alternateContactNo
    ) {
      return;
    }

    const existingMessageIndex = this.conversationList.findIndex(
      (m: any) =>
        m.customerId === newData.customerId && m.messageId === newData.messageId
    );

    if (existingMessageIndex !== -1) {
      if (newData.waEvent == WhatsAppEvents.Receive) {
        this.isMsgSentIn24Hr = true;
      }
      // Update the existing message by creating a new array with updated data
      this.conversationList = [
        ...this.conversationList.slice(0, existingMessageIndex),
        newData,
        ...this.conversationList.slice(existingMessageIndex + 1),
      ];
    } else {
      if (newData.waEvent == WhatsAppEvents.Receive) {
        this.isMsgSentIn24Hr = true;
        this.isReceiveNewMsg = true;
      }
      // Append the new message by creating a new array
      if (newData.customerId === this.data?.id) {
        this.conversationList = [...this.conversationList, newData];
      }
      if (this.isLastMsgView) {
        this.scrollToBottom();
      }
    }
  }

  ngAfterViewChecked() {
    this.scrollToBottom();
  }

  connectWebSocket() {
    this.SignalRService.startConnection();
  }

  check24HrsValidity() {
    let payload: any = {
      customerId: this.data?.id,
      utcCurrentDateTime: getTimeZoneDate(this.currentDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'ISO'),
    };
    this.store.dispatch(new Fetch24HrValidation(payload));
  }

  initGetters() {
    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUsers = data;
        this.allUsers = this.allUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
      });

    this.store
      .select(get24HrValidation)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isMsgSentIn24Hr = data;
      });

    this.store
      .select(getConversation)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.conversationList = data;
        this.chatScrolled = false;
      });

    this.store
      .select(getWhatsAppTemplates)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res != '' && res != undefined) {
          this.originalWhatsAppTemplateList = res;
          this.whatsAppTemplateList = res?.waTemplates
            .filter((data: any) => data)
            .slice()
            .sort((a: any, b: any) => a.title.localeCompare(b.title));
          this.headersList = res?.waApiInfoForTextMessaging?.headerTypes;
        }
      });

    this.store
      .select(getTemplatesModule)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res != '' && res != undefined) {
          this.originalCRMTemplateList = res;
          this.CRMTemplateList = res?.templates
            .filter((data: any) => data)
            .slice()
            .sort((a: any, b: any) => a.title.localeCompare(b.title));
        }
      });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data;
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
      });
  }

  scrollToBottom(): void {
    try {
      const chatContainer = document.getElementById('chatContainer');
      if (chatContainer && this.chatScrolled === false) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
        setTimeout(() => {
          this.chatScrolled = true;
        }, 2000);
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  onScroll(): void {
    const chatContainer = document.getElementById('chatContainer');
    if (chatContainer) {
      const isAtBottom =
        chatContainer.scrollHeight - chatContainer.scrollTop ===
        chatContainer.clientHeight;
      this.isLastMsgView = isAtBottom;
      if (isAtBottom) {
        this.isReceiveNewMsg = false;
      }
    }
  }

  setState(
    variable:
      | 'showTempSelectionSec'
      | 'selectedTempVariablesSec'
      | 'selectedTempDataSec',
    value: boolean
  ) {
    if (value) {
      this.showTempSelectionSec = false;
      this.selectedTempVariablesSec = false;
      this.selectedTempDataSec = false;
    }
    if (
      !this.isSelectProjectProperty &&
      this.selectedCRMTab !== 'lead' &&
      variable === 'selectedTempVariablesSec'
    ) {
      this.showTempSelectionSec = true;
      return;
    }
    if (variable === 'showTempSelectionSec')
      this.isSelectProjectProperty = false;
    this[variable] = value;
    this.msgForm.controls['sendingMsg'].setValue(null);
  }

  chooseWhatsAppNo(whatsAppModal: TemplateRef<any>) {
    this.chatScrolled = false;
    if (this.modalRef) {
      this.modalRef.hide();
    }
    if (
      this.selectedWhatsAppNo == 'alternate' &&
      !this.data?.alternateContactNo.includes('+')
    ) {
      this._notificationService.warn(`country code is required`);
      return;
    } else if (!this.data?.contactNo.includes('+')) {
      this._notificationService.warn(`country code is required`);
      return;
    }

    this.modalRef = this.modalService.show(whatsAppModal, {
      class: 'modal-550 right-modal ip-modal-unset',
    });
    this.fetchConversation();
  }

  onKeyup(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage('text');
    }
  }

  sendMessage(section: string) {
    if (section === 'text') {
      if (
        this.msgForm.invalid &&
        !this.msgForm.get('sendingMsg')?.value?.trim()
      )
        return;
    } else {
      if (
        this.templateForm.invalid &&
        !this.msgForm.get('sendingTemplate')?.value?.trim()
      ) {
        this._notificationService.warn('Message Required');
        return;
      }
    }
    const tenantId = this.tenantId;
    const userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    const media: any = {
      fileName: '',
      mediaUrl: '',
      mediaType: this.headersList?.['Text'],
    };
    const normalText = {
      message: this.msgForm.get('sendingMsg')?.value,
    };
    let customerNo = !this.data.alternateContactNo
      ? this.data.contactNo
      : this.selectedWhatsAppNo == 'primary'
        ? this.data.contactNo
        : this.data.alternateContactNo;
    let waWrapperDto: WAWrapperDto = {
      ...(this.selectedTemplateType == 'WhatsApp Templates'
        ? {
          templateName: this.selectedTemplate?.title
            ? this.selectedTemplate?.title
            : null,
        }
        : {}),
      customerId: this.data?.id,
      customerNo: customerNo,
      userId: userId,
      tenantId: tenantId,
      waPayloadMapping: {
        ...this.originalWhatsAppTemplateList?.waPayloadMapping,
      },
      wAButtons: this.selectedTemplate?.waButtons || [],
    };
    if (
      this.selectedTemplateType == 'WhatsApp Templates' &&
      !this.msgForm.get('sendingMsg')?.value
    ) {
      //WA Template
      waWrapperDto = {
        ...waWrapperDto,
        waApiInfo: {
          ...this.selectedTemplate?.waApiInfo,
          jsonPayload: WhatsAppTemplateMsg(
            this.selectedTemplate,
            { ...this.data, contactNo: customerNo },
            '',
            this.defaultCurrency,
            this.selectedTemplate?.waApiInfo?.jsonPayload,
            null,
            this.allUsers,
            this.userData,
            this.currentDate
          ),
        },
        mediaType: this.headersList?.[this.selectedTemplate?.mediaType],
        mediaURL: this.selectedTemplate?.mediaURL,
        message: LeadTemplateMsg(
          this.selectedTemplate?.message,
          { ...this.data, contactNo: customerNo },
          '',
          this.defaultCurrency,
          this.selectedTemplate?.header,
          this.selectedTemplate?.footer,
          this.allUsers,
          this.userData,
          this.currentDate
        ),
      };
    } else if (
      this.selectedTemplateType != 'WhatsApp Templates' &&
      !this.msgForm.get('sendingMsg')?.value
    ) {
      let msg = this.templateForm.get('sendingTemplate')?.value;
      let selectedTemplate = {
        ...this.selectedTemplate,
      };
      selectedTemplate.message =
        this.templateForm.get('sendingTemplate')?.value;
      //CRM Template
      waWrapperDto = {
        ...waWrapperDto,
        waApiInfo: {
          ...this.originalWhatsAppTemplateList?.waApiInfoForTextMessaging,
          jsonPayload: WhatsAppTemplateMsg(
            selectedTemplate,
            { ...this.data, contactNo: customerNo },
            '',
            this.defaultCurrency,
            this.originalWhatsAppTemplateList?.waApiInfoForTextMessaging
              ?.jsonPayload,
            media,
            this.allUsers,
            this.userData,
            this.currentDate
          ),
        },
        message: LeadTemplateMsg(
          selectedTemplate.message,
          this.data,
          '',
          this.defaultCurrency,
          this.selectedTemplate?.header,
          this.selectedTemplate?.footer,
          this.allUsers,
          this.userData
        ),
      };
    } else if (this.msgForm.get('sendingMsg')?.value) {
      //Text Message
      waWrapperDto = {
        ...waWrapperDto,
        waApiInfo: {
          ...this.originalWhatsAppTemplateList?.waApiInfoForTextMessaging,
          jsonPayload: WhatsAppTemplateMsg(
            normalText,
            { ...this.data, contactNo: customerNo },
            '',
            this.defaultCurrency,
            this.originalWhatsAppTemplateList?.waApiInfoForTextMessaging
              ?.jsonPayload,
            media,
            this.allUsers,
            this.userData,
            this.currentDate
          ),
        },
        message: WhatsAppTemplateMsg(
          this.msgForm.get('sendingMsg')?.value,
          { ...this.data, contactNo: customerNo },
          '',
          this.defaultCurrency,
          this.msgForm.get('sendingMsg')?.value,
          media,
          this.allUsers,
          this.userData,
          this.currentDate
        ),
      };
    }

    this.SignalRService.sendMessageToGroup(tenantId, waWrapperDto);

    this.msgForm.reset();
    this.templateForm.reset();
    this.check24HrsValidity();
    this.showTempSelectionSec = false;
    this.selectedTempVariablesSec = false;
    this.selectedTempDataSec = false;
    this.selectedTemplate = null;
    this.chatScrolled = false;
    this.scrollToBottom();
  }

  onTemplateTypeChange(type: string) {
    this.selectedTemplateType = type;
    this.searchTemplates(this.searchTerm);
  }

  onInputChange(searchTerm: string) {
    this.searchTerm = searchTerm;
    this.searchTemplates(searchTerm);
  }

  searchTemplates(searchTerm: string) {
    if (searchTerm.trim() === '') {
      this.whatsAppTemplateList = this.originalWhatsAppTemplateList?.waTemplates
        .filter((data: any) => data)
        .slice()
        .sort((a: any, b: any) => a.title.localeCompare(b.title));
      this.CRMTemplateList = this.originalCRMTemplateList?.templates
        .filter((data: any) => data)
        .slice()
        .sort((a: any, b: any) => a.title.localeCompare(b.title));
    } else {
      if (this.selectedTemplateType === 'WhatsApp Templates') {
        this.whatsAppTemplateList =
          this.originalWhatsAppTemplateList?.waTemplates
            .filter((data: any) => data)
            .slice()
            .sort((a: any, b: any) => a.title.localeCompare(b.title))
            ?.filter((template: any) =>
              this.templateContainsSearchTerm(template, searchTerm)
            );
      } else {
        this.CRMTemplateList = this.originalCRMTemplateList?.templates
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.title.localeCompare(b.title))
          ?.filter((template: any) =>
            this.templateContainsSearchTerm(template, searchTerm)
          );
      }
    }
  }

  templateContainsSearchTerm(template: any, searchTerm: string): boolean {
    return (
      template?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template?.header?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template?.message?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template?.footer?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }

  fetchTemplateList() {
    this.searchTerm = '';
    if (this.selectedTemplateType == 'WhatsApp Templates') {
      this.store.dispatch(new FetchWhatsappTemplate());
    } else {
      this.store.dispatch(new FetchTemplateModule(0));
    }
  }

  chosenTemplate(template: any) {
    if (
      !this.isSelectProjectProperty &&
      this.selectedCRMTab !== 'lead' &&
      this.selectedTemplateType !== 'WhatsApp Templates'
    ) {
      this._notificationService.alert(`Please select ${this.selectedCRMTab}!`);
      return;
    }
    this.selectedTempVariablesSec = true;
    this.selectedTemplate = template;
    let content = LeadTemplateMsg(
      template?.message,
      this.data,
      '',
      this.defaultCurrency,
      template?.header,
      template?.footer,
      this.allUsers,
      this.userData,
      this.currentDate
    );

    if (this.selectedCRMTab === 'project') {
      content = ProjectTemplateMsg(
        this.selectedTemplate?.message,
        this.projectData,
        this.areaSizeUnits,
        this.tenantName,
        this.selectedTemplate?.header,
        this.selectedTemplate?.footer,
        this.allUserList,
        'share-project',
        this.userData,
        this.currentDate
      );
    } else if (this.selectedCRMTab === 'property') {
      content = PropertyTemplateMsg(
        this.selectedTemplate?.message,
        this.propertyData,
        this.areaSizeUnits,
        this.tenantName,
        this.selectedTemplate?.header,
        this.selectedTemplate?.footer,
        true,
        'share-property',
        this.userData,
        this.currentDate
      );
    }
    this.templateForm.controls['sendingTemplate'].setValue(
      content.replace(/\\n/g, '\n')
    );
  }

  clearTemplate() {
    this.templateForm.controls['sendingTemplate'].setValue(null);
  }

  uploadMedia(e: any, type: string) {
    this.mediaType = type;
    if (type === 'Image' || type === 'Video') {
      let fileSize = e;
      if (fileSize[0][1]?.length > 30) {
        this._notificationService.warn(
          `File Name value can be at max 30 characters.`
        );
        return;
      }

      if (fileSize && Array.isArray(fileSize)) {
        if (fileSize.some((size) => size[2] > 5000000)) {
          this._notificationService.warn(`File size should be less than 5 MB.`);
          return;
        }
        this.showImagePreview = true;
        this.showDocPreview = false;
        this.uploadedImageURL = e[0][0];
        this.galleryImageArray = e;
        return;
      }
    } else if (type === 'Document') {
      let fileSize = e;
      if (!fileSize) {
        return;
      }

      if (fileSize?.name?.length > 30) {
        this._notificationService.warn(
          `File Name value can be at max 30 characters.`
        );
        return;
      }

      if (fileSize.size > 5000000) {
        this._notificationService.warn(`File size should be less than 5 MB.`);
        return;
      }
      this.showDocPreview = true;
      this.showImagePreview = false;
      this.uploadedDoc = e;
    }
  }

  sendImage() {
    if (this.galleryImageArray?.length) {
      let imagesToBeUploadToS3Bucket = this.galleryImageArray
        .filter((imagePath: string) => {
          return imagePath[0].startsWith('data:');
        })
        .map((imagePath: string) => imagePath[0]);

      if (imagesToBeUploadToS3Bucket.length) {
        this.s3UploadService
          .uploadImageBase64(imagesToBeUploadToS3Bucket, FolderNamesS3.WhatsApp)
          .pipe(takeUntil(this.stopper))
          .subscribe((res: any) => {
            if (res?.data) {
              let pathArr = res?.data;

              pathArr?.map((path: string, index: number) => {
                this.allImagePath = getAWSImagePath(path);
                this.galleryS3Paths?.push({
                  fileName: this.galleryImageArray[index][1],
                  mediaUrl: this.allImagePath,
                  mediaType: this.headersList?.[this.mediaType],
                });
              });
              this.sendMedia({
                fileName: this.galleryImageArray[0][1],
                mediaUrl: this.allImagePath,
                mediaType: this.headersList?.[this.mediaType],
              });
            }
          });
      }
    }
  }

  sendDoc() {
    this.s3UploadService
      .uploadDoc(this.uploadedDoc, FolderNamesS3.WhatsApp)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res?.data) {
          let pathArr = res?.data;

          pathArr?.map((path: string, index: number) => {
            this.allImagePath = getAWSImagePath(path);
            this.galleryS3Paths?.push({
              fileName: this.selectedFileName,
              mediaUrl: this.allImagePath,
              mediaType: this.headersList?.[this.mediaType],
            });
          });
          this.sendMedia({
            fileName: this.selectedFileName,
            mediaUrl: this.allImagePath,
            mediaType: this.headersList?.[this.mediaType],
          });
        }
      });
  }

  getSanitizedHtml(msg: string) {
    let splitMessage: string[] = msg.split(' ');
    msg = splitMessage
      .map((message: string) => {
        if (isUrl(message)) {
          const ref: string = message?.includes('http')
            ? message
            : 'http://' + message;
          return `<a class='cursor-pointer text-decoration-underline' href='${ref}' target='_blank'>${message}</a>`;
        }

        return message;
      })
      .join(' ');
    msg = msg.replace(/\\n/g, '<br>');
    msg = msg.replace('<script', '<noscript');
    return msg.replace(/\\<br>/g, '<br>');
  }

  sendMedia(mediaPayload: any) {
    const tenantId = this.tenantId;
    const userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    const media: any = {
      ...mediaPayload,
    };
    let selectedTemplate = {
      ...this.selectedTemplate,
      message: this.msgForm.get('sendingMsg').value || media.fileName,
    };
    let customerNo = !this.data.alternateContactNo
      ? this.data.contactNo
      : this.selectedWhatsAppNo == 'primary'
        ? this.data.contactNo
        : this.data.alternateContactNo;

    let waWrapperDto: WAWrapperDto = {
      templateName: this.selectedTemplate?.title
        ? this.selectedTemplate?.title
        : null,
      customerId: this.data?.id,
      customerNo: customerNo,
      userId: userId,
      tenantId: tenantId,
      message: this.msgForm.get('sendingMsg').value || media.fileName,
      waPayloadMapping: {
        ...this.originalWhatsAppTemplateList?.waPayloadMapping,
      },
      waApiInfo: {
        ...this.originalWhatsAppTemplateList?.waApiInfoForTextMessaging,
        jsonPayload: WhatsAppTemplateMsg(
          selectedTemplate,
          { ...this.data, contactNo: customerNo },
          '',
          this.defaultCurrency,
          this.originalWhatsAppTemplateList?.waApiInfoForTextMessaging
            ?.jsonPayload,
          media,
          this.allUsers,
          this.userData,
          this.currentDate
        ),
      },
      mediaType: this.headersList?.[media?.mediaType],
      mediaURL: media?.mediaUrl,
      mediaName: media?.fileName,
    };

    this.SignalRService.sendMessageToGroup(tenantId, waWrapperDto);

    this.msgForm.reset();
    this.templateForm.reset();
    this.check24HrsValidity();
    this.showTempSelectionSec = false;
    this.selectedTempVariablesSec = false;
    this.selectedTempDataSec = false;
    this.selectedTemplate = null;
    this.closeImagePreview();
    this.chatScrolled = false;
    this.scrollToBottom();
  }

  closeImagePreview() {
    this.msgForm.reset();
    this.showDocPreview = false;
    this.showImagePreview = false;
    this.mediaType = '';
    this.galleryImageArray = [];
    this.selectedFileName = '';
    this.uploadedImageURL = '';
  }

  handleFileInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      this.uploadMedia(file, 'Document');
      this.selectedFileName = file.name;

      input.value = '';
    }
  }

  bytesToMegabytes(bytes: number): number {
    const bytesInOneMB = 1024 * 1024;
    const megabytes = bytes / bytesInOneMB;
    return Math.round(megabytes * 100) / 100;
  }

  openLeadPreviewModal() {
    const initialState: any = {
      data: this.data,
      selectedSection: 'Overview',
      closeLeadPreviewModal: () => {
        leadPreview.hide();
      },
    };
    var leadPreview = this.modalService.show(LeadPreviewComponent, {
      initialState: initialState,
      class: 'right-modal modal-550 ip-modal-unset',
    });
  }

  sortButtons(waButtons: any[]) {
    return waButtons.slice().sort((a, b) => a.orderRank - b.orderRank);
  }

  getButtonIcon(type: WAButtonType) {
    const buttonTypeString = WAButtonType[type]; // Convert enum number to string
    const buttonType = WA_BUTTON_TYPE.find(
      (bt: any) => bt.type === buttonTypeString
    );
    return buttonType ? buttonType.icon : 'ic-default';
  }

  onButtonClick(btn: any) {
    switch (btn.type) {
      case WAButtonType.None:
      case WAButtonType.QuickReply:
        break;

      case WAButtonType.PHONE_NUMBER:
        if (btn.value) {
          window.location.href = 'tel:' + btn.value;
        }
        break;

      case WAButtonType.URL:
        if (btn.value) {
          window.open(btn.value, '_blank');
        }
        break;

      case WAButtonType.COPY_CODE:
        if (btn.value) {
          navigator.clipboard?.writeText(btn.value);
        }
        break;

      default:
        console.warn('Unhandled button type:', btn.type);
    }
  }

  selectCRMTab(tab: string) {
    this.selectedTemplate = null;
    this.isSelectProjectProperty = false;
    let moduleId: number;
    switch (tab) {
      case 'lead':
        moduleId = 0;
        break;
      case 'project':
        moduleId = 5;
        break;
      default:
        moduleId = 6;
    }

    this.store.dispatch(new FetchTemplateModule(moduleId));
    this.subscribeToTemplateModule();

    if (tab === 'property') {
      this.fetchAndSubscribeToList(
        FetchPropertyWithIdNameList,
        getPropertyWithIdNameList,
        'propertyList',
        getPropertyWithIdLoading,
        'propertyListIsLoading',
        true
      );
    } else if (tab === 'project') {
      this.fetchAndSubscribeToList(
        FetchProjectIdWithName,
        getProjectsIDWithName,
        'projectList',
        getProjectsIDWithNameIsLoading,
        'projectListIsLoading',
        false
      );
    }
    this.selectedCRMTab = tab;
  }

  fetchAndSubscribeToList(
    action: any,
    selector: any,
    listProperty: keyof this,
    loadingSelector: any,
    loadingProperty: keyof this,
    isProject: boolean = false
  ): void {
    this.store.dispatch(new action());

    this.store
      .select(selector)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data) {
          if (isProject) {
            (this[listProperty] as any) = data
              ?.slice()
              .sort((a: any, b: any) => {
                const titleA = a.title || '';
                const titleB = b.title || '';
                return titleA.localeCompare(titleB);
              });
          } else {
            (this[listProperty] as any) = data
              ?.slice()
              .sort((a: any, b: any) => {
                const nameA = a.name || '';
                const nameB = b.name || '';
                return nameA.localeCompare(nameB);
              });
          }
        }
      });

    this.store
      .select(loadingSelector)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        (this[loadingProperty] as any) = isLoading;
      });
  }

  subscribeToTemplateModule(): void {
    this.store
      .select(getTemplatesModule)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        this.isTemplatesLoading = res?.isTemplatesLoading;
        if (res?.templates) {
          this.CRMTemplateList = res.templates
            .slice()
            .filter((data: any) => data)
            .sort((a: any, b: any) => a.title.localeCompare(b.title));
        }
      });
  }

  onProjectSelect(selectedProject: any): void {
    if (selectedProject) {
      this.isSelectProjectProperty = true;
      const projectId = selectedProject.id;
      if (projectId) {
        this.store.dispatch(new FetchProjectById(projectId));
      }
      this.store
        .select(getSelectedProjectById)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          if (data) {
            this.projectData = data;
          }
        });
    }
  }

  onPropertySelect(selectedProperty: any): void {
    if (selectedProperty) {
      this.isSelectProjectProperty = true;
      const propertyId = selectedProperty.id;
      if (propertyId) {
        this.store.dispatch(new FetchPropertyById(propertyId));
      }
      this.store
        .select(getPropertyListDetails)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          if (data) {
            this.propertyData = data;
          }
        });
    }
  }

  ngOnDestroy() {
    if (this.webSocketSubscription) {
      this.webSocketSubscription.unsubscribe();
    }
    this.stopper.next();
    this.stopper.complete();
  }
}
