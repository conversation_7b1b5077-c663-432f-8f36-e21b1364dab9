<div class="justify-center">
    <div class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 tb-flex-col z-index-2"
        [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}">
        <div class="flex-center" *ngIf="currentPath === '/properties/manage-properties'">
            <div class=" fw-600 text-coal mr-20 text-xl">{{gridApi?.getSelectedNodes()?.length}}
                {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' | translate}}
            </div>
            <ng-container *ngIf="isViewAllProperties">
                <button class="btn-bulk" *ngIf="canBulkReassign" (click)="openBulkReassignModal(BulkReassignModal)"
                    [disabled]="gridApi?.getSelectedNodes()?.length < 1">
                    {{ 'GLOBAL.reassign' | translate }}
                </button>
                <button class="btn-bulk" (click)="openShareExternalModal()" *ngIf="canBulkShare">Bulk
                    Share</button>
                <button *ngIf="canBulkDelete" class="btn-bulk-red"
                    (click)="openBulkDeleteModal(BulkDeleteModal,true)">Bulk
                    Delete</button>
            </ng-container>
            <button *ngIf="canBulkRestore && !isViewAllProperties" class="btn-bulk-blue"
                (click)="openBulkDeleteModal(BulkDeleteModal)">Bulk Restore</button>
            <button *ngIf="canPermanentDelete && !isViewAllProperties" title="Delete Permanently"
                class="btn btn-danger btn-sm text-xs fw-semi-bold px-16 text-nowrap tb-mb-10 ip-p-8 ml-4"
                (click)="openBulkDeleteModal(BulkDeleteModal,true)">Bulk Delete</button>
        </div>
        <div class="d-flex scrollbar max-w-100-260 tb-max-w-100-190 ip-max-w-100-70 scroll-hide">
            <div class="flex-center" *ngIf="currentPath === '/properties/manage-listing'">
                <div class="fw-600 text-coal mr-20 text-xl">{{gridApi?.getSelectedNodes()?.length}}
                    {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' | translate}}
                </div>
                <ng-container *ngIf="filtersPayload?.PropertyVisiblity !== 4">
                    <button
                        *ngIf="filtersPayload?.PropertyVisiblity !== 2 && filtersPayload?.PropertyVisiblity !== 5 && canBulkList"
                        class="btn-bulk" (click)="openListProperty('list')"
                        [disabled]="gridApi?.getSelectedNodes()?.length < 2">
                        List</button>
                    <button *ngIf="filtersPayload?.PropertyVisiblity !== 5 && canBulkDeList" class="btn-bulk"
                        (click)="openListProperty('delist')" [disabled]="gridApi?.getSelectedNodes()?.length < 2">
                        Delist</button>
                    <button *ngIf="filtersPayload?.PropertyVisiblity === 2 && canBulkModifyListing" class="btn-bulk"
                        (click)="openListProperty('modify')" [disabled]="gridApi?.getSelectedNodes()?.length < 1">
                        Modify Listing</button>
                    <button class="btn-bulk" *ngIf="canBulkReassign" (click)="openBulkReassignModal(BulkReassignModal)"
                        [disabled]="gridApi?.getSelectedNodes()?.length < 1">
                        {{ 'GLOBAL.reassign' | translate }}
                    </button>
                    <button class="btn-bulk" (click)="openShareExternalModal()" *ngIf="canBulkShare">Bulk
                        Share</button>
                    <button *ngIf="canBulkDelete" class="btn-bulk-red" title="Delete Temporarily"
                        (click)="openBulkDeleteModal(BulkDeleteModal, true)">Bulk Delete</button>
                </ng-container>
                <button *ngIf="canBulkRestore && shouldShowBulkRestore" class="btn-bulk-blue"
                    (click)="openBulkDeleteModal(BulkDeleteModal)">Bulk Restore</button>
                <button *ngIf="canPermanentDelete && shouldShowBulkRestore" title="Delete Permanently"
                    class="btn-bulk-red" (click)="openBulkDeleteModal(BulkDeleteModal,true)">Bulk Delete </button>
            </div>
        </div>
    </div>
</div>
<ng-template #BulkReassignModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{ 'LEADS.bulk' | translate }} {{ 'GLOBAL.reassign' | translate }} Properties</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
        </div>
        <div class="px-24 scrollbar h-100-108">
            <div class="fw-600 text-coal text-large my-8">{{'PROPERTY.selected-property' | translate}} -
                {{selectedBulkReassign?.length}}</div>
            <div class="scrollbar table-scrollbar ip-w-100-40">
                <table class="table standard-table no-vertical-border">
                    <thead>
                        <tr class="w-100">
                            <th class="w-180">
                                <div class="flex-between">
                                    <span>Property {{'GLOBAL.name' | translate}}</span>
                                    <!-- <span class="icon ic-xx-xs my-auto cursor-pointer"
                        [ngClass]="!isLeadNameColInAscOrder ? 'ic-arrow-down' : 'ic-arrow-up'"
                        (click)="sortColumn('name', isLeadNameColInAscOrder)"></span> -->
                                </div>
                            </th>
                            <th class="w-180">
                                <div class="flex-between">
                                    <span>{{ 'LEADS.assign-to' | translate }}</span>
                                    <!-- <span class="icon ic-xx-xs my-auto cursor-pointer"
                        [ngClass]="!isAssignToColInAscOrder ? 'ic-arrow-down' : 'ic-arrow-up'"
                        (click)="sortColumn('assignTo', isAssignToColInAscOrder)"></span> -->
                                </div>
                            </th>
                            <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-secondary fw-semi-bold max-h-100-250 scrollbar">
                        <ng-container>
                            <tr *ngFor="let property of selectedBulkReassign">
                                <td class="w-180" [title]="property.title">
                                    <div class="text-truncate-1 break-all"> {{ property.title }} </div>
                                </td>
                                <td class="w-180" [title]="assignToName(property)">
                                    <div class="text-truncate-1 break-all">
                                        {{assignToName(property)}}
                                    </div>
                                </td>
                                <td class="w-70px">
                                    <a (click)="openConfirmDeleteModal(property.title, property.id)"
                                        class="bg-light-red icon-badge" id="clkDeleteBulkRessiagn"
                                        data-automate-id="clkDeleteBulkRessiagn">
                                        <span class="icon ic-trash m-auto ic-xxxs"></span></a>
                                </td>
                            </tr>
                        </ng-container>
                    </tbody>
                </table>
            </div>
            <form [formGroup]="bulkReassignForm">
                <div class="field-label-req mt-16">{{ 'LEADS.assign-to' | translate }}</div>
                <div class="ng-select-sm">
                    <form-errors-wrapper [control]="bulkReassignForm.controls['assignedToUsers']"
                        label="{{'LEADS.assign-to' | translate}}">
                        <ng-select [virtualScroll]="true" placeholder="ex. Mounika Pampana" [items]="allUserList"
                            [multiple]="true" [closeOnSelect]="false" bindLabel="fullName" bindValue="id"
                            name="assignedUser" formControlName="assignedToUsers"
                            (change)="assignToUserListChanged($event)">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="onClear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                        [checked]="item$.selected" [disabled]="!item.isActive">
                                    <span class="checkmark"></span>
                                    <span class="text-truncate-1 break-all">{{item.firstName}} {{item.lastName}}</span>
                                    <span class="error-message-custom top-13" *ngIf="!item.isActive">( Disabled )</span>
                                </div>
                            </ng-template>
                        </ng-select>

                    </form-errors-wrapper>
                </div>
            </form>
        </div>
        <div class="flex-center mt-20">
            <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal" id="btnSaveBulkReassign" data-automate-id="btnSaveBulkReassign"
                (click)="updateBulkAssign('bulkReassign')">{{ 'BUTTONS.save' | translate }}</button>
        </div>
    </div>
</ng-template>

<ng-template #BulkDeleteModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3> {{ 'LEADS.bulk' | translate }}
                {{ !shouldShowBulkRestore || isPermanentDelete ? ('BUTTONS.delete' | translate) : ('BUTTONS.restore' |
                translate) }}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()">
            </div>
        </div>
        <div class="px-12">
            <div class="label mb-10">Selected Properties - {{selectedNodes?.length}}</div>
            <div class="flex-column scrollbar max-h-100-150">
                <div class="scrollbar table-scrollbar ph-w-100-60 h-100-150">
                    <table class="table standard-table no-vertical-border">
                        <thead>
                            <tr class="w-100">
                                <th class="w-120">Property name</th>
                                <th *ngIf="shouldShowBulkRestore" class="w-70px">Associated Leads</th>
                                <th *ngIf="shouldShowBulkRestore" class="w-70px">Associated Data</th>
                                <th class="w-70px">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let row of gridApi?.getSelectedNodes()">
                                <td class="w-120">
                                    <div [title]="row?.data?.title" class="text-truncate-1 break-all">
                                        {{ row?.data?.title? row?.data?.title: '--'
                                        }}
                                    </div>
                                </td>
                                <td *ngIf="shouldShowBulkRestore" class="w-70px">{{ row?.data?.leadsCount ?
                                    row?.data?.leadsCount : '--' }}</td>
                                <td *ngIf="shouldShowBulkRestore" class="w-70px">{{ row?.data?.prospectCount ?
                                    row?.data?.prospectCount : '--' }}</td>
                                <td class="w-70px" (click)="openConfirmDeleteModal(row?.data?.title, row?.data?.id)">
                                    <a class="bg-light-red icon-badge br-50 ic-large">
                                        <span class="icon ic-cancel m-auto ic-xx-xs"></span>
                                    </a>
                                </td>
                            </tr>
                    </table>
                </div>
            </div>
            <div class="flex-center" (click)="bulkDelete()">
                <button class="btn-coal mt-20 mr-8 px-10 min-w-fit-content" id="bulkdelete"
                    data-automate-id="bulkdelete">
                    <span *ngIf="!isBulkDeleting else buttonDots">{{isPermanentDelete ? 'Delete' : 'Restore'}}</span>
                </button>
            </div>
        </div>
    </div>
</ng-template>
<ng-template #buttonDots>
    <div class="container px-4">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling dot-white"></div>
        </ng-container>
    </div>
</ng-template>