import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class DeviceInfoService {
  constructor() { }

  /** Get Device ID (stored in localStorage) */
  getDeviceId(): string {
    let deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      deviceId = this.generateDeviceId();
      localStorage.setItem('deviceId', deviceId);
    }
    return deviceId;
  }

  /** Generate a Unique Device ID */
  private generateDeviceId(): string {
    return 'device-' + Math.random().toString(36).substring(2, 18);
  }

  /** Get Device Type (Mobile, Tablet, Desktop) */
  getDeviceType(): string {
    const ua = navigator.userAgent;
    if (/mobile/i.test(ua)) return 'Mobile';
    if (/tablet/i.test(ua)) return 'Tablet';
    return 'Desktop';
  }

  /** Check if the app is running as a PWA */
  isPWA(): boolean {
    return window.matchMedia('(display-mode: standalone)').matches ||
      (window.navigator as any).standalone === true ||
      document.referrer.includes('android-app://');
  }

  /** Check if device is mobile or tablet */
  isMobileDevice(): boolean {
    const deviceType = this.getDeviceType();
    return deviceType === 'Mobile' || deviceType === 'Tablet';
  }

  /** Get enhanced geolocation options based on device type and PWA status */
  getGeolocationOptions(): PositionOptions {
    const isPWA = this.isPWA();
    const isMobile = this.isMobileDevice();

    if (isPWA && isMobile) {
      return {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 0
      };
    } else if (isMobile) {
      return {
        enableHighAccuracy: true,
        timeout: 12000,
        maximumAge: 30000
      };
    } else {
      return {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      };
    }
  }

  /** Get Public IP Address */
  async getIpAddress(): Promise<string> {
    try {
      const response = await fetch('https://api64.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      console.error('Error fetching IP:', error);
      return 'Unknown';
    }
  }

  /** Get Full Device Info */
  async getDeviceInfo() {
    return {
      deviceId: this.getDeviceId(),
      deviceType: this.getDeviceType(),
      ip: await this.getIpAddress(),
      isPWA: this.isPWA(),
      isMobile: this.isMobileDevice(),
    };
  }
}
