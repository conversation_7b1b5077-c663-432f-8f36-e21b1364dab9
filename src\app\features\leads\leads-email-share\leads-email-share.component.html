<div class="flex-between bg-coal w-100 px-20 py-12 text-white">
  <h3 class="fw-semi-bold">Send {{data?.shareType}} message</h3>
  <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
</div>
<div class="h-100-110 scrollbar ip-min-w-350 ip-max-w-350">
  <form class="p-10 w-100" [formGroup]="emailForm">
    <div class="align-center bg-light-pearl br-4 p-10 text-nowrap">
      <drag-scroll class="align-center scrollbar w-100-90">
        <div class="align-center">
          <div class="bg-linear br-50 h-32 w-32 fw-700 text-sm text-white flex-center">{{data?.name[0]}} </div>

          <div class="fw-600 text-xl ml-12">{{data?.name}}</div>
          <div class="border-left-black-200 h-10 mx-12"></div>
        </div>
        <div class="align-center">
          <div class="icon ic-call-ring ic-gray ic-xxs mr-6"></div>
          <div class="text-coal text-sm">{{data?.contactNo}}</div>
        </div>
        <div class="align-center ml-12">
          <div class="icon ic-envelope ic-gray ic-xxs mr-6"></div>
          <div class="text-coal text-sm">{{data?.email}}</div>
        </div>
      </drag-scroll>
    </div>
    <!-- <div class="flex-end mt-10">
      <button class="btn-coal w-160" (click)="showTemplates = true">Select Mail Templates<span
          class="icon rotate-90 ic-triangle-up  ic-xx-xs ml-8"></span></button>
    </div> -->
    <!-- <ng-container *ngIf="showTemplates"> -->
    <div class=" mt-12">
      <div class="d-flex flex-wrap">
        <ng-container *ngFor="let template of templatesList">
          <div class="form-check form-check-inline mr-10 mb-12 bg-light-pearl br-20 p-10 mt-10">
            <input type="radio" id="inpSelectedUser{{template}}" name="templatesListOption"
              formControlName="selectedOption" [value]="template" class="radio-check-input">
            <label class="text-dark-gray cursor-pointer text-large text-sm ml-6" for="inpSelectedUser{{template}}">
              {{template}}
            </label>
          </div>
        </ng-container>

      </div>
      <div class="no-validation">
        <ng-container *ngIf="emailForm.get('selectedOption').value === 'Project'">
          <h5 class="field-label-req text-dark-800 w-100px"> Select Project:</h5>
          <div class="flex-col w-100 mt-4 mb-8 border br-5 ng-select-sm">
            <form-errors-wrapper label="Project" [control]="emailForm.controls['selectedProject']">
              <ng-select [clearSearchOnAdd]="true" 
                [items]="allProjectList" 
                [ngClass]="{'blinking pe-none': projectListIsLoading}" 
                [multiple]="true" 
                [closeOnSelect]="false" 
                ResizableDropdown 
                bindLabel="name" 
                bindValue="id" 
                formControlName="selectedProject"
                (change)="projectPropertyChange('Project')" 
                placeholder="Select Project">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label">{{item.name}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container">
                      <input type="checkbox" id="project-{{index}}" data-automate-id="project-{{index}}" [checked]="item$.selected">
                      <span class="checkmark"></span>
                      <span class="text-truncate-1 break-all">{{item.name}}</span>
                    </div>
                  </div>
                </ng-template>
              </ng-select>
            </form-errors-wrapper>
          </div>
        </ng-container>
        <ng-container *ngIf="emailForm.get('selectedOption').value === 'Property'">
          <h5 class="field-label-req text-dark-800 w-100px"> Select Property:</h5>
          <div class="flex-col w-100 mt-4 mb-8 border br-5 ng-select-sm">
            <form-errors-wrapper label="Property" [control]="emailForm.controls['selectedProperty']">
              <ng-select [clearSearchOnAdd]="true" 
                [items]="allPropertyList" 
                [ngClass]="{'blinking pe-none': propertyListIsLoading}" 
                [multiple]="true" 
                [closeOnSelect]="false" 
                ResizableDropdown 
                bindLabel="title" 
                bindValue="id" 
                formControlName="selectedProperty"
                (change)="projectPropertyChange('Property')" 
                placeholder="Select Property">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label">{{item.title}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container">
                      <input type="checkbox" id="property-{{index}}" data-automate-id="property-{{index}}" [checked]="item$.selected">
                      <span class="checkmark"></span>
                      <span class="text-truncate-1 break-all">{{item.title}}</span>
                    </div>
                  </div>
                </ng-template>
              </ng-select>
            </form-errors-wrapper>
          </div>
        </ng-container>
        <div class="d-flex text-nowrap">


          <h5 class="field-label text-dark-800 w-60"> {{ 'BULK_LEAD.template' | translate }}:</h5>
          <div class="flex-col w-100 mt-4">
            <ng-select [virtualScroll]="true" [ngClass]="{ 'pe-none blinking': isTemplatesLoading }"
              class="bg-white border-0 w-100" [items]="templates" bindLabel="title" [(ngModel)]="selectedTemplate"
              [ngModelOptions]="{standalone: true}" (change)="templateChange()" ResizableDropdown
              placeholder="{{ 'GLOBAL.select' | translate }} {{'BULK_LEAD.template' | translate}}"></ng-select>
            <div class="w-100">
              <div class="border-bottom ml-12"></div>
            </div>
          </div>
        </div>
        <!-- </ng-container> -->
        <div class="d-flex text-nowrap mt-12 position-relative">
          <div class="w-60">
            <div class="field-label-req text-dark-800">From:</div>
          </div>
          <div class="w-100 flex-col mt-4">
            <form-errors-wrapper label="From" [control]="emailForm.controls['from']">
              <ng-select [items]="fromList" bindLabel="from" class="bg-white border-0 w-100" formControlName="from"
                ResizableDropdown placeholder="ex. <EMAIL>">
              </ng-select>
            </form-errors-wrapper>
            <div class="w-100">
              <div class="border-bottom ml-12"></div>
            </div>
          </div>
        </div>
        <div class="text-nowrap align-center position-relative mt-8">
          <div class="w-60">
            <div class="field-label-req text-dark-800">To:</div>
          </div>
          <div class="flex-col w-100">
            <div class="position-relative mt-12">
              <div class="w-80pr ip-w-60">
                <div class="d-flex flex-wrap">
                  <ng-container *ngFor="let list of toList">
                    <div class="br-15 px-10 py-6 bg-ash mr-4 mb-4">{{ list }}</div>
                  </ng-container>
                </div>
              </div>
              <div class="d-flex fw-700 header-5 text-accent-green position-absolute right-10 bottom-10 bg-white px-10">
                <div class="mr-30 cursor-pointer" (click)="ccEnabled = !ccEnabled">CC</div>
                <div class="cursor-pointer" (click)="bccEnabled = !bccEnabled">BCC</div>
              </div>
            </div>
            <div class="w-100">
              <div class="border-bottom ml-12"></div>
            </div>
          </div>
        </div>
        <div class="d-flex text-nowrap mt-12" *ngIf="ccEnabled">
          <div class="field-label text-dark-800 w-60">CC:</div>
          <div class="w-100 flex-col">
            <div class="flex-between">
              <form-errors-wrapper label="CC" [control]="emailForm.controls['cc']" class="w-100">
                <div class="d-flex flex-wrap">
                  <ng-container *ngFor="let email of ccList; let i = index">
                    <div class="align-center br-15 px-10 py-6 bg-ash mr-4 mb-4">{{ email }} <div
                        class="icon ic-x-xs ic-cancel ic-gray ml-6 cursor-pointer" (click)="removeCCFromList(i)"></div>
                    </div>
                  </ng-container>
                  <div class="flex-between  flex-grow-1">
                    <input type="text" class="bg-white border-0 mt-4" formControlName="cc"
                      placeholder="<EMAIL>" (keydown.enter)="addCCToList()">
                    <small class="text-muted text-nowrap mr-10">
                      (press enter to add)</small>
                  </div>
                </div>
              </form-errors-wrapper>
              <div class="icon ic-close ic-sm ic-red-350 cursor-pointer" (click)="ccEnabled = !ccEnabled"></div>

            </div>
            <div class="w-100">
              <div class="border-bottom ml-12"></div>
            </div>
          </div>
        </div>
        <div class="d-flex text-nowrap mt-12" *ngIf="bccEnabled">
          <div class="field-label text-dark-800 w-60">BCC:</div>
          <div class="w-100 flex-col">
            <div class="flex-between">
              <form-errors-wrapper label="BCC" [control]="emailForm.controls['bcc']" class="w-100">
                <div class="d-flex flex-wrap">
                  <ng-container *ngFor="let email of bccList">
                    <div class="align-center br-15 px-10 py-6 bg-ash mr-4 mb-4">{{ email }} <div
                        class="icon ic-x-xs ic-cancel ic-gray ml-6 cursor-pointer" (click)="removeBCCFromList(i)"></div>
                    </div>
                  </ng-container>
                  <div class="flex-between flex-grow-1">
                    <input type="text" class="bg-white border-0 mt-4" formControlName="bcc"
                      placeholder="ex. <EMAIL>" (keydown.enter)="addBCCToList()">
                    <small class="text-muted text-nowrap mr-10">
                      (press enter to add)</small>
                  </div>
                </div>
              </form-errors-wrapper>
              <div class="icon ic-close ic-sm ic-red-350 cursor-pointer" (click)="bccEnabled = !bccEnabled"></div>
            </div>
            <div class="w-100">
              <div class="border-bottom ml-12"></div>
            </div>
          </div>
        </div>
        <div class="d-flex text-nowrap mt-12 position-relative">
          <div class="w-68">
            <div class="field-label-req text-dark-800">Subject:</div>
          </div>
          <div class="w-100 flex-col no-validation mt-4">
            <form-errors-wrapper label="Subject" [control]="emailForm.controls['subject']">
              <input type="text" class="bg-white border-0" formControlName="subject" placeholder="type here....">
            </form-errors-wrapper>

            <div class="w-100">
              <div class="border-bottom ml-12"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex-between mt-20">
      <div class="field-label mt-0 text-dark-800 w-60 text-nowrap">Content:</div>
      <label class="checkbox-container my-4" (change)="toggleLeadPreview()">
        <input type="checkbox">
        <span class="checkmark"></span>Preview mail content
      </label>
    </div>
  </form>
  <div class="border-bottom"></div>
  <div class="mt-16 form-group no-validation" *ngIf="!isShowLeadPreview">
    <textarea rows="8" [(ngModel)]="template" [ngModelOptions]="{standalone: true}" [value]="template"
      class="scrollbar border-0" id="txtLeadMsg" data-automate-id="txtLeadMsg" placeholder="ex. Dear Nichola Ferrell, 
As per Our Telephonic Discussion your Site Visit is Confirm. This is a Reminder Message Please Confirm the time sir.

Thankyou

Regards,
Vikram Surelia"></textarea>
  </div>
  <ng-container *ngIf="isShowLeadPreview">
    <div class="mt-16 form-group no-validation">
      <textarea rows="8" [value]="messagePreview" class="scrollbar border-0" disabled="true" id="txtLeadMsg"
        data-automate-id="txtLeadMsg" placeholder="ex. Dear Nichola Ferrell,
As per Our Telephonic Discussion your Site Visit is Confirm. This is a Reminder Message Please Confirm the time sir.

Thankyou

Regards,
Vikram Surelia"></textarea>
    </div>
  </ng-container>
  <div class="p-10" *ngIf="files?.length">
    <div class="flex-between text-accent-green cursor-pointer" (click)="showFiles = !showFiles">
      <div>View <b>{{ files?.length }}</b> Attachments</div>
      <div class="icon ic-accent-green ic-x-xs ic-triangle-down"></div>
    </div>
    <ng-container *ngIf="showFiles">
      <div class="my-10 border-bottom"></div>
      <div class="d-flex flex-wrap">
        <div class="align-center p-10 bg-light-pearl br-4 mr-10 mt-4" *ngFor="let file of files; let i = index">
          <div class="text-truncate-1">{{ file.name }}</div>
          <div class="border h-10 mx-10"></div>
          <div class="icon ic-delete ic-xxs ic-red-350 cursor-pointer" (click)="deleteFiles(i, 1)"></div>
        </div>
      </div>
    </ng-container>
  </div>
</div>
<div class="position-fixed p-12 bottom-0 border-top flex-between w-100 bg-white">
  <div class="align-center ph-flex-col">
    <div class="position-relative">
      <div class="my-4">
        <button class="btn-coal" [ngClass]="{'pe-none opacity-50' : isShowLeadPreview}"
          (click)="isShowVariablePopup = !isShowLeadPreview ? !isShowVariablePopup : isShowVariablePopup">
          <span> # Select {{ 'GLOBAL.variable' | translate }}</span>
        </button>
      </div>
      <div class="position-absolute bg-light-pearl w-460 ip-max-w-340 br-10 z-index-2 left-0 bottom-40"
        *ngIf="isShowVariablePopup">
        <div class="br-4 p-12 bg-slate border">
          <div class="flex-between">
            <div class="align-center">
              <div class="icon ic-xxs mr-4 ic-coal"
                [ngClass]="isData ? 'ic-address-card-solid' : 'ic-secondary-filter-solid'"></div>
              <h5 class="fw-600 text-coal">{{ isData ? 'Data' : 'Lead' }}</h5>
            </div>
            <div class="icon ic-close ic-sm ic-coal cursor-pointer" (click)="isShowVariablePopup = false"></div>
          </div>
          <div class="pt-12 d-flex flex-wrap w-100 max-h-100-300 scrollbar mt-10">
            <div class="py-4 px-8 border br-10 mr-8 mb-8 cursor-pointer" *ngFor="let variable of variables;let i=index"
              (click)="addVariable(variable, $event)">
              {{(i + 1) + '. ' + variable}}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="btn-coal ml-10 ph-ml-0" (click)="attachFiles.click()">
      <span class="icon ic-xxs  ic-paper-clip mr-10"></span>Attach Files
    </div>
    <input type="file" #attachFiles style="display: none" (change)="onFileSelection($event)">
  </div>
  <div class="flex-end">
    <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="modalRef.hide()">{{'BUTTONS.cancel' |
      translate }}</u>
    <button class="btn-coal" (click)="sendMessage()">Send Email</button>
  </div>
</div>
<!-- <drag-scroll class="scrollbar">
  <div class="d-flex">
    <div *ngFor="let url of files; let i = index" class="position-relative mr-20 mb-16 br-5">
      <div
        *ngIf="url?.imageFilePath?.includes('jpeg') || url.imageFilePath.includes('png') || url.imageFilePath.includes('gif')">
        <a [href]="url.imageFilePath" target="_blank">
          <img [type]="'leadrat'" [appImage]="url.imageFilePath" width="120" height="120" class="obj-cover cursor-pointer br-5" alt="img" />
        </a>
        <span (click)="deleteFile(url?.imageFilePath)"
          class="dot bg-black-4 cursor-pointer bg-hover-red position-absolute top-4 right-4">
          <span class="icon ic-delete ic-xxxs" id="clkDeleteImage" data-automate-id="clkDeleteImage"></span>
        </span>
        <div class="position-absolute w-100 bottom-0 image-dropdown">
          <div class="p-1 flex-between bg-white-100 brbr-4 brbl-4">
            <div class="text-truncate-1 break-all fw-semi-bold">{{url.name}}</div>
          </div>
        </div>
      </div>
      <div *ngIf="url?.imageFilePath?.includes('mp4')" class="box-shadow-1 br-5">
        <video width="120" height="120" controls class="br-5" alt="video">
          <source [src]="url.imageFilePath" type="video/mp4">
          Your browser does not support the video tag.
        </video>
        <span (click)="deleteFile(url.imageFilePath)"
          class="dot bg-black-4 cursor-pointer bg-hover-red position-absolute top-4 right-4">
          <span class="icon ic-delete ic-xxxs" id="clkDeleteImage" data-automate-id="clkDeleteImage"></span>
        </span>
        <div class="position-absolute w-100 bottom-0 image-dropdown">
          <div class="p-1 flex-between bg-white-100 brbr-6 brbl-6">
            <div class="text-truncate-1 break-all fw-semi-bold">{{url.name}}</div>
          </div>
        </div>
      </div>
      <div *ngIf="url?.imageFilePath?.includes('pdf')">
        <a [href]="url?.imageFilePath" target="_blank">
          <img src="../../../../assets/images/pdf.svg" class="h-88">
        </a>
        <div class="p-10 flex-between bg-white-100 brbr-6 brbl-6">
          <div class="text-truncate w-80 fw-semi-bold">{{url?.name}}</div>
          <span class="dot bg-light-red cursor-pointer" (click)="deleteFile(url?.imageFilePath)">
            <div title="Delete" class="ic-delete icon ic-xxxs cursor-pointer" id="clkDeletePdf"
              data-automate-id="clkDeletePdf"></div>
          </span>
        </div>
      </div>
    </div>
  </div>
</drag-scroll> -->
<!-- <div class="version-two">
  <div for="fileUpload" class="mt-10 label">Upload Files</div>

  <div class="position-relative w-100">
    <div class="image-container">
      <div class="position-absolute top-55 right-50 icon ic-download ic-coal ic-large"></div>
    </div>
    <browse-drop-upload [allowedFileType]="'media'" [isExcelFile]="false" [allowedFileFormat]="fileFormat"
      [fileMessage]="fileMessage" [isEmail]="true" (uploadedFile)="onFileSelection($event)">
    </browse-drop-upload>
  </div>
</div> -->