<ng-container *ngIf="!selectedFieldsIsLoading else loading">
    <ng-container *ngIf="selectedFieldsInfo?.length else noFieldsSelected">
        <div class="px-30 py-20">
            <div class="d-flex mb-12" *ngIf="selectedFieldsInfo?.length > 1">
                <div class="border br-20 bg-white align-center user">
                    <ng-container *ngFor="let section of selectedFieldsInfo; let i = index">
                        <div class="activation" [ngClass]="{ active: currentActive == i }"
                            (click)="scrollTo(section?.name, i)">
                            {{ section?.name }}
                        </div>
                    </ng-container>
                </div>
            </div>
            <form [formGroup]="leadForm" autocomplete="off" class="scrollbar" (scroll)="onScroll($event)"
                [ngClass]="selectedFieldsInfo?.length > 1 ? 'h-100-207' : 'h-100-152'">
                <div class="mr-12">
                    <ng-container *ngFor="let section of selectedFieldsInfo; let i = index; let last = last">
                        <div [ngClass]="{ 'pb-12': !last }" [id]="section?.name">
                            <div class="bg-white">
                                <div class="cursor-pointer align-center py-12 px-20"
                                    [ngClass]="!section.isHide ? 'border-bottom' : ''"
                                    (click)="toggleSectionVisibility(section)">
                                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                                        [ngClass]="{ 'rotate-270': section.isHide }"></span>
                                    <h6 class="text-black-200">{{ section?.name }}</h6>
                                </div>
                                <div *ngIf="!section.isHide" class="d-flex flex-wrap pl-20 pb-20">
                                    <ng-container *ngFor="let field of section?.customFields">
                                        <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                            <div class="mr-12">
                                                <div [ngClass]="field?.isRequiredField ? 'field-label-req' : 'field-label'">
                                                    {{ field?.name }}
                                                </div>
                                                <ng-container [ngSwitch]="field?.fieldType">
                                                    <ng-container *ngSwitchCase="'text'">
                                                        <form-errors-wrapper [label]="field?.name"
                                                            [control]="leadForm.controls[field?.controlName]">
                                                            <input type="text" [formControlName]="field?.controlName"
                                                                [placeholder]="field?.placeholder ? field?.placeholder : 'enter ' + field?.name?.toLowerCase()" />
                                                        </form-errors-wrapper>
                                                    </ng-container>
                                                    <ng-container *ngSwitchCase="'number'">
                                                        <form-errors-wrapper [label]="field?.name"
                                                            [control]="leadForm.controls[field?.controlName]">
                                                            <input type="number" [formControlName]="field?.controlName"
                                                                [placeholder]="field?.placeholder ? field?.placeholder : 'enter ' + field?.name?.toLowerCase()" />
                                                        </form-errors-wrapper>
                                                    </ng-container>
                                                    <ng-container *ngSwitchCase="'textarea'">
                                                        <form-errors-wrapper [label]="field?.name"
                                                            [control]="leadForm.controls[field?.controlName]">
                                                            <textarea rows="4" [formControlName]="field?.controlName"
                                                                [placeholder]="field?.placeholder ? field?.placeholder : 'I want to say.....'">
                                                    </textarea>
                                                        </form-errors-wrapper></ng-container>
                                                    <ng-container *ngSwitchCase="'date'">
                                                        <form-errors-wrapper [label]="field?.name"
                                                            [control]="leadForm.controls[field?.controlName]">
                                                            <ng-container >
                                                                <span
                                                                    class="icon ic-calendar ic-sm ic-coal position-absolute right-20 top-12 cursor-pointer"
                                                                    (click)="isOpenPossessionModal = !isOpenPossessionModal"></span>
                                                                <input type="text"
                                                                    [value]="selectedPossession ? selectedPossession : (leadForm.get(field?.controlName)?.value || '')"
                                                                    [placeholder]="field?.placeholder ? field?.placeholder : 'select ' + field?.name?.toLowerCase()"
                                                                    readonly
                                                                    (click)="isOpenPossessionModal = !isOpenPossessionModal">
                                                                <div class="position-relative top-0 bg-white box-shadow-10 mr-10 z-index-1021" *ngIf="isOpenPossessionModal">
                                                                    <div class="d-flex">
                                                                        <div class="w-100 bg-white">
                                                                            <ng-container *ngFor="let type of dateFilterList">
                                                                                <div class="form-check form-check-inline w-fit-content">
                                                                                    <input type="radio" id="inpShowData{{type.value}}" name="globalRange"
                                                                                        [value]="type.value"
                                                                                        style="padding-left: 0 !important;" class="radio-check-input w-10 h-10">
                                                                                    <label class="text-dark-gray text-large ml-8"
                                                                                        for="inpShowData{{type.value}}">{{type.displayName}}</label>
                                                                                </div>
                                                                            </ng-container>
                                                                            <div class="position-relative dashboard-filter form-group m-6 mb-16 border py-6">
                                                                                <form-errors-wrapper>
                                                                                    <span *ngIf="selectedMonth"
                                                                                        class="fw-700 text-large text-black-200 px-12 w-90pr cursor-pointer"
                                                                                        [owlDateTimeTrigger]="dt5">
                                                                                        {{selectedMonth}} {{selectedYear}}
                                                                                    </span>
                                                                                    <span *ngIf="!selectedMonth" class="text-dark-gray px-12 w-90pr cursor-pointer"
                                                                                        [owlDateTimeTrigger]="dt5">
                                                                                        select month and year
                                                                                    </span>
                                                                                    <input type="text" [value]="selectedMonthAndYear" [owlDateTimeTrigger]="dt5"
                                                                                        [owlDateTime]="dt5" placeholder="Select date" class="p-0 border-0 border-remove"
                                                                                        (click)="isOpenPossessionModal = true"
                                                                                        style="height: 0 !important; opacity: 0; position: absolute; top: 0; left: 0; pointer-events: none;" />
                                                                                    <owl-date-time #dt5 startView="year" [yearOnly]="true" [pickerType]="'calendar'"
                                                                                        (monthSelected)="monthChanged($event)"></owl-date-time>
                                                                                </form-errors-wrapper>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="flex-end p-6 border-top">
                                                                        <div class="btn-coal" (click)="closePossessionModal()">Close</div>
                                                                    </div>
                                                                </div>
                                                            </ng-container>
                                                            
                                                        </form-errors-wrapper>
                                                    </ng-container>
                                                    <ng-container *ngSwitchCase="'dateTime'">
                                                        <form-errors-wrapper [label]="field?.name"
                                                            [control]="leadForm.controls[field?.controlName]">
                                                            <span [owlDateTimeTrigger]="dt1"
                                                                class="icon ic-calendar ic-sm ic-coal position-absolute right-20 top-12 cursor-pointer"></span>
                                                            <input type="text" [owlDateTime]="dt1" readonly bsDatepicker
                                                                [formControlName]="field?.controlName"
                                                                [owlDateTimeTrigger]="dt1"
                                                                [placeholder]="field?.placeholder ? field?.placeholder : 'select ' + field?.name?.toLowerCase()">
                                                            <owl-date-time [hour12Timer]="'true'"
                                                                [startAt]="currentDate" #dt1
                                                                (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                                                        </form-errors-wrapper>
                                                    </ng-container>
                                                    <ng-container *ngSwitchCase="'singleDropdown'">
                                                        <form-errors-wrapper [label]="field?.name"
                                                            [control]="leadForm.controls[field?.controlName]">
                                                            <ng-select [items]="field?.List"
                                                                [formControlName]="field?.controlName"
                                                                [virtualScroll]="true" ResizableDropdown
                                                                [placeholder]="field?.placeholder ? field?.placeholder : 'select ' + field?.name?.toLowerCase()">
                                                            </ng-select>
                                                        </form-errors-wrapper>
                                                    </ng-container>
                                                    <ng-container *ngSwitchCase="'multipleDropdown'">
                                                        <form-errors-wrapper [label]="field?.name"
                                                            [control]="leadForm.controls[field?.controlName]">
                                                            <ng-select [items]="field?.List"
                                                                [formControlName]="field?.controlName"
                                                                [virtualScroll]="true" [multiple]="true" appSelectAll
                                                                [closeOnSelect]="false" ResizableDropdown
                                                                [placeholder]="field?.placeholder ? field?.placeholder : 'select ' + field?.name?.toLowerCase()">
                                                                <ng-template ng-option-tmp let-item="item"
                                                                    let-item$="item$" let-index="index">
                                                                    <div class="checkbox-container"><input
                                                                            type="checkbox" id="item-{{index}}"
                                                                            data-automate-id="item-{{index}}"
                                                                            [checked]="item$.selected"><span
                                                                            class="checkmark"></span><span
                                                                            class="text-truncate-1 break-all">
                                                                            {{item}}</span>
                                                                    </div>
                                                                </ng-template>
                                                            </ng-select>
                                                        </form-errors-wrapper>
                                                    </ng-container>
                                                    <ng-container *ngSwitchDefault>
                                                        <form-errors-wrapper [label]="field?.name"
                                                            [control]="leadForm.controls[field?.controlName]">
                                                            <input type="text" [formControlName]="field?.controlName"
                                                                [placeholder]="field?.placeholder ? field?.placeholder : 'enter ' + field?.name?.toLowerCase()" />
                                                        </form-errors-wrapper>
                                                    </ng-container>
                                                </ng-container>
                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </form>
        </div>
        <div class="flex-end px-20 py-16 bg-white">
            <button class="btn-gray" (click)="goToManageLead()">{{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal ml-20" (click)="postData()"
                [ngClass]="{'pe-none opacity-5': !leadForm?.dirty}">{{'BUTTONS.save' | translate }}</button>
        </div>
    </ng-container>
    <ng-template #noFieldsSelected>
        <h1 class="flex-center h-100">NO FIELDS SELECTED YET.....</h1>
    </ng-template>
</ng-container>
<ng-template #loading>
    <div class="flex-center h-100">
        <application-loader></application-loader>
    </div>
</ng-template>