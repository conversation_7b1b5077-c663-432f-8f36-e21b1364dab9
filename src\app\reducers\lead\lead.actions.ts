import { Action } from '@ngrx/store';
import {
  AssignLead,
  BulkAssign<PERSON>ead,
  Lead,
  LeadExcel,
  LeadNote,
  LeadPayload,
  MapColumnsExcel,
  MultipleLeads,
  SuccessLeadUpdate,
  UpdateTags
} from 'src/app/core/interfaces/leads.interface';
import { FetchResponse } from 'src/app/reducers/lead/lead.reducer';

export enum LeadActionTypes {
  FETCH_LEAD_BY_ID = '[LEAD] Fetch Lead By Id',
  FETCH_LEAD_BY_ID_LIST_SUCCESS = '[LEAD] Fetch Lead By Id Success',
  FETCH_LEAD_LIST = '[LEAD] Fetch Lead List',
  FETCH_LEAD_LIST_SUCCESS = '[LEAD] Fetch Lead List Success',
  FETCH_LEAD_LIST_V2 = '[LEAD] Fetch Lead List V2',
  FETCH_CAMPAIGN_LIST_ANONYMOUS = '[LEAD] Fetch Campaign List Anonymous',
  FETCH_CAMPAIGN_LIST_ANONYMOUS_SUCCESS = '[LEAD] Fetch Campaign List Anonymous Success',
  FETCH_AGENCY_NAME_LIST_ANONYMOUS = '[LEAD] Fetch Agency Name List Anonymous',
  FETCH_AGENCY_NAME_LIST_ANONYMOUS_SUCCESS = '[LEAD] Fetch Agency Name List Anonymous Success',
  FETCH_CHANNEL_PARTNER_LIST = '[LEAD] Fetch Channel Partner List',
  FETCH_CHANNEL_PARTNER_LIST_SUCCESS = '[LEAD] Fetch Channel Partner List Success',
  FETCH_CHANNEL_PARTNER_LIST_ANONYMOUS = '[LEAD] Fetch Channel Partner List Anonymous',
  FETCH_CHANNEL_PARTNER_LIST_ANONYMOUS_SUCCESS = '[LEAD] Fetch Channel Partner List Anonymous Success',
  ADD_LEAD = '[LEAD] Add Lead',
  ADD_LEAD_SUCCESS = '[LEAD] Add Lead Success',
  ADD_QR_LEAD = '[LEAD] Add QR Code Lead',
  ADD_QR_LEAD_SUCCESS = '[LEAD] Add QR Code Lead Success',
  UPDATE_LEAD = '[LEAD] Update Lead',
  UPDATE_LEAD_SUCCESS = '[LEAD] Update Lead Success',
  UPDATE_LEAD_NOTES = '[LEAD] Update Lead Notes',
  UPDATE_LEAD_NOTES_SUCCESS = '[LEAD] Update Lead Notes Success',
  UPDATE_MULTIPLE_LEAD = '[LEAD] Update Multiple Lead',
  UPDATE_MULTIPLE_LEAD_SUCCESS = '[LEAD] Update Multiple Lead Success',
  UPDATE_LEAD_STATUS = '[LEAD] Update Lead Status',
  UPDATE_LEAD_STATUS_SUCCESS = '[LEAD] Update Lead Status Success',
  UPDATE_MULTIPLE_LEAD_STATUS = '[LEAD] Update Multiple Lead Status',
  UPDATE_MULTIPLE_LEAD_STATUS_SUCCESS = '[LEAD] Update Multiple Lead Status Success',
  GENERATE_STATUS_CHANGE_OTP_SUCCESS = '[LEAD] Generated Status Change Otp successfully',
  VERIFY_OTP = '[LEAD] Otp Verified successfully',
  EXCEL_UPLOAD = '[LEAD] Upload Excel',
  EXCEL_UPLOAD_SUCCESS = '[LEAD] Upload Excel Success',
  UPDATE_LEAD_SHARE_COUNT = '[LEAD] Update Lead Share Count',
  UPDATE_LEADS_TAG_INFO = '[LEAD] Update Leads Info',
  LEAD_EXCEL_UPLOAD = '[Lead] Upload Leads Excel File',
  LEAD_EXCEL_UPLOAD_SUCCESS = '[Lead] Upload Leads Excel File Success',
  UPLOAD_MAPPED_COLUMNS = '[Lead] Upload Mapped Column Data',
  REASSIGN_LEAD = '[LEAD] Reassign Lead',
  REASSIGN_LEAD_SUCCESS = '[LEAD] Reassign Lead Success',
  REASSIGN_BOTH = '[LEAD] Reassign',
  REASSIGN_BOTH_SUCCESS = '[LEAD] Reassign Success',
  SECONDARY_ASSIGN_LEAD = '[LEAD] Secondary assign Lead',
  SECONDARY_ASSIGN_LEAD_SUCCESS = '[LEAD] Secondary assign Lead Success',
  BULK_REASSIGN_LEAD = '[LEAD] Bulk Reassign Lead',
  BULK_REASSIGN_LEAD_SUCCESS = '[LEAD] Bulk Reassign Lead Success',
  HAS_LEAD_INFO = '[LEAD] Has Lead Info',
  HAS_LEAD_INFO_SUCCESS = '[LEAD] Has Lead Info Success',
  HAS_LEAD_ALT_INFO = '[LEAD] Has Lead Alt Info',
  HAS_LEAD_ALT_INFO_SUCCESS = '[LEAD] Has Lead Alt Info Success',
  UPDATE_FILTER_PAYLOAD = '[LEAD] Update Filter Payload',
  UPDATE_INVOICE_FILTER = '[LEAD] Update Invoice Filter',
  UPLOAD_DOCUMENT = '[LEAD] Update Document',
  UPLOAD_DOCUMENT_SUCCESS = '[LEAD] Update Document Success',
  DELETE_DOCUMENT = '[LEAD] Delete Documents',
  FETCH_PROJECT_LIST = '[LEAD] Fetch Project List',
  FETCH_PROJECT_LIST_SUCCESS = '[LEAD] Fetch Project List Success',
  FETCH_QR_PROJECT_LIST = '[LEAD] Fetch QR Code Project List',
  FETCH_QR_PROJECT_LIST_SUCCESS = '[LEAD] Fetch QR Code Project List Success',
  FETCH_PROPERTY_LIST = '[LEAD] Fetch Property List',
  FETCH_PROPERTY_LIST_SUCCESS = '[LEAD] Fetch Property List Success',
  FETCH_QR_PROPERTY_LIST = '[LEAD] Fetch QR Code Property List',
  FETCH_QR_PROPERTY_LIST_SUCCESS = '[LEAD] Fetch QR Code Property List Success',
  FETCH_EXCEL_UPLOADED_LIST = '[LEAD] Fetch Excel Uploaded List',
  FETCH_EXCEL_UPLOADED_LIST_SUCCESS = '[LEAD] Fetch Excel Uploaded List Success',
  FETCH_EXPORT_STATUS = '[LEAD] Fetch Export Status List',
  FETCH_EXPORT_STATUS_SUCCESS = '[LEAD] Fetch Export Status List Success',
  COMMUNICATION_COUNT = '[LEAD] Communication Count',
  COMMUNICATION_COUNT_SUCCESS = '[LEAD] Communication Count Success',
  COMMUNICATION_BULK_COUNT = '[LEAD] Communication Bulk Count',
  COMMUNICATION_BULK_COUNT_SUCCESS = '[LEAD] Communication Bulk Count Success',
  MEETING_OR_VISIT_DONE = '[LEAD] Meeting Or Visit Done',
  MEETING_OR_VISIT_DONE_SUCCESS = '[LEAD] Meeting Or Visit Done Success',
  FETCH_LOCATIONS = '[LEAD] Fetch Locations',
  FETCH_LOCATIONS_SUCCESS = '[LEAD] Fetch Locations Success',
  FETCH_LEAD_CITIES = '[LEAD] Fetch Lead Cities',
  FETCH_LEAD_CITIES_SUCCESS = '[LEAD] Fetch Lead Cities Success',
  FETCH_LEAD_STATES = '[LEAD] Fetch Lead States',
  FETCH_LEAD_STATES_SUCCESS = '[LEAD] Fetch Lead States Success',
  FETCH_LEAD_COUNTRIES = '[LEAD] Fetch Lead Countries',
  FETCH_LEAD_COUNTRIES_SUCCESS = '[LEAD] Fetch Lead Countries Success',
  FETCH_LEAD_SUB_COMMUNITIES = '[LEAD] Fetch Lead SubCommunities',
  FETCH_LEAD_SUB_COMMUNITIES_SUCCESS = '[LEAD] Fetch Lead SubCommunities Success',
  FETCH_LEAD_COMMUNITIES = '[LEAD] Fetch Lead Communities',
  FETCH_LEAD_COMMUNITIES_SUCCESS = '[LEAD] Fetch Lead Communities Success',
  FETCH_LEAD_TOWER_NAME = '[LEAD] Fetch Lead TowerNames',
  FETCH_LEAD_TOWER_NAME_SUCCESS = '[LEAD] Fetch Lead TowerNames Success',
  FETCH_LEAD_LOCALITES = '[LEAD] Fetch Lead Localites',
  FETCH_LEAD_LOCALITES_SUCCESS = '[LEAD] Fetch Lead Localites Success',
  FETCH_LEAD_ZONES = '[LEAD] Fetch Lead Zones',
  FETCH_LEAD_ZONES_SUCCESS = '[LEAD] Fetch Lead Zones Success',
  DELETE_LEADS = '[LEAD] Delete Leads',
  DELETE_LEADS_SUCCESS = '[LEAD] Delete Leads Success',
  RESTORE_LEADS = '[LEAD] Restore Leads',
  RESTORE_LEADS_SUCCESS = '[LEAD] Restore Leads Success',
  FETCH_MATCHING_PROPERTIES_OR_PROJECTS = '[LEAD] Fetch Matching Property and Project',
  FETCH_MATCHING_PROPERTIES_OR_PROJECTS_LIST_SUCCESS = '[LEAD] Fetch Matching Property and Project Success',
  FETCH_SUB_SOURCE_LIST = '[LEAD] Fetch Sub Sources List',
  FETCH_SUB_SOURCE_LIST_SUCCESS = '[LEAD] Fetch Sub Sources List Success',
  BULK_SOURCE = '[LEAD] Bulk Lead Source',
  BULK_SOURCE_SUCCESS = '[LEAD] Bulk Lead Source Success',
  BULK_PROJECTS = '[LEAD] Bulk Lead Projects',
  BULK_PROJECTS_SUCCESS = '[LEAD] Bulk Lead Projects Success',
  FETCH_AGENCY_NAME_LIST = '[LEAD] Fetch Agency Name',
  FETCH_AGENCY_NAME_LIST_SUCCESS = '[LEAD] Fetch Agency Name Success',
  UPDATE_DUPLICATE_ASSIGNMENT_LIST = '[LEAD] Update Duplicate Assignment List',
  UPDATE_DUPLICATE_ASSIGNMENT_LIST_SUCCESS = '[LEAD] Update Duplicate Assignment List Success',
  FETCH_DUPLICATE_FEATURE = '[LEAD] Fetch Duplicate Feature',
  FETCH_DUPLICATE_FEATURE_SUCCESS = '[LEAD] Fetch Duplicate Feature Success',
  FETCH_LEADS_EXPORT = '[REPORTS] Fetch Leads Export',
  FETCH_LEADS_EXPORT_SUCCESS = '[REPORTS] Fetch Leads Export Success',
  FETCH_BASEFILTER_COUNT = '[LEAD] Fetch Base Filter Count',
  FETCH_BASEFILTER_COUNT_SUCCESS = '[LEAD] Fetch Base Filter Count Success',
  FETCH_ACTIVE_COUNT = '[LEAD] Fetch Active Count',
  FETCH_ACTIVE_COUNT_SUCCESS = '[LEAD] Fetch Active Count Success',
  FETCH_NOT_INTERESTED_COUNT = '[LEAD] Fetch Not Interested Count',
  FETCH_NOT_INTERESTED_COUNT_SUCCESS = '[LEAD] Fetch Not Interested Count Success',
  FETCH_DROPPED_COUNT = '[LEAD] Fetch Droppped Count',
  FETCH_DROPPED_COUNT_SUCCESS = '[LEAD] Fetch Dropped Count Success',
  FETCH_LEAD_STATUS_COUNT = '[LEAD] Fetch Status Count',
  FETCH_LEAD_STATUS_COUNT_SUCCESS = '[LEAD] Fetch Status Count Success',
  FETCH_FLAGS_COUNT = '[LEAD] Fetch Flags Count',
  FETCH_FLAGS_COUNT_SUCCESS = '[LEAD] Fetch Flags Count Success',
  EXPORT_LEADS = '[LEAD] export leads through excel',
  EXPORT_LEADS_SUCCESS = '[LEAD] export leads through excel Success',
  COMMUNICATION_MESSAGE = '[LEAD] Communication Message',
  COMMUNICATION_MESSAGE_SUCCESS = '[LEAD] Communication Message Success',
  COMMUNICATION_BULK_MESSAGE = '[LEAD] Communication Bulk Message',
  COMMUNICATION_BULK_MESSAGE_SUCCESS = '[LEAD] Communication Bulk Message Success',
  EMPTY_EFFECT = '[LEAD] Empty Effect',
  FETCH_LEAD_APPOINTMENTS_BY_PROJECTS = '[LEAD] Fetch Lead Appointments By Projects',
  FETCH_LEAD_APPOINTMENTS_BY_PROJECTS_SUCCESS = '[LEAD] Fetch Lead Appointments By Projects Success',
  ADD_LEAD_PROJECTS = '[LEAD] Add Projects To Lead',
  ADD_LEAD_PROJECTS_SUCCESS = '[LEAD] Add Projects To Lead Success',
  FETCH_LEADS_COMMUNICATION_BY_IDS = '[LEAD] Fetch Lead Communications By IDs',
  FETCH_LEADS_COMMUNICATION_BY_IDS_SUCCESS = '[LEAD] Fetch Lead Communications By IDs Success',
  FETCH_LEAD_NOTES_LIST = '[LEAD] Fetch Lead Notes List',
  FETCH_LEAD_NOTES_LIST_SUCCESS = '[LEAD] Fetch Lead Notes List Success',
  FETCH_LEAD_CURRENCY_LIST = '[LEAD] Fetch Lead Currency List',
  FETCH_LEAD_CURRENCY_LIST_SUCCESS = '[LEAD] Fetch Lead Currency List Success',
  ADD_LEAD_ROTATION_GROUP = '[LEAD] Add Lead Rotation Group',
  FETCH_LEAD_ROTATION = '[LEAD] Fetch Lead Rotation',
  FETCH_LEAD_ROTATION_SUCCESS = '[LEAD] Fetch Lead Rotation Success',
  DELETE_LEAD_ROTATION = '[LEAD] Delete Lead Rotation',
  UPDATE_LEAD_ROTATION = '[LEAD] Update Lead Rotation',
  FETCH_BOOKING_DETAILS_BY_ID = '[LEAD] Fetch booking Details By Id',
  FETCH_BOOKING_DETAILS_BY_ID_SUCCESS = '[LEAD] Fetch booking Details By Id Success',
  UPDATE_LEAD_CUSTOM_STATUS_ENABLED = '[LEAD] Update Custom Status Enabled',
  FETCH_LEAD_CUSTOM_TOP_FILTERS = '[LEAD] Fetch Lead Custom Top Filters',
  FETCH_LEAD_CUSTOM_TOP_FILTERS_SUCCESS = '[LEAD] Fetch Lead Custom Top Filters Success',
  FETCH_LEAD_CUSTOM_TOP_FILTERS_CHILDREN = '[LEAD] Fetch Lead Custom Top Filters Children',
  FETCH_LEAD_CUSTOM_TOP_FILTERS_CHILDREN_SUCCESS = '[LEAD] Fetch Lead Custom Top Filters Children Success',
  ADD_LEAD_CANCELLED = '[LEAD] Add Lead Cancelled',
  ADD_DUPLICATE = '[LEAD] Add duplicate',
  PERMANENT_DELETE_LEADS = '[LEAD] Permanent Delete Leads',
  PERMANENT_DELETE_LEADS_SUCCESS = '[LEAD] Permanent Delete Leads Success',
  FETCH_MIGRATE_EXCEL_UPLOADED_LIST = '[LEAD] Fetch Migrate Excel Uploaded List',
  FETCH_MIGRATE_EXCEL_UPLOADED_LIST_SUCCESS = '[LEAD] Fetch Migrate Excel Uploaded List Success',
  UPLOAD_MIGRATE_MAPPED_COLUMNS = '[Lead] Upload Migrate Mapped Column Data',
  FETCH_BULK_OPERATION = '[LEAD] Fetch Bulk Operation List',
  FETCH_BULK_OPERATION_SUCCESS = '[LEAD] Fetch Bulk Operation List Success',
  FETCH_UPLOADTYPENAME_LIST = '[LEAD] Fetch Upload Type Name List',
  FETCH_UPLOADTYPENAME_LIST_SUCCESS = '[LEAD] Fetch Upload Type Name List Success',
  FETCH_LEAD_HISTORY = '[LEAD] Fetch Lead History List',
  FETCH_LEAD_HISTORY_SUCCESS = '[LEAD] Fetch Lead History List Success',
  FETCH_ADDITIONAL_PROPERTY_LIST = '[LEAD] Fetch Additional Property List',
  FETCH_ADDITIONAL_PROPERTY_LIST_SUCCESS = '[LEAD] Fetch Additional Property List Success',
  FETCH_ADDITIONAL_PROPERTY_VALUE = '[LEAD] Fetch Additional Property Value',
  FETCH_ADDITIONAL_PROPERTY_VALUE_SUCCESS = '[LEAD] Fetch Additional Property Value Success',
  NAVIGATE_TO_LINK = '[LEAD] Navigate To Link',
  NAVIGATE_TO_LINK_SUCCESS = '[LEAD] Navigate To Link Success',
  FETCH_CAMPAIGN_LIST = '[LEAD] Fetch Campaign List',
  FETCH_CAMPAIGN_LIST_SUCCESS = '[LEAD] Fetch Campaign List Success',
  FETCH_COUNTRY_BASED_CITY = '[LEAD] Fetch Country Based City',
  FETCH_COUNTRY_BASED_CITY_SUCCESS = '[LEAD] Fetch Country Based City Success',
  UPDATE_CARD_DATA = '[LEAD] Update Card Data',
  CLEAR_CARD_DATA = '[LEAD] Clear Card Data',
  UPDATE_IS_LOADMORE = '[LEAD] Update IsLoadMore',
  IS_CARDVIEW = '[LEAD] IS CARD VIEW',
  FETCH_LEAD_NATIONALITY = '[LEAD] Fetch Nationality',
  FETCH_LEAD_NATIONALITY_SUCCESS = '[LEAD] Fetch Nationality Success',
  FETCH_LEAD_CLUSTER_NAME = '[LEAD] Fetch Cluster Name',
  FETCH_LEAD_CLUSTER_NAME_SUCCESS = '[LEAD] Fetch Cluster Name Success',
  FETCH_LEAD_UNIT_NAME = '[LEAD] Fetch Unit Name',
  FETCH_LEAD_UNIT_NAME_SUCCESS = '[LEAD] Fetch Unit Name Success',
  FETCH_LEAD_COUNTRY_CODE = '[LEAD] Fetch Country Code',
  FETCH_LEAD_COUNTRY_CODE_SUCCESS = '[LEAD] Fetch Country Code Success',
  FETCH_LEAD_ALT_COUNTRY_CODE = '[LEAD] Fetch Alt Country Code',
  FETCH_LEAD_ALT_COUNTRY_CODE_SUCCESS = '[LEAD] Fetch Alt Country Code Success',
  FETCH_ALL_PARENT_LEAD_BY_ID = '[LEAD] Fetch All Parent Lead By Id',
  FETCH_ALL_PARENT_LEAD_BY_ID_SUCCESS = '[LEAD] Fetch All Parent Lead By Id Success',
  FETCH_LEAD_BY_ID_WITH_ARCHIVE = '[LEAD] Fetch Lead By Id With Archive',
  FETCH_LEAD_BY_ID_WITH_ARCHIVE_SUCCESS = '[LEAD] Fetch Lead By Id With Archive Success',
  FETCH_LEAD_POSTAL_CODE = '[LEAD] Fetch Lead Postal Code',
  FETCH_LEAD_POSTAL_CODE_SUCCESS = '[LEAD] Fetch Lead Postal Code Success',
  FETCH_MODULE_WISE_SEARCH_PROPERTIES = '[LEAD] Fetch Module Wise Search Properties',
  FETCH_MODULE_WISE_SEARCH_PROPERTIES_SUCCESS = '[LEAD] Fetch Module Wise Search Properties Success'
}
export class FetchLeadById implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_BY_ID;
  constructor(public leadId: string) { }
}
export class FetchLeadIdSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_BY_ID_LIST_SUCCESS;
  constructor(public response: Lead = {} as Lead) { }
}
export class FetchLeadList implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_LIST;
  constructor(
    public canFetchLeadCounts: boolean = true,
    public isInvoice: boolean = false
  ) { }
}

export class FetchLeadListV2 implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_LIST_V2;
  constructor() { }
}

export class FetchLeadListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_LIST_SUCCESS;
  constructor(public response: FetchResponse = {}) { }
}
export class AddLead implements Action {
  readonly type: string = LeadActionTypes.ADD_LEAD;
  constructor(public payload: LeadPayload) { }
}
export class AddLeadSuccess implements Action {
  readonly type: string = LeadActionTypes.ADD_LEAD_SUCCESS;
  constructor() { }
}
export class AddQRLead implements Action {
  readonly type: string = LeadActionTypes.ADD_QR_LEAD;
  constructor(public payload: LeadPayload, public templateId: string) { }
}
export class AddQRLeadSuccess implements Action {
  readonly type: string = LeadActionTypes.ADD_QR_LEAD_SUCCESS;
  constructor(public response: any = {}) { }
}
export class UpdateLead implements Action {
  readonly type: string = LeadActionTypes.UPDATE_LEAD;
  constructor(public id: string, public payload: LeadPayload) { }
}
export class UpdateLeadSuccess implements Action {
  readonly type: string = LeadActionTypes.UPDATE_LEAD_SUCCESS;
  constructor(public resp: string) { }
}
export class UpdateLeadNotes implements Action {
  readonly type: string = LeadActionTypes.UPDATE_LEAD_NOTES;
  constructor(
    public id: string,
    public payload: LeadNote,
    public canFetchLeadList: boolean = true
  ) { }
}
export class UpdateMultipleLead implements Action {
  readonly type: string = LeadActionTypes.UPDATE_MULTIPLE_LEAD;
  constructor(
    public payload: MultipleLeads,
    public isUpdateStatus: boolean = false
  ) { }
}

export class UpdateMultipleLeadStatus implements Action {
  readonly type: string = LeadActionTypes.UPDATE_MULTIPLE_LEAD_STATUS;
  constructor() { }
}
export class UpdateMultipleLeadStatusSuccess implements Action {
  readonly type: string = LeadActionTypes.UPDATE_MULTIPLE_LEAD_STATUS_SUCCESS;
  constructor() { }
}
export class UpdateLeadStatus implements Action {
  readonly type: string = LeadActionTypes.UPDATE_LEAD_STATUS;
  constructor(
    public payload: SuccessLeadUpdate,
    public leadId: string,
    public canFetchLeadList: boolean = true,
    public isSelectedBook: boolean
  ) { }
}
export class UpdateLeadStatusSuccess implements Action {
  readonly type: string = LeadActionTypes.UPDATE_LEAD_STATUS_SUCCESS;
  constructor() { }
}
//  TODO: To validate/revisit once the teams are introduced.
export class GenerateOtpSuccess implements Action {
  readonly type: string = LeadActionTypes.GENERATE_STATUS_CHANGE_OTP_SUCCESS;
  constructor(public id: string) { }
}
export class VerifyOtp implements Action {
  readonly type: string = LeadActionTypes.VERIFY_OTP;
  constructor(public payload: any) { }
}
export class ExcelUpload implements Action {
  readonly type: string = LeadActionTypes.EXCEL_UPLOAD;
  constructor(public file: File) { }
}
export class ExcelUploadSuccess implements Action {
  readonly type: string = LeadActionTypes.EXCEL_UPLOAD_SUCCESS;
  constructor(public resp: any) { }
}
export class UpdateLeadShareCount implements Action {
  readonly type: string = LeadActionTypes.UPDATE_LEAD_SHARE_COUNT;
  constructor(public leadId: string) { }
}
export class UpdateLeadsTagInfo implements Action {
  readonly type: string = LeadActionTypes.UPDATE_LEADS_TAG_INFO;
  constructor(
    public payload: UpdateTags,
    public id: string,
    public canFetchLeadList: boolean = true
  ) { }
}
export class LeadExcelUpload implements Action {
  readonly type: string = LeadActionTypes.LEAD_EXCEL_UPLOAD;
  constructor(public file: File) { }
}
export class LeadExcelUploadSuccess implements Action {
  readonly type: string = LeadActionTypes.LEAD_EXCEL_UPLOAD_SUCCESS;
  constructor(public resp: LeadExcel) { }
}
export class UploadMappedColumns implements Action {
  readonly type: string = LeadActionTypes.UPLOAD_MAPPED_COLUMNS;
  constructor(public payload: MapColumnsExcel) { }
}
export class ReassignLead implements Action {
  readonly type: string = LeadActionTypes.REASSIGN_LEAD;
  constructor(
    public payload: AssignLead,
    public canFetchLeadList: boolean = true
  ) { }
}
export class ReassignLeadSuccess implements Action {
  readonly type: string = LeadActionTypes.REASSIGN_LEAD_SUCCESS;
  constructor(public resp: string = '') { }
}

export class ReassignBoth implements Action {
  readonly type: string = LeadActionTypes.REASSIGN_BOTH;
  constructor(
    public payload: AssignLead,
    public canFetchLeadList: boolean = true
  ) { }
}
export class ReassignBothSuccess implements Action {
  readonly type: string = LeadActionTypes.REASSIGN_BOTH_SUCCESS;
  constructor(public resp: string = '') { }
}

export class SecondaryAssignLead implements Action {
  readonly type: string = LeadActionTypes.SECONDARY_ASSIGN_LEAD;
  constructor(
    public payload: AssignLead,
    public canFetchLeadList: boolean = true
  ) { }
}
export class SecondaryAssignLeadSuccess implements Action {
  readonly type: string = LeadActionTypes.SECONDARY_ASSIGN_LEAD_SUCCESS;
  constructor(public resp: string = '') { }
}
export class BulkReassignLead implements Action {
  readonly type: string = LeadActionTypes.BULK_REASSIGN_LEAD;
  constructor(public payload: BulkAssignLead) { }
}
export class BulkReassignLeadSuccess implements Action {
  readonly type: string = LeadActionTypes.BULK_REASSIGN_LEAD_SUCCESS;
  constructor(public resp: string = '') { }
}
export class HasLeadInfo implements Action {
  readonly type: string = LeadActionTypes.HAS_LEAD_INFO;
  constructor(public data: any) { }
}
export class HasLeadInfoSuccess implements Action {
  readonly type: string = LeadActionTypes.HAS_LEAD_INFO_SUCCESS;
  constructor(public resp: string = '') { }
}
export class HasLeadAltInfo implements Action {
  readonly type: string = LeadActionTypes.HAS_LEAD_ALT_INFO;
  constructor(public data: any) { }
}
export class HasLeadAltInfoSuccess implements Action {
  readonly type: string = LeadActionTypes.HAS_LEAD_ALT_INFO_SUCCESS;
  constructor(public resp: string = '') { }
}
export class UpdateFilterPayload implements Action {
  readonly type: string = LeadActionTypes.UPDATE_FILTER_PAYLOAD;
  constructor(public filter: any) { }
}

export class UpdateInvoiceFilter implements Action {
  readonly type: string = LeadActionTypes.UPDATE_INVOICE_FILTER;
  constructor(public filter: any) { }
}
export class UploadLeadDocument implements Action {
  readonly type: string = LeadActionTypes.UPLOAD_DOCUMENT;
  constructor(public id: string, public payload?: any) { }
}
export class DeleteLeadDocument implements Action {
  readonly type: string = LeadActionTypes.DELETE_DOCUMENT;
  constructor(public payload?: any) { }
}
export class UploadLeadDocumentSuccess implements Action {
  readonly type: string = LeadActionTypes.UPLOAD_DOCUMENT_SUCCESS;
  constructor(public resp?: any) { }
}
export class FetchProjectList implements Action {
  readonly type: string = LeadActionTypes.FETCH_PROJECT_LIST;
  constructor(public isWithArchive: boolean = false) { }
}
export class FetchProjectListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_PROJECT_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}
export class FetchQRProjectList implements Action {
  readonly type: string = LeadActionTypes.FETCH_QR_PROJECT_LIST;
  constructor() { }
}
export class FetchQRProjectListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_QR_PROJECT_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}
export class FetchPropertyList implements Action {
  readonly type: string = LeadActionTypes.FETCH_PROPERTY_LIST;
  constructor(public isWithArchive: boolean = false) { }
}
export class FetchPropertyListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_PROPERTY_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchQRPropertyList implements Action {
  readonly type: string = LeadActionTypes.FETCH_QR_PROPERTY_LIST;
  constructor() { }
}
export class FetchQRPropertyListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_QR_PROPERTY_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}
export class FetchExcelUploadedList implements Action {
  readonly type: string = LeadActionTypes.FETCH_EXCEL_UPLOADED_LIST;
  constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchExcelUploadedSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_EXCEL_UPLOADED_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}
export class FetchExportStatus implements Action {
  readonly type: string = LeadActionTypes.FETCH_EXPORT_STATUS;
  constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchExportStatusSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_EXPORT_STATUS_SUCCESS;
  constructor(public response: any[] = []) { }
}
export class CommunicationCount implements Action {
  readonly type: string = LeadActionTypes.COMMUNICATION_COUNT;
  constructor(public id: string, public payload: any) { }
}

export class CommunicationCountSuccess implements Action {
  readonly type: string = LeadActionTypes.COMMUNICATION_COUNT_SUCCESS;
  constructor(public id: string, public payload: any) { }
}
export class CommunicationBulkCount implements Action {
  readonly type: string = LeadActionTypes.COMMUNICATION_BULK_COUNT;
  constructor(public payload: any) { }
}

export class CommunicationBulkCountSuccess implements Action {
  readonly type: string = LeadActionTypes.COMMUNICATION_BULK_COUNT_SUCCESS;
  constructor(public payload: any) { }
}
export class MeetingOrVisitDone implements Action {
  readonly type: string = LeadActionTypes.MEETING_OR_VISIT_DONE;
  constructor(public payload: any) { }
}
export class MeetingOrVisitDoneSuccess implements Action {
  readonly type: string = LeadActionTypes.MEETING_OR_VISIT_DONE_SUCCESS;
  constructor() { }
}
export class FetchLocations implements Action {
  readonly type: string = LeadActionTypes.FETCH_LOCATIONS;
  constructor() { }
}
export class FetchLocationsSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LOCATIONS_SUCCESS;
  constructor(public response: string[] = []) { }
}
export class FetchLeadCities implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_CITIES;
  constructor() { }
}
export class FetchLeadCitiesSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_CITIES_SUCCESS;
  constructor(public response: string[] = []) { }
}

export class FetchLeadStates implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_STATES;
  constructor() { }
}
export class FetchLeadStatesSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_STATES_SUCCESS;
  constructor(public response: string[] = []) { }
}
export class FetchLeadCountries implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_COUNTRIES;
  constructor() { }
}
export class FetchLeadCountriesSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_COUNTRIES_SUCCESS;
  constructor(public response: string[] = []) { }
}
export class FetchLeadSubCommunities implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_SUB_COMMUNITIES;
  constructor() { }
}
export class FetchLeadSubCommunitiesSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_SUB_COMMUNITIES_SUCCESS;
  constructor(public response: string[] = []) { }
}
export class FetchLeadCommunities implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_COMMUNITIES;
  constructor() { }
}
export class FetchLeadCommunitiesSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_COMMUNITIES_SUCCESS;
  constructor(public response: string[] = []) { }
}
export class FetchLeadTowerNames implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_TOWER_NAME;
  constructor() { }
}
export class FetchLeadTowerNamesSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_TOWER_NAME_SUCCESS;
  constructor(public response: string[] = []) { }
}
export class FetchLeadLocalites implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_LOCALITES;
  constructor() { }
}
export class FetchLeadLocalitesSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_LOCALITES_SUCCESS;
  constructor(public response: string[] = []) { }
}
export class FetchLeadZones implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_ZONES;
  constructor() { }
}
export class FetchLeadZonesSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_ZONES_SUCCESS;
  constructor(public response: string[] = []) { }
}

export class DeleteLeads implements Action {
  readonly type = LeadActionTypes.DELETE_LEADS;
  constructor(public payload: string[], public isInvoice: boolean) { }
}


export class DeleteLeadsSuccess implements Action {
  readonly type: string = LeadActionTypes.DELETE_LEADS_SUCCESS;
  constructor() { }
}

export class RestoreLeads implements Action {
  readonly type: string = LeadActionTypes.RESTORE_LEADS;
  constructor(public payload: string[]) { }
}
export class RestoreLeadsSuccess implements Action {
  readonly type: string = LeadActionTypes.RESTORE_LEADS_SUCCESS;
  constructor() { }
}
export class FetchMatchingPropertyOrProjectList implements Action {
  readonly type: string = LeadActionTypes.FETCH_MATCHING_PROPERTIES_OR_PROJECTS;
  constructor(public payload: any) { }
}
export class FetchMatchingPropertyOrProjectListSuccess implements Action {
  readonly type: string =
    LeadActionTypes.FETCH_MATCHING_PROPERTIES_OR_PROJECTS_LIST_SUCCESS;
  constructor(public response: any) { }
}
export class FetchSubSourceList implements Action {
  readonly type: string = LeadActionTypes.FETCH_SUB_SOURCE_LIST;
  constructor() { }
}
export class FetchSubSourceListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_SUB_SOURCE_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}
export class BulkSource implements Action {
  readonly type: string = LeadActionTypes.BULK_SOURCE;
  constructor(public payload: any) { }
}
export class BulkSourceSuccess implements Action {
  readonly type: string = LeadActionTypes.BULK_SOURCE_SUCCESS;
  constructor() { }
}
export class BulkProjects implements Action {
  readonly type: string = LeadActionTypes.BULK_PROJECTS;
  constructor(public payload: any) { }
}
export class BulkProjectsSuccess implements Action {
  readonly type: string = LeadActionTypes.BULK_PROJECTS_SUCCESS;
  constructor() { }
}
export class FetchAgencyNameList implements Action {
  readonly type: string = LeadActionTypes.FETCH_AGENCY_NAME_LIST;
  constructor() { }
}
export class FetchAgencyNameListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_AGENCY_NAME_LIST_SUCCESS;
  constructor(public response: any) { }
}
export class FetchAgencyNameListAnonymous implements Action {
  readonly type: string = LeadActionTypes.FETCH_AGENCY_NAME_LIST_ANONYMOUS;
  constructor() { }
}
export class FetchAgencyNameListAnonymousSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_AGENCY_NAME_LIST_ANONYMOUS_SUCCESS;
  constructor(public response: any) { }
}
export class FetchChannelPartnerList implements Action {
  readonly type: string = LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST;
  constructor() { }
}
export class FetchChannelPartnerListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST_SUCCESS;
  constructor(public response: any) { }
}
export class FetchChannelPartnerListAnonymous implements Action {
  readonly type: string = LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST_ANONYMOUS;
  constructor() { }
}
export class FetchChannelPartnerListAnonymousSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST_ANONYMOUS_SUCCESS;
  constructor(public response: any) { }
}
export class updateDuplicateAssign implements Action {
  readonly type: string = LeadActionTypes.UPDATE_DUPLICATE_ASSIGNMENT_LIST;
  constructor(public payload: AssignLead) { }
}
export class updateDuplicateAssignSuccess implements Action {
  readonly type: string =
    LeadActionTypes.UPDATE_DUPLICATE_ASSIGNMENT_LIST_SUCCESS;
  constructor(public resp: string = '') { }
}
export class FetchDuplicateFeature implements Action {
  readonly type: string = LeadActionTypes.FETCH_DUPLICATE_FEATURE;
  constructor() { }
}
export class FetchDuplicateFeatureSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_DUPLICATE_FEATURE_SUCCESS;
  constructor(public response: any) { }
}
export class FetchLeadExport implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEADS_EXPORT;
  constructor(public payload: any) { }
}
export class FetchLeadExportSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEADS_EXPORT_SUCCESS;
  constructor(public response: any = []) { }
}
export class FetchLeadBaseFilterCount implements Action {
  readonly type: string = LeadActionTypes.FETCH_BASEFILTER_COUNT;
  constructor() { }
}
export class FetchLeadBaseFilterCountSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_BASEFILTER_COUNT_SUCCESS;
  constructor(public response: any = []) { }
}
export class FetchLeadActiveCount implements Action {
  readonly type: string = LeadActionTypes.FETCH_ACTIVE_COUNT;
  constructor(public payload: any = []) { }
}
export class FetchLeadActiveCountSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_ACTIVE_COUNT_SUCCESS;
  constructor(public response: any = []) { }
}
export class FetchLeadNotInterestedCount implements Action {
  readonly type: string = LeadActionTypes.FETCH_NOT_INTERESTED_COUNT;
  constructor(public payload: any = []) { }
}
export class FetchLeadNotInterestedCountSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_NOT_INTERESTED_COUNT_SUCCESS;
  constructor(public response: any = []) { }
}
export class FetchLeadDroppedCount implements Action {
  readonly type: string = LeadActionTypes.FETCH_DROPPED_COUNT;
  constructor(public payload: any = []) { }
}
export class FetchLeadDroppedCountSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_DROPPED_COUNT_SUCCESS;
  constructor(public response: any = []) { }
}
export class FetchLeadStatusCountSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_STATUS_COUNT_SUCCESS;
  constructor(public response: any = []) { }
}

export class FetchLeadFlagsCount implements Action {
  readonly type: string = LeadActionTypes.FETCH_FLAGS_COUNT;
  constructor() { }
}
export class FetchLeadFlagsCountSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_FLAGS_COUNT_SUCCESS;
  constructor(public response: any = []) { }
}
export class ExportLeads implements Action {
  readonly type: string = LeadActionTypes.EXPORT_LEADS;
  constructor(public payload: any) { }
}
export class ExportLeadsSuccess implements Action {
  readonly type: string = LeadActionTypes.EXPORT_LEADS_SUCCESS;
  constructor(public resp: string = '') { }
}
export class CommunicationMessage implements Action {
  readonly type: string = LeadActionTypes.COMMUNICATION_MESSAGE;
  constructor(public payload: any) { }
}
export class CommunicationMessageSuccess implements Action {
  readonly type: string = LeadActionTypes.COMMUNICATION_MESSAGE_SUCCESS;
  constructor(public resp: string = '') { }
}
export class CommunicationBulkMessage implements Action {
  readonly type: string = LeadActionTypes.COMMUNICATION_BULK_MESSAGE;
  constructor(public payload: any) { }
}
export class CommunicationBulkMessageSuccess implements Action {
  readonly type: string = LeadActionTypes.COMMUNICATION_BULK_MESSAGE_SUCCESS;
  constructor(public resp: string = '') { }
}
export class EmptyEffect implements Action {
  readonly type: string = LeadActionTypes.EMPTY_EFFECT;
  constructor() { }
}

export class FetchLeadAppointmentsByProjects implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_APPOINTMENTS_BY_PROJECTS;
  constructor(public payload: any = {}) { }
}

export class FetchLeadAppointmentsByProjectsSuccess implements Action {
  readonly type: string =
    LeadActionTypes.FETCH_LEAD_APPOINTMENTS_BY_PROJECTS_SUCCESS;
  constructor(public resp: any = []) { }
}

export class FetchLeadsCommunicationByIds implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEADS_COMMUNICATION_BY_IDS;
  constructor(public payload: { LeadIds: string[] }) { }
}

export class FetchLeadsCommunicationByIdsSuccess implements Action {
  readonly type: string =
    LeadActionTypes.FETCH_LEADS_COMMUNICATION_BY_IDS_SUCCESS;
  constructor(public resp: any = []) { }
}

export class AddLeadProjects implements Action {
  readonly type: string = LeadActionTypes.ADD_LEAD_PROJECTS;
  constructor(public payload: any = {}) { }
}

export class AddLeadProjectsSuccess implements Action {
  readonly type: string = LeadActionTypes.ADD_LEAD_PROJECTS_SUCCESS;
  constructor() { }
}

export class FetchLeadNotesList implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_NOTES_LIST;
  constructor(public id: string, public filterKeys: number) { }
}

export class FetchLeadNotesListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_NOTES_LIST_SUCCESS;
  constructor(public data: any) { }
}

export class FetchLeadCurrency implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_CURRENCY_LIST;
  constructor() { }
}
export class FetchLeadCurrencySuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_CURRENCY_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class AddLeadRotationGroup implements Action {
  readonly type: string = LeadActionTypes.ADD_LEAD_ROTATION_GROUP;
  constructor(public payload: any) { }
}

export class FetchLeadRotation implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_ROTATION;
  constructor() { }
}

export class FetchLeadRotationSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_ROTATION_SUCCESS;
  constructor(public resp: any) { }
}

export class DeleteLeadRotation implements Action {
  readonly type: string = LeadActionTypes.DELETE_LEAD_ROTATION;
  constructor(public id: string) { }
}

export class updateLeadRotation implements Action {
  readonly type: string = LeadActionTypes.UPDATE_LEAD_ROTATION;
  constructor(public payload: any) { }
}

export class cancelAddLead implements Action {
  readonly type: string = LeadActionTypes.ADD_LEAD_CANCELLED;
  constructor(public state: boolean) { }
}

export class addDuplicate implements Action {
  readonly type: string = LeadActionTypes.ADD_DUPLICATE;
  constructor(public state: boolean, public duplicateId: any) { }
}

export class UpdateLeadCustomStatusEnabled implements Action {
  readonly type: string = LeadActionTypes.UPDATE_LEAD_CUSTOM_STATUS_ENABLED;
  constructor(public isCustomStatusEnabled: boolean = false) { }
}

export class FetchLeadCustomTopFilters implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS;
  constructor() { }
}

export class FetchLeadCustomTopFiltersSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS_SUCCESS;
  constructor(public filteres: any) { }
}

export class FetchLeadCustomTopFiltersChildren implements Action {
  readonly type: string =
    LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS_CHILDREN;
  constructor() { }
}

export class FetchLeadCustomTopFiltersChildrenSuccess implements Action {
  readonly type: string =
    LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS_CHILDREN_SUCCESS;
  constructor(public filteres: any) { }
}
export class PermanentDeleteLeads implements Action {
  readonly type: string = LeadActionTypes.PERMANENT_DELETE_LEADS;
  constructor(public payload: string[]) { }
}

export class PermanentDeleteLeadsSuccess implements Action {
  readonly type: string = LeadActionTypes.PERMANENT_DELETE_LEADS_SUCCESS;
  constructor() { }
}

export class FetchMigrateExcelUploadedList implements Action {
  readonly type: string = LeadActionTypes.FETCH_MIGRATE_EXCEL_UPLOADED_LIST;
  constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchMigrateExcelUploadedSuccess implements Action {
  readonly type: string =
    LeadActionTypes.FETCH_MIGRATE_EXCEL_UPLOADED_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class UploadMigrateMappedColumns implements Action {
  readonly type: string = LeadActionTypes.UPLOAD_MIGRATE_MAPPED_COLUMNS;
  constructor(public payload: MapColumnsExcel) { }
}

export class FetchBulkOperation implements Action {
  readonly type: string = LeadActionTypes.FETCH_BULK_OPERATION;
  constructor(public pageNumber: number, public pageSize: number, public moduleType: string) { }
}

export class FetchBulkOperationSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_BULK_OPERATION_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchUploadTypeNameList implements Action {
  readonly type: string = LeadActionTypes.FETCH_UPLOADTYPENAME_LIST;
  constructor() { }
}

export class FetchUploadTypeNameListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_UPLOADTYPENAME_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchLeadHistoryList implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_HISTORY;
  constructor(public id: string) { }
}

export class FetchLeadHistoryListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_HISTORY_SUCCESS;
  constructor(public data: any) { }
}

export class FetchAdditionalPropertyList implements Action {
  readonly type: string = LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_LIST;
  constructor() { }
}

export class FetchAdditionalPropertyListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchAdditionalPropertyValue implements Action {
  readonly type: string = LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_VALUE;
  constructor(public key: any) { }
}

export class FetchAdditionalPropertyValueSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_VALUE_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class NavigateToLink implements Action {
  readonly type: string = LeadActionTypes.NAVIGATE_TO_LINK;
  constructor(public payload: any) { }
}

export class NavigateToLinkSuccess implements Action {
  readonly type: string = LeadActionTypes.NAVIGATE_TO_LINK_SUCCESS;
  constructor(public resp: any) { }
}

export class FetchCampaignList implements Action {
  readonly type: string = LeadActionTypes.FETCH_CAMPAIGN_LIST;
  constructor() { }
}

export class FetchCampaignListSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_CAMPAIGN_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchCampaignListAnonymous implements Action {
  readonly type: string = LeadActionTypes.FETCH_CAMPAIGN_LIST_ANONYMOUS;
  constructor() { }
}

export class FetchCampaignListAnonymousSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_CAMPAIGN_LIST_ANONYMOUS_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchCountryBasedCity implements Action {
  readonly type: string = LeadActionTypes.FETCH_COUNTRY_BASED_CITY;
  constructor() { }
}

export class FetchCountryBasedCitySuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_COUNTRY_BASED_CITY_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class FetchActiveCount implements Action {
  readonly type: string = LeadActionTypes.FETCH_ACTIVE_COUNT;
  constructor() { }
}

export class FetchLeadStatusCount implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_STATUS_COUNT;
  constructor() { }
}

export class UpdateCardData implements Action {
  readonly type: string = LeadActionTypes.UPDATE_CARD_DATA;
  constructor(public data: any = []) { }
}

export class ClearCardData implements Action {
  readonly type: string = LeadActionTypes.CLEAR_CARD_DATA;
  constructor() { }
}

export class UpdateIsLoadMore implements Action {
  readonly type: string = LeadActionTypes.UPDATE_IS_LOADMORE;
  constructor(public isLoadMore: boolean) { }
}

export class UpdateIsCardView implements Action {
  readonly type: string = LeadActionTypes.IS_CARDVIEW;
  constructor(public isCardView: boolean) { }
}

export class FetchLeadNationality implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_NATIONALITY;
  constructor() { }
}

export class FetchLeadNationalitySuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_NATIONALITY_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class FetchLeadClusterName implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_CLUSTER_NAME;
  constructor() { }
}

export class FetchLeadClusterNameSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_CLUSTER_NAME_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class FetchLeadUnitName implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_UNIT_NAME;
  constructor() { }
}

export class FetchLeadUnitNameSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_UNIT_NAME_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class FetchLeadCountryCode implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_COUNTRY_CODE;
  constructor() { }
}

export class FetchLeadCountryCodeSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_COUNTRY_CODE_SUCCESS;
  constructor(public resp: any[] = []) { }
}

export class FetchLeadAltCountryCode implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_ALT_COUNTRY_CODE;
  constructor() { }
}

export class FetchLeadAltCountryCodeSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_ALT_COUNTRY_CODE_SUCCESS;
  constructor(public resp: any[] = []) { }
}

export class FetchAllParentLeadById implements Action {
  readonly type: string = LeadActionTypes.FETCH_ALL_PARENT_LEAD_BY_ID;
  constructor(public leadId: any) { }
}
export class FetchAllParentLeadByIdSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_ALL_PARENT_LEAD_BY_ID_SUCCESS;
  constructor(public response: any) { }
}

export class FetchLeadByIdWithArchive implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_BY_ID_WITH_ARCHIVE;
  constructor(public leadId: any) { }
}
export class FetchLeadByIdWithArchiveSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_BY_ID_WITH_ARCHIVE_SUCCESS;
  constructor(public response: any) { }
}

export class FetchLeadPostalCode implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_POSTAL_CODE;
  constructor() { }
}

export class FetchLeadPostalCodeSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_LEAD_POSTAL_CODE_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class FetchModuleWiseSearchProperties implements Action {
  readonly type: string = LeadActionTypes.FETCH_MODULE_WISE_SEARCH_PROPERTIES;
  constructor(public moduleType: any) { }
}

export class FetchModuleWiseSearchPropertiesSuccess implements Action {
  readonly type: string = LeadActionTypes.FETCH_MODULE_WISE_SEARCH_PROPERTIES_SUCCESS;
  constructor(public resp: any = {}) { }
}