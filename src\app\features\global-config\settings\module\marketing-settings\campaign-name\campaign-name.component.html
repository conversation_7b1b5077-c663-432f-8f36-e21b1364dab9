<div class="align-center bg-white w-100 border-gray tb-align-center-unset tb-flex-col">
    <div class="align-center border-end flex-grow-1 no-validation">
        <ng-container>
            <div class="align-center w-100 px-10 py-12">
                <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
                <input placeholder="type to search" (keydown)="onSearch($event)" (input)="isEmptyInput($event)"
                    name="search" [(ngModel)]="searchTerm" class="border-0 outline-0 w-100" autocomplete="off">
            </div>
            <small class="text-muted text-nowrap ph-d-none pr-6">
                ({{ 'LEADS.lead-search-prompt' | translate}})</small>
        </ng-container>
        <div class="bg-accent-green text-white px-20 py-12 h-100 align-center cursor-pointer border-end"
            (click)="exportCampaign()">{{
            'REPORTS.export' | translate }}</div>
    </div>
    <div class="d-flex ip-br-top">
        <div (click)="openAdvFiltersModal()" class="px-10 align-center cursor-pointer border-end tb-flex-grow-1">
            <div class="icon ic-filter-solid ic-xxs ic-black mr-10"></div>
            <span class="fw-600 ph-d-none">{{'PROPERTY.advanced-filters' | translate}}</span>
        </div>
        <div class="align-center position-relative cursor-pointer d-flex border-end">
            <span class="position-absolute left-15 z-index-2 fw-600 text-sm"><span class="ph-d-none">Manage
                </span>Columns</span>
            <div class="show-hide-gray w-140 ph-w-110px">
                <ng-select [virtualScroll]="true" class="bg-white" [items]="columns" [multiple]="true" appSelectAll
                    [searchable]="false" (change)="onColumnsSelected($event)" [closeOnSelect]="false"
                    [ngModel]="defaultColumns" ResizableDropdown>
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container" [title]="item.label"><input type="checkbox" id="item-{{index}}"
                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                class="checkmark"></span>{{item.label}}</div>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div (click)="onSetColumnDefault()"
            class="bg-coal text-white px-10 py-12 ip-w-30px  align-center cursor-pointer">
            <span class="ip-d-none">{{ 'GLOBAL.default' | translate }}</span> <span
                class="ic-refresh d-none ip-d-block"></span>
        </div>
        <div class="show-dropdown-white align-center position-relative">
            <span class="fw-600 position-absolute left-5 z-index-2"> <span>
                    {{ 'GLOBAL.show' | translate }}</span> {{ 'GLOBAL.entries' | translate }}</span>
            <ng-select [virtualScroll]="true" [placeholder]="PageSize" bindValue="id" class="w-150"
                (change)="assignCount()" [(ngModel)]="selectedPageSize" [searchable]="false" ResizableDropdown>
                <ng-option name="showEntriesSize" *ngFor="let PageSize of showEntriesSize" [value]="PageSize">
                    {{PageSize}}</ng-option>
            </ng-select>
        </div>
    </div>
</div>
<div class="bg-white px-4 py-12 tb-w-100-40" [ngClass]="showLeftNav ? 'w-100-190' : 'w-100-90'">
    <ng-container *ngIf="showFilters">
        <div class="bg-secondary flex-between">
            <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
                <div class="d-flex" *ngFor="let filter of appliedFilter | keyvalue">
                    <ng-container *ngIf="isKeyVisible(filter.key) && hasValue(filter.value)">
                        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                            *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                            {{filter.key}}: {{value}}
                            <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                                (click)="onRemoveFilter(filter.key, value)"></span>
                        </div>
                    </ng-container>
                </div>
            </drag-scroll>
            <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                (click)="onClearAllFilters()">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}</div>
        </div>
    </ng-container>
</div>
<div *ngIf="allCampaignData?.items?.length else noData" class="reports pinned-grid checkbox-align">
    <ag-grid-angular *ngIf="!campaignListIsLoading else Loader" #agGrid class="ag-theme-alpine" [pagination]="true"
        [paginationPageSize]="PageSize + 1" [gridOptions]="gridOptions" [rowData]="allCampaignData.items"
        [suppressPaginationPanel]="true" [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true"
        (gridReady)="onGridReady($event)" (filterChanged)="onFilterChanged($event)">
    </ag-grid-angular>
</div>
<div class="justify-center" *ngIf="gridApi?.getSelectedNodes()?.length">
    <div class="position-absolute bg-white bottom-110 br-12 flex-between box-shadow-10 p-16 z-index-1021 ip-flex-col">
        <div>
            <h4 class="mr-20 fw-600 text-nowrap">{{gridApi?.getSelectedNodes()?.length}}
                {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' | translate}}</h4>
            <div class="text-red-350 text-decoration-underline fw-semi-bold cursor-pointer ip-mb-10"
                (click)="deselectOptions()">
                {{ 'GLOBAL.deselect' | translate }}</div>
        </div>
        <div class="flex-center flex-wrap">
            <button class="btn-bulk-red" [disabled]="gridApi?.getSelectedNodes()?.length < 2"
                (click)="openBulkDeleteModal(BulkDeleteModal)">
                {{ 'LEADS.bulk' | translate }} {{'BUTTONS.delete' | translate }}
            </button>
        </div>
    </div>
</div>
<div *ngIf="allCampaignData?.totalCount > 0 && !campaignListIsLoading" class="mt-20 flex-end">
    <div class="mr-10">{{ 'GLOBAL.showing' | translate }}
        {{(currOffset * PageSize) + 1}} {{ 'GLOBAL.to-small' | translate }} {{allCampaignData?.items?.length > 1 ?
        currOffset*PageSize + allCampaignData?.items?.length : currOffset*PageSize + allCampaignData?.items?.length}}
        {{ 'GLOBAL.of-small' | translate }} {{allCampaignData.totalCount}} {{ 'GLOBAL.entries-small' | translate }}
    </div>
    <pagination [offset]=currOffset [limit]="1" [range]="1" [size]='getPages(allCampaignData?.totalCount,PageSize)'
        (pageChange)="onPageChange($event)">
    </pagination>
</div>
<ng-template #BulkDeleteModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{ 'LEADS.bulk' | translate }} {{ 'BUTTONS.delete' | translate }}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
        </div>
        <div class="px-12">
            <div class="field-label mb-10">Selected Campaign(s)</div>
            <div class="scrollbar table-scrollbar ph-w-100-60 h-100-150">
                <table class="table standard-table no-vertical-border">
                    <thead>
                        <tr class="w-100">
                            <th class="w-120">Campaign name</th>
                            <th class="w-120">Associated Leads</th>
                            <th class="w-120">Associated Data</th>
                            <th class="w-70px">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let row of gridApi?.getSelectedNodes()">
                            <td class="w-120">{{ row?.data?.name? row?.data?.name: '--' }}</td>
                            <td class="w-120">{{ row?.data?.leadsCount ? row?.data?.leadsCount : '--' }}</td>
                            <td class="w-120">{{ row?.data?.prospectCount ? row?.data?.prospectCount : '--' }}</td>
                            <td class="w-70px" (click)="openConfirmDeleteModal(row?.data?.name, row?.data?.id)">
                                <a class="bg-light-red icon-badge">
                                    <span class="icon ic-cancel m-auto ic-xx-xs"></span>
                                </a>
                            </td>
                        </tr>
                </table>
            </div>
            <div class="flex-center">
                <button class="btn-coal mt-20" (click)="deleteCampaign()">
                    <span *ngIf="!isBulkDeleteLoading else buttonDots">{{ 'BUTTONS.delete' |translate}}</span>
                </button>

            </div>
        </div>
    </div>
</ng-template>
<ng-template #buttonDots>
    <div class="container px-4">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling dot-white"></div>
        </ng-container>
    </div>
</ng-template>
<ng-template #noData>
    <div *ngIf="!campaignListIsLoading else leadsLoader" class="flex-col flex-center h-100-270">
        <img src="assets/images/layered-cards.svg" alt="No data found" width="160" height="140">
        <div class="fw-semi-bold text-xl text-mud">No Campaign Found!</div>
    </div>
</ng-template>
<ng-template #Loader>
    <div class="flex-center h-100 mt-100">
        <application-loader></application-loader>
    </div>
</ng-template>