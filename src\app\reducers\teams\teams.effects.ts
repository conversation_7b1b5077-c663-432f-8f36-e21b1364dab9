import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { of, throwError } from 'rxjs';
import { catchError, map, mergeMap, switchMap, take } from 'rxjs/operators';

import { CloseModal, OnError } from 'src/app/app.actions';
import { EXCEPTION_MESSAGES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import {
  AddDepartment,
  AddDesignation,
  AddGeoFencing,
  AddGeoFencingSuccess,
  AddRetention,
  AddRole,
  AddTeams,
  AddUser,
  BulkToggleMFA,
  BulkToggleUserStatus,
  BulkUpdatePermissions,
  DeleteAssignedUser,
  DeleteAssignedUserSuccess,
  DeleteMember,
  DeleteRetention,
  DeleteRole,
  DeleteRoles,
  DeleteRolesSuccess,
  DeleteTeams,
  DeleteUser,
  DeleteUserAssignment,
  DeleteUserDocuments,
  // DeleteUserId,
  // DeleteUserIdSuccess,
  DeleteUserSuccess,
  DoesEmailExists,
  DoesEmailExistsSuccess,
  DoesPhoneNoExists,
  DoesPhoneNoExistsSuccess,
  DoesUsernameExists,
  DoesUsrnameExistsSuccess,
  ExportTeams,
  ExportTeamsSuccess,
  ExportUsers,
  ExportUsersSuccess,
  FetchAdminsAndReportees,
  FetchAdminsAndReporteesSuccess,
  FetchAllExistingPermission,
  FetchAllExistingPermissionSuccess,
  FetchAllTeams,
  FetchAllTeamsSuccess,
  FetchBulkUserLeadCount,
  FetchBulkUserLeadCountSuccess,
  FetchDeletedUserList,
  FetchDeletedUserListSuccess,
  FetchDepartmentsList,
  FetchDepartmentsListSuccess,
  FetchDesignationsList,
  FetchDesignationsListSuccess,
  FetchGeneralManagerList,
  FetchGeneralManagerListSuccess,
  FetchGeoFencingList,
  FetchGeoFencingListSuccess,
  FetchIVRSettingList,
  FetchIVRSettingListById,
  FetchIVRSettingListByIdSuccess,
  FetchIVRSettingListSuccess,
  FetchManageUserRolesList,
  FetchOnlyAdmins,
  FetchOnlyAdminsSuccess,
  FetchOnlyReportees,
  FetchOnlyReporteesSuccess,
  FetchOnlyReporteesWithInactive,
  FetchOnlyReporteesWithInactiveSuccess,
  FetchRecentSearch,
  FetchRecentSearchSuccess,
  FetchReportees,
  FetchReporteesSuccess,
  FetchReportingManagerDetails,
  FetchReportingManagerDetailsSuccess,
  FetchRetentionList,
  FetchRetentionListSuccess,
  FetchRolePermissionById,
  FetchRolePermissionByIdSuccess,
  FetchRolesList,
  FetchRolesListSuccess,
  FetchRotationList,
  FetchRotationListSuccess,
  FetchTeamExportTracker,
  FetchTeamExportTrackerSuccess,
  FetchTeamList,
  FetchTeamListSuccess,
  FetchTeamMember,
  FetchTeamMemberSuccess,
  FetchUnassignedUser,
  FetchUnassignedUserSuccess,
  FetchUserAssignedDataById,
  FetchUserAssignedDataByIdSuccess,
  FetchUserAssignmentsById,
  FetchUserAssignmentsByIdSuccess,
  FetchUserBasicDetailsById,
  FetchUserBasicDetailsByIdSuccess,
  FetchUserById,
  FetchUserByIdSuccess,
  FetchUserExcelUploadedList,
  FetchUserExcelUploadedSuccess,
  FetchUserExportTracker,
  FetchUserExportTrackerSuccess,
  FetchUserProfile,
  FetchUserProfileSuccess,
  FetchUsersByDesignation,
  FetchUsersByDesignationSuccess,
  FetchUsersList,
  FetchUsersListForReassignment,
  FetchUsersListForReassignmentSuccess,
  FetchUsersListSuccess,
  FetchWhatsappSettingList,
  FetchWhatsappSettingListSuccess,
  FetchWithoutAdmins,
  FetchWithoutAdminsSuccess,
  TeamsActionTypes,
  ToggleAutomation,
  ToggleGeoFencing,
  UpdateBulkUsers,
  UpdateBulkUsersSuccess,
  UpdateGeoFencing,
  UpdateGeoFencingSuccess,
  UpdateIVRSettingList,
  UpdateImg,
  UpdateRetention,
  UpdateRolePermissionById,
  UpdateTeams,
  UpdateUser,
  UpdateUserProfile,
  UpdateUserSearch,
  UpdateUserSearchSuccess,
  UpdateWhatsappSettingList,
  UploadMappedColumns,
  UploadUserDocuments,
  UserExcelUpload,
  UserExcelUploadSuccess
} from 'src/app/reducers/teams/teams.actions';
import {
  getFiltersPayload,
  getTeamsFilterPayload,
} from 'src/app/reducers/teams/teams.reducer';
import { TeamsService } from 'src/app/services/controllers/teams.service';
import { CommonService } from 'src/app/services/shared/common.service';
import {
  FetchAttendanceListNoAuth,
  UpdateFilterPayloadNoAuth,
} from '../attendance/attendance.actions';
import { getFiltersPayloadNoAuth } from '../attendance/attendance.reducer';
import { FetchSubscription } from '../profile/profile.actions';

@Injectable()
export class TeamsEffects {
  userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;

  getAllExistingPermissionList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_ALL_EXISTING_PERMISSIONS),
      map((action: FetchAllExistingPermission) => action),
      switchMap((data: any) => {
        return this.api.getAllPermissions().pipe(
          map((resp: any) => {
            return new FetchAllExistingPermissionSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getRolesList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_ROLES_LIST),
      map((action: FetchRolesList) => action),
      switchMap((data: any) => {
        return this.commonService.getModuleList(data.payload, false, true).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchRolesListSuccess(resp);
            }
            return new FetchRolesListSuccess(null);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getManageUserRolesList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_MANAGE_USER_ROLES_LIST),
      map((action: FetchManageUserRolesList) => action),
      switchMap((data: any) => {
        return this.api.getAllRolesList().pipe(
          map((resp: any) => {
            return new FetchRolesListSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getUsersList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_USERS_LIST),
      map((action: FetchUsersList) => action),
      switchMap((data: any) => {
        return this.store.select(getFiltersPayload).pipe(
          take(1),
          switchMap((filterPayload: any) => {
            if (filterPayload === undefined) {
              return of(new FetchUsersListSuccess());
            }
            this.store.dispatch(new FetchBulkUserLeadCount());
            return this.commonService
              .getModuleListByAdvFilter(filterPayload)
              .pipe(
                map((resp: any) => {
                  if (resp.succeeded) {
                    return new FetchUsersListSuccess(resp);
                  }
                  return new FetchUsersListSuccess();
                }),
                catchError((err) => of(new OnError(err)))
              );
          })
        );
      })
    )
  );

  getUsersListForReassignment$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_USERS_LIST_FOR_REASSIGNMENT),
      map((action: FetchUsersList) => action),
      switchMap((data: any) => {
        return this.api.getUsersList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUsersListForReassignmentSuccess(resp.items);
            }
            return new FetchUsersListForReassignmentSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getRolesPermissionById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_ROLES_PERMISSION_BY_ID),
      map((action: FetchRolePermissionById) => action.roleId),
      switchMap((data: any) => {
        return this.api.getRolesPermissionById(data).pipe(
          map((resp: any) => {
            return new FetchRolePermissionByIdSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getUserById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_USER_BY_ID),
      map((action: FetchUserById) => action.userId),
      switchMap((data: any) => {
        return this.api.getUserById(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUserByIdSuccess(resp.data);
            }
            return new FetchUserByIdSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getUserProfile$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_USER_PROFILE),
      map((action: FetchUserProfile) => action.userId),
      switchMap((data: any) => {
        return this.api.getUserProfile(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUserProfileSuccess(resp.data);
            }
            return new FetchUserProfileSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getUserBasicDetailsById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_USER_BASIC_DETAILS_BY_ID),
      map((action: FetchUserBasicDetailsById) => action.userId),
      switchMap((data: any) => {
        return this.api.getUserBasicDetailsById(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUserBasicDetailsByIdSuccess(resp.data);
            }
            return new FetchUserBasicDetailsByIdSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getUserAssignmentsById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_USER_ASSIGNMENTS_BY_ID),
      map((action: FetchUserAssignmentsById) => action.userId),
      switchMap((data: any) => {
        return this.api.getUserAssignmentsById(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUserAssignmentsByIdSuccess(resp.data);
            }
            return new FetchUserAssignmentsByIdSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateRolesPermissionById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPDATE_ROLES_PERMISSION_BY_ID),
      map((action: UpdateRolePermissionById) => action),
      switchMap((data: any) => {
        return this.api
          .updateRolesPermissionById(data.roleId, data.payload)
          .pipe(
            map((resp: any) => {
              let payload = {
                pageNumber: data.payload.pageNumber,
                pageSize: data.payload.pageSize,
                Name: data.payload.Name,
                path: 'roles/withpermissions',
              };
              if (resp.succeeded) {
                this._notificationService.success(`Role Updated successfully.`);
                return new FetchRolesList(payload);
              }
              return new FetchRolesList(payload);
            }),
            catchError((err: any) => {
              this._notificationService.error(err?.error?.messages?.[0]);
              return throwError(() => err);
            })
          );
      })
    )
  );

  addRole$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.ADD_ROLE),
      switchMap((action: AddRole) => {
        return this.api.addRole(action.payload).pipe(
          map((resp: any) => {
            let payload = {
              pageNumber: action.payload.pageNumber,
              pageSize: action.payload.pageSize,
              Name: action.payload.Name,
              path: 'roles/withpermissions',
            };
            if (resp.succeeded) {
              this._notificationService.success(`Role created successfully.`);
              return new FetchRolesList(payload);
            }
            return new FetchRolesList(payload);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteRole$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.DELETE_ROLE),
      switchMap((action: DeleteRole) => {
        return this.api.deleteRole(action.roleId).pipe(
          map((resp: any) => {
            let payload = {
              pageNumber: action.pageNumber,
              pageSize: action.pageSize,
              Name: action.searchName,
              path: 'roles/withpermissions',
            };
            if (resp.message.includes('Deleted')) {
              this._notificationService.success(resp.message);
              return new FetchRolesList(payload);
            }
            return new FetchRolesList(payload);
          }),
          catchError((err) => {
            this._notificationService.error(err.error.messages[0]);
            return of(new OnError(err));
          })
        );
      })
    )
  );

  deleteUser$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.DELETE_USER),
      switchMap((action: DeleteUser) => {
        return this.api.deleteUser(action.payload).pipe(
          map((resp: any) => {
            if (!action?.payload?.activateUser) {
              this._notificationService.success(
                `User deactivated successfully.`
              );
            } else {
              this._notificationService.success(`User activated successfully.`);
            }
            this.store.dispatch(new FetchUserById(action?.payload?.userId));
            this.store.dispatch(new FetchUsersListForReassignment());
            this.store.dispatch(new FetchSubscription(action?.payload?.timeZoneInfo));
            return new FetchUsersList();
          }),
          catchError((err) => {
            if (err.error.messages[0]?.includes('Administrators Profile')) {
              this._notificationService.error(EXCEPTION_MESSAGES.adminUser);
            }
            this.store.dispatch(new DeleteUserSuccess());
            return of(new OnError(err));
          })
        );
      })
    )
  );

  // deleteUserId$ = createEffect(() =>
  //   this.actions$.pipe(
  //     ofType(TeamsActionTypes.DELETE_USER_ID),
  //     map((action: DeleteUserId) => action),
  //     switchMap((data: any) => {
  //       return this.api.deleteUserId(data?.userId).pipe(
  //         map((resp: any) => {
  //           if (resp.succeeded && resp?.data?.isDeleted) {
  //             this._notificationService.success('Deleted Successfully');
  //             this.store.dispatch(new DeleteUserIdSuccess())
  //             return new FetchUsersList();
  //           }
  //           return new FetchUsersList();
  //         }),
  //         catchError((err) => {
  //           if (err.error.messages?.[0]) {
  //             this._notificationService.error(err.error.messages?.[0]);
  //           }
  //           return of(new OnError(err));
  //         })
  //       );
  //     })
  //   )
  // );

  bulkToggleUserStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.BULK_TOGGLE_USER_STATUS),
      switchMap((action: BulkToggleUserStatus) => {
        return this.api.bulkToggleUserStatus(action.payload).pipe(
          map((resp: any) => {
            if (resp.data) {
              this._notificationService.success(resp.message);
            } else {
              this._notificationService.error(resp.message);
            }
            this.store.dispatch(new FetchSubscription(action?.payload?.timeZoneInfo));
            return new FetchUsersList();
          }),
          catchError((err) => {
            if (err.error.messages[0]?.includes('Administrators Profile')) {
              this._notificationService.error(EXCEPTION_MESSAGES.adminUser);
            }
            this.store.dispatch(new DeleteUserSuccess());
            return of(new OnError(err));
          })
        );
      })
    )
  );

  addUser$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.ADD_USER),
      switchMap((action: AddUser) => {
        return this.api.addUser(action.payload).pipe(
          map((resp: any) => {
            this._notificationService.success(resp.message);
            // this._notificationService.success(`User created successfully.`);
            return new FetchUsersList();
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        );
      })
    )
  );

  updateUser$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPDATE_USER),
      switchMap((action: UpdateUser) => {
        return this.api.updateUser(action.userId, action.payload).pipe(
          map((resp: any) => {
            this._notificationService.success(`User updated successfully.`);
            this.userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
            if (this.userId) {
              this.store.dispatch(new FetchUserBasicDetailsById(this.userId));
            }
            return new FetchUsersList();
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        );
      })
    )
  );

  updateBulkUsers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPDATE_BULK_USERS),
      switchMap((action: UpdateBulkUsers) => {
        return this.api.updateBulkUsers(action.payload).pipe(
          map((resp: any) => {
            this._notificationService.success(`Users updated successfully.`);
            this.store.dispatch(new UpdateBulkUsersSuccess());
            return new FetchUsersList();
          }),
          catchError((err: any) => {
            if (err?.error?.messages?.[0])
              this._notificationService.error(err.error.messages[0]);
            return of(new OnError(err));
          })
        );
      })
    )
  );

  updateUserProfile$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPDATE_USER_PROFILE),
      switchMap((action: UpdateUserProfile) => {
        return this.api.updateUserProfile(action.userId, action.payload).pipe(
          map((resp: any) => {
            this._notificationService.success(`User updated successfully.`);
            this.userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
            let filterPayload;
            let currentDate = changeCalendar(
              action.payload?.timeZoneInfo?.baseUTcOffset
            );
            this.store
              .select(getFiltersPayloadNoAuth)
              .subscribe((data: any) => {
                filterPayload = {
                  ...data,
                  fromDate: setTimeZoneDate(
                    new Date(
                      new Date(currentDate).getFullYear(),
                      new Date(currentDate).getMonth(),
                      1
                    ),
                    action.payload?.timeZoneInfo?.baseUTcOffset
                  ),
                  toDate: setTimeZoneDate(
                    new Date(currentDate),
                    action.payload?.timeZoneInfo?.baseUTcOffset
                  ),
                  timeZoneId:
                    action.payload?.timeZoneInfo?.timeZoneId ||
                    getSystemTimeZoneId(),
                  baseUTcOffset:
                    action.payload?.timeZoneInfo?.baseUTcOffset ||
                    getSystemTimeOffset(),
                };
              });
            if (this.userId) {
              this.store.dispatch(new FetchUserProfile(this.userId));
              this.store.dispatch(new FetchUserBasicDetailsById(this.userId));
            }
            if (action?.shouldFetchUserById) {
              this.store.dispatch(new FetchUserById(action.userId));
              this.store.dispatch(new UpdateFilterPayloadNoAuth(filterPayload));
              this.store.dispatch(new FetchAttendanceListNoAuth());
            }
            return new FetchUserProfile(action.userId);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDesignationsList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_DESIGNATION_LIST),
      map((action: FetchDesignationsList) => action),
      switchMap((data: any) => {
        return this.api.getDesignationsList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDesignationsListSuccess(resp.items);
            }
            return new FetchDesignationsListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDepartmentList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_DEPARTMENT_LIST),
      map((action: FetchDepartmentsList) => action),
      switchMap((data: any) => {
        return this.api.getDepartmentsList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDepartmentsListSuccess(resp.items);
            }
            return new FetchDepartmentsListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addDepartment$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.ADD_DEPARTMENT),
      map((action: AddDepartment) => action.department),
      switchMap((data: any) => {
        return this.api.addDepartment(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Department added successfully`
              );
              return new FetchDepartmentsList();
            }
            this._notificationService.success(`Unable to add department`);
            return new FetchDepartmentsList();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addDesignation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.ADD_DESIGNATION),
      map((action: AddDesignation) => action.designation),
      switchMap((data: any) => {
        return this.api.addDesignation(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Designation added successfully`
              );
              return new FetchDesignationsList();
            }
            this._notificationService.success(`Unable to add designation`);
            return new FetchDesignationsList();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  doesUserNameExists$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.DOES_USERNAME_EXISTS),
      map((action: DoesUsernameExists) => action.userName),
      switchMap((data: any) => {
        return this.api.doesUserNameExists(data).pipe(
          map((resp: any) => {
            return new DoesUsrnameExistsSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  doesEmailExists$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.DOES_EMAIL_EXISTS),
      map((action: DoesEmailExists) => action.email),
      switchMap((data: any) => {
        return this.api.doesEmailExists(data).pipe(
          map((resp: any) => {
            return new DoesEmailExistsSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  doesPhoneNoExists$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.DOES_PHONENO_EXISTS),
      map((action: DoesPhoneNoExists) => action.phone),
      switchMap((data: any) => {
        return this.api.doesPhoneExists(data).pipe(
          map((resp: any) => {
            return new DoesPhoneNoExistsSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  UserExcelUpload$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.USER_EXCEL_UPLOAD),
      switchMap((action: UserExcelUpload) => {
        return this.api.UserExcelUpload(action.file).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'User Excel uploaded Successfully'
              );
              return new UserExcelUploadSuccess(resp.data);
            } else {
              this.store.dispatch(new CloseModal());
              this._notificationService.warn(`${resp.message}`);
              return new FetchUsersList();
            }
          }),
          catchError((err) => {
            this._notificationService.error(err?.error?.messages[0]);
            return of(new OnError(err));
          })
        );
      })
    )
  );

  updateImg$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPDATE_IMG),
      switchMap((action: UpdateImg) => {
        return this.api.updateImg(action.url).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Image Updated Successfully');
              let userId = JSON.parse(localStorage.getItem('userDetails')).sub;
              this.store.dispatch(new FetchUserBasicDetailsById(userId));
              return new FetchUserProfile(resp.data);
            }
            return new FetchUserProfileSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  uploadUserDocuments$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPLOAD_USER_DOCUMENTS),
      map((action: UploadUserDocuments) => action),
      switchMap((data: any) => {
        return this.api.uploadUserDocuments(data.payload, data.userId).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Document Successfully Uploaded'
              );
              return new FetchUserProfile(data.userId);
            }
            this._notificationService.warn(
              'Document Not Uploded, Please Try Again!'
            );
            return new FetchUserProfile(data.userId);
          }),
          catchError((err: any) => {
            if (err.error.messages[0]) {
              this._notificationService.error(err.error.messages[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  deleteUserDocuments$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.DELETE_USER_DOCUMENTS),
      map((action: DeleteUserDocuments) => action),
      switchMap((data: any) => {
        return this.api.deleteUserDocuments(data.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Document Successfully Deleted'
              );
              return new FetchUserProfile(this.userId);
            }
            return new FetchUserProfile(this.userId);
          }),
          catchError((err: any) => {
            if (err.error.messages[0]) {
              this._notificationService.error(err.error.messages[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  toggleAutomation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.TOGGLE_AUTOMATION),
      map((action: ToggleAutomation) => action),
      switchMap((data: any) => {
        return this.api.toggleAutomation(data.userIds).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Toggled Successfully');
              return new FetchUsersList();
            }
            return new FetchUsersList();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getReportees$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_REPORTEES),
      map((action: FetchReportees) => action),
      switchMap((data: any) => {
        return this.api.getReportees().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchReporteesSuccess(resp.items);
            }
            return new FetchReporteesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAdminsAndReportees$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_ADMINS_AND_REPORTEES),
      map((action: FetchAdminsAndReportees) => action),
      switchMap((data: any) => {
        return this.api.getAdminsAndReportees().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAdminsAndReporteesSuccess(resp.items);
            }
            return new FetchAdminsAndReporteesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getOnlyReportees$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_ONLY_REPORTEES),
      map((action: FetchOnlyReportees) => action),
      switchMap((data: any) => {
        return this.api.getOnlyReportees().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchOnlyReporteesSuccess(resp.items);
            }
            return new FetchOnlyReporteesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getOnlyReporteesWithInactive$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_ONLY_REPORTEES_WITH_INACTIVE),
      map((action: FetchOnlyReporteesWithInactive) => action),
      switchMap((data: any) => {
        return this.api.getOnlyReporteesWithInactive().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchOnlyReporteesWithInactiveSuccess(resp.items);
            }
            return new FetchOnlyReporteesWithInactiveSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteUserAssignment$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.DELETE_USER_ASSIGNMENT),
      map((action: DeleteUserAssignment) => action),
      switchMap((data: any) => {
        return this.api.deleteUserAssignment(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Assignment Deleted Successfully'
              );
              return new FetchUserAssignmentsById(data.payload.userId);
            }
            return new FetchUserAssignmentsById();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getBulkUserLeadCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_BULK_USER_LEAD_COUNT),
      map((action: FetchBulkUserLeadCount) => action.payload),
      switchMap((data: any) => {
        let filterPayload;
        this.store.select(getFiltersPayload).subscribe((data: any) => {
          filterPayload = {
            ...data,
            path: 'user/lead-count',
          };
        });
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchBulkUserLeadCountSuccess(resp.items);
            }
            return new FetchBulkUserLeadCountSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getIVRSettingUserList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_IVR_SETTING_LIST),
      map((action: FetchIVRSettingList) => action),
      switchMap((data: any) => {
        return this.api.getIVRSettingUserList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchIVRSettingListSuccess(resp.data);
            }
            return new FetchIVRSettingListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getIVRSettingUserListById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_IVR_SETTING_LIST_ID),
      map((action: FetchIVRSettingListById) => action),
      switchMap((data: any) => {
        return this.api.getIVRSettingUserListById(data.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchIVRSettingListByIdSuccess(resp.data);
            }
            return new FetchIVRSettingListByIdSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateIVRSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPDATE_IVR_SETTING),
      switchMap((action: UpdateIVRSettingList) => {
        return this.api.updateIVRSettings(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Users updated successfully.`);
              return new FetchIVRSettingListSuccess(action.payload);
            }
            return new FetchIVRSettingListSuccess();
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        );
      })
    )
  );

  bulkUpdateRoles$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.BULK_UPDATE_PERMISSIONS),
      map((action: BulkUpdatePermissions) => action),
      switchMap((action: BulkUpdatePermissions) => {
        return this.api.updateBulkRoles(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Roles update Successfully');
              return new FetchUsersList();
            }
            return new FetchUsersList();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );
  getOnlyAdmins$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_ONLY_ADMINS),
      map((action: FetchOnlyAdmins) => action),
      switchMap((data: any) => {
        return this.api.getOnlyAdmins().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchOnlyAdminsSuccess(resp.data);
            }
            return new FetchOnlyAdminsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkToggleMFA$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.BULK_TOGGLE_MFA),
      switchMap((action: BulkToggleMFA) => {
        return this.api.bulkToggleMFA(action.payload).pipe(
          map((resp: any) => {
            if (resp.data) {
              this._notificationService.success(resp.message);
              this.store.dispatch(new FetchOnlyAdmins());
              return new FetchOnlyAdminsSuccess();
            }
            return new FetchOnlyAdminsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getWithoutAdmins$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_WITHOUT_ADMINS),
      map((action: FetchWithoutAdmins) => action),
      switchMap((data: any) => {
        return this.api.getWithoutAdmins().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchWithoutAdminsSuccess(resp);
            }
            return new FetchWithoutAdminsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  exportUsers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.EXPORT_USERS),
      switchMap((action: ExportUsers) => {
        return this.api.exportUsers(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Users are being exported in excel format'
              );
              return new ExportUsersSuccess();
            }
            return new ExportUsersSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getExportStatusList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.EXPORT_USERS_TRACKER),
      map((action: FetchUserExportTracker) => action),
      switchMap((data: any) => {
        return this.api
          .getUsersExportStatus(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchUserExportTrackerSuccess(resp);
              }
              return new FetchUserExportTrackerSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  getManagerDetails$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_REPORTING_MANAGER_DETAILS),
      map((action: FetchReportingManagerDetails) => action),
      switchMap((action: FetchReportingManagerDetails) => {
        return this.api.getUserProfile(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchReportingManagerDetailsSuccess(resp.data);
            }
            return new FetchReportingManagerDetailsSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteRoles$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.DELETE_ROLES),
      map((action: DeleteRoles) => action),
      switchMap((data: any) => {
        return this.api.deleteRoles(data.roleId).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this.store.dispatch(new DeleteRolesSuccess(resp?.data));
              return new FetchRolesList(data.payload);
            }
            return new FetchRolesList(data.payload);
          }),
          // catchError((err) => {
          //   if (err.error.messages[0]?.includes('Not allowed')) {
          //     this._notificationService.error(EXCEPTION_MESSAGES.notAllowed);
          //   }
          //   return of(new OnError(err));
          // })
        );
      })
    )
  );

  // getTeamsList$ = createEffect(() =>
  //   this.actions$.pipe(
  //     ofType(TeamsActionTypes.FETCH_TEAM_LIST),
  //     withLatestFrom(
  //       this.store.select(getPageNumber),
  //       this.store.select(getPageSize)
  //     ),
  //     switchMap(([action, pageNumber, pageSize]: [FetchTeamList, number, number]) => {
  //       return this.commonService.getModuleListByAdvFilter(payload).pipe(
  //         map((resp: any) => {
  //           if (resp.succeeded) {
  //             return new FetchTeamListSuccess(resp);
  //           } else {
  //             return new FetchTeamListSuccess(resp);
  //           }
  //         }),
  //         catchError((err) => of(new OnError(err)))
  //       );
  //     })
  //   )
  // );

  deleteAssignedUser$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.DELETE_ASSIGNED_USER),
      map((action: DeleteAssignedUser) => action),
      switchMap((action: DeleteAssignedUser) => {
        return this.api.deleteAssignedUser(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `User deletion is in progress.`
              );
              this.store.dispatch(new DeleteAssignedUserSuccess());

              return new FetchUsersList();
            }
            return new FetchUsersList();
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        );
      })
    )
  );

  getUserAssignedDataById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_USER_ASSIGNED_DATA_BY_ID),
      map((action: FetchUserAssignedDataById) => action.userId),
      switchMap((data: any) => {
        return this.api.getUserAssignedDataById(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUserAssignedDataByIdSuccess(resp?.data);
            }
            return new FetchUserAssignedDataByIdSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDeletedTracker$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_DELETED_USER_LIST),
      map((action: FetchDeletedUserList) => action),
      switchMap((data: any) => {
        return this.api
          .getDeletedTracker(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchDeletedUserListSuccess(resp);
              }
              return new FetchDeletedUserListSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  getTeamsList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_TEAM_LIST),
      map((action: FetchTeamList) => action.payload),
      switchMap((payload: any) => {
        return this.store.select(getTeamsFilterPayload).pipe(
          take(1),
          switchMap((data: any) => {
            let filterPayload = {
              ...data,
              path: 'team',
            };
            return this.commonService
              .getModuleListByAdvFilter(filterPayload)
              .pipe(
                map((resp: any) => {
                  if (resp.succeeded) {
                    return new FetchTeamListSuccess(resp.items);
                  }
                  return new FetchTeamListSuccess(resp.items);
                }),
                catchError((err) => of(new OnError(err)))
              );
          })
        );
      })
    )
  );

  addTeams$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.ADD_TEAMS),
      switchMap((action: AddTeams) => {
        return this.api.addTeam(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Teams created successfully.`);
              return new FetchTeamList({});
            }
            return new FetchTeamListSuccess(resp);
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        );
      })
    )
  );

  updateTeams$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPDATE_TEAMS),
      switchMap((action: UpdateTeams) => {
        return this.api.updateTeam(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (action.payload.configuration) {
                this.store.dispatch(new FetchRotationList());
                this._notificationService.success(`Configuration updated successfully.`);
              } else {
                this._notificationService.success(`Teams updated successfully.`);
              }

              return new FetchTeamList({});
            }
            return new FetchTeamListSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteTeams$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.DELETE_TEAMS),
      map((action: DeleteTeams) => action),
      switchMap((action: DeleteTeams) => {
        return this.api.deleteTeams(action?.ids).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Teams deleted Successfully');
              return new FetchTeamList({});
            }
            return new FetchTeamListSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getTeamMembers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_TEAM_MEMBERS),
      switchMap((action: FetchTeamMember) => {
        return this.commonService.getModuleListByAdvFilter(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchTeamMemberSuccess(resp);
            }
            return new FetchTeamMemberSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getUnassignedUsers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_UNASSIGNED_USERS),
      map((action: FetchUnassignedUser) => action),
      switchMap((data: any) => {
        return this.api.getUnassignedUsers().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUnassignedUserSuccess(resp.data);
            }
            return new FetchUnassignedUserSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getGeneralManagerList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_GENERAL_MANAGER_LIST),
      map((action: FetchGeneralManagerList) => action),
      switchMap((data: any) => {
        return this.api.getGeneralManagerList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchGeneralManagerListSuccess(resp.items);
            }
            return new FetchGeneralManagerListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  exportTeams$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.EXPORT_TEAMS),
      switchMap((action: ExportTeams) => {
        return this.api.exportTeams(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Teams are being exported in excel format'
              );
              return new ExportTeamsSuccess();
            }
            return new ExportTeamsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteTeamMembers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.DELETE_TEAM_MEMBER),
      map((action: DeleteMember) => action),
      switchMap((action: DeleteMember) => {
        return this.api.deleteMember(action?.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Members deleted Successfully');
              let payload = {
                path: 'team/id',
                Id: action.payload.teamId,
              };
              return new FetchTeamMember(payload);
            }
            let payload = {
              path: 'team/id',
              Id: action.payload.teamId,
              pageSize: 10,
              pageNumber: 1,
            };
            return new FetchTeamMember(payload);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAllTeams$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_ALL_TEAMS),
      map((action: FetchAllTeams) => action),
      switchMap((action: FetchAllTeams) => {
        return this.api.getAllTeams().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAllTeamsSuccess(resp?.items);
            }
            return new FetchAllTeamsSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  fetchRetention$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_RETENTION_LIST),
      map((action: FetchRetentionList) => action),
      switchMap((action: FetchRetentionList) => {
        return this.api.getRetentionList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchRetentionListSuccess(resp?.data);
            }
            return new FetchRetentionListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addRetention$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.ADD_RETENTION),
      map((action: AddRetention) => action),
      switchMap((action: AddRetention) => {
        return this.api.addRetention(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              // If the payload contains a configuration property, also dispatch FetchRotationList
              if (action.payload.configuration) {
                this.store.dispatch(new FetchRotationList());
              }
              return new FetchRetentionList();
            }
            return new FetchRetentionList();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateRetention$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPDATE_RETENTION),
      map((action: UpdateRetention) => action),
      switchMap((action: UpdateRetention) => {
        return this.api.updateRetention(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              // If the payload contains a configuration property, also dispatch FetchRotationList
              if (action.payload.configuration) {
                this.store.dispatch(new FetchRotationList());
              }
              return new FetchRetentionList();
            }
            return new FetchRetentionList();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getTeamssExportList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.EXPORT_TEAMS_TRACKER),
      map((action: FetchTeamExportTracker) => action),
      switchMap((data: any) =>
        this.api
          .getTeamsExportStatus(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) =>
              resp.succeeded
                ? new FetchTeamExportTrackerSuccess(resp)
                : new FetchTeamExportTrackerSuccess()
            )
          )
      )
    )
  );

  getUsersByDesignation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_USERS_BY_DESIGNATION),
      map((action: FetchUsersByDesignation) => action),
      switchMap((data: any) => {
        return this.api.getUsersByDesignation().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUsersByDesignationSuccess(resp.data);
            }
            return new FetchUsersByDesignationSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteRetention$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.DELETE_RETENTION),
      map((action: DeleteRetention) => action),
      switchMap((action: DeleteRetention) => {
        return this.api.deleteRetention(action.id).pipe(
          switchMap((resp: any) => {
            if (resp.succeeded) {
              return of(
                new FetchRetentionList(),
                new FetchRotationList()
              );
            }
            return of(new FetchRetentionList());
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  uploadMappedColumns$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPLOAD_MAPPED_COLUMNS),
      switchMap((action: UploadMappedColumns) => {
        const params = new URLSearchParams({
          S3BucketKey: action.payload.s3BucketKey,
          SheetName: action.payload.sheetName,
        }).toString();
        return this.api.uploadMappedColumns(params).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (resp.data) {
                if (resp.data?.excelUrl) {
                  const dataCount = resp.message?.DataCount || '';
                  this._notificationService.success(
                    `${dataCount} Invalid Data Not Uploaded`
                  );
                  return new UserExcelUploadSuccess(resp.data);
                } else {
                  this._notificationService.success(
                    'Excel Uploaded Successfully'
                  );
                  return new UserExcelUploadSuccess(resp.data);
                }
              } else {
                this._notificationService.error(resp.message);
              }
              return new FetchUsersList();
            }
            return new FetchUsersListSuccess();
          }),
          catchError((err: any) => {
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getExcelUploadedList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_EXCEL_UPLOADED_LIST),
      map((action: FetchUserExcelUploadedList) => action),
      switchMap((data: any) => {
        return this.api
          .getExcelUploadedList(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchUserExcelUploadedSuccess(resp);
              }
              return new FetchUserExcelUploadedSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  getWhatsappSettingUserList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_WHATSAPP_SETTING_LIST),
      map((action: FetchWhatsappSettingList) => action),
      switchMap((data: any) => {
        return this.api.getWhatsappSettingUserList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchWhatsappSettingListSuccess(resp.data);
            }
            return new FetchWhatsappSettingListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateWhatsappSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPDATE_WHATSAPP_SETTING),
      switchMap((action: UpdateWhatsappSettingList) => {
        return this.api.updateWhatsappSettings(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Users updated successfully.`);
              return new FetchWhatsappSettingListSuccess(action.payload);
            }
            return new FetchWhatsappSettingListSuccess();
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        );
      })
    )
  );

  toggleGeoFencing$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.TOGGLE_GEO_FENCING),
      map((action: ToggleGeoFencing) => action),
      switchMap((data: any) => {
        return this.api.toggleGeoFencing(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Geo Fencing Enabled Successfully');
              return new FetchUsersList();
            }
            return new FetchUsersList();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addGeoFencing$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.ADD_GEO_FENCING),
      map((action: AddGeoFencing) => action),
      switchMap((data: any) => {
        return this.api.addGeoFencing(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Geo Fencing Added Successfully');
              return new AddGeoFencingSuccess(resp);
            }
            return new AddGeoFencingSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateGeoFencing$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPDATE_GEO_FENCING),
      map((action: UpdateGeoFencing) => action),
      switchMap((data: any) => {
        return this.api.updateGeoFencing(data.payload).pipe(
          mergeMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Geo Fencing Updated Successfully');
              let userId = JSON.parse(localStorage.getItem('userDetails')).sub;
              return [
                new UpdateGeoFencingSuccess(resp),
                new FetchGeoFencingList(userId)
              ];
            }
            return [new UpdateGeoFencingSuccess(resp)];
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateUserSearch$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.UPDATE_USER_SEARCH),
      switchMap((action: UpdateUserSearch) =>
        this.api.updateUserSearch(action.payload).pipe(
          switchMap((resp: any) => {
            if (resp.succeeded) {
              const successAction = new UpdateUserSearchSuccess(resp.data);
              const fetchAction = new FetchRecentSearch(action.payload.module); // 0 or 1
              return of(successAction, fetchAction);
            }
            return of(new UpdateUserSearchSuccess());
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        )
      )
    )
  );


  fetchRecentSearch$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_RECENT_SEARCH),
      map((action: FetchRecentSearch) => action),
      switchMap((data: any) => {
        return this.api.getRecentSearch(data.moduleType).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchRecentSearchSuccess(resp.data);
            }
            return new FetchRecentSearchSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getRotationList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_ROTATION_LIST),
      map((action: FetchRotationList) => action),
      switchMap((data: any) => {
        return this.api.getRotationList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchRotationListSuccess(resp.data);
            }
            return new FetchRotationListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getGeoFencingList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TeamsActionTypes.FETCH_GEO_FENCING_LIST),
      map((action: FetchGeoFencingList) => action),
      switchMap((data: any) => {
        return this.api.getGeoFencingList(data.userId).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchGeoFencingListSuccess(resp.data);
            }
            return new FetchGeoFencingListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private api: TeamsService,
    private _notificationService: NotificationsService,
    private store: Store<AppState>,
    private commonService: CommonService,
    private modalService: BsModalService,
    private modalRef: BsModalRef
  ) { }
}
