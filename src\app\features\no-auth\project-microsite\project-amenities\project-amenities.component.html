<div *ngFor="let amenityType of amenitiesArr">
  <h4 *ngIf="amenityType[1]?.length > 0"
    class="px-20 py-20 br-50px cursor-pointer fw-600 text-mud text-decoration-underline">
    {{ amenityType[0] }}
  </h4>
  <div class="d-flex flex-wrap">
    <ng-container *ngFor="let amenity of amenityType[1]">
      <div class="text-center w-60 mr-20 mt-10" >
        <img *ngIf="amenity.imageURL; else dummy" [src]="amenity.imageURL" alt="amenity" class="p-10">
        <div class="amenity-name text-sm text-truncate-1 break-all" [title]="amenity.amenityDisplayName">{{ amenity.amenityDisplayName }}</div>
      </div>
      <ng-template #dummy>
        <span class="icon ic-black ic-lg">{{ getFirstCharacter(amenity?.amenityDisplayName) }}</span>
      </ng-template>
    </ng-container>
  </div>
</div>