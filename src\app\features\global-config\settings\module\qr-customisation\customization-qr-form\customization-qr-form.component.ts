import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subject, debounceTime, takeUntil } from 'rxjs';

import { QR_DISPLAY_FIELDS } from 'src/app/app.constants';
import { FolderNamesS3 } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  getLocationDetailsByObj,
  getTenantName,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { FetchProfile } from 'src/app/reducers/profile/profile.actions';
import { getProfile } from 'src/app/reducers/profile/profile.reducers';
import {
  AddQrForm,
  FetchQrFormById,
  FetchQrFormsFields,
  TemplateNameExists,
  UpdateQrForm,
} from 'src/app/reducers/qr-form/qr-form.action';
import {
  getQRFormById,
  getQRFormFields,
  getTemplateNameExists,
} from 'src/app/reducers/qr-form/qr-form.reducer';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'customization-qr-form',
  templateUrl: './customization-qr-form.component.html',
})
export class CustomizationQRFormComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  private templateNameChanged: Subject<void> = new Subject<void>();
  selectedSection: string = 'Contents';
  isTemplateNameExists: boolean = false;
  enquiryInfoFields: Array<any> = QR_DISPLAY_FIELDS;
  // enquiryPreviewsqUnitFields: Array<any> = QR_DISPLAY_FIELDS.slice(1, 6);
  // enquiryPreviewDropDownFields: Array<any> = QR_DISPLAY_FIELDS.slice(6, 20);
  // enquiryPreviewTextFields: Array<any> = QR_DISPLAY_FIELDS.slice(20, 47);
  qrCustomForm: FormGroup;
  templateName: string = '';

  qrCustomFormFields: Array<any>;
  s3BucketUrl: string = env.s3ImageBucketURL;
  currentYear: number = new Date().getFullYear();
  isFiltered: Record<number, boolean> = {
    1: true,
    2: true,
    3: true,
    4: true,
    5: true,
    6: true,
  };
  isData: Record<number, boolean> = {
    1: true,
    2: true,
    3: true,
    4: true,
    5: true,
    6: true,
  };
  isAddHeaderClicked: boolean = false;
  isAddFooterClicked: boolean = false;
  showChooseTemplate: boolean = false;
  showEdit: boolean = true;
  isHovered: boolean = false;
  showEvents: boolean = false;
  hideElements: boolean = false;
  showLeftNav: boolean = true;
  isEditing: boolean = false;
  showEnquiryInfo: boolean = false;
  showWidth = true;
  screen = window;
  activeImages: string[] = [];

  socialMedia: any = [
    {
      image: '../../../../../../assets/images/integration/facebook-logo.svg',
      controlName: 'facebook',
      placeholder: 'enter your facebook link',
      baseUrl: 'https://www.facebook.com/',
    },
    {
      image: '../../../../../../assets/images/qr-form/instagram-logo.svg',
      controlName: 'instagram',
      placeholder: 'enter your instagram link',
      baseUrl: 'https://www.instagram.com/',
    },
    {
      image: '../../../../../../assets/images/qr-form/linkedin-logo.svg',
      controlName: 'linkedin',
      placeholder: 'enter your linkedin link',
      baseUrl: 'https://www.linkedin.com/in/',
    },
    {
      image: '../../../../../../assets/images/qr-form/twitter-logo.svg',
      controlName: 'twitter',
      placeholder: 'enter your twitter link',
      baseUrl: 'https://www.twitter.com/',
    },
    {
      image: '../../../../../../assets/images/integration/whatsapp-logo.svg',
      controlName: 'whatsapp',
      placeholder: 'enter your whatsapp number',
      baseUrl: 'https://wa.me/',
    },
  ];
  selectedTemplateId: any;
  selectedTemplateInfo: any;
  headerPic: string;
  footerPic: string;
  orgProfileData: any;
  id: any;
  subDomain: string = getTenantName();
  isNameTouched: boolean = false;
  globalSettingsDetails: any;
  filteredFields: any[];

  constructor(
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private store: Store<AppState>,
    private fb: FormBuilder,
    private s3UploadService: BlobStorageService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private shareDataService: ShareDataService
  ) {
    this.qrCustomForm = this.fb.group({
      name: [true, Validators.required],
      phoneNo: [true],
      altPhoneNo: [null],
      email: [null],
      referralName: [null],
      referralContactNo: [null],
      locality: [null],
      city: [null],
      state: [null],
      budget: [null],
      carpetArea: [null],
      enquiredFor: [null],
      basePropertyType: [null],
      subPropertyType: [null],
      noOfBHK: [null],
      bhkType: [null],
      project: [null],
      property: [null],
      agencyName: [null],
      profession: [null],
      companyName: [null],
      designation: [null],
      possessionAvailability: [null],
      channelPartnerName: [null],
      campaigns: [null],
      executiveName: [null],
      executiveContactNo: [null],
      customerLocality: [null],
      customerCity: [null],
      customerState: [null],
      notes: [null],
      referralEmail: [null],
      beds: [null],
      baths: [null],
      preferredFloors: [null],
      builtUpArea: [null],
      saleableArea: [null],
      propertyArea: [null],
      netArea: [null],
      unitName: [null],
      customerSubCommunity: [null],
      customerCommunity: [null],
      customerTowerName: [null],
      customerCountry: [null],
      furnishStatus: [null],
      offeringType: [null],
      enquiredSubCommunity: [null],
      enquiredCommunity: [null],
      enquiredTowerName: [null],
      enquiredCountry: [null],
      clusterName: [null],
      nationality: [null],
      purpose: [null],

      // headerLogo: [null],
      headerBgColor: ['#FFFFFF'],
      headerTextColor: ['#000000'],
      headerDesign: [0],

      // footerLogo: [null],
      footerBgColor: ['#FFFFFF'],
      footerTextColor: ['#000000'],
      footerDesign: [0],

      orgCompanyName: null,
      orgAddress: null,
      orgPhoneNo: [''],
      orgEmail: [''],
      facebook: [null],
      instagram: [null],
      linkedin: [null],
      twitter: [null],
      whatsapp: [null],
    });

    this.activatedRoute.params.subscribe((params: any) => {
      if ((params || {})?.id) {
        this.selectedTemplateId = params?.id;
        this.store.dispatch(new FetchQrFormById(this.selectedTemplateId));
      }
    });
  }

  ngOnInit(): void {
    this.metaTitle.setTitle('CRM | Form customization');
    this.headerTitle.setLangTitle('SIDEBAR.form-customization');
    this.store.dispatch(new FetchQrFormsFields());
    this.store
      .select(getQRFormFields)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.qrCustomFormFields = data;
      });
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
        this.filterFields();
      });
    this.templateNameChanged
      .pipe(debounceTime(300))
      .subscribe(() => this.templateNameDuplicateCheck());
    this.store.dispatch(new FetchProfile());
    this.store
      .select(getProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.orgProfileData = item;
        if (!this.selectedTemplateId) {
          this.qrCustomForm.patchValue({
            orgCompanyName: this.orgProfileData?.displayName,
            orgAddress: getLocationDetailsByObj(this.orgProfileData?.address),
            orgPhoneNo: this.orgProfileData?.phoneNumber,
            orgEmail: this.orgProfileData?.email,
            facebook: this.getSocialMediaId(this.orgProfileData, 'facebook'),
            instagram: this.getSocialMediaId(this.orgProfileData, 'instagram'),
            linkedin: this.getSocialMediaId(this.orgProfileData, 'linkedin'),
            twitter: this.getSocialMediaId(this.orgProfileData, 'twitter'),
            whatsapp: this.getSocialMediaId(this.orgProfileData, 'whatsapp'),
          });
          this.headerPic = this.orgProfileData.logoImgUrl;
          this.footerPic = this.orgProfileData.logoImgUrl;
        }
      });
    if (this.selectedTemplateId) {
      this.store
        .select(getQRFormById)
        .pipe(takeUntil(this.stopper))
        .subscribe((item: any) => {
          if (!item) {
            return;
          }
          this.selectedTemplateInfo = item?.data;

          this.selectedTemplateInfo?.content?.forEach((contentItem: any) => {
            const controlName = contentItem.displayName;

            if (this.qrCustomForm.get(controlName)) {
              this.qrCustomForm.get(controlName).patchValue(true);
            }
          });
          this.templateName = this.selectedTemplateInfo?.name;
          this.headerPic =
            this.selectedTemplateInfo?.header?.logoUrl ||
            this.orgProfileData.logoImgUrl;
          this.footerPic =
            this.selectedTemplateInfo?.footer?.logoUrl ||
            this.orgProfileData.logoImgUrl;
          this.qrCustomForm.patchValue({
            headerBgColor:
              this.selectedTemplateInfo?.header?.backgroundColor || '#FFFFFF',
            headerTextColor:
              this.selectedTemplateInfo?.header?.textColor || '#000000',
            headerDesign: this.selectedTemplateInfo?.header?.headerDesign,

            footerBgColor:
              this.selectedTemplateInfo?.footer?.backgroundColor || '#FFFFFF',
            footerTextColor:
              this.selectedTemplateInfo?.footer?.textColor || '#000000',
            footerDesign: this.selectedTemplateInfo?.footer?.footerDesign,
            status: this.selectedTemplateInfo?.status,

            orgCompanyName:
              this.selectedTemplateInfo?.companyName ||
              this.orgProfileData?.displayName,
            orgAddress:
              this.selectedTemplateInfo?.address ||
              getLocationDetailsByObj(this.orgProfileData?.address),
            orgPhoneNo: this.selectedTemplateInfo?.phoneNumber,
            orgEmail: this.selectedTemplateInfo?.email,
            facebook:
              this.getSocialMediaId(this.selectedTemplateInfo, 'facebook') ||
              this.getSocialMediaId(this.orgProfileData, 'facebook'),
            instagram:
              this.getSocialMediaId(this.selectedTemplateInfo, 'instagram') ||
              this.getSocialMediaId(this.orgProfileData, 'instagram'),
            linkedin:
              this.getSocialMediaId(this.selectedTemplateInfo, 'linkedin') ||
              this.getSocialMediaId(this.orgProfileData, 'linkedin'),
            twitter:
              this.getSocialMediaId(this.selectedTemplateInfo, 'twitter') ||
              this.getSocialMediaId(this.orgProfileData, 'twitter'),
            whatsapp:
              this.getSocialMediaId(this.selectedTemplateInfo, 'whatsapp') ||
              this.getSocialMediaId(this.orgProfileData, 'whatsapp'),
          });
        });
    }

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this.qrCustomForm.valueChanges.subscribe((data: any) => {
      this.showEnquiryInfo = Object.keys(data).some(
        (key: string) => data[key] && this.isFieldVisible(key, data)
      );
    });
  }
  templateNameDuplicateCheck() {
    if (
      this.templateName === '' ||
      this.templateName === this.selectedTemplateInfo?.name
    ) {
      this.isTemplateNameExists = false;
    } else {
      this.store.dispatch(new TemplateNameExists(this.templateName));
      this.store.dispatch(new LoaderHide());
      this.store
        .select(getTemplateNameExists)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: boolean) => {
          this.isTemplateNameExists = data;
        });
    }
  }

  filterFields() {
    const isCustomLeadFormEnabled = this.globalSettingsDetails?.isCustomLeadFormEnabled;
    const excludedFields = isCustomLeadFormEnabled 
      ? ['BR', 'BHK', 'BHK Type']
      : ['BR'];

    this.filteredFields = this.enquiryInfoFields
      .filter(field => isCustomLeadFormEnabled 
        ? field.customization !== false 
        : field.customization !== true)
      .filter(field => !excludedFields.includes(field.displayName));
  }

  onTemplateNameChange() {
    this.templateNameChanged.next();
  }

  toggleAllCheckboxes() {
    this.qrCustomForm.markAsDirty();
    const allChecked = this.allCheckboxesChecked();
    this.enquiryInfoFields?.forEach((field) => {
      this.qrCustomForm.get(field.controlName).setValue(!allChecked);
    });
  }

  allCheckboxesChecked(): boolean {
    const isCustomLeadFormEnabled = this.globalSettingsDetails?.isCustomLeadFormEnabled;
    const excludedFields = isCustomLeadFormEnabled 
      ? ['BR', 'BHK', 'BHK Type']
      : ['BR'];

    return this.enquiryInfoFields
      .filter(field => isCustomLeadFormEnabled 
        ? field.customization !== false 
        : field.customization !== true)
      .filter(field => !excludedFields.includes(field.displayName))
      .every(field => this.qrCustomForm.get(field.controlName)?.value);
  }

  isFieldVisible(fieldName: string, formData: any): boolean {
    const field = QR_DISPLAY_FIELDS.find(
      (item: any) => item.controlName === fieldName
    );
    return field && formData[fieldName] ? true : false;
  }

  toggleActiveImage(image: string): void {
    const index = this.activeImages.indexOf(image);
    if (index === -1) {
      this.activeImages.push(image);
    } else {
      this.activeImages.splice(index, 1);
    }
  }

  toggleColor(imageNumber: number): void {
    this.qrCustomForm.markAsDirty();
    Object.keys(this.isFiltered).forEach((key) => {
      this.isFiltered[+key] = true;
    });
    this.isFiltered[imageNumber] = false;
    this.qrCustomForm.patchValue({ headerDesign: imageNumber });
  }

  toggleImage(imageNumber: number): void {
    this.qrCustomForm.markAsDirty();
    Object.keys(this.isData).forEach((key) => {
      this.isData[+key] = true;
    });
    this.isData[imageNumber] = false;
    this.qrCustomForm.patchValue({ footerDesign: imageNumber });
  }

  removeHeaderSection(): void {
    Object.keys(this.isFiltered).forEach((key) => {
      this.isFiltered[+key] = true;
    });

    this.qrCustomForm.patchValue({ headerDesign: 0 });
    this.isAddHeaderClicked = true;
  }

  removeFooterSection(): void {
    Object.keys(this.isData).forEach((key) => {
      this.isData[+key] = true;
    });

    this.qrCustomForm.patchValue({ footerDesign: 0 });
    this.isAddFooterClicked = true;
  }

  onEditSectionHeader() {
    if (this.selectedSection === 'Contents') {
      this.showChooseTemplate = true;
      this.selectedSection = 'Header';
    } else if (this.selectedSection === 'Header') {
      this.showChooseTemplate = true;
    }
  }

  onEditSectionFooter() {
    if (this.selectedSection === 'Contents') {
      this.showChooseTemplate = true;
      this.selectedSection = 'Footer';
    } else if (this.selectedSection === 'Footer') {
      this.showChooseTemplate = true;
    }
  }

  onFileSelected(event: any, imageSection: string) {
    const files: any[] = [];
    if (event.target.files && event.target.files[0]) {
      for (let i = 0; i < event.target.files.length; i++) {
        files.push(event.target.files[i]);
        let reader = new FileReader();
        reader.onload = (eventOnload: any) => {
          const image = new Image();
          image.src = eventOnload.target.result;
          image.onload = (rs: any) => {
            const img_height = rs.currentTarget['height'];
            const img_width = rs.currentTarget['width'];
          };
          if (imageSection == 'HeaderLogo') {
            this.headerPic = eventOnload.target.result;
            if (this.headerPic?.includes('data:')) {
              this.s3UploadService
                .uploadImageBase64([this.headerPic], FolderNamesS3.Images)
                .pipe(takeUntil(this.stopper))
                .subscribe((response: any) => {
                  if (response.data.length) {
                    this.headerPic = response.data?.[0];
                  }
                });
            }
          }
          if (imageSection == 'FooterLogo') {
            this.footerPic = eventOnload.target.result;
            if (this.footerPic?.includes('data:')) {
              this.s3UploadService
                .uploadImageBase64([this.footerPic], FolderNamesS3.Images)
                .pipe(takeUntil(this.stopper))
                .subscribe((response: any) => {
                  if (response.data.length) {
                    this.footerPic = response.data?.[0];
                  }
                });
            }
          }
        };
        reader.readAsDataURL(event.target.files[i]);
      }
    }
  }

  getSocialMediaId(item: any, platform: string): string | null {
    const socialMediaEntry = item?.socialMedias?.find(
      (entry: any) => entry.socialMediaPlatform === platform
    );

    return socialMediaEntry ? socialMediaEntry.socialMediaId : null;
  }

  getSocialMediaLink(platform: string): string {
    const socialMediaId =
      this.getSocialMediaId(this.selectedTemplateInfo, platform) ||
      this.getSocialMediaId(this.orgProfileData, platform);

    if (socialMediaId) {
      return `${socialMediaId}`;
    }
    return '#';
  }

  updatePropertyType(controlName: string) {
    const subPropertyTypeControl = this.qrCustomForm.get('subPropertyType');
    const basePropertyTypeControl = this.qrCustomForm.get('basePropertyType');

    if (controlName === 'subPropertyType') {
      const newValue = subPropertyTypeControl.value;
      basePropertyTypeControl.setValue(newValue);
    } else if (controlName === 'basePropertyType') {
      const newValue = basePropertyTypeControl.value;
      subPropertyTypeControl.setValue(newValue);
    }
  }

  saveData() {
    if (!this.qrCustomForm.valid) {
      validateAllFormFields(this.qrCustomForm);
      return;
    }
    const qrFormData = this.qrCustomForm.value;
    const payload: any = {
      name: this.templateName,
      id: this.selectedTemplateId,
      companyName: qrFormData.orgCompanyName,
      email: qrFormData.orgEmail,
      AgencyName: this.selectedTemplateInfo?.agencyName,
      phoneNumber: qrFormData.orgPhoneNo,
      address: qrFormData.orgAddress,
      status: this.selectedTemplateInfo?.status,
      header: {
        logoUrl: this.headerPic,
        backgroundColor: qrFormData.headerBgColor,
        textColor: qrFormData.headerTextColor,
        headerDesign: qrFormData.headerDesign,
      },
      footer: {
        logoUrl: this.footerPic,
        backgroundColor: qrFormData.footerBgColor,
        textColor: qrFormData.footerTextColor,
        footerDesign: qrFormData.footerDesign,
      },
      formDtos: this.qrCustomFormFields.map((field) => ({
        formId: field.id,
        isSelected: qrFormData[field.displayName] || false,
        fieldName: field.displayName,
      })),
      socialMedias: this.socialMedia.map((field: any) => ({
        socialMediaId: qrFormData[field.controlName],
        socialMediaPlatform: field.controlName,
      })),
    };

    if (this.selectedTemplateId) {
      this.store.dispatch(new UpdateQrForm(payload, this.selectedTemplateId));
    } else {
      this.store.dispatch(new AddQrForm(payload));
    }
    this.router.navigate(['global-config/manage-qr']);
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
