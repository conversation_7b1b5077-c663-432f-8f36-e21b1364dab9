<div id="swipeArea">
  <div class="px-20" [ngClass]="{'blinking': (isNextDataLoading || isPreviousDataLoading) && !isMobileView}">
    <div class="flex-between mt-10">
      <div class="align-center mt-10">
        <div class="d-flex cursor-pointer">
          <span class="icon ic-black ic-xs ic-circle-chevron-left mr-8" *ngIf="!whatsAppComp"
            (click)="modalRef.hide()"></span>
          <h4 class="fw-600">{{ "GLOBAL.lead" | translate }} {{ "LEADS.preview" | translate }}</h4>
        </div>
      </div>
      <div class="flex-end ph-d-none flex-grow-1">
        <ng-container *ngIf="!whatsAppComp">
          <div title="Previous Lead" class="mt-4 bg-coal icon-badge" (click)="prevData();getNextLead()"
            [ngClass]="{'bg-black-200 pe-none' : (foundData == 0 && isMobileView) || (!isMobileView && isFirstPage && foundData == 0), 'blinking': (isPreviousDataLoading && !isMobileView)}">
            <span class="icon ic-chevron-left m-auto ic-xxs"></span>
          </div>
        </ng-container>
        <div class="w-70">
          <leads-actions [data]="data" [isLeadPreviewOpen]="true" (changeSelection)="onSectionSelect($event)"
            [whatsAppComp]="whatsAppComp"></leads-actions>
        </div>
        <ng-container *ngIf="!whatsAppComp">
          <div title="Next Lead" class="mt-4 bg-coal icon-badge" (click)="nextData();getNextLead()"
            [ngClass]="{'bg-black-200 pe-none' : (foundData == (cardData?.length - 1) && isMobileView) || (totalPages === currentPage && foundData == (cardData?.length - 1)), 'blinking': (isNextDataLoading && foundData == (cardData?.length - 1)) || (isNextDataLoading && !isMobileView)}">
            <span class="icon ic-chevron-right m-auto ic-xxs"></span>
          </div>
        </ng-container>
      </div>
    </div>
    <div class="bg-secondary my-16 p-16 br-4 text-mud text-sm ip-w-100-40">
      <h4 class="fw-600 text-coal text-truncate-1 break-all">
        {{ data?.name }}
      </h4>
      <div class="align-center mt-8">
        <ng-container *ngIf="!globalSettingsData?.isMaskedLeadContactNo">
          <span class="align-center">
            <span class="icon ic-Call mr-6 ic-slate-90 ic-xxxs"></span>
            <span>{{data?.contactNo ? data?.contactNo : "---" }}</span>
            <!-- <a [href]="data.contactNo ? 'tel:' + data.contactNo : ''">{{
              data?.contactNo
              }}</a> -->
          </span>
          <span class="align-center mr-20">
            <span class="icon ic-Call ic-slate-90 ic-xxxs mr-6 ml-20"></span>
            <span>{{data?.alternateContactNo ? data?.alternateContactNo : "---" }}</span>
            <!-- <a [ngClass]="{ 'pe-none': !data.alternateContactNo }" [href]="'tel:' + data.alternateContactNo">{{
              data?.alternateContactNo ? data?.alternateContactNo : "---" }}</a> -->
          </span>
          <span class="align-center mr-20">
            <span class="icon ic-Call ic-slate-90 ic-xxxs mr-6 ml-20"></span>
            <span>{{data?.landLine ? data?.landLine : "---" }}</span>
            <!-- <a [ngClass]="{ 'pe-none': !data.leadNumber }" [href]="'tel:' + data.leadNumber">{{
              data?.leadNumber ? data?.leadNumber : "---" }}</a> -->
          </span>
          <span class="align-center">
            <span class="icon ic-mail ic-slate-90 ic-xxs mr-6 ml-2"></span>
            <span class="text-truncate-1 break-all">{{data?.email ? data?.email : "---" }}</span>
            <!-- <a [href]="'mailto:' + data.email" [ngClass]="{ 'pe-none': !data.email }">
              {{ data?.email ? data.email : "---" }}</a> -->
          </span>
        </ng-container>
      </div>
      <div class="align-center mt-8">
        <span class="icon ic-location-circle ic-slate-90 ic-xxs mr-6"></span>
        <drag-scroll class="scrollbar scroll-hide">
          <div class="text-truncate-1 break-all">{{addresses}}
          </div>
        </drag-scroll>
      </div>
      <div class="d-flex mt-8">
        <span class="icon ic-apartment ic-slate-90 ic-xxs mr-6"></span>
        <drag-scroll class="scrollbar scroll-hide">
          <div class="align-center text-nowrap">
            <span class="align-center" *ngFor="let enquiry of data?.enquiry?.enquiryTypes || ['']; let last = last">
              <span class="text-accent-green text-sm fw-600">{{enquiry
                ? EnquiryType[enquiry] : "--"}}</span>
              <span *ngIf="!last" class="dot dot-xxs bg-accent-green mx-6"></span>
            </span>
            <ng-container *ngIf="data?.enquiry?.propertyTypes?.length">
              <span class="dot dot-xxs bg-dark-700 mx-6"></span>
              <span>{{ data.enquiry.propertyTypes?.[0]?.displayName }}</span>
            </ng-container>
            <ng-container *ngIf="data?.enquiry?.propertyTypes?.length">
              <span class="dot dot-xxs bg-dark-700 mx-6"></span>
              <ng-container *ngIf="data?.enquiry?.propertyTypes?.length; else noSubtype">
                <ng-container *ngFor="let type of data?.enquiry?.propertyTypes; let last = last">
                  <span *ngIf="type?.childType?.displayName">
                    {{ type.childType.displayName}}<span *ngIf="!last">, </span>
                  </span>
                </ng-container>
              </ng-container>
              <ng-template #noSubtype>
                <h6>--</h6>
              </ng-template>
            </ng-container>
            <ng-container *ngIf="data?.enquiry?.bhKs?.length">
              <span class="dot dot-xxs bg-dark-700 mx-6"></span>
              <span>{{ bhkNo && bhkNo?.length > 0 ? bhkNo : '' }}</span>
            </ng-container>
            <ng-container *ngIf="!globalSettingsData?.isCustomLeadFormEnabled">
              <span class="dot dot-xxs bg-dark-700 mx-6"></span>
              <span>{{ bhkTypes }}</span>
            </ng-container>
            <ng-container *ngIf="data?.enquiry?.lowerBudget || data?.enquiry?.upperBudget">
              <span class="dot dot-xxs bg-dark-700 mx-6"></span>
              <span>{{ formatBudget(data?.enquiry?.lowerBudget, data?.enquiry?.currency || defaultCurrency) }}</span>
              <span *ngIf="data?.enquiry?.lowerBudget && data?.enquiry?.upperBudget">-
                {{ formatBudget(data?.enquiry?.upperBudget, data?.enquiry?.currency || defaultCurrency) }}</span>
            </ng-container>
          </div>
        </drag-scroll>
      </div>
    </div>
    <div class="Horizontal-navbar">
      <ng-container *ngFor="let item of filteredNavigationItems()">
        <div class="nav-item" [ngClass]="{ active: selectedSection === item.section }"
          (click)="onSectionSelect(item.section)">
          {{ item.label | translate }}
        </div>
      </ng-container>
    </div>
    <div *ngIf="selectedSection == 'Overview'">
      <div class="ph-h-100-275 scrollbar pr-12" [ngClass]="whatsAppComp ? 'h-100-377' : 'h-100-250'">
        <div class="flex-between mt-8" *ngIf="canUpdateStatus">
          <div class="field-label mt-0">
            {{ "GLOBAL.lead" | translate }} {{ "GLOBAL.status" | translate }}
          </div>
          <div class="btn btn-sm btn-linear-green text-nowrap br-8 mr-10" (click)="checkStatus('Status')"
            *ngIf="currentPath === '/invoice' ? (canUpdateInvoice && !data?.isArchived) : canEditLead && !(!canUpdateBookedLead && data?.status?.displayName == 'Booked') && !data?.isArchived && data?.status?.status !== 'invoiced' && data?.assignTo !== EMPTY_GUID">
            {{ "LEADS.change-status" | translate }}
          </div>
        </div>
        <div class="bg-secondary mt-12 mr-12 px-16 py-12 br-4" *ngIf="canUpdateStatus">
          <div class="align-center w-100">
            <div class="align-center w-50">
              <span class="icon ic-person-walking ic-slate-90 ic-xxs mr-8"></span>
              <h5 class="fv-sm-caps fw-600">{{ data?.status?.displayName }}<span
                  *ngIf="data?.status?.childType?.displayName"> - {{data.status.childType.displayName}}</span></h5>
            </div>
            <div class="align-center w-50" *ngIf="data.scheduledDate">
              <span class="icon ic-alarm ic-slate-90 ic-xxs mr-8"></span>
              <div>
                <h5 class="fv-sm-caps fw-600">
                  {{data.scheduledDate ? getTimeZoneDate(data.scheduledDate,userData?.timeZoneInfo?.baseUTcOffset,
                  'dateWithTime') :
                  "---"}}
                </h5>
                <div class="text-truncate-1 break-all text-sm"
                  *ngIf="userData?.timeZoneInfo?.timeZoneName && data.scheduledDate && userData?.shouldShowTimeZone">
                  ({{userData?.timeZoneInfo?.timeZoneName }})
                </div>
              </div>
            </div>
          </div>
          <div class="d-flex mt-16" *ngIf="data?.notes">
            <span class="icon ic-message-lines ic-slate-90 ic-xxs mr-8"></span>
            <p [innerHTML]="data?.notes ? convertUrlsToLinks(data.notes) : '---'"
              class="text-black-20 text-sm word-break line-break text-truncate-2">
            </p>
          </div>
        </div>
        <div
          *ngIf="currentPath === '/invoice' ? (canUpdateInvoice && !data?.isArchived) : (data?.status?.actionName !== 'Invoiced' && canEditTags && !data?.isArchived)">
          <div class="field-label">{{ "LEADS.tags" | translate }}</div>
          <div class="d-flex flex-wrap bg-white" [ngClass]="{'pe-none': !canEditTags}">
            <ng-container *ngFor="let flag of flagOptions">
              <div class="align-center br-4 cursor-pointer p-6 mr-10 mt-6 border"
                [ngClass]="getActiveBackgroundColor(flag)" (click)="flagAction(flag)">
                <div class="flex-center w-16 h-16 br-50 mr-4" [ngClass]="{'bg-white': flag.isActive }">
                  <img [type]="'leadrat'" [appImage]="flag.isActive ? flag?.activeImagePath : flag?.inactiveImagePath"
                    class="w-16 h-16" alt="">
                </div>
                <span [ngClass]="{'opacity-5': !flag.isActive}">{{ flag.name | translate }}</span>
              </div>
            </ng-container>
          </div>
        </div>
        <individual-reassign [data]="data" [isLastLead]="isLastLead"
          [whatsAppComp]="whatsAppComp"></individual-reassign>
        <div *ngIf="data?.callRecordingUrls">
          <div class="field-label">{{ "LEADS.ivr-call-recordings" | translate }}</div>
          <div class="flex-between mr-12 py-12 px-10 bg-secondary br-4">
            <div class="align-center">
              <span class="icon ic-ivr-printer ic-black ic-large mr-16"></span>
              <h5 class="text-dark fw-semi-bold">
                {{ "LEADS.call-are-recorded" | translate }}
              </h5>
            </div>
            <div class="btn btn-sm btn-linear-green w-110 flex-center" (click)="
                data.assignTo !== EMPTY_GUID
                  ? openAudioPlayer(audioOption)
                  : openUnassignModal()
              ">
              View all
            </div>
          </div>
        </div>
        <div class="field-label">{{ "GLOBAL.lead" | translate }} {{ "LEADS.enquiry-info" | translate }}</div>
        <div class="bg-secondary p-12 br-4 fw-semi-bold text-large">
          <div class="align-center flex-wrap">
            <div class="mr-40 ip-mr-10" *ngIf="canViewLeadSource">
              <div class="mb-4 text-black-200 text-sm fv-sm-caps">
                {{ "GLOBAL.lead" | translate }} {{ "LEADS.source" | translate }}
              </div>
              <h5 class="fw-600 text-truncate-1 break-all">{{ LeadSource[data.enquiry?.leadSource] ?
                LeadSource[data.enquiry?.leadSource] : '---'
                }}</h5>
            </div>
            <div class="mr-40 ip-mr-10" *ngIf="canViewLeadSource">
              <div class="mb-4 text-black-200 text-sm fv-sm-caps">
                {{ "LEADS.sub-source" | translate }}
              </div>
              <h5 class="fw-600 text-wrap">{{ data.enquiry?.subSource ? data.enquiry?.subSource :'--' }}</h5>
            </div>
            <div class="mr-40 ip-mr-10">
              <div class="mb-4 text-black-200 text-sm fv-sm-caps">{{ "LEAD_FORM.enquired-for" | translate }}
              </div>
              <h5 class="fw-600">{{ enquiryTypes }}
              </h5>
            </div>
            <div>
              <div class="mb-4 text-black-200 text-sm fv-sm-caps">Purpose
              </div>
              <h5 class="fw-600">{{ PurposeType[data.enquiry?.purpose] ?
                PurposeType[data.enquiry?.purpose] : '---' }}
              </h5>
            </div>
          </div>
          <div class="align-center mt-16">
            <span class="icon ic-apartment ic-accent-green ic-xxs mr-8"></span>
            <h5 class="text-accent-green fw-700">{{ "GLOBAL.details" | translate }}</h5>
          </div>
          <div class="mt-10" *ngIf="data?.properties?.length != 0">
            <div class="text-black-200 fv-sm-caps">{{ "LABEL.property" | translate }}</div>
            <div class="d-flex flex-wrap">
              <div *ngFor="let value of data?.properties">
                <div *ngIf="value?.title" class="py-4 px-8 bg-black-100 br-20 text-gray-110 mr-4 mt-6">
                  {{ value?.title }}
                </div>
              </div>
            </div>
          </div>
          <div class="mt-20" *ngIf="data?.projects?.length != 0">
            <div class="text-black-200 fv-sm-caps">{{ "SIDEBAR.project" | translate }}</div>
            <div class="d-flex flex-wrap">
              <div *ngFor="let value of data?.projects">
                <div *ngIf="value?.name" class="py-4 px-8 bg-black-100 br-20 text-gray-110 mr-4 mt-6">
                  {{ value?.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="d-flex flex-wrap w-100">
            <div class="w-25 ip-w-33 ph-w-50 pr-10">
              <div class="fv-sm-caps mb-4 mt-16 text-black-200">Property {{ "LABEL.type" | translate }}</div>
              <h5 class="fw-600">
                {{data?.enquiry?.propertyTypes?.[0]?.displayName ? data?.enquiry?.propertyTypes?.[0]?.displayName :
                "--"}}
              </h5>
            </div>
            <div class="w-25 ip-w-33 ph-w-50 pr-10">
              <div class="fv-sm-caps mb-4 mt-16 text-black-200">Property Sub-type </div>
              <div class="fw-600 mt-4 mr-30 text-truncate-1 break-all ip-mr-10 header-5 cursor-pointer">
                <ng-container *ngIf="data?.enquiry?.propertyTypes?.length; else noSubtype">
                  <ng-container *ngFor="let type of data?.enquiry?.propertyTypes; let last = last">
                    <span [title]="getSubtypesTitle()">
                      {{ type.childType.displayName }}<span *ngIf="!last">, </span>
                    </span>
                  </ng-container>
                </ng-container>

                <ng-template #noSubtype>
                  --
                </ng-template>
              </div>
            </div>
            <ng-container
              *ngIf="data?.enquiry?.propertyTypes?.[0]?.displayName == 'Residential' &&  !hasPlotSubType(data?.enquiry?.propertyTypes)">
              <div *ngIf="!globalSettingsData?.isCustomLeadFormEnabled" class="w-25 ip-w-33 ph-w-50 pr-10">
                <div class="fv-sm-caps mb-4 mt-16 text-black-200">
                  {{ 'BHK' }}
                </div>
                <h5 class="fw-600">{{bhkNo && bhkNo?.length > 0 ? bhkNo : '--'}}</h5>
              </div>
              <div class="w-25 ip-w-33 ph-w-50 pr-10" *ngIf="!globalSettingsData?.isCustomLeadFormEnabled">
                <div class="fv-sm-caps mb-4 mt-16 text-black-200">{{ "PROPERTY.bhk" | translate }} {{ "LABEL.type" |
                  translate }}</div>
                <h5 class="fw-600">{{bhkTypes}}</h5>
              </div>
              <ng-container *ngIf="globalSettingsData?.isCustomLeadFormEnabled">
                <div class="w-25 ip-w-33 ph-w-50 pr-10">
                  <div class="fv-sm-caps mb-4 mt-16 text-black-200">Beds</div>
                  <h5 class="fw-600 text-truncate-1 break-all" [title]="beds">{{beds?.length ? beds : '--'}}</h5>
                </div>
                <div class="w-25 ip-w-33 ph-w-50 pr-10">
                  <div class="fv-sm-caps mb-4 mt-16 text-black-200">Baths</div>
                  <h5 class="fw-600 text-truncate-1 break-all" [title]="data?.enquiry?.baths">{{data?.enquiry?.baths &&
                    data?.enquiry?.baths?.length > 0 ? data?.enquiry?.baths :
                    '--'}}</h5>
                </div>
              </ng-container>
            </ng-container>
            <ng-container *ngIf="globalSettingsData?.isCustomLeadFormEnabled">
              <div class="w-25 ip-w-33 ph-w-50 pr-10"
                *ngIf="data?.enquiry?.propertyTypes?.[0]?.displayName == 'Commerical' || data?.enquiry?.propertyTypes?.[0]?.displayName == 'Residential'">
                <div class="fv-sm-caps mb-4 mt-16 text-black-200">Furnish Status</div>
                <h5 class="fw-600">{{data?.enquiry?.furnished ? FurnishStatus[data?.enquiry?.furnished] : '--'}}</h5>
              </div>
              <div class="w-25 ip-w-33 ph-w-50 pr-10"
                *ngIf="data?.enquiry?.propertyTypes?.[0]?.displayName == 'Commerical' || data?.enquiry?.propertyTypes?.[0]?.displayName == 'Residential'">
                <div class="fv-sm-caps mb-4 mt-16 text-black-200">Preferred Floors</div>
                <h5 class="fw-600">{{data?.enquiry?.floors && data?.enquiry?.floors?.length > 0 ?
                  data?.enquiry?.floors : '--'}}</h5>
              </div>
              <div class="w-25 ip-w-33 ph-w-50 pr-10">
                <div class="fv-sm-caps mb-4 mt-16 text-black-200">Offering Type</div>
                <h5 class="fw-600">{{data?.enquiry?.offerType ? OfferType[data?.enquiry?.offerType] : '--'}}</h5>
              </div>
            </ng-container>
            <div class="w-25 ip-w-33 ph-w-50 pr-10">
              <div class="fv-sm-caps mb-4 mt-16 text-black-200">Min. {{ "LABEL.budget" | translate }}</div>
              <h5 class="fw-600">
                {{ data?.enquiry?.lowerBudget ? formatBudget(data?.enquiry?.lowerBudget, data?.enquiry?.currency) :
                "--"
                }}
              </h5>
            </div>
            <div class="w-25 ip-w-33 ph-w-50 pr-10">
              <div class="fv-sm-caps mb-4 mt-16 text-black-200">Max. {{ "LABEL.budget" | translate }}</div>
              <h5 class="fw-600">{{ data?.enquiry?.upperBudget ? formatBudget(data?.enquiry?.upperBudget,
                data?.enquiry?.currency) : "--" }}
              </h5>
            </div>
          </div>
          <div *ngIf="data?.enquiry?.addresses?.length" class="align-center mt-16">
            <span class="icon ic-location-circle ic-xxs ic-accent-green mr-8"></span>
            <h5 class="fw-700 text-accent-green">{{ "LOCATION.location" | translate }}</h5>
          </div>
          <div class="mb-10" *ngFor="let address of data?.enquiry?.addresses; let last = last"
            [ngClass]="{'border-bottom pb-4': !last}">
            <div class="align-center mt-12">
              <span class="dot dot-xxs bg-slate-250 mr-8"> </span>
              <span>{{getLocationDetailsByObj(address)}}
              </span>
            </div>
          </div>
          <div class="align-center mt-16">
            <span class="icon ic-paper-clip ic-xxs ic-accent-green mr-8"></span>
            <h5 class="fw-700 text-accent-green">{{ "SIDEBAR.others" | translate }}</h5>
          </div>
          <div class="w-100 d-flex flex-wrap">
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">{{'INTEGRATION.agency-name' | translate}}</h5>
              <div class="fw-600 mt-4 mr-30 text-truncate-1 break-all ip-mr-10 header-5 cursor-pointer">
                <ng-container *ngIf="data?.agencies && data?.agencies?.length > 0; else noAgencies">
                  <ng-container *ngFor="let agency of data?.agencies; let last = last">
                    {{ agency.name }}<span *ngIf="!last">, </span>
                  </ng-container>
                </ng-container>
                <ng-template #noAgencies>
                  --
                </ng-template>
              </div>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">{{'GLOBAL.assign-from' | translate}}</h5>
              <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">{{getAssignedToDetails(data.assignedFrom,
                users, true) || '--'}}</h5>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">{{'AUTH.company-name' | translate}}</h5>
              <h5 class="fw-600 mt-4 text-truncate-1 word-break line-break mr-30 ip-mr-10"> {{data.companyName ?
                data.companyName: "--"}}
              </h5>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">{{'LEADS.carpet-area' | translate}}</h5>
              <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10"> {{data?.enquiry?.carpetArea ?
                data?.enquiry?.carpetArea: "--"}} {{data?.enquiry?.carpetArea && data?.enquiry?.carpetAreaUnit ?
                data?.enquiry?.carpetAreaUnit : ''}}
              </h5>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">Built-up Area</h5>
              <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10"> {{data?.enquiry?.builtUpArea ?
                data?.enquiry?.builtUpArea:
                "--"}} {{data?.enquiry?.builtUpArea && data?.enquiry?.builtUpAreaUnit ?
                data?.enquiry?.builtUpAreaUnit : ''}}
              </h5>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">Saleable Area</h5>
              <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10"> {{data?.enquiry?.saleableArea ?
                data?.enquiry?.saleableArea:
                "--"}} {{data?.enquiry?.saleableArea && data?.enquiry?.saleableAreaUnit ?
                data?.enquiry?.saleableAreaUnit : ''}}
              </h5>
            </div>
            <ng-container *ngIf="globalSettingsData?.isCustomLeadFormEnabled">
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Property Area</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10"> {{data?.enquiry?.propertyArea ?
                  data?.enquiry?.propertyArea:
                  "--"}} {{data?.enquiry?.propertyArea && data?.enquiry?.propertyAreaUnit ?
                  data?.enquiry?.propertyAreaUnit : ''}}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Net Area</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10"> {{data?.enquiry?.netArea ?
                  data?.enquiry?.netArea:
                  "--"}} {{data?.enquiry?.netArea && data?.enquiry?.netAreaUnit ?
                  data?.enquiry?.netAreaUnit : ''}}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Unit Number/Name</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 word-break line-break mr-30 ip-mr-10"> {{data.enquiry?.unitName ?
                  data?.enquiry?.unitName:"--" }}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Cluster Name</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 word-break line-break mr-30 ip-mr-10">
                  {{data.enquiry?.clusterName ?
                  data?.enquiry?.clusterName:"--" }}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Nationality
                </h5>
                <h5 [title]="data?.nationality"
                  class="fw-600 mt-4 text-truncate-1 word-break line-break mr-30 ip-mr-10">
                  {{data?.nationality ?
                  data?.nationality:"--" }}
                </h5>
              </div>
            </ng-container>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">Deleted Date</h5>
              <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10"> {{
                data.archivedOn
                ? getTimeZoneDate( data.archivedOn, userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYearText')
                : "---"
                }}
              </h5>
              <div class="text-truncate-1 break-all text-sm"
                *ngIf="userData?.timeZoneInfo?.timeZoneName && data.archivedOn && userData?.shouldShowTimeZone">
                ({{userData?.timeZoneInfo?.timeZoneName }})
              </div>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">Possession Needed By</h5>
              <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10"> {{data.enquiry?.possessionDate ?
                getTimeZoneDate(data.enquiry?.possessionDate, userData?.timeZoneInfo?.baseUTcOffset,
                'dayMonthYearText')
                : "---"
                }}
              </h5>
              <div class="text-truncate-1 break-all text-sm"
                *ngIf="userData?.timeZoneInfo?.timeZoneName && data.enquiry?.possessionDate && userData?.shouldShowTimeZone">
                ({{userData?.timeZoneInfo?.timeZoneName }})
              </div>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">Referral Name</h5>
              <h5 class="fw-600 mt-4 text-truncate-1 word-break line-break mr-30 ip-mr-10"> {{data.referralName ?
                data.referralName:"--" }}
              </h5>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">Referral Number</h5>
              <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10"> {{data.referralContactNo ?
                data.referralContactNo:"--" }}
              </h5>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">Referral Email</h5>
              <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10"> {{data.referralEmail ?
                data.referralEmail : "--" }}
              </h5>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">Serial Number</h5>
              <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10"> {{data.serialNumber ?
                data.serialNumber: "--"}}</h5>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">{{ "LEADS.sourcing-manager" | translate }}</h5>
              <h5 class="fw-600 mt-4 mr-30 text-truncate-1 break-all ip-mr-10">{{data?.sourcingManager ?
                getUserName(data.sourcingManager) :
                "--"}}</h5>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">{{ "LEADS.closing-manager" | translate }}</h5>
              <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                {{data?.closingManager ? getUserName(data?.closingManager) : "--"}}
              </h5>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">{{ "LEAD_FORM.channel-partner-name" | translate }}</h5>
              <h5 class="fw-600 mt-4 mr-30 text-truncate-1 ip-mr-10">
                <ng-container *ngIf="data?.channelPartners?.length else empty">
                  <ng-container *ngFor="let partner of data?.channelPartners; let last = last">
                    {{partner?.firmName}}<ng-container *ngIf="!last">,
                    </ng-container></ng-container>
                </ng-container><ng-template #empty>--</ng-template>
              </h5>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">{{'LEAD_FORM.campaign-name' | translate}}</h5>
              <div class="fw-600 mt-4 mr-30 text-truncate-1 break-all ip-mr-10 header-5 cursor-pointer">
                <ng-container *ngIf="data?.campaigns && data?.campaigns?.length > 0; else noCampaign">
                  <ng-container *ngFor="let campaign of data?.campaigns; let last = last">
                    {{ campaign.name }}<span *ngIf="!last">, </span>
                  </ng-container>
                </ng-container>
                <ng-template #noCampaign>
                  --
                </ng-template>
              </div>
            </div>
            <div class="w-33 ip-w-50 mt-12">
              <h5 class="fv-sm-caps text-black-200">{{ "LEADS.profession" | translate }}</h5>
              <h5 class="fw-600 mt-4 mr-30 text-truncate-1 break-all ip-mr-10">
                {{data?.profession ? getProfession(data.profession) : "--"}}
              </h5>
            </div>
  
            <div class="w-100 ip-w-50 mt-12">
              <div class="fv-sm-caps text-black-200">{{ "LEADS.customer-location" | translate }}</div>
              <h5 class="fw-600 mt-4 mr-30 text-truncate-1 break-all text-wrap ip-mr-10">
                {{ data?.address?.subLocality && data?.address?.city && data?.address?.state ?
                data?.address?.subLocality
                +
                ", " +
                data?.address?.city + ", " + data?.address?.state : data?.address?.city
                ? data?.address?.city : "--"}}
              </h5>
            </div>
          </div>
        </div>
        <ng-container *ngIf="data?.additionalProperties">
          <div class="field-label">Facebook Info</div>
          <div class="bg-secondary p-12 br-4 fw-semi-bold text-large">
            <div class="w-100 d-flex flex-wrap">
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Ad ID</h5>
                <div class="fw-600 mt-4 mr-30 text-truncate-1 break-all ip-mr-10 header-5 cursor-pointer">
                  {{data?.additionalProperties?.AdId ? data?.additionalProperties?.AdId : '--'}}
                </div>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Ad Name</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.AdName ? data?.additionalProperties?.AdName : '--'}}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Ad Set ID</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.AdSetId ? data?.additionalProperties?.AdSetId : '--'}}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Ad Set Name</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.AdSetName ? data?.additionalProperties?.AdSetName : '--'}}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Page ID</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 word-break line-break mr-30 ip-mr-10">
                  {{data?.additionalProperties?.PageId ? data?.additionalProperties?.PageId : '--'}}
                </h5>
              </div>
              <!-- <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Status</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.Status ? data?.additionalProperties?.Status : '--'}}
                </h5>
              </div> -->
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Campaign ID</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.CampaignId ? data?.additionalProperties?.CampaignId : '--'}}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Campaign Name</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.CampaignName ? data?.additionalProperties?.CampaignName : '--'}}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Ad Account ID</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.AdAccountId ? data?.additionalProperties?.AdAccountId : '--'}}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Ad Account Name</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.AdAccountName ? data?.additionalProperties?.AdAccountName : '--'}}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Name</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.Name ? data?.additionalProperties?.Name : '--'}}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Facebook ID</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.FacebookId ? data?.additionalProperties?.FacebookId : '--'}}
                </h5>
              </div>
              <!-- <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">IsAutomated</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.IsAutomated ? data?.additionalProperties?.IsAutomated : '--'}}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Automation ID</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.AutomationId !== EMPTY_GUID ? data?.additionalProperties?.AutomationId :
                  '--'}}
                </h5>
              </div>
              <div class="w-33 ip-w-50 mt-12">
                <h5 class="fv-sm-caps text-black-200">Is Subscribed</h5>
                <h5 class="fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10">
                  {{data?.additionalProperties?.IsSubscribed ? data?.additionalProperties?.IsSubscribed : '--'}}
                </h5>
              </div> -->
            </div>
          </div>
        </ng-container>
        <div class="border-bottom-slate-20 mt-16"></div>
        <div class="field-label">{{ "LEADS.last-activity" | translate }}</div>
        <div class="w-100 d-flex ph-flex-col mt-6">
          <div class="align-center">
            <h5 class="fw-700 text-mud mr-6">{{ "LEADS.created-by" | translate }}</h5>
            <div class="text-sm">
              <div class="fw-semi-bold mb-4">{{getAssignedToDetails(data.createdBy,
                users, true) || ''}}</div>
              <div class="text-black-200">At
                {{ getTimeZoneDate(data.createdOn,userData?.timeZoneInfo?.baseUTcOffset) }}</div>
              <div class="text-truncate-1 break-all text-sm"
                *ngIf="userData?.timeZoneInfo?.timeZoneName && data.createdOn && userData?.shouldShowTimeZone">
                ({{userData?.timeZoneInfo?.timeZoneName }})
              </div>
            </div>
          </div>
          <div class="border-left mx-20"></div>
          <div class="align-center ph-mt-10">
            <h5 class="fw-700 text-mud mr-6">{{ "LEADS.modified-by" | translate }}</h5>
            <div class="text-sm">
              <div class="fw-semi-bold mb-4">{{getAssignedToDetails(data?.lastModifiedBy,
                users, true) || ''}}</div>
              <div class="text-black-200">At
                {{ getTimeZoneDate(data.lastModifiedOn, userData?.timeZoneInfo?.baseUTcOffset) }}</div>
              <div class="text-truncate-1 break-all text-sm"
                *ngIf="userData?.timeZoneInfo?.timeZoneName && data.lastModifiedOn && userData?.shouldShowTimeZone">
                ({{userData?.timeZoneInfo?.timeZoneName }})
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <span [ngClass]="{'blinking': (isNextDataLoading || isPreviousDataLoading) && !isMobileView}">
    <div *ngIf="selectedSection == 'Status'">
      <div class="w-100 mt-20">
        <status-change *ngIf="!isCustomStatusEnabled" #statusChangeComponent [leadInfo]="data"
          [canShowStatusPopupInPreview]="canShowStatusPopupInPreview && showOnlyPopup"
          [canUpdateStatus]="canUpdateStatus" [isLeadPreview]="true" [isLastLead]="isLastLead"
          [closeLeadPreviewModal]="closeLeadPreviewModal" [whatsAppComp]="whatsAppComp"></status-change>
        <custom-status-change *ngIf="isCustomStatusEnabled" #customStatusChangeComponent [leadInfo]="data"
          [canShowStatusPopupInPreview]="canShowStatusPopupInPreview && showOnlyPopup"
          [canUpdateStatus]="canUpdateStatus" [isLeadPreview]="true" [isLastLead]="isLastLead"
          [closeLeadPreviewModal]="closeLeadPreviewModal" [whatsAppComp]="whatsAppComp"></custom-status-change>
      </div>
    </div>

    <div *ngIf="selectedSection == 'History'">
      <div class="w-100">
        <lead-history [data]="data" [whatsAppComp]="whatsAppComp"></lead-history>
      </div>
    </div>

    <div *ngIf="selectedSection == 'Notes'">
      <div class="w-100">
        <lead-notes [data]="data" (notesAdded)="updateNotes($event)" [whatsAppComp]="whatsAppComp"></lead-notes>
      </div>
    </div>

    <div *ngIf="selectedSection == 'Document'">
      <div class="w-100" [ngClass]="{'position-relative' : whatsAppComp }">
        <leads-document-upload [leadData]="data" [whatsAppComp]="whatsAppComp"></leads-document-upload>
      </div>
    </div>
  </span>

  <ng-template #audioOption>
    <h3 class="bg-coal px-24 py-12 text-white fw-semi-bold">
      {{ "LEADS.lead-call-recordings" | translate }}
    </h3>
    <div class="px-20 h-100-80 scrollbar">
      <ng-container *ngFor="let year of callRecordingDetails">
        <ng-container *ngFor="let month of year.yearData">
          <div class="align-center w-100 mt-20">
            <h5 class="text-accent-green fw-600">
              {{ moment(month?.months).format("MMM") }} {{ year?.years }}
            </h5>
            <div class="flex-grow-1 border-bottom ml-8"></div>
          </div>
          <ng-container *ngFor="let audio of month.monthData">
            <div class="bg-slate-150 br-10">
              <h5 class="mt-20 bg-coal px-20 py-10 br-10 text-white fw-600">
                {{ getTimeZoneDate(audio?.date,userData?.timeZoneInfo?.baseUTcOffset) }}
              </h5>
              <div class="position-relative">
                <audio #audioPlayer controls (play)="pauseOtherAudio(audioPlayer)" (canplay)="isLoading = false"
                  (loadedmetadata)="isLoading = false">
                  <source [src]="audio?.audioUrl" type="audio/mp3" />
                </audio>
                <div *ngIf="isLoading">
                  <ng-lottie [options]="loader" width="30px" height="30px" class="position-absolute top-10 left-6">
                  </ng-lottie>
                </div>
              </div>
            </div>
          </ng-container>
        </ng-container>
      </ng-container>
    </div>
  </ng-template>
  <div class="ph-d-block d-none bottom-0 p-10 position-absolute bg-white">
    <div class="d-flex ph-w-100-20 mble-preview">
      <div title="Previous Lead" class="bg-coal icon-badge mt-4" (click)="prevData();getNextLead()"
        [ngClass]="{'bg-black-200 pe-none' : (foundData == 0 && isMobileView) || (!isMobileView && isFirstPage && foundData == 0), 'blinking': (isPreviousDataLoading && !isMobileView)}">
        <span class="icon ic-chevron-left m-auto ic-xxs"></span>
      </div>
      <div class="w-100">
        <leads-actions [data]="data" [isLeadPreviewOpen]="true" (changeSelection)="onSectionSelect($event)"
          [whatsAppComp]="whatsAppComp"></leads-actions>
      </div>
      <div title="Next Lead" class="bg-coal icon-badge mt-4" (click)="nextData()"
        [ngClass]="{'bg-black-200 pe-none' : (foundData == (cardData?.length - 1) && isMobileView) || (totalPages === currentPage && foundData == (cardData?.length - 1)), 'blinking': (isNextDataLoading && foundData == (cardData?.length - 1)) || (isNextDataLoading && !isMobileView)}">
        <span class="icon ic-chevron-right m-auto ic-xxs"></span>
      </div>
    </div>
  </div>
</div>