<a (click)="openMatchLeadsModal(matchingLeadsModal)" class="text-accent-green border-bottom-green">
    {{'GLOBAL.match' | translate}}</a>
<ng-template #matchingLeadsModal>
    <div class="h-100vh text-coal">
        <div class="flex-between bg-coal w-100 px-16 py-12 text-white">
            <h3 class="fw-semi-bold">{{ 'PROPERTY.matching-leads' | translate }}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
        </div>
        <div class="px-16 pb-20 h-100-90 scrollbar">
            <div class="flex-wrap w-100 align-center text-sm fw-600 text-dark-gray-350">
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.title || params.name">
                    <div class="text-gray-90 text-nowrap">
                        <ng-container *ngIf="isProject">
                            Project Title:
                        </ng-container>
                        <ng-container *ngIf="isProjectUnit">
                            Project Unit:
                        </ng-container>
                        <ng-container *ngIf="!isProject && !isProjectUnit">
                            {{ 'PROPERTY.PROPERTY_DETAIL.title' | translate }}:
                        </ng-container>
                    </div>
                    <div class="ml-10 text-truncate-1 break-all">{{isProject || isProjectUnit ? params?.name :
                        params.title}}</div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8"
                    *ngIf="isProjectUnit ? params?.unitType?.displayName : params?.propertyType?.displayName">
                    <div class="text-gray-90 text-nowrap">
                        <ng-container *ngIf="isProjectUnit">
                            Unit Type:
                        </ng-container>
                        <ng-container *ngIf="!isProject && !isProjectUnit">
                            {{ 'LABEL.property' | translate}} {{'LABEL.type' |
                            translate}}: </ng-container>
                    </div>
                    <div class="ml-10 text-truncate-1 break-all">{{isProjectUnit ? params?.unitType?.displayName :
                        params?.propertyType.displayName}}</div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8"
                    *ngIf="isProjectUnit ? params?.unitType?.childType?.displayName :  params.propertyType?.childType?.displayName">
                    <div class="text-gray-90 text-nowrap">
                        <ng-container *ngIf="isProjectUnit">
                            Unit Sub-type
                        </ng-container>
                        <ng-container *ngIf="!isProject && !isProjectUnit">
                            {{'PROPERTY.sub-type' | translate}}:
                        </ng-container>
                    </div>
                    <div class="ml-10 text-truncate-1 break-all">{{isProjectUnit ?
                        params?.unitType?.childType?.displayName : params.propertyType?.childType?.displayName}}</div>
                </div>
                <ng-container
                    *ngIf="isProjectUnit ? (params?.unitType?.displayName == 'Residential' && params?.unitType?.childType?.displayName !== 'Plot') : 
                    (params?.propertyType?.displayName == 'Residential' && params?.propertyType?.childType?.displayName !== 'Plot')">
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.noOfBHK && !isListing">
                        <div class="text-gray-90 text-nowrap">
                            {{ ('PROPERTY.bhk' | translate) }}:
                        </div>
                        <div class="ml-10 text-truncate-1 break-all">{{params.noOfBHK}}</div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.bhkType && !isListing">
                        <div class="text-gray-90 text-nowrap">{{ 'PROPERTY.bhk' | translate}}
                            {{'LABEL.type' |
                            translate}}:</div>
                        <div class="ml-10 text-truncate-1 break-all">{{BHKType[params.bhkType]}}</div>
                    </div>
                </ng-container>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.enquiredFor">
                    <div class="text-gray-90 text-nowrap">Looking to:</div>
                    <div class="ml-10 text-truncate-1 break-all">{{EnquiryType[params.enquiredFor]}}</div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.budget">
                    <div class="text-gray-90 text-nowrap">{{'LEAD_FORM.budget' | translate}}:</div>
                    <div class="ml-10 text-truncate-1 break-all">{{params.budget}}</div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.price">
                    <div class="text-gray-90 text-nowrap">Price:</div>
                    <div class="ml-10 text-truncate-1 break-all">{{params.price}}</div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.area">
                    <div class="text-gray-90 text-nowrap">{{'PROPERTY.PROPERTY_DETAIL.size' | translate}}:</div>
                    <div class="ml-10 text-truncate-1 break-all">{{params.area}}</div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8" *ngIf="params.location">
                    <div class="text-gray-90 text-nowrap">{{'LOCATION.location' | translate}}:</div>
                    <div class="ml-10 text-truncate-1 break-all ph-w-100px">{{params.location}}</div>
                </div>
            </div>

            <div class="d-flex ip-flex-col bg-white w-100 border-gray mt-20">
                <div class="align-center px-10 flex-grow-1 border-end no-validation py-12 ip-br-0">
                    <ng-container>
                        <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
                        <input type="text" (keydown.enter)="searchTermSubject.next($event.target.value)"
                            (input)="isEmptyInput($event)" placeholder="type to search" [(ngModel)]="searchTerm"
                            class="border-0 outline-0 w-100">
                        <small class="text-muted text-nowrap">({{ 'LEADS.lead-search-prompt' | translate }})</small>
                    </ng-container>
                </div>
                <!-- manage colums -->
                <div class="d-flex ip-br-top">
                    <div class="align-center position-relative cursor-pointer d-flex border-end bg-white">
                        <span class="position-absolute left-15 z-index-2 fw-600 text-sm" *ngIf="!matchingRadius">
                            Matching Radius</span>
                        <div class=" w-130 no-validation">
                            <ng-select [virtualScroll]="true" [items]="matchingRadiusList" bindLabel="label"
                                bindValue="value" (change)="filterFunction()" [clearable]="true"
                                [(ngModel)]="matchingRadius" class="bg-white border-0 no-validation"
                                [searchable]="false">
                            </ng-select>
                        </div>
                    </div>
                    <div class="align-center position-relative cursor-pointer d-flex border-end">
                        <span class="position-absolute left-15 z-index-2 fw-600 text-sm">
                            {{ 'BUTTONS.manage-columns' | translate }}</span>
                        <div class="show-hide-gray w-140">
                            <ng-select [virtualScroll]="true" class="bg-white" [items]="columns" [multiple]="true"
                                ResizableDropdown [searchable]="false" [closeOnSelect]="false"
                                [ngModel]="defaultColumns" (change)="onColumnsSelected($event)">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span>{{item.label}}</div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="bg-coal text-white px-10 py-12 ip-w-30px  align-center cursor-pointer"
                        (click)="onSetColumnDefault()">
                        <span class="ip-d-none">{{ 'GLOBAL.default' | translate }}</span> <span
                            class="ic-refresh d-none ip-d-block"></span>
                    </div>
                    <!-- endManage column -->
                    <div class="show-dropdown-white align-center position-relative">
                        <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
                                {{ 'GLOBAL.show' | translate }}</span> {{ 'GLOBAL.entries' | translate }}</span>
                        <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" ResizableDropdown
                            class="w-150 tb-w-120px" (change)="assignCount()" [(ngModel)]="selectedPageSize"
                            [searchable]="false">
                            <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize"
                                [value]="pageSize">
                                {{pageSize}}</ng-option>
                        </ng-select>
                    </div>
                </div>
            </div>
            <div class="py-4 border-left border-right" *ngIf="rowData.length"></div>
            <div class="matching" *ngIf="rowData.length else noData">
                <ag-grid-angular #agGrid class="ag-theme-alpine" [gridOptions]="gridOptions"
                    (gridReady)="onGridReady($event)" [defaultColDef]="gridOptions.defaultColDef" [pagination]="true"
                    [paginationPageSize]="pageSize" [rowData]="rowData" [suppressPaginationPanel]="true">
                </ag-grid-angular>
            </div>

            <div class="flex-end mt-20" *ngIf="totalMatchCount">
                <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{(currOffset * pageSize) + 1}}
                    {{ 'GLOBAL.to-small' | translate }} {{(currOffset * pageSize) + pageSize > totalMatchCount ?
                    totalMatchCount : (currOffset * pageSize) + pageSize}} {{'GLOBAL.of-small' | translate }}
                    {{totalMatchCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
                <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]="getPages(totalMatchCount,pageSize)"
                    (pageChange)="onPageChange($event)">
                </pagination>
            </div>
        </div>
    </div>
    <!-- <div *ngIf="rowData?.length" class="align-center mt-10 p-20 box-shadow-5 position-absolute bottom-0 w-100">
        <div class="fw-600 cursor-pointer" (click)="getMatchingLead()">Click here to search as a separate filter or
            query</div>
    </div> -->
</ng-template>
<ng-template #noData>
    <div class="flex-center-col h-100-114">
        <img src="assets/images/layered-cards.svg" alt="No Data Found">
        <div class="header-3 fw-600 text-center">{{'PROFILE.no-data-found' | translate }}</div>
    </div>
</ng-template>