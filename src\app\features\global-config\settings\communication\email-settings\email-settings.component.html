<div class="p-20 pb-0">
    <div class="flex-between">
        <div class="align-center">
            <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-16" routerLink='/global-config'></div>
            <h5 class="text-black-100 fw-600"> Email SMTP Servers</h5>
        </div>
        <button class="btn-coal" (click)="openAddEmailSMTPModal(add)">
            <span class="ic-add icon ic-xxs"></span>
            <span class="ip-d-none ml-8">Add SMTP Server</span>
        </button>
    </div>
    <ng-container *ngIf="!emailSMTPListIsLoading; else loader">
        <div class="pt-10">
            <div class="bg-white w-100 border-gray flex-between">
                <form autocomplete="off" class="align-center border-end  w-100 py-10 px-12 no-validation">
                    <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
                    <input placeholder="type to search" name="searchQrForm" class="border-0 outline-0 w-100"
                        autocomplete="off" id="inpSearchQrForm"
                        (keydown.enter)="searchTermSubject.next($event.target.value)" (input)="isEmptyInput($event)"
                        [(ngModel)]="searchTerm">
                    <small class="text-muted text-nowrap ph-d-none pr-8">({{ 'LEADS.lead-search-prompt' | translate
                        }})</small>
                </form>
                <div class="show-dropdown-white align-center position-relative ip-br-0">
                    <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
                            {{ 'GLOBAL.show' | translate}}</span> {{ 'GLOBAL.entries' | translate }}</span>
                    <ng-select [items]="showEntriesSize" [formControl]="pageEntry" (change)="assignPageSize()"
                        [virtualScroll]="true" class="w-150 tb-w-120px" [searchable]="false">
                    </ng-select>
                </div>
            </div>
        </div>
        <div class="scrollbar scroll-hide tb-w-100-40 table-scrollbar">
            <table class="table standard-table no-vertical-border">
                <thead [ngClass]="emailSMTPList?.length ?'':'ph-d-none'">
                    <tr class="w-100 text-nowrap">
                        <th class="w-150 ">Sender Email</th>
                        <th class="w-150">SMTP Server</th>
                        <th class="w-70px">Port</th>
                        <th class="w-150">Username</th>
                        <th class="w-110">Actions</th>
                    </tr>
                </thead>
                <ng-container *ngIf="emailSMTPList?.length">
                    <tbody class="text-secondary fw-semi-bold max-h-100-270">

                        <tr *ngFor="let form of emailSMTPList; let i = index">
                            <td class="w-150 text-truncate">{{form.from}}</td>
                            <td class="w-150 text-truncate">{{form.serverName}}</td>
                            <td class="w-70px">
                                {{form.port}}
                            </td>
                            <td class="w-150 text-truncate">
                                {{form.userName}}
                            </td>
                            <td class="w-110">
                                <div class="align-center">
                                    <div title="Edit" class="bg-accent-green icon-badge"
                                        (click)="openEditModal(form,add)">
                                        <span class="icon m-auto ic-xxs ic-pen"></span></div>
                                    <div title="Assign Users" class="bg-blue-800 icon-badge"
                                        (click)="openAssignUserModal(form,assignUsers)">
                                        <span class="icon ic-assign-to m-auto ic-xxs"></span>
                                    </div>
                                    <div title="Delete" class="bg-light-red icon-badge"
                                        (click)="openDeleteModal(form.id,delete)">
                                        <span class="icon ic-delete m-auto ic-xxs"></span></div>
                                </div>
                            </td>
                        </tr>


                    </tbody>
                </ng-container>
            </table>
        </div>
        <div class="mt-16  flex-end" *ngIf="emailSMTPList?.length>0">
            <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{currOffset*currPageSize + 1}}
                {{ 'GLOBAL.to-small' | translate }} {{currOffset*currPageSize + emailSMTPList?.length}}
                {{ 'GLOBAL.of-small' | translate }} {{totalPages}} {{ 'GLOBAL.entries-small' | translate }}</div>
            <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]='getPages(totalPages,currPageSize)'
                (pageChange)="onPageChange($event)">
            </pagination>
        </div>
    </ng-container>
</div>
<ng-template #add>
    <div class="min-w-350 max-w-350">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>Enter Your SMTP Configuration </h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
        </div>
        <div class="h-100-100 scrollbar scroll-hde">
            <form class="px-20" [formGroup]="emailSettingsForm">
                <div for="senderEmail" class="field-label-req">
                    Sender Email</div>
                <form-errors-wrapper label="Sender Email" [control]="emailSettingsForm.controls['senderEmail']">
                    <input type="text" formControlName="senderEmail" id="senderEmail" data-automate-id="senderEmail"
                        autocomplete="off" placeholder="ex. <EMAIL>" />
                </form-errors-wrapper>
                <div for="SMTPServer" class="field-label-req">
                    SMTP Server</div>
                <form-errors-wrapper label="SMTP Server" [control]="emailSettingsForm.controls['SMTPServer']">
                    <input type="text" formControlName="SMTPServer" id="SMTPServer" data-automate-id="SMTPServer"
                        autocomplete="off" placeholder="ex. smtp.gmail.com" />
                </form-errors-wrapper>
                <div for="port" class="field-label-req">
                    Port</div>
                <form-errors-wrapper label="Port" [control]="emailSettingsForm.controls['port']">
                    <input type="number" formControlName="port" id="port" data-automate-id="port" autocomplete="off"
                        placeholder="ex. 8080" />
                </form-errors-wrapper>
                <div for="username" class="field-label-req">
                    Username</div>
                <form-errors-wrapper label="username" [control]="emailSettingsForm.controls['username']">
                    <input type="text" formControlName="username" id="username" data-automate-id="username"
                        autocomplete="off" placeholder="ex. Mounika Pampana" />
                </form-errors-wrapper>
                <div for="password" class="field-label-req">
                    Password</div> <span type="button" class="ms-3" data-bs-toggle="tooltip" data-bs-html="true"
                    title="The app password is provided by the email service provider and is not email password">
                    <img src="../../../../../assets/images/i-btn.svg" /></span>
                <form-errors-wrapper label="password" [control]="emailSettingsForm.controls['password']">
                    <input type="password" formControlName="password" id="password" data-automate-id="password"
                        placeholder="Password" autocomplete="new-password" />
                </form-errors-wrapper>
                <div class="flex-center">
                    <button class="btn-coal w-310 mt-30" (click)="sendTestMail()">
                        <!-- <span class="ic-add icon ic-sm mr-10"></span> -->
                        <span class="text-white">Test Configuration</span>
                    </button>
                </div>
                <div class="my-20">Allow these users to use Email Feature
                    <div class="d-flex flex-wrap">
                        <label class="form-check form-check-inline mr-10 mb-12 bg-light-pearl br-20 p-10 mt-10">
                            <input type="radio" class="radio-check-input" formControlName="selectionType"
                                value="selectAll">
                            <div class="text-dark-gray cursor-pointer text-large text-sm ml-6">
                                Select all</div>
                        </label>

                        <!-- Select Users -->
                        <label class="form-check form-check-inline mr-10 mb-12 bg-light-pearl br-20 p-10 mt-10">
                            <input type="radio" class="radio-check-input" formControlName="selectionType"
                                value="selectUsers">
                            <div class="text-dark-gray cursor-pointer text-large text-sm ml-6">
                                Select Users</div>
                        </label>
                    </div>
                </div>
                <ng-container *ngIf="emailSettingsForm.get('selectionType').value === 'selectUsers'">
                    <div class="field-label">
                        Select User(s)</div>
                    <ng-select [virtualScroll]="true" [items]="allUsersList" class="bg-white" bindLabel="fullName"
                        bindValue="id" placeholder="ex. Mounika Pampana" formControlName="usersSelection"
                        [multiple]="true" appSelectAll [closeOnSelect]="false">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container">
                                <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                    [checked]="item$.selected">
                                <span class="checkmark"></span>
                                <span class="text-truncate-1 break-all"> {{ item.fullName }}</span>
                            </div>
                        </ng-template>
                    </ng-select>
                    <div class="d-flex flex-wrap fw-600 text-black-200">
                        <div class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap"
                            *ngFor="let user of emailSettingsForm.get('usersSelection').value">
                            <span class="align-center cursor-pointer text-coal text-truncate-1 break-all">
                                {{getAssignedToDetails(user, allUsersList, true)}}
                            </span>
                            <span class="icon ic-cancel ic-x-xs ml-6 ic-light-gray cursor-pointer"
                                (click)="removeUser(user)">
                            </span>
                        </div>
                    </div>
                </ng-container>

            </form>
        </div>
        <div class="bg-white flex-end p-12 position-fixed bottom-0 w-100 box-shadow-10">
            <div class="text-decoration-underline cursor-pointer mr-10" (click)="modalRef.hide()">
                {{ 'BUTTONS.cancel' | translate }}</div>
            <button class="btn-coal" (click)="onSaveEmailSMTP()">
                <span *ngIf="!addSMTPIsLoading && !editSMTPIsLoading else buttonDots">{{ 'BUTTONS.save' | translate
                    }}</span></button>
        </div>
    </div>
</ng-template>

<ng-template #delete>
    <div class="p-20">
        <h4 class="text-black-100 fw-semi-bold mb-20 text-center word-break line-break">Are you sure you want to delete
            this Email?</h4>
        <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: Deleted email cannot be retrieved again.
        </div>
        <div class="flex-end mt-30">
            <button class="btn-gray mr-20" id="deleteNo" data-automate-id="deleteNo" (click)="modalRef.hide()">
                {{ 'GLOBAL.no' | translate }}</button>
            <button class="btn-green" id="deleteYes" data-automate-id="deleteYes"
                (click)="modalService.setDismissReason('confirmed');modalRef.hide()">
                {{ 'GLOBAL.yes' | translate }}</button>
        </div>
    </div>
</ng-template>

<ng-template #assignUsers>
    <div class="h-100vh min-w-350 max-w-350">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>Assigned Users </h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
        </div>

        <div class="px-24" [formGroup]="emailSettingsForm">
            <div class="bg-light-pearl mt-20 br-6 flex-between break-all bg-profile">
                <div class="flex-column pt-20 pl-10 pb-20">
                    <div *ngIf="selectedEmail" class="fw-semi-bold fv-sm-caps">
                        Template Name
                    </div>
                    <div class="fw-700 text-small mt-2">{{ selectedEmail?.from }}</div>
                </div>
            </div>
            <div class="field-label-req mt-16">{{ 'LEADS.assign-to' | translate }}</div>
            <div class=" mt-10">
                <div class="d-flex flex-wrap">
                    <label class="form-check form-check-inline mr-10 mb-12 bg-light-pearl br-20 p-10 mt-10">
                        <input type="radio" class="radio-check-input" formControlName="selectionType" value="selectAll">
                        <div class="text-dark-gray cursor-pointer text-large text-sm ml-6">
                            Select all</div>
                    </label>
                    <label class="form-check form-check-inline mr-10 mb-12 bg-light-pearl br-20 p-10 mt-10">
                        <input type="radio" class="radio-check-input" formControlName="selectionType"
                            value="selectUsers">
                        <div class="text-dark-gray cursor-pointer text-large text-sm ml-6">
                            Select Users</div>
                    </label>
                </div>
                <div *ngIf="emailSettingsForm.get('selectionType').value === 'selectUsers' else buttons" class="mt-16">
                    <form [formGroup]="emailSettingsForm">
                        <ng-select [virtualScroll]="true" [items]="allUsersList" class="bg-white" bindLabel="fullName"
                            bindValue="id" placeholder="ex. Mounika Pampana" formControlName="usersSelection"
                            [multiple]="true" appSelectAll [closeOnSelect]="false">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                        [checked]="item$.selected">
                                    <span class="checkmark"></span>
                                    <span class="text-truncate-1 break-all"> {{ item.fullName }}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </form>
                    <div class="mt-10" *ngIf="emailSettingsForm.get('usersSelection').value?.length">
                        <div class="d-flex">
                            <div class="field-label-underline">Assigned Users
                            </div>
                        </div>
                        <div class="scrollbar h-100-410 mt-12">
                            <div class="flex-between mb-12"
                                *ngFor="let user of emailSettingsForm.get('usersSelection').value; let i = index">
                                <div class="align-center">
                                    <div class="dot dot-xl bg-pearl-90 mr-6">
                                        <span
                                            class="fw-semi-bold text-normal text-white text-uppercase">{{getAssignedToDetails(user,
                                            allUsersList, true)[0]}}</span>
                                    </div>
                                    <div class="fw-semi-bold text-large text-coal text-truncate-1 break-all">
                                        {{getAssignedToDetails(user, allUsersList, true)}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <ng-container #buttons>
                    <div class="flex-end mt-20">
                        <button class="btn-gray mr-20" (click)="modalRef.hide()">{{ 'BUTTONS.cancel' | translate
                            }}</button>
                        <button class="btn-coal" (click)="updateUsers()">{{ 'BUTTONS.save' | translate }}</button>
                    </div>
                </ng-container>
            </div>
        </div>
    </div>
</ng-template>
<ng-template #loader>
    <div class="flex-center h-330">
        <application-loader></application-loader>
    </div>
</ng-template>
<ng-template #buttonDots>
    <div class="container px-4">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling dot-white"></div>
        </ng-container>
    </div>
</ng-template>