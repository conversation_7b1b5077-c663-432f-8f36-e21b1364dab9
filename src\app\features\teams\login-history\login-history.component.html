<div class="px-30 pt-16">
  <div class="d-flex">
    <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
      <div class="field-label">{{'GLOBAL.assigned-to'| translate}}</div>
      <div class="mr-20">
        <ng-select [virtualScroll]="true" [items]="users" [multiple]="true" [closeOnSelect]="false" ResizableDropdown
          placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id">
          <ng-template ng-label-tmp let-item="item" let-clear="clear">
            <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
            <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
          </ng-template>
          <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                [checked]="item$.selected"><span class="checkmark"></span>{{item.firstName}} {{item.lastName}} <span
                class="error-message-custom" *ngIf="!item.isActive">( Disabled )</span>
            </div>
          </ng-template>
        </ng-select>
      </div>
    </div>
    <div class="filters-grid d-flex pl-0">
      <div class="dropdown-date-picker mt-8 mb-8 d-flex rounded">
        <div class="date-picker align-center rounded-end" id="leadsAppointmentDate"
          data-automate-id="leadsAppointmentDate">
          <span class="ic-appointment icon ic-xxs ic-black" [owlDateTimeTrigger]="dt1"></span>
          <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [selectMode]="'range'"
            class="fw-400 pl-30 text-large" (ngModelChange)="$event"
            placeholder="ex. 19-06-2025 - 29-06-2025" />
          <owl-date-time [pickerType]="'calendar'" #dt1 (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
        </div>
      </div>
    </div>
  </div>
  <ag-grid-angular #agGrid class="ag-theme-alpine" [pagination]="true" [paginationPageSize]="pageSize"
    [gridOptions]="gridOptions" [rowData]="rowData" [icons]="gridOptions.icons" [suppressPaginationPanel]="true"
    (gridReady)="onGridReady($event)">
  </ag-grid-angular>
  <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]="getPages(rowData?.length, pageSize)"
    (pageChange)="onPageChange($event)">
  </pagination>
</div>