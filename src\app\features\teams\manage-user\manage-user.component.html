<div *ngIf="canView || usersListIsLoading">
  <div class="tb-left-165" [ngClass]="showLeftNav ? 'left-285' : 'left-190'">
    <div class="flex-between py-8 px-24 bg-white border-bottom">
      <ul class="align-center top-nav-bar text-nowrap scrollbar ph-w-100-190 ip-w-100-300 tb-w-100-300 scroll-hide">
        <ng-container *ngFor="let visibilityImage of visibilityList;let i=index">
          <div [title]="usersData?.[visibilityImage?.visibility] ? usersData?.[visibilityImage?.visibility] : ''"
            (click)="currentVisibility(visibilityImage.userStatus)" class="cursor-pointer">
            <div class="align-center ph-mb-4">
              <a [class.active]="appliedFilter?.userStatus == visibilityImage.userStatus">
                <img [type]="'leadrat'" [appImage]="s3BucketUrl + visibilityImage.image" alt="muso" width="22"
                  height="22"></a>
              <span [class.active]="appliedFilter?.userStatus == visibilityImage.userStatus"
                class="text-large ml-8 mr-16">{{ visibilityImage.name }}
                <span
                  *ngIf="!isUsersCountLoading else countsLoader">({{usersData?.[visibilityImage?.visibility]}})</span>
              </span>
            </div>
          </div>
        </ng-container>
      </ul>
      <div class="align-center">
        <div class="btn-full-dropdown btn-w-100" *ngIf="canView || canAdd">
          <div class="position-absolute top-9 left-9 ip-top-11 align-center z-index-2">
            <span class="ic-tracker icon ic-xxs"></span>
            <span class="ml-8 ip-d-none">Tracker</span>
          </div>
          <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" [(ngModel)]="selectedTrackerOption"
            (change)="openUserTracker()">
            <ng-option (click)="selectedTrackerOption = null" value="delete" *ngIf="canView">
              <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
              Delete Tracker</ng-option>
            <ng-option (click)="selectedTrackerOption = null" value="bulkUpload">
              <span class="ic-download icon ic-xxs ic-dark mr-8"></span>
              Bulk upload Tracker</ng-option>
            <ng-option (click)="selectedTrackerOption = null" value="export" *ngIf="canExport">
              <span class="ic-download icon ic-xxs ic-dark mr-8"></span>
              Export Tracker</ng-option>
          </ng-select>
        </div>
        <ng-container *ngIf="canAdd">
          <div class="btn-left-dropdown ml-10" (click)="navigateToAddUser()">
            <span class="ic-add icon ic-xxs"></span>
            <span class="ml-8 ip-d-none">{{ 'USER.add-new-user' | translate }}</span>
          </div>
          <div class="btn-right-dropdown btn-w-30 black-100">
            <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" [(ngModel)]="selectedOption"
              (change)="openBulkUserUpload()">
              <ng-option (click)="selectedOption = null" value="bulkUpload">
                <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
            </ng-select>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
  <div class="px-24">
    <div class="mt-12 align-center bg-white w-100" *ngIf="!usersListIsLoading">
      <div class="align-center ip-flex-col ip-align-center-unset bg-white w-100 border-gray">
        <div class="align-center flex-grow-1 no-validation border-end ip-br-0">
          <div class="align-center px-10 py-12 flex-grow-1 no-validation">
            <ng-container *ngIf="canSearch">
              <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
              <input type="text" (keydown)="onSearch($event)" (input)="isEmptyInput($event)"
                placeholder="type to search" [(ngModel)]="searchTerm" class="border-0 outline-0 w-100">
              <small class="text-muted text-nowrap">({{ 'LEADS.lead-search-prompt' | translate }})</small>
            </ng-container>
          </div>
          <div class="flex-end">
            <div *ngIf="canExport"
              class="bg-accent-green text-white px-20 py-12 h-100 align-center cursor-pointer border-start w-70px"
              (click)="exportUserReport()">{{ 'REPORTS.export' | translate }}</div>
          </div>
        </div>
        <div class="ip-br-top align-center ip-flex-col ip-align-center-unset">
          <div class="d-flex w-100">
            <div class="px-10 align-center cursor-pointer border-end tb-flex-grow-1"
              (click)="openAdvFiltersModal(AdvancedFilters)">
              <div class="icon ic-filter-solid ic-xxs ic-black mr-10"></div>
              <span class="fw-600 ph-d-none">{{'PROPERTY.advanced-filters' | translate}}</span>
            </div>
            <div class="align-center position-relative cursor-pointer d-flex border-end">
              <span class="position-absolute ph-left-12 left-15 z-index-2 fw-600 text-sm">
                {{ 'BUTTONS.manage-columns' | translate }}</span>
              <div class="show-hide-gray w-140 ph-w-130px">
                <ng-select [virtualScroll]="true" class="bg-white" [items]="columns" [multiple]="true" appSelectAll
                  [searchable]="false" ResizableDropdown [closeOnSelect]="false" [ngModel]="defaultColumns"
                  (change)="onColumnsSelected($event)">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span>{{item.label}}</div>
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div class="bg-coal text-white px-10 py-12 ip-w-30px h-100 align-center cursor-pointer"
              (click)="onSetColumnDefault()">
              <span class="ip-d-none">{{ 'GLOBAL.default' | translate }}</span> <span
                class="ic-refresh d-none ip-d-block"></span>
            </div>
            <div class="flex-center">
              <div class="show-dropdown-white align-center position-relative">
                <span class="fw-600 position-absolute left-5 z-index-2">
                  <span class="tb-d-none">{{ 'GLOBAL.show' | translate }}</span>
                  <span> {{ 'GLOBAL.entries' | translate }}</span> </span>
                <div class="text-secondary-color">
                  <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" [searchable]="false"
                    ResizableDropdown class="w-150 tb-w-120px" (change)="assignCount()" [(ngModel)]="selectedPageSize">
                    <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                      {{pageSize}}</ng-option>
                  </ng-select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-white px-4 py-8 tb-w-100-34" [ngClass]="showLeftNav ? 'w-100-190' : 'w-100-90'"
      *ngIf="!usersListIsLoading">
      <ng-container *ngIf="showFilters && !usersListIsLoading">
        <div class="bg-secondary flex-between">
          <drag-scroll class="br-4 scrollbar d-flex scroll-hide w-100">
            <div class="d-flex" *ngFor="let filter of appliedFilter | keyvalue">
              <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                {{usersFiltersKeyLabel[filter.key] || filter.key}}: {{
                filter.key === 'reportsTo' ? getReportsName(value) :
                filter.key === 'users' ? getReportsName(value) :
                filter.key === 'generalManager' ? getReportsName(value) :
                filter.key === 'timezone' ? getTimeZoneName(value): value }}
                <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                  (click)="onRemoveFilter(filter.key, value)"></span>
              </div>
            </div>
          </drag-scroll>
          <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
            (click)="reset(true);filterFunction();">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}
          </div>
        </div>
      </ng-container>
    </div>
    <div class="manage-user checkbox-align" *ngIf="!usersListIsLoading else usersLoader">
      <ag-grid-angular class="ag-theme-alpine" [gridOptions]="gridOptions" [pagination]="true"
        [paginationPageSize]="pageSize" [gridOptions]="gridOptions" [rowData]="rowData" [suppressPaginationPanel]="true"
        [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true" (gridReady)="onGridReady($event)"
        [suppressRowClickSelection]="true" (cellClicked)="onCellClicked($event)"
        (filterChanged)="onFilterChanged($event)">
      </ag-grid-angular>
    </div>
    <div class="mt-20 flex-end" *ngIf="!usersListIsLoading && rowData?.length">
      <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{(currOffset * pageSize) + 1}}
        {{ 'GLOBAL.to-small' | translate }}
        {{(currOffset * pageSize) + gridApi?.getDisplayedRowCount()}} {{ 'GLOBAL.of-small' | translate }}
        {{usersTotalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
      <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]="getPages(usersTotalCount, pageSize)"
        (pageChange)="onPageChange($event)">
      </pagination>
    </div>
  </div>
</div>
<div class="justify-center">
  <div class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 z-index-2"
    [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}">
    <div class="align-center tb-mb-10">
      <div class="fw-600 text-coal mr-20 text-xl">{{gridApi?.getSelectedNodes()?.length}}
        {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' | translate}}</div>
    </div>
    <div class="d-flex scrollbar max-w-100-260 tb-max-w-100-190 ip-max-w-100-70 scroll-hide">
      <button class="btn-bulk" id="btnBulkUpdate" data-automate-id="btnBulkUpdate" *ngIf="canBulkUpdate"
        (click)="openBulkUpdateModal(BulkUpdateModal)">Bulk {{ 'GLOBAL.update' | translate }}
      </button>
      <button class="btn-bulk" (click)="openBulkDeactiveModal(BulkDeactiveModal)" *ngIf="canBulkDeactive"
        [disabled]="!sameAll">Bulk {{selectedToggleUsers?.[0] ? 'Deactive' : 'Active'}}
      </button>
      <button [disabled]="gridApi?.getSelectedNodes()?.length < 1" *ngIf="canBulkRoleUpdate" class="btn-bulk"
        (click)="openUpdateBulkRoles(updateBulkRoles)">Bulk Role Update</button>
      <button *ngIf="canMakeDefaultPassword" title="Reset to Default Password"
        [disabled]="gridApi?.getSelectedNodes()?.length < 1" class="btn-bulk" (click)="bulkResetPassword()">Bulk Reset
        Password</button>
    </div>
  </div>
</div>
<ng-template #AdvancedFilters>
  <div class="lead-adv-filter px-30 pb-10 bg-white brbl-15 brbr-15">
    <div class="d-flex flex-wrap ng-select-sm">
      <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="justify-between align-end mr-20">
          <div class="field-label">User</div>
          <label class="checkbox-container mb-4">
            <input type="checkbox" [(ngModel)]="appliedFilter.ShouldShowReportees">
            <span class="checkmark"></span>{{'DASHBOARD.with-team' | translate}}
          </label>
        </div>
        <div class="mr-20 position-relative">
          <ng-select [virtualScroll]="true" *ngIf="!reporteesIsLoading else fieldLoader" [items]="allUsers"
            ResizableDropdown [multiple]="true" appSelectAll [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
            bindLabel="fullName" bindValue="id" [(ngModel)]="appliedFilter.users">
            <ng-template ng-label-tmp let-item="item" let-clear="clear">
              <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
              <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="flex-between">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item.firstName}} {{item.lastName}}</span></div> <span
                  class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">Designation</div>
        <div class="mr-20">
          <ng-select *ngIf="!isDesignationsListLoading else fieldLoader" [clearSearchOnAdd]="true" ResizableDropdown
            [items]="designationsList" [multiple]="true" appSelectAll [closeOnSelect]="false"
            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="name" bindValue="name"
            [(ngModel)]="appliedFilter.designation">
            <ng-template ng-label-tmp let-item="item" let-clear="clear">
              <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
              <span class="ng-value-label"> {{item.name}}</span>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                  class="text-truncate-1 break-all">{{item.name}}</span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">Department</div>
        <div class="mr-20">
          <ng-select *ngIf="!isDepartmentsListLoading else fieldLoader" [clearSearchOnAdd]="true" ResizableDropdown
            [items]="departmentList" [multiple]="true" appSelectAll [closeOnSelect]="false"
            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="name" bindValue="name"
            [(ngModel)]="appliedFilter.department">
            <ng-template ng-label-tmp let-item="item" let-clear="clear">
              <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
              <span class="ng-value-label"> {{item.name}}</span>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                  class="text-truncate-1 break-all">{{item.name}}</span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">Reporting To</div>
        <div class="mr-20">
          <ng-select [virtualScroll]="true" *ngIf="!reporteesIsLoading else fieldLoader" [items]="reportsTo"
            ResizableDropdown [multiple]="true" appSelectAll [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
            bindLabel="fullName" bindValue="id" [(ngModel)]="appliedFilter.reportsTo">
            <ng-template ng-label-tmp let-item="item" let-clear="clear">
              <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
              <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="flex-between">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item.firstName}} {{item.lastName}}</span></div> <span
                  class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">General Manager</div>
        <div class="mr-20">
          <ng-select [virtualScroll]="true" *ngIf="!isGeneralManagerLoading else fieldLoader" [items]="generalManager"
            [multiple]="true" appSelectAll [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName"
            ResizableDropdown bindValue="id" [(ngModel)]="appliedFilter.generalManager">
            <ng-template ng-label-tmp let-item="item" let-clear="clear">
              <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
              <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="flex-between">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item.firstName}}
                    {{item.lastName}}</span></div><span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">Timezone</div>
        <div class="mr-20">
          <ng-select [clearSearchOnAdd]="true" ResizableDropdown [items]="timeZoneList" [multiple]="true" appSelectAll
            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName"
            bindValue="ianaZoneId" [(ngModel)]="appliedFilter.timezone">
            <ng-template ng-label-tmp let-item="item" let-clear="clear">
              <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
              <span class="ng-value-label"> {{item?.displayName}}</span>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="d-flex">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span>
                  <div class="w-100">
                    <span class="text-truncate-1 break-all">{{item?.displayName}}</span>
                    <i class="text-truncate-1 break-all text-dark-gray">({{item?.ianaZoneId}})</i>
                  </div>
                </div>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">{{'REPORTS.date-filters' | translate }} </div>
        <div class="mr-20">
          <div class="w-100">
            <div class="ph-mr-0 align-center">
              <div class="w-33">
                <ng-select [virtualScroll]="true" placeholder=All [searchable]="false" class="mr-10 ng-select-w-171"
                  ResizableDropdown [(ngModel)]="appliedFilter.DateType">
                  <ng-option name="dateType" ngDefaultControl *ngFor="let dType of dateTypeList"
                    [value]="dType">{{dType}}</ng-option>
                </ng-select>
              </div>
              <div class="w-67 align-center position-relative filters-grid clear-padding">
                <div class="date-picker border pt-8 pb-6 br-4 align-center w-100" id="reportsAppointmentDate"
                  data-automate-id="reportsAppointmentDate">
                  <span class="ic-appointment icon ic-xxs ic-black" [owlDateTimeTrigger]="dt1"></span>
                  <input type="text" readonly placeholder="ex. 5-03-2025 - 14-03-2025" class="pl-20 text-large w-100"
                    [max]="appliedFilter.DateType === 'Modified Date' || appliedFilter.DateType === 'Created Date' ? maxDate : ''"
                    [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [selectMode]="'range'"
                    (ngModelChange)="appliedFilter.date = $event" [ngModel]="appliedFilter.date"
                    [disabled]="!appliedFilter.DateType" />
                  <owl-date-time [pickerType]="'calendar'" #dt1
                    (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex-end mt-10">
      <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="modalService.hide()">{{'BUTTONS.cancel' |
        translate }}</u>
      <button class="btn-gray mr-20" (click)="onClearAllFilters()">{{ 'GLOBAL.reset' | translate }}</button>
      <button class="btn-coal" (click)="applyAdvancedFilter()">{{ 'GLOBAL.search' | translate }}</button>
    </div>
  </div>
</ng-template>
<ng-template #BulkUpdateModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern scrollbar">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} {{ 'GLOBAL.update' | translate }}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24">
      <div class="fw-600 text-coal text-large my-8">Selected User(s) -
        {{gridApi?.getSelectedNodes()?.length}}</div>
      <div class="scrollbar table-scrollbar ph-w-100-45 tb-max-w-100-40">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-100px">{{'AUTH.full-name' | translate}}</th>
              <th class="w-100px">{{ 'USER_MANAGEMENT.designation' | translate }}</th>
              <th class="w-100px">{{ 'USER_MANAGEMENT.department' | translate }}</th>
              <th class="w-100px">{{ 'USER_MANAGEMENT.reporting-to' | translate }}</th>
              <th class="w-120 text-nowrap">General Manager</th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary max-h-100-215 fw-semi-bold scrollbar">
            <ng-container>
              <tr *ngFor="let user of selectedUsers">
                <td class="w-100px">
                  <div class="text-truncate-1 break-all">{{ user?.firstName }} {{ user?.lastName }}</div>
                </td>
                <td class="w-100px">
                  <div class="text-truncate-1 break-all">{{ user?.designation?.name }}</div>
                </td>
                <td class="w-100px">
                  <div class="text-truncate-1 break-all">{{ user?.department?.name }}</div>
                </td>
                <td class="w-100px">
                  <div class="text-truncate-1 break-all">{{ user?.reportsTo?.name }}</div>
                </td>
                <td class="w-120">
                  <div class="text-truncate-1 break-all">{{ user?.generalManager?.name }}</div>
                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(user?.firstName, user?.userId)" class="bg-light-red icon-badge"
                    id="clkDeleteBulkUpdate" data-automate-id="clkDeleteBulkUpdate">
                    <span class="icon ic-delete m-auto ic-xxs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>

      <form [formGroup]="userBulkUpdateForm">
        <div class="field-label">{{'USER_MANAGEMENT.designation'| translate}}</div>
        <div class="ng-select-sm-gray">
          <ng-select [virtualScroll]="true" formControlName='designation' placeholder="select designation"
            ResizableDropdown [ngClass]="{'blinking pe-none': isDesignationsListLoading}">
            <ng-option *ngFor="let designation of designationsList"
              [value]="designation.id">{{designation.name}}</ng-option>
          </ng-select>
        </div>
        <div class="field-label">{{'USER_MANAGEMENT.department'| translate}}</div>
        <div class="ng-select-sm-gray">
          <ng-select [virtualScroll]="true" formControlName='department' placeholder="select department"
            ResizableDropdown [ngClass]="{'blinking pe-none': isDepartmentsListLoading}">
            <ng-option *ngFor="let department of departmentList" [value]="department.id">{{department.name}}</ng-option>
          </ng-select>
        </div>
        <label class="checkbox-container mt-12">
          <input type="checkbox" [checked]="userBulkUpdateForm.get('changeGM').value"
            [disabled]="isUnassignLeadSelected" formControlName="changeGM">
          <span class="checkmark"></span> <span class="fw-600 text-sm">{{'GLOBAL.change'
            | translate}} General Manager</span>
        </label>
        <div class="field-label" *ngIf="userBulkUpdateForm.get('changeGM').value">
          General Manager</div>
        <div class="ng-select-sm-gray" *ngIf="userBulkUpdateForm.get('changeGM').value">
          <ng-select [virtualScroll]="true" formControlName='generalManager' placeholder="ex: Mounika"
            [ngClass]="{'blinking pe-none': isUsersListForReassignmentLoading}">
            <ng-option *ngFor="let user of activeUsers" [value]="user.id">{{user.firstName + " " +
              user.lastName}}</ng-option>
          </ng-select>
        </div>
        <label class="checkbox-container mt-12">
          <input type="checkbox" [checked]="userBulkUpdateForm.get('changeReportsTo').value"
            [disabled]="isUnassignLeadSelected" formControlName="changeReportsTo">
          <span class="checkmark"></span> <span class="fw-600 text-sm">{{'GLOBAL.change'
            | translate}} {{'USER_MANAGEMENT.reporting-to'| translate}}</span>
        </label>
        <div class="field-label" *ngIf="userBulkUpdateForm.get('changeReportsTo').value">
          {{'USER_MANAGEMENT.reporting-to'| translate}}</div>
        <div class="ng-select-sm-gray" *ngIf="userBulkUpdateForm.get('changeReportsTo').value">
          <ng-select [virtualScroll]="true" formControlName='reportingTo' placeholder="ex: Manasa" ResizableDropdown
            [ngClass]="{'blinking pe-none': isUsersListForReassignmentLoading}">
            <ng-option *ngFor="let user of activeUsers" [value]="user.id">{{user.firstName + " " +
              user.lastName}}</ng-option>
          </ng-select>
        </div>
      </form>
      <div class="flex-center mt-20 mb-40">
        <button class="btn-gray mr-20" (click)="modalService.hide()">
          {{ 'BUTTONS.cancel' | translate }}
        </button>
        
        <button 
          class="btn-coal" 
          id="btnSaveBulkProject" 
          data-automate-id="btnSaveBulkProject"
          (click)="updateBulkUsers()"
        >
          {{ isUpdateBulkUsersLoading ? '' : ('BUTTONS.save' | translate) }}
          <ng-container *ngIf="isUpdateBulkUsersLoading" [ngTemplateOutlet]="buttonDots"></ng-container>
        </button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #BulkDeactiveModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} {{selectedToggleUsers?.[0] ? 'Deactivate' : 'Activate'}}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24">
      <div class="fw-600 text-coal text-large my-8">Selected User(s) -
        {{gridApi?.getSelectedNodes()?.length}}</div>
      <div class="scrollbar table-scrollbar ph-w-100-45 tb-max-w-100-40">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-100px">{{'AUTH.full-name' | translate}}</th>
              <th class="w-100px">{{ 'USER_MANAGEMENT.designation' | translate }}</th>
              <th class="w-100px">{{ 'USER_MANAGEMENT.department' | translate }}</th>
              <th class="w-100px">{{ 'USER_MANAGEMENT.reporting-to' | translate }}</th>
              <th class="w-120 text-nowrap">General Manager</th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary max-h-100-215 fw-semi-bold scrollbar">
            <ng-container>
              <tr *ngFor="let user of selectedUsers">
                <td class="w-100px">
                  <div class="text-truncate-1 break-all"> {{ user?.firstName }} {{ user?.lastName }} </div>
                </td>
                <td class="w-100px">
                  <div class="text-truncate-1 break-all">{{ user?.designation?.name }}</div>
                </td>
                <td class="w-100px">
                  <div class="text-truncate-1 break-all">{{ user?.department?.name }}</div>
                </td>
                <td class="w-100px">
                  <div class="text-truncate-1 break-all">{{ user?.reportsTo?.name }}</div>
                </td>
                <td class="w-120">
                  <div class="text-truncate-1 break-all">{{ user?.generalManager?.name }}</div>
                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(user?.firstName, user?.userId)" class="bg-light-red icon-badge"
                    id="clkBulkDeactive" data-automate-id="clkBulkDeactive">
                    <span class="icon ic-delete m-auto ic-xxs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <h5 class="mt-20 flex-center">Are you sure you want to {{selectedToggleUsers?.[0] ? 'deactivate' : 'activate'}}
        these users?</h5>
      <div class="flex-center mt-10">
        <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'GLOBAL.no' | translate }}</button>
        <button class="btn-green" (click)="handleUserStatus(remainingUsersModal)">{{ 'GLOBAL.yes' |
          translate }}</button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #usersLoader>
  <div class="flex-center h-100-170">
    <application-loader></application-loader>
  </div>
</ng-template>

<ng-template #ratLoader>
  <div class="flex-center h-20px mt-8">
    <img src="assets/images/loader-rat.svg" class="rat-loader h-20px w-20px" alt="loader">
  </div>
</ng-template>

<ng-template #countsLoader>
  (<div class="container px-4 d-inline">
    <ng-container *ngFor="let dot of [1,2,3]">
      <div class="dot-falling"></div>
    </ng-container>
  </div>)
</ng-template>

<ng-template #remainingUsersModal>
  <div class="p-20">
    <h4 class="fw-semi-bold text-black-200">You have selected {{ gridApi?.getSelectedNodes()?.length }} user(s), <span
        *ngIf="remainingUsers >= 1">only {{
        remainingUsers }}</span><span *ngIf="remainingUsers < 1"> no </span> available licenses.<span
        *ngIf="remainingUsers >= 1"> Reselect Only {{ remainingUsers }}
        users</span></h4>
    <h4 class="fw-600 text-black-200 mt-12"> License Limit Reached Connect with your admin or {{getAppName()}} Support
      **********
      to Buy More </h4>
    <div class="flex-end mt-30">
      <button class="btn-green" (click)="deleteConfirm(); closeModal()">OK</button>
    </div>
  </div>
</ng-template>

<ng-template #updateBulkRoles>
  <div class="w-100">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} Roles {{ 'GLOBAL.update' | translate }}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="scrollbar my-10 pr-8 h-100-110 px-24">
      <ng-container *ngFor="let role of rolesList">
        <div class="d-flex">
          <label class="checkbox-container field-label pl-30">
            <input type="checkbox" id="inp{{role.name}}" data-automate-id="inp{{role.name}}"
              (change)="assignRole($event.target.checked, role)">
            <span class="checkmark"></span>{{role.name}}
          </label>
        </div>
        <p class="text-gray mt-4 ml-30 text-xs word-break" [innerHTML]="getSanitizedHtml(role.permissionDescription)">
        </p>
      </ng-container>
    </div>
    <div class="flex-end">
      <button class="btn-gray" (click)="modalRef.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal ml-20 mr-10" (click)="updateBulkRole()">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</ng-template>
<ng-template #buttonDots>
  <div class="container px-4">
      <ng-container *ngFor="let dot of [1,2,3]">
          <div class="dot-falling dot-white"></div>
      </ng-container>
  </div>
</ng-template>