import { Component, EventEmitter, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, firstValueFrom, map, skipWhile, switchMap, take, takeUntil } from 'rxjs';

import { AnimationOptions } from 'ngx-lottie';
import { PAGE_SIZE, REPORTS_DATE_TYPE, REPORT_FILTERS_KEY_LABEL, SHOW_ENTRIES } from 'src/app/app.constants';
import { IntegrationSource, LeadSource, ReportDateType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { ReportsFilter } from 'src/app/core/interfaces/reports.interface';
import {
  assignToSort,
  changeCalendar,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  getTotalCountForReports,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
  snakeToCamel,
} from 'src/app/core/utils/common.util';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading, getGlobalAnonymousIsLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchAgencyNameList } from 'src/app/reducers/Integration/integration.actions';
import { getAgencyNameList, getAgencyNameListIsLoading } from 'src/app/reducers/Integration/integration.reducer';
import {
  FetchLeadCities,
  FetchLeadCountries,
  FetchLeadStates,
  FetchProjectList,
  FetchSubSourceList,
} from 'src/app/reducers/lead/lead.actions';
import {
  getIsLeadCustomStatusEnabled,
  getLeadCities,
  getLeadCitiesIsLoading,
  getLeadCountries,
  getLeadCountriesIsLoading,
  getLeadStates,
  getLeadStatesIsLoading,
  getLocations,
  getLocationsIsLoading,
  getProjectList,
  getProjectListIsLoading,
  getSubSourceList,
  getSubSourceListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions, getPermissionsIsLoading } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchReportsCustomSubSources, FetchReportsSubSources, FetchSubSourceExportSuccess, UpdateSubSourcesFilterPayload } from 'src/app/reducers/reports/reports.actions';
import {
  getReportsCustomSubSourcesList,
  getReportsCustomSubSourcesListIsLoading,
  getReportsCustomSubSourcesTotalCount,
  getReportsSubSourcesList,
  getReportsSubSourcesListIsLoading,
  getSubSourcesFiltersPayload,
} from 'src/app/reducers/reports/reports.reducer';
import { CustomStatus, getCustomStatusList, getCustomStatusListIsLoading } from 'src/app/reducers/status/status.reducer';
import { FetchOnlyReporteesWithInactive, FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getOnlyReporteesWithInactive, getOnlyReporteesWithInactiveIsLoading, getUserBasicDetails, getUsersListForReassignment, getUsersListForReassignmentIsLoading } from 'src/app/reducers/teams/teams.reducer';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'sub-source-report',
  templateUrl: 'sub-source-report.component.html',
})
export class SubSourceReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  leadSources: Array<any> = [];
  gridOptions: any;
  columnDropDown: { field: string; hide: boolean }[] = [];
  rowData: Array<any> = [];
  filteredColumnDefsCache: any[] = [];
  gridApi: any;
  gridColumnApi: any;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  sourcesTotalCount: number;
  getPages = getPages;
  searchTerm: string;
  currentView: 'table' | 'graph' = 'table';
  appliedFilter: any;
  filtersPayload: ReportsFilter;
  canExportAllUsers: boolean = false;
  canViewAllUsers: boolean = false;
  canViewReportees: boolean = false;
  canExportReportees: boolean = false;
  projectList: any;
  agencyNameList: any;
  subSourceList: any;
  allSubSourceList: any;
  dateTypeList: Array<string> = REPORTS_DATE_TYPE.slice(0, 4);
  subSourcesTotalCount: number;
  allUsers: Array<any> = [];
  onlyReportees: Array<any> = [];
  users: Array<any> = [];
  reportees: Array<any> = [];
  isSubSourceReportLoading: boolean = true;
  isCustomSubSourceReportLoading: boolean = true;
  isAllUsersLoading: boolean = true;
  isOnlyReporteesLoading: boolean = true;
  allSubSourceListIsLoading: boolean = true;
  isProjectListLoading: boolean = true;
  agencyNameListIsLoading: boolean = true;
  isSourcesLoading: boolean = true;
  locations: string[];
  locationsIsLoading: boolean = true;
  cities: string[];
  citiesIsLoading: boolean = true;
  states: string[];
  statesIsLoading: boolean = true;
  customStatusList: CustomStatus[] = [];
  isCustomStatusListLoading: boolean = true;
  snakeToCamel = snakeToCamel;

  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;
  showLeftNav: boolean = true;
  showFilters: boolean = false;

  isDateFilter: string;
  isCustomStatusEnabled: boolean;
  isGlobalSettingsLoading: boolean = true;
  countryList: any[];
  countryIsLoading: boolean = true;
  userData: any;
  currentDate: Date = new Date();
  toDate: any = new Date();
  fromDate: any = new Date();
  toDateForSubSource: any = new Date();
  fromDateForSubSource: any = new Date();
  onPickerOpened = onPickerOpened
  globalSettingsData: any;

  @ViewChild('reportsGraph') reportsGraph: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    public router: Router,
    private modalService: BsModalService,
    private shareDataService: ShareDataService,
    private modalRef: BsModalRef

  ) {
    this.headerTitle.setTitle('Leads - Sub-Source Report');
    this.metaTitle.setTitle('CRM | Reports');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);

    this._store
      .select(getUsersListForReassignment)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          this.users = data;
          this.allUsers = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.allUsers = assignToSort(this.allUsers, '');
          return this._store.select(getUsersListForReassignmentIsLoading).pipe(
            takeUntil(this.stopper)
          );
        }))
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
      });

    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          this.reportees = data;
          this.onlyReportees = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.onlyReportees = assignToSort(this.onlyReportees, '');
          return this._store.select(getOnlyReporteesWithInactiveIsLoading).pipe(
            takeUntil(this.stopper)
          );
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isOnlyReporteesLoading = isLoading;
      });
    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProjectListLoading = isLoading;
      });
    this._store
      .select(getReportsSubSourcesListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isSubSourceReportLoading = isLoading;
      });
    this._store
      .select(getReportsCustomSubSourcesListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isCustomSubSourceReportLoading = data;
      });
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });
    this._store
      .select(getSubSourcesFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = { ...data, isNavigatedFromReports: true };
        this.pageSize = this.filtersPayload?.pageSize;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          dateType: ReportDateType[Number(this.filtersPayload?.dateType)],
          date: [patchTimeZoneDate(this.filtersPayload?.fromDate, this.userData?.timeZoneInfo?.baseUTcOffset), patchTimeZoneDate(this.filtersPayload?.toDate, this.userData?.timeZoneInfo?.baseUTcOffset)],
          withTeam: this.filtersPayload?.IsWithTeam,
          agencyNames: this.filtersPayload?.AgencyNames,
          users: this.filtersPayload?.UserIds,
          search: this.filtersPayload?.SearchText,
          sources: this.filtersPayload?.Sources,
          subSources: this.filtersPayload?.SubSources,
          projects: this.filtersPayload?.Projects,
          // locations: this.filtersPayload?.Locations,
          cities: this.filtersPayload?.Cities,
          states: this.filtersPayload?.States,
          dateForSubSource: [patchTimeZoneDate(this.filtersPayload?.fromDateForSubSource, this.userData?.timeZoneInfo?.baseUTcOffset), patchTimeZoneDate(this.filtersPayload?.toDateForSubSource, this.userData?.timeZoneInfo?.baseUTcOffset)],
        };
        this.activeDate();
      });
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExportAllUsers = permissionsSet.has('Permissions.Reports.ExportAllUsers');
        this.canViewAllUsers = permissionsSet.has('Permissions.Reports.ViewAllUsers');
        this.canExportReportees = permissionsSet.has('Permissions.Reports.ExportReportees');
        this.canViewReportees = permissionsSet.has('Permissions.Reports.ViewReportees');
        if (this.canViewAllUsers) {
          this._store.dispatch(new FetchUsersListForReassignment());
        }
        if (this.canViewReportees) {
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
      });
    this._store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agencyNameList = item
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getAgencyNameListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.agencyNameListIsLoading = isLoading;
      });
    this._store
      .select(getSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allSubSourceList = data;
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.updateSubSource()
      });
    this._store
      .select(getSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.allSubSourceListIsLoading = isLoading;
      });

    this._store
      .select(getPermissionsIsLoading)
      .pipe(skipWhile((isLoading: boolean) => isLoading), take(1))
      .subscribe((isLoading: boolean) => {
        if (!isLoading) {
          this.filterFunction();
        }
      });

    this.shareDataService.showLeftNav$.subscribe(show => {
      this.showLeftNav = show;
    });
  }

  fetchCustomStatuses() {
    this._store
      .select(getCustomStatusList)
      .pipe(takeUntil(this.stopper))
      .subscribe((customStatus: any) => {
        this.customStatusList = customStatus;
      });
  }

  async ngOnInit() {
      this.globalSettingsData = await firstValueFrom(
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(skipWhile((data) => !Object.keys(data).length))
  );

    this._store
  .select(getCustomStatusListIsLoading)
  .pipe(takeUntil(this.stopper))
  .subscribe((isLoading: any) => {
    this.isCustomStatusListLoading = isLoading;
    this.fetchCustomStatuses();
    if (!isLoading) this.initializeGridSettings();
    this.filteredColumnDefsCache = this.gridOptions?.columnDefs?.filter(
      (col: any) => col.field !== 'Sub-Source Name'
    );
  });
    this._store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        if (leadSource) {
          const enabledSources = leadSource
            .filter((source: any) => source.isEnabled)
            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));
          this.leadSources = [...enabledSources];
        } else {
          this.leadSources = [];
        }
        this.updateSubSource()
      });

    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });

    await this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    this.isCustomStatusEnabled = await this._store
      .select(getIsLeadCustomStatusEnabled)
      .pipe(
        map((data: any) => data),
        take(1)
      ).toPromise();
    this.filterFunction()
    this.initializeGridSettings();
    if (this.isCustomStatusEnabled) {
      this._store
        .select(getReportsCustomSubSourcesList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data.map((row: any) => {
            let statuses: any = {};
            row?.status?.forEach((status: any) => {
              statuses[status?.statusDisplayName] = status?.count || 0;
            })
            return {
              ...row,
              statuses,
              subSource: row?.name,
            };
          });
          let totalRow: any = {
            name: 'Total',
            statuses: {}
          }
          this.rowData.forEach((row: any) => {
            for (let key in row?.statuses) {
              if (!totalRow?.statuses?.[key]) {
                totalRow.statuses[key] = 0;
              }
              totalRow.statuses[key] += row?.statuses?.[key] || 0;
            }
          })
          if (this.rowData?.length > 1) {
            this.rowData?.push(totalRow)
          }
        });
      this._store
        .select(getReportsCustomSubSourcesTotalCount)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.subSourcesTotalCount = data;
        });
    }
    else
      this._store
        .select(getReportsSubSourcesList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = getTotalCountForReports(data.items);
          this.subSourcesTotalCount = data.totalCount;
        });
    this.selectedPageSize = 50;
    this._store
      .select(getLocations)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.locations = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLocationsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.locationsIsLoading = data;
      });

    this._store
      .select(getLeadCities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.cities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadCitiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.citiesIsLoading = data;
      });

    this._store
      .select(getLeadStates)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.states = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadStatesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.statesIsLoading = data;
      });
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });

    this._store
      .select(getLeadCountries)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryList = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadCountriesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.countryIsLoading = data;
      });

    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });

  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    const nameAndLeads: any = [
      {
        headerName: 'Sub-Source Name',
        field: 'Sub-Source Name',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [(this.isCustomStatusEnabled ? params?.data?.name : params.data?.subSource)],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<p class="py-16 text-truncate">${params.value[0]} </p>`;
        },
      },
      {
        headerName: 'All Leads',
        field: 'All Leads',
        filter: false,
        valueGetter: (params: any) => [
          (this.isCustomStatusEnabled ? params?.data?.statuses?.AllCount : params.data?.allCount),
          (this.isCustomStatusEnabled ? params?.data?.statuses?.ActiveCount : params.data?.activeCount),
          params?.data?.userId,
          params?.data?.projectTitle
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          return !this.isCustomStatusEnabled ? `${params?.value?.[3] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`}
          <p class="text-truncate"><span class="text-dark-gray">active: </span>
          <span class="fw-600">${params.value[1] && params?.value?.[3] != "Total" ? `<a>${params.value[1]}</a>` : params.value[1] ? params.value[1] : '--'}<span>
          </p>` : `${params?.value?.[3] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`}
          <p class="text-truncate"><span class="text-dark-gray">active: </span>
          <span class="fw-600">${params.value[1] && params?.value?.[3] != "Total" ? `<span>${params.value[1]}</span>` : params.value[1] ? params.value[1] : '--'}<span>
          </p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.event?.target?.innerText == event.value[0]) {
            if (isCtrlClick) {
              this.getDataInNewTab('All Leads', params);
              return;
            }
            this.getDataFromCell('All Leads', event);
          } else if (event.event.target.innerText == event.value[1]) {
            if (isCtrlClick) {
              this.getDataInNewTab('Active Leads', params);
              return;
            }
            this.getDataFromCell('Active Leads', event);
          }
        },
      },
    ];
    const newAndPending: any = [
      {
        headerName: 'New',
        field: 'New',
        filter: false,
        valueGetter: (params: any) => [params.data?.newCount, params?.data?.userId, params?.data?.projectTitle],
        minWidth: 70,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>` : params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('New', params);
              return;
            }
            this.getDataFromCell('New', event);
          }
        },
      },
      {
        headerName: 'Pending',
        field: 'Pending',
        filter: false,
        valueGetter: (params: any) => [params.data?.pendingCount, params?.data?.userId, params?.data?.projectTitle],
        minWidth: 80,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>` : params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Pending', params);
              return;
            }
            this.getDataFromCell('Pending', event);
          }
        },
      },
    ];
    const overview: any = [
      {
        headerName: 'Overdue',
        field: 'Overdue',
        filter: false,
        valueGetter: (params: any) => [(this.isCustomStatusEnabled ? params?.data?.statuses?.OverdueCount : params.data?.overdueCount), params?.data?.userId, params?.data?.projectTitle],
        minWidth: 90,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>` : params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Overdue', params);
              return;
            }
            this.getDataFromCell('Overdue', event);
          }
        },
      },
    ];
    const callbackAndMS = [
      {
        headerName: 'Callback',
        field: 'Callback',
        filter: false,
        valueGetter: (params: any) => [params.data?.callbackCount, params?.data?.userId, params?.data?.projectTitle],
        minWidth: 90,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>` : params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Callback', params);
              return;
            }
            this.getDataFromCell('Callback', event);
          }
        },
      },
      {
        headerName: 'Meeting Scheduled',
        field: 'Meeting Scheduled',
        filter: false,
        valueGetter: (params: any) => [params.data?.meetingScheduledCount, params?.data?.userId, params?.data?.projectTitle],
        minWidth: 140,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p></a>` : `<span><p>${params.value[0] ? params.value[0] : '--'}</p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Meeting Scheduled', params);
              return;
            }
            this.getDataFromCell('Meeting Scheduled', event);
          }
        },
      },
    ];
    const meetingDoneAndNotDone: any = [
      {
        headerName: 'Meeting Done',
        field: 'Meeting Done',
        filter: false,
        valueGetter: (params: any) => [
          (this.isCustomStatusEnabled ? params?.data?.statuses?.MeetingDoneCount : params.data?.meetingDoneCount),
          (this.isCustomStatusEnabled ? params?.data?.statuses?.MeetingDoneUniqueCount : params.data?.meetingDoneUniqueCount),
          params?.data?.userId,
          params?.data?.projectTitle
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>` : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
          }<span></p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountInNewTab('All Leads', params, 'Meeting Done');
              return;
            }
            this.getMeetingCountFromCell('All Leads', event, 'Meeting Done');
          }
        },
      },
      {
        headerName: 'Meeting Not Done',
        field: 'Meeting Not Done',
        filter: false,
        valueGetter: (params: any) => [
          (this.isCustomStatusEnabled ? params?.data?.statuses?.MeetingNotDoneCount : params.data?.meetingNotDoneCount),
          (this.isCustomStatusEnabled ? params?.data?.statuses?.MeetingNotDoneUniqueCount : params.data?.meetingNotDoneUniqueCount),
          params?.data?.userId,
          params?.data?.projectTitle
        ],
        minWidth: 150,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>` : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
          }<span></p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountInNewTab('All Leads', params, 'Meeting Not Done');
              return;
            }
            this.getMeetingCountFromCell('All Leads', event, 'Meeting Not Done');
          }
        },
      },
    ];
    const svs: any = [
      {
        headerName:  this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',
        field:  this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',
        filter: false,
        valueGetter: (params: any) => [params.data?.siteVisitScheduledCount, params?.data?.userId, params?.data?.projectTitle],
        minWidth: 150,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p></a>` : `<span><p>${params.value[0] ? params.value[0] : '--'}</p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab(!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Scheduled' : 'Referral Scheduled', params);
              return;
            }
            this.getDataFromCell(!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Scheduled' : 'Referral Scheduled', event);
          }
        },
      },
    ];
    const siteVisitDoneAndNotDone: any = [
      {
        headerName:  !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
        field:  !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
        filter: false,
        valueGetter: (params: any) => [
          (this.isCustomStatusEnabled ? params?.data?.statuses?.SiteVisitDoneCount : params.data?.siteVisitDoneCount),
          (this.isCustomStatusEnabled ? params?.data?.statuses?.SiteVisitDoneUniqueCount : params.data?.siteVisitDoneUniqueCount),
          params?.data?.userId,
          params?.data?.projectTitle
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>` : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
          }<span></p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountInNewTab('All Leads', params, 'Site Visit Done');
              return;
            }
            this.getMeetingCountFromCell('All Leads', event, 'Site Visit Done');
          }
        },
      },
      {
        headerName: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Not Done' : 'Referral Not Taken',
        field: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Not Done' : 'Referral Not Taken',
        filter: false,
        valueGetter: (params: any) => [
          (this.isCustomStatusEnabled ? params?.data?.statuses?.SiteVisitNotDoneCount : params.data?.siteVisitNotDoneCount),
          (this.isCustomStatusEnabled ? params?.data?.statuses?.SiteVisitNotDoneUniqueCount : params.data?.siteVisitNotDoneUniqueCount),
          params?.data?.userId,
          params?.data?.projectTitle
        ],
        minWidth: 150,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>` : `<span ><p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
          }<span></p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountInNewTab('All Leads', params, 'Site Visit Not Done');
              return;
            }
            this.getMeetingCountFromCell('All Leads', event, 'Site Visit Not Done');
          }
        },
      },
    ];
    const others: any = [
      {
        headerName: 'Booked',
        field: 'Booked',
        filter: false,
        valueGetter: (params: any) => [params.data?.bookedCount, params?.data?.userId, params?.data?.projectTitle],
        minWidth: 90,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>` : params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Booked', params);
              return;
            }
            this.getDataFromCell('Booked', event);
          }
        },
      },
      {
        headerName: 'Invoiced',
        field: 'Invoiced',
        filter: false,
        valueGetter: (params: any) => [params.data?.invoicedLeadsCount, params?.data?.userId, params?.data?.projectTitle],
        minWidth: 90,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == "Total" || event.value[0] == 0) {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Invoiced', params);
              return;
            }
            this.getDataFromCell('Invoiced', event);
          }
        },
      },
      {
        headerName: 'Booking Cancel',
        field: 'Booking Cancel',
        filter: false,
        valueGetter: (params: any) => [params.data?.bookingCancelCount, params?.data?.userId, params?.data?.projectTitle],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>` : params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Booking Cancel', params);
              return;
            }
            this.getDataFromCell('Booking Cancel', event);
          }
        },
      },
      {
        headerName: 'Not Interested',
        field: 'Not Interested',
        filter: false,
        valueGetter: (params: any) => [params.data?.notInterestedCount, params?.data?.userId, params?.data?.projectTitle],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>` : params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Not Interested', params);
              return;
            }
            this.getDataFromCell('Not Interested', event);
          }
        },
      },
      {
        headerName: 'Dropped',
        field: 'Dropped',
        filter: false,
        valueGetter: (params: any) => [params.data?.droppedCount, params?.data?.userId, params?.data?.projectTitle],
        minWidth: 80,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return !this.isCustomStatusEnabled ? params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>` : params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
            `<p><span >${params.value[0] ? params.value[0] : '--'}</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Dropped', params);
              return;
            }
            this.getDataFromCell('Dropped', event);
          }
        },
      },
      {
        headerName: 'Expression Of Interest',
        field: 'Expression Of Interest',
        filter: false,
        valueGetter: (params: any) => [params.data?.expressionOfInterestCount, params?.data?.userId, params?.data?.projectTitle],
        minWidth: 160,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam)
            filters.IsWithTeam = true;
          return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              const filters = { ...this.filtersPayload };
              if (filters?.IsWithTeam)
                filters.IsWithTeam = true;
              window?.open(`leads/manage-leads?leadReportGetData=true&assignTo=${params?.value?.[0]}&data=${encodeURIComponent(JSON.stringify(params?.data))}&operation=Dropped&filtersPayload=${encodeURIComponent(JSON.stringify({ ...this.filtersPayload, SubSources: null }))}`, "_blank");
              return;
            }
            this.getDataFromCell('Expression Of Interest', event);
          }
        },
      },
    ];
    this.gridOptions.columnDefs = (this.isCustomStatusEnabled ? [
      ...nameAndLeads,
      // ...newAndPending,
      ...overview,
      // ...callbackAndMS,
      ...meetingDoneAndNotDone,
      // ...svs,
      ...siteVisitDoneAndNotDone,
      // ...others
    ] : [
      ...nameAndLeads,
      ...newAndPending,
      ...overview,
      ...callbackAndMS,
      ...meetingDoneAndNotDone,
      ...svs,
      ...siteVisitDoneAndNotDone,
      ...others
    ]);

    if (this.isCustomStatusEnabled)
      this.customStatusList.forEach((customStatus: CustomStatus) => {
        let col: any = {
          headerName: customStatus?.displayName,
          field: customStatus?.displayName,
          filter: false,
          valueGetter: (params: any) => [params?.data?.statuses?.[customStatus?.displayName], params?.data?.userId, params?.data?.projectTitle],
          minWidth: 90,
          cellRenderer: (params: any) => {
            const filters = { ...this.filtersPayload };
            if (filters?.IsWithTeam)
              filters.IsWithTeam = true;
            return !this.isCustomStatusEnabled ? params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
              `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>` : params?.value?.[2] == "Total" || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` :
              `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`;
          },
          cellClass: 'cursor-pointer',
          onCellClicked: (event: any) => {
            const isCtrlClick = event?.event?.ctrlKey;
            const params = { value: event?.value, data: event?.data };
            if (event.data.projectTitle == 'Total') {
              return;
            } else if (event.value[0] != 0) {
              if (isCtrlClick) {
                this.getDataInNewTab(customStatus?.displayName, params);
                return;
              }
              this.getDataFromCell(customStatus?.displayName, event);
            }
          },
        };

        this.gridOptions?.columnDefs?.push(col);
      })

    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  getDataFromCell(operation: string, event: any) {
    // if (this.isCustomStatusEnabled) {
    //   return
    // }
    this.router.navigate(['leads/manage-leads']);
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.dateType = this.appliedFilter.dateType;
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.status = operation;
    if (this.appliedFilter.dateForSource?.[0]) {
      this.gridOptionsService.meetingStatus = ['Meeting Done', 'Meeting Not Done', 'Site Visit Done', 'Site Visit Not Done'];
      this.filtersPayload = {
        ...this.filtersPayload,
        fromDate: this.appliedFilter?.dateForSubSource?.[0],
        toDate: this.appliedFilter?.dateForSubSource?.[0],
      }
    }
    this.gridOptionsService.payload = { ...this.filtersPayload, SubSources: null };
  }


  getDataInNewTab(operation: string, params: any) {
    // if (this.isCustomStatusEnabled) {
    //   return
    // }
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = true;
    }
    window?.open(`leads/manage-leads?leadReportGetData=true&data=${encodeURIComponent(JSON.stringify(params?.data))}&operation=${operation}&filtersPayload=${encodeURIComponent(JSON.stringify({ ...this.filtersPayload, SubSources: null }))}`, "_blank");
  }

  getMeetingCountFromCell(operation: string, event: any, meetingStatus: string) {
    // if(this.isCustomStatusEnabled){
    //   return
    // }
    this.router.navigate(['leads/manage-leads']);
    let visitMeeting = [];
    visitMeeting.push(meetingStatus);
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.dateType = this.appliedFilter.dateType;
    this.gridOptionsService.status = operation;
    this.gridOptionsService.payload = { ...this.filtersPayload, SubSources: null };
    this.gridOptionsService.meetingStatus = visitMeeting;
  }


  getMeetingCountInNewTab(operation: string, params: any, meetingStatus: string) {
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = true;
    }
    window?.open(`leads/manage-leads?leadReportGetMeetingCount=true&data=${encodeURIComponent(JSON.stringify(params?.data))}&operation=${operation}&meetingStatus=${meetingStatus}&filtersPayload=${encodeURIComponent(JSON.stringify({ ...filters, SubSources: null }))}`, "_blank");
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this._store.dispatch(
      new UpdateSubSourcesFilterPayload(this.filtersPayload)
    );
    this.isCustomStatusEnabled ? this._store.dispatch(new FetchReportsCustomSubSources()) : this._store.dispatch(new FetchReportsSubSources());
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(
      new UpdateSubSourcesFilterPayload(this.filtersPayload)
    );
    this.isCustomStatusEnabled ? this._store.dispatch(new FetchReportsCustomSubSources()) : this._store.dispatch(new FetchReportsSubSources());
    this.currOffset = 0;
  }

  onResetDateFilter() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateType: null,
      date: '',
    };
    this.filterFunction();
  }

  filterFunction() {
    this.appliedFilter.pageNumber = 1;

    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      dateType: ReportDateType[this.appliedFilter.dateType],
      fromDate: setTimeZoneDate(this.appliedFilter?.date?.[0], this.userData?.timeZoneInfo?.baseUTcOffset),
      toDate: setTimeZoneDate(this.appliedFilter.date?.[1], this.userData?.timeZoneInfo?.baseUTcOffset),
      IsWithTeam: this.appliedFilter.withTeam,
      UserIds: this.appliedFilter.users,
      AgencyNames: this.appliedFilter.agencyNames,
      SearchText: this.searchTerm,
      Sources: this.appliedFilter.sources,
      SubSources: this.appliedFilter.subSources,
      Projects: this.appliedFilter.projects,
      ReportPermission: this.canViewAllUsers ? 0 : 1,
      ExportPermission: this.canExportAllUsers ? 0 : 1,
      // Locations: this.appliedFilter?.locations,
      Cities: this.appliedFilter?.cities,
      States: this.appliedFilter?.states,
      Countries: this.appliedFilter?.Countries,
      fromDateForSubSource: setTimeZoneDate(
        this.appliedFilter?.dateForSubSource?.[0], this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      toDateForSubSource: setTimeZoneDate(
        this.appliedFilter?.dateForSubSource?.[1], this.userData?.timeZoneInfo?.baseUTcOffset
      ),
    };
    this._store.dispatch(
      new UpdateSubSourcesFilterPayload(this.filtersPayload)
    );
    if (this.isCustomStatusEnabled !== undefined) {
      this.isCustomStatusEnabled ? this._store.dispatch(new FetchReportsCustomSubSources()) : this._store.dispatch(new FetchReportsSubSources());
    }
    this.currOffset = 0;
    if (this.appliedFilter?.dateType?.length ||
      this.appliedFilter?.date?.[0]?.length ||
      this.appliedFilter.users?.length ||
      this.appliedFilter.projects?.length ||
      this.appliedFilter.subSources?.length ||
      this.appliedFilter.sources?.length ||
      this.appliedFilter.agencyNames?.length ||
      this.appliedFilter.cities?.length ||
      this.appliedFilter.states?.length ||
      this.appliedFilter.dateForSubSource?.[0] ||
      this.appliedFilter.cities?.length ||
      this.appliedFilter.Countries?.length ||
      this.appliedFilter.states?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
  }

  reset() {
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.pageSize,
    };
    this.filterFunction();
  }

  resetDate() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateForSubSource: [null, null],
    };
    this.isDateFilter = '';
    this.filterFunction();
  }

  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = ['subSources', 'projects', 'agencyNames', 'cities', 'states'];

    if (
      [
        'pageSize',
        'pageNumber',
        'visibility',
        'withTeam',
        'userStatus',
        'search'
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'dateForSubSource' && values.length === 2) {
      if (key === 'dateForSubSource' && values[0] !== null) {
        this.toDateForSubSource = setTimeZoneDate(new Date(values[0]), this.userData?.timeZoneInfo?.baseUTcOffset);
        this.fromDateForSubSource = setTimeZoneDate(new Date(values[1]), this.userData?.timeZoneInfo?.baseUTcOffset);
        const formattedtoDateForSubSource = getTimeZoneDate(this.toDateForSubSource, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear');
        const formattedfromDateForSubSource = getTimeZoneDate(this.fromDateForSubSource, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear');
        const dateString = `${formattedtoDateForSubSource} to ${formattedfromDateForSubSource}`;
        return [dateString];

      } else {
        return null;
      }
    }
    else if (key === 'date' && values.length === 2) {
      if (key === 'date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(new Date(values[0]), this.userData?.timeZoneInfo?.baseUTcOffset);
        this.fromDate = setTimeZoneDate(new Date(values[1]), this.userData?.timeZoneInfo?.baseUTcOffset);
        const formattedToDate = getTimeZoneDate(this.toDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear');
        const formattedFromDate = getTimeZoneDate(this.fromDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear');
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    } else if (allowedKeys.includes(key)) {
      return values;
    } return values?.toString()?.split(',');
  }

  applyAdvancedFilter() {
    this.filterFunction();
    this.modalService.hide();
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.fullName}`;
    });
    return userName;

  }

  filterByDate(type?: string) {
    let newDate = new Date();
    let date = new Date(this.currentDate.setHours(0, 0, 0, 0));
    switch (type) {
      case 'today':
        this.isDateFilter = 'today';
        this.appliedFilter.dateForSubSource[0] = new Date(date);
        this.appliedFilter.dateForSubSource[1] = new Date(date);
        break;
      case 'yesterday':
        this.isDateFilter = 'yesterday';
        this.appliedFilter.dateForSubSource[0] = new Date(date).setDate(
          new Date(date).getDate() - 1
        );
        this.appliedFilter.dateForSubSource[1] = new Date(date).setDate(
          new Date(date).getDate() - 1
        );
        break;
      case 'sevenDays':
        this.isDateFilter = 'sevenDays';
        this.appliedFilter.dateForSubSource[0] = new Date(date).setDate(
          new Date(date).getDate() - 6
        );
        this.appliedFilter.dateForSubSource[1] = new Date(date);
        break;
      case 'custom':
        this.isDateFilter = 'custom';
        this.appliedFilter.dateForSubSource[0] = null;
        this.appliedFilter.dateForSubSource[1] = null;
        break;
    }
  }

  activeDate() {
    const fromDateForSubSource = new Date(this.appliedFilter.dateForSubSource[0]);
    const toDateForSubSource = new Date(this.appliedFilter.dateForSubSource[1]);

    const today = new Date(this.currentDate);
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 6);

    if (
      fromDateForSubSource.toDateString() === today.toDateString() &&
      toDateForSubSource.toDateString() === today.toDateString()
    ) {
      this.isDateFilter = 'today';
    } else if (
      fromDateForSubSource.toDateString() === yesterday.toDateString() &&
      toDateForSubSource.toDateString() === yesterday.toDateString()
    ) {
      this.isDateFilter = 'yesterday';
    } else if (
      fromDateForSubSource.toDateString() === sevenDaysAgo.toDateString() &&
      toDateForSubSource.toDateString() === today.toDateString()
    ) {
      this.isDateFilter = 'sevenDays';
    } else if (this.appliedFilter.dateForAgency?.[0]
    ) {
      this.isDateFilter = 'custom';
    }
  }

  onRemoveFilter(key: string, value: string) {
    if (key === 'date' || key === 'dateType') {
      delete this.appliedFilter[key];
      const dependentKey = key === 'date' ? 'dateType' : 'date';
      if (this.appliedFilter[dependentKey]) {
        delete this.appliedFilter[dependentKey];
      }
    } else if (key === 'dateForSubSource') {
      delete this.appliedFilter[key];
      this.isDateFilter = '';
    }
    else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter((item: any, index: number) => {
        const matchIndex = this.appliedFilter[key]?.indexOf(value);
        // Only remove the item if it's the first match
        return index !== matchIndex;
      });
    }
    this.filterFunction();
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchAgencyNameList());
    this._store.dispatch(new FetchSubSourceList());
    this._store.dispatch(new FetchLeadCities());
    this._store.dispatch(new FetchLeadStates());
    // this._store.dispatch(new FetchLeadCountries());
    this._store.dispatch(new FetchAllSources());
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }


  updateSubSource() {
    if (this.appliedFilter?.sources?.length) {
      this.subSourceList = [];
      this.appliedFilter?.sources.forEach((i: any) => {
        const source: any = LeadSource[i];
        const leadSource = IntegrationSource[source];
        if (leadSource === '99 Acres') {
          this.subSourceList.push.apply(
            this.subSourceList,
            this.allSubSourceList['NinetyNineAcres'] || []
          );
        } else {
          const formattedKey = leadSource?.replace(/\s+/g, '');
          if (Array.isArray(this.allSubSourceList[formattedKey])) {
            this.subSourceList.push.apply(
              this.subSourceList,
              this.allSubSourceList[formattedKey] || []
            );
          } else {
            this.subSourceList.push.apply(
              this.subSourceList,
              this.allSubSourceList[leadSource] || []
            );
          }
        }
      });
    } else {
      let subSourceList: string[] = this.leadSources?.flatMap((lead: any): string[] => {
        if (lead?.displayName === '99 Acres') {
          return this.allSubSourceList?.['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        let match = this.allSubSourceList[formattedKey];
        if (!match) {
          match = this.allSubSourceList[lead?.displayName];
        }
        if (!match && formattedKey?.toLowerCase() === '99acres') {
          match = this.allSubSourceList['NinetyNineAcres'];
        }
        return Array.isArray(match) ? match : [];
      }) || [];
      this.subSourceList = subSourceList
    }
  }

  onSelectSource(source: any) {
    if (source) {
      this.updateSubSources(source.displayName);
    } else {
      this.updateSubSources(null);
    }
  }

  updateSubSources(sourceName: string | null) {
    if (sourceName) {
      if (sourceName === '99 Acres') {
        this.subSourceList = this.allSubSourceList['NinetyNineAcres'] || [];
      } else {
        const formattedKey = sourceName.replace(/\s+/g, '');
        if (Array.isArray(this.allSubSourceList[formattedKey])) {
          this.subSourceList = this.allSubSourceList[formattedKey] || [];
        } else {
          this.subSourceList = this.allSubSourceList[sourceName] || [];
        }
      }
    } else {
      let subSourceList: string[] = this.leadSources?.flatMap((lead: any): string[] => {
        if (lead?.displayName === '99 Acres') {
          return this.allSubSourceList?.['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        let match = this.allSubSourceList[formattedKey];
        if (!match) {
          match = this.allSubSourceList[lead?.displayName];
        }
        if (!match && formattedKey?.toLowerCase() === '99acres') {
          match = this.allSubSourceList['NinetyNineAcres'];
        }
        return Array.isArray(match) ? match : [];
      }) || [];
      this.subSourceList = subSourceList
    }
  }

  exportSubSourceReport() {
    this._store.dispatch(new FetchSubSourceExportSuccess(''));
    this.filterFunction();

    let initialState: any = {
      payload: {
        ...this.filtersPayload,
        timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset()
      }, class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  toggleView() {
    this.currentView = this.currentView === 'graph' ? 'table' : 'graph';
  }

  exportGraphAsPDF() {
    if (this.reportsGraph && this.isGraphExportEnabled()) {
      this.reportsGraph.exportGraph();
    }
  }

  isGraphExportEnabled(): boolean {
    return this.currentView === 'graph' && 
           this.reportsGraph?.isChartReady && 
           !this.reportsGraph?.showSelectionMessage;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
