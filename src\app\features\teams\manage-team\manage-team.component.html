<div *ngIf="canView">
    <div class="bg-white w-100 px-24 py-6 border-bottom" [ngClass]="showLeftNav ? 'left-230' : 'left-125'">
        <div class="flex-between flex-grow-1">
            <div class="align-center">
                <img [type]="'leadrat'" [appImage]="s3BucketUrl + 'logos/muso-team.svg'" alt="Team" class="header-image">
                <h6 class="ml-4 fw-600">{{ title }}</h6>
            </div>
            <div class="align-center">
                <ng-container *ngIf="canExport">
                    <button *ngIf="canExport" class="btn-coal mr-10" id="btnExportTracker"
                        data-automate-id="btnExportTracker" (click)="openTeamsTracker()">
                        <span class="ic-download icon ic-xxs"></span>
                        <span class="ml-8 ip-d-none">Export Tracker</span>
                    </button>
                </ng-container>
                <ng-container *ngIf="canAdd">
                    <div class="btn-coal" (click)="addTeamModal()">
                        <span class="ic-add icon ic-xxs"></span>
                        <span class="ml-8 ip-d-none">Add Team</span>
                    </div>
                </ng-container>
            </div>
        </div>
    </div>
    <div class="px-24 py-30 flex-grow-1">
        <div class="flex-between ip-flex-wrap">
            <div class="flex-col">
                <h3 class="ml-4 fw-600">{{ 'SIDEBAR.manage-team' | translate }}</h3>
                <h6 class="text-sm ml-4 text-light-gray">Create, customize, and delete teams with ease</h6>
            </div>
            <div class="d-flex">
                <!-- <h5 class="align-center fw-400 mr-4 tb-d-none">Search</h5> -->
                <div class="bg-white align-center px-12 no-validation py-6 br-20 border mr-12 ip-mt-10">
                    <input placeholder="Search team" name="search"
                        class="border-0 bg-white outline-0 w-260 bg-light-pearl" autocomplete="off"
                        (input)="onSearchTeam($event.target.value)" />
                    <span class="justify-end search icon ic-search-solid ic-xxs ic-slate-90"> </span>
                </div>
            </div>
        </div>
        <div *ngIf="(!allTeams?.length || !filteredTeams?.length) && !teamListIsLoading"
            class="flex-center-col mt-40 h-100-250">
            <img src="assets/images/layered-cards.svg" alt="No team found" class="">
            <div class="header-3 fw-600 text-center">{{'TEAM.no-team-found' |translate}}</div>
        </div>
        <div *ngIf="!teamListIsLoading; else teamLoader" class="d-flex flex-wrap">
            <ng-container *ngFor="let user of filteredTeams; let i = index">
                <div class="w-20 tb-w-33 ip-w-50 ph-w-100 mt-20 position-relative">
                    <div class="bg-white hovered-card p-12 br-6 mr-20 h-170">
                        <div class="position-relative mb-0">
                            <div *ngIf="canBulkDelete" [ngClass]="{ 'hover-container': !isTeamSelected(user) }">
                                <div class="flex-start checkbox-container">
                                    <label class="checkbox-label">
                                        <input type="checkbox" [checked]="isTeamSelected(user)"
                                            (change)="toggleTeamSelection(user, $event)">
                                        <span class="checkmark cursor-pointer"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="flex-center">
                                <img *ngIf="user?.imageUrl"
                                    [appImage]="user?.imageUrl?.includes(s3BucketUrl) ? user?.imageUrl: s3BucketUrl+user?.imageUrl"
                                    [type]="'defaultAvatar'" alt="logo" height="56px" width="56px"
                                    class="br-30 border-gray-2 obj-fill border-gray">
                                <img *ngIf="!user?.imageUrl" src="../../../../assets/images/single-muso-white.svg"
                                    [type]="'defaultAvatar'" alt="logo" height="56px" width="56px"
                                    class="br-30 obj-fill bg-light-pearl">
                                <div class="hover-container mb-56">
                                    <div class="position-absolute end-0 align-center">
                                        <div *ngIf="canEdit" title="Edit" class="icon-badge">
                                            <span class="icon ic-accent-green ic-pen-solid m-auto ic-xxs"
                                                id="clkEditTeam" data-automate-id="clkEditTeam"
                                                (click)="editTeam(user)"></span>
                                        </div>
                                        <div class="border h-10 ml-8"></div>
                                        <div *ngIf="canDelete" title="Delete" class="icon-badge" id="clkDelete"
                                            data-automate-id="clkDelete">
                                            <span class="icon ic-red-120 ic-trash m-auto ic-xxs"
                                                (click)="deleteTeam(user)"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <h4
                                class="header-4 pt-10 text-black-200 fw-300 clear-margin text-nowrap text-truncate-1 break-all heading-hover">
                                {{user?.teamName}}</h4>
                            <h6 class="text-sm fw-300 heading-hover text-nowrap text-truncate-1 break-all">
                                {{user?.manager?.name}}
                            </h6>
                        </div>
                        <div class="flex-grow-1 border-bottom mt-8"></div>
                        <div class="flex-between mt-10 br-6 ph-mr-0 cursor-pointer">
                            <div class="align-center text-light-gray clear-margin">
                                <span class="icon ic-two-persons ic-xxs ic-light-gray ic-hover"></span>
                                <div class="flex-col ml-4">
                                    <div class="mt-2 text-truncate-1 break-all text-nowrap"><span
                                            class="ml-4 header-6 fw-semi-bold typo-hover">{{user?.totalMembers}} </span>
                                        <span class="text-xs fw-300 typo-hover">Members</span>
                                    </div>
                                    <div class="hover-container ml-2">
                                        <div class="text-decoration-underline text-xs text-accent-green fw-semi-bold text-truncate-1 break-all text-nowrap"
                                            (click)="navigateToMember(user); $event.stopPropagation();">
                                            View Members
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ml-10 border h-10 mx-4"></div>
                            <div class="align-center text-light-gray clear-margin pl-10 ">
                                <span class="icon ic-secondary-filter-solid ic-xxs ic-light-gray ic-hover"></span>
                                <div class="flex-col ml-4">
                                    <div class="mt-2 text-truncate-1 break-all text-nowrap"><span
                                            class="ml-4 header-6 fw-semi-bold typo-hover">{{user?.leadsAssigned}}
                                        </span>
                                        <span class="text-xs fw-300 typo-hover">Leads</span>
                                    </div>
                                    <div class="hover-container ml-2">
                                        <div *ngIf="canViewAllLeads" class="text-decoration-underline text-xs text-accent-green fw-semi-bold text-truncate-1 break-all text-nowrap" 
                                            (click)="user?.users?.length ? navigateToLeads(user) : '';trackingService.trackFeature('Web.Team.Button.ViewLead.Click')">
                                            View Leads
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>
        </div>
        <div class="justify-center" *ngIf="selectedTeams?.length > 0">
            <div
                class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 z-index-2 tb-flex-col">
                <div class="text-large fw-semi-bold">
                    <span class="fw-600 text-coal mr-4">{{ selectedTeams?.length }}</span>
                    <span class="text-coal mr-20">{{ selectedTeams?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected'
                        |
                        translate}}</span>
                    <div class="text-red-350 fw-semi-bold cursor-pointer" (click)="deselectAll()">{{ 'GLOBAL.deselect' |
                        translate }}</div>
                </div>
                <div *ngIf="canBulkDelete" class="flex-center flex-wrap">
                    <button class="btn-bulk-red" (click)="openBulkDeleteModal(BulkDeleteModal)"
                        [disabled]="selectedTeams?.length < 2" id="btnBulkDelete" data-automate-id="btnBulkDelete">
                        {{ 'LEADS.bulk' | translate }} {{ ('BUTTONS.delete') | translate }}</button>
                </div>
            </div>
        </div>
    </div>
    <ng-template #BulkDeleteModal>
        <div class="bg-light-pearl h-100vh bg-triangle-pattern">
            <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
                <h3>Bulk Delete</h3>
                <div class="icon ic-close  ic-sm cursor-pointer" (click)="modalService.hide()"></div>
            </div>
            <div class="px-12">
                <div class="field-label mb-10">Selected Team(s) -
                    {{selectedTeams?.length}}
                </div>
                <div class="flex-column scrollbar max-h-100-176">
                    <ng-container *ngFor="let team of selectedTeams">
                        <div class="flex-between p-12 fw-600 text-sm border-bottom text-secondary bg-white">
                            <span class="text-truncate-1 break-all mr-8">{{ team?.teamName }}</span>
                            <div (click)="openConfirmDeleteModal(team?.teamName, team?.id)"
                                class="bg-light-red icon-badge" id="clkBulkDelete" data-automate-id="clkBulkDelete">
                                <span class="icon ic-trash m-auto ic-xxxs"></span>
                            </div>
                        </div>
                    </ng-container>
                </div>
                <div class="flex-center">
                    <button class="btn-coal mt-20" (click)="bulkDelete()">{{ 'BUTTONS.delete' | translate }}</button>
                </div>
            </div>
        </div>
    </ng-template>
    <ng-template #teamLoader>
        <div class="flex-center h-100-170">
            <application-loader></application-loader>
        </div>
    </ng-template>