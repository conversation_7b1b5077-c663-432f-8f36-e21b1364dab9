import { Component, EventE<PERSON>ter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { filter, takeUntil } from 'rxjs';

import { WHATSAPP_SHARE_API } from 'src/app/app.constants';
import { ContactType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  getTenantName,
  LeadTemplateMsg,
  ProjectTemplateMsg,
  PropertyTemplateMsg
} from 'src/app/core/utils/common.util';
import {
  CommunicationBulkDataCount,
  CommunicationBulkDataMessage,
  CommunicationDataCount,
  CommunicationDataMessage,
} from 'src/app/reducers/data/data-management.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  CommunicationBulkCount,
  CommunicationBulkMessage,
  CommunicationCount,
  CommunicationMessage,
} from 'src/app/reducers/lead/lead.actions';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import {
  FetchProjectById,
  FetchProjectIdWithName,
} from 'src/app/reducers/project/project.action';
import {
  getProjectsIDWithName,
  getProjectsIDWithNameIsLoading,
  getSelectedProjectById,
} from 'src/app/reducers/project/project.reducer';
import {
  FetchPropertyById,
  FetchPropertyWithIdNameList,
} from 'src/app/reducers/property/property.actions';
import {
  getPropertyListDetails,
  getPropertyWithIdLoading,
  getPropertyWithIdNameList,
} from 'src/app/reducers/property/property.reducer';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { FetchTemplateModule } from 'src/app/reducers/template/template.actions';
import { getTemplatesModule } from 'src/app/reducers/template/template.reducer';
import { TenantService } from 'src/app/services/controllers/tenant.service';
import { ProjectsService } from 'src/app/services/controllers/projects.service';
import { PropertyService } from 'src/app/services/controllers/properties.service';

@Component({
  selector: 'leads-template-share',
  templateUrl: './leads-template-share.component.html',
})
export class LeadsTemplateShareComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  data: any;
  templates: [];
  isTemplatesLoading: boolean = true;
  tenantName: string = '';
  selectedCallType: any;
  selectedTemplate: any;
  message: string = '';
  defaultCurrency: string;
  selectedTab: string = 'lead';
  allUserList: any[] = [];
  userData: any;
  projectList: any[] = [];
  projectListIsLoading: boolean = true;
  propertyListIsLoading: boolean = true;
  propertyList: any[] = [];
  selectedProjects: any[] = [];
  selectedProperties: any[] = [];
  projectData: any[] = [];
  propertyData: any[] = [];
  areaSizeUnits: any;
  isSelectedProjectProperty: boolean = false;
  currentDate: Date = new Date();
  currentPath: string;

  constructor(
    public modalRef: BsModalRef,
    private _store: Store<AppState>,
    private tenantService: TenantService,
    public router: Router,
    private projectService: ProjectsService,
    private propertyService: PropertyService,
  ) { }

  ngOnInit(): void {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
        this.tenantName = this.userData?.organizationName;
      });
    this._store.dispatch(new FetchAreaUnitList());
    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this._store.dispatch(new FetchTemplateModule(0));

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
      });
    this._store
      .select(getTemplatesModule)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        this.isTemplatesLoading = res?.isTemplatesLoading;
        if (res != '' && res != undefined) {
          this.templates = res.templates
            .filter((data: any) => data)
            .slice()
            .sort((a: any, b: any) => a.title.localeCompare(b.title));
        }
      });
    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((user: any) => {
        this.allUserList = user;
      });


  }

  sendMessage() {
    const selectedContactNo =
      this.selectedCallType === this.data?.alternateContactNo
        ? this.data?.alternateContactNo
        : this.data?.contactNo;
    let messageToSend = '';
    switch (this.selectedTab) {
      case 'lead':
        messageToSend = LeadTemplateMsg(
          this.message || this.selectedTemplate?.message,
          this.data,
          this.tenantName,
          this.defaultCurrency,
          this.selectedTemplate?.header,
          this.selectedTemplate?.footer,
          this.allUserList,
          this.userData,
          this.currentDate,
          this.currentPath
        )?.replace(/\\n/g, '\n');
        break;
      case 'project':
        messageToSend = ProjectTemplateMsg(
          this.selectedTemplate?.message,
          this.projectData,
          this.areaSizeUnits,
          this.tenantName,
          this.selectedTemplate?.header,
          this.selectedTemplate?.footer,
          this.allUserList,
          'share-project',
          this.userData,
          this.currentDate
        );
        break;
      default:
        messageToSend = PropertyTemplateMsg(
          this.selectedTemplate?.message,
          this.propertyData,
          this.areaSizeUnits,
          this.tenantName,
          this.selectedTemplate?.header,
          this.selectedTemplate?.footer,
          true,
          'share-property',
          this.userData,
          this.currentDate
        );
        break;
    }

    if (this.data?.shareType == 'WhatsApp') {
      window.open(
        `${WHATSAPP_SHARE_API}?phone=${selectedContactNo}&text=${messageToSend ? encodeURIComponent(messageToSend) : 'Hi'
        }`,
        '_blank'
      );
    } else if (this.data?.shareType == 'Email') {
      window.location.href = `mailto:${this.data?.email ? this.data?.email : ''
        }?body=${encodeURIComponent(messageToSend)}`;
    } else if (this.data?.shareType == 'SMS') {
      window.location.href = `sms:${selectedContactNo}?body=${encodeURIComponent(
        messageToSend
      )}`;
    }

    const payload: any = {
      contactType: ContactType[this.data?.shareType],
      message: messageToSend,
    };

    let bulkData: boolean = false;
    if (Array.isArray(this.data)) {
      bulkData = true;
    }

    const ids: string[] = bulkData
      ? this.data?.map((item: any) => item?.id)
      : [];

    if (bulkData && this.data?.isData) {
      payload.prospectIds = ids;
    } else if (!bulkData && this.data?.isData) {
      payload.prospectId = this.data.id;
    } else if (bulkData && !this.data?.isData) {
      payload.leadIds = ids;
    } else if (!bulkData && !this.data?.isData) {
      payload.leadId = this.data.id;
    }

    if (bulkData && this.data?.isData) {
      this._store.dispatch(new CommunicationBulkDataMessage(payload));
    } else if (!bulkData && this.data?.isData) {
      this._store.dispatch(new CommunicationDataMessage(payload));
    } else if (bulkData && !this.data?.isData) {
      this._store.dispatch(new CommunicationBulkMessage(payload));
    } else if (!bulkData && !this.data?.isData) {
      this._store.dispatch(new CommunicationMessage(payload));
    }

    let payloadCount: any = {
      contactType: ContactType[this.data?.shareType],
    };

    if (bulkData) {
      payloadCount.ids = ids;
    } else {
      payloadCount.id = this.data.id;
    }

    if (bulkData && this.data?.isData) {
      this._store.dispatch(new CommunicationBulkDataCount(payloadCount));
    } else if (!bulkData && this.data?.isData) {
      this._store.dispatch(
        new CommunicationDataCount(payloadCount.id, payloadCount)
      );
    } else if (bulkData && !this.data?.isData) {
      this._store.dispatch(new CommunicationBulkCount(payloadCount));
    } else if (!bulkData && !this.data?.isData) {
      this._store.dispatch(
        new CommunicationCount(payloadCount.id, payloadCount)
      );
    }

    this.modalRef.hide();
    this._store.dispatch(new LoaderHide());
  }

  selectTab(tab: string) {
    this.selectedTemplate = null;
    let moduleId: number;
    switch (tab) {
      case 'lead':
        moduleId = 0;
        break;
      case 'project':
        moduleId = 5;
        break;
      default:
        moduleId = 6;
    }

    this._store.dispatch(new FetchTemplateModule(moduleId));
    this.subscribeToTemplateModule();

    if (tab === 'property') {
      this.fetchAndSubscribeToList(
        FetchPropertyWithIdNameList,
        getPropertyWithIdNameList,
        'propertyList',
        getPropertyWithIdLoading,
        'propertyListIsLoading',
        true
      );
    } else if (tab === 'project') {
      this.fetchAndSubscribeToList(
        FetchProjectIdWithName,
        getProjectsIDWithName,
        'projectList',
        getProjectsIDWithNameIsLoading,
        'projectListIsLoading',
        false
      );
    }
    this.selectedTab = tab;
  }

  subscribeToTemplateModule(): void {
    this._store
      .select(getTemplatesModule)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        this.isTemplatesLoading = res?.isTemplatesLoading;
        if (res?.templates) {
          this.templates = res.templates
            .slice()
            .filter((data: any) => data)
            .sort((a: any, b: any) => a.title.localeCompare(b.title));
        }
      });
  }

  fetchAndSubscribeToList(
    action: any,
    selector: any,
    listProperty: keyof this,
    loadingSelector: any,
    loadingProperty: keyof this,
    isProject: boolean = false
  ): void {
    this._store.dispatch(new action());

    this._store
      .select(selector)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data) {
          if (isProject) {
            (this[listProperty] as any) = data
              ?.slice()
              .sort((a: any, b: any) => {
                const titleA = a.title || '';
                const titleB = b.title || '';
                return titleA.localeCompare(titleB);
              });
          } else {
            (this[listProperty] as any) = data
              ?.slice()
              .sort((a: any, b: any) => {
                const nameA = a.name || '';
                const nameB = b.name || '';
                return nameA.localeCompare(nameB);
              });
          }
        }
      });

    this._store
      .select(loadingSelector)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        (this[loadingProperty] as any) = isLoading;
      });
  }

  onProjectSelect(selectedProjects: any[]): void {
    this.isSelectedProjectProperty = selectedProjects?.length > 0;
    this.selectedTemplate = null;
    this.projectData = [];

    if (selectedProjects?.length) {
      let ids:any = []
      selectedProjects.forEach(project => {
        ids = [...ids, project.id]
      });
      this.projectService.getProjectsByIds(ids).subscribe((res: any) => {
        if(res.succeeded)
        this.projectData = res.data.map((project: any) => ({
          ...project,
          leadName: this.data?.name,
        }));
      });
    }
  }

onPropertySelect(selectedProperties: any[]): void {
  this.isSelectedProjectProperty = selectedProperties?.length > 0;
  this.selectedTemplate = null;
  this.propertyData = [];

  if (selectedProperties?.length) {
    let ids: any = []
    selectedProperties.forEach(property => {
      ids = [...ids, property.id]
    });
    this.propertyService.getPropertiesByIds(ids).subscribe((res: any) => {
      if(res.succeeded)
        this.propertyData = res.data?.map((property: any) => ({
          ...property,
          leadName: this.data?.name,
        }));
    });
  }
}

  getTextareaValue(): string {
    if (this.selectedTab === 'lead') {
      return LeadTemplateMsg(
        this.selectedTemplate?.message,
        this.data,
        this.tenantName,
        this.defaultCurrency,
        this.selectedTemplate?.header,
        this.selectedTemplate?.footer,
        this.allUserList,
        this.userData,
        this.currentDate,
        this.currentPath
      )?.replace(/\\n/g, '\n');
    } else if (this.selectedTab === 'project' && this.projectData.length) {
      return this.projectData.map(project => 
        ProjectTemplateMsg(
          this.selectedTemplate?.message,
          project,
          this.areaSizeUnits,
          this.tenantName,
          this.selectedTemplate?.header,
          this.selectedTemplate?.footer,
          this.allUserList,
          'share-project',
          this.userData
        )
      ).join('\n\n');
    } else if (this.selectedTab === 'property' && this.propertyData.length) {
      return this.propertyData.map(property => 
        PropertyTemplateMsg(
          this.selectedTemplate?.message,
          property,
          this.areaSizeUnits,
          this.tenantName,
          this.selectedTemplate?.header,
          this.selectedTemplate?.footer,
          true,
          'share-property',
          this.userData
        )
      ).join('\n\n');
    }
    return '';
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
