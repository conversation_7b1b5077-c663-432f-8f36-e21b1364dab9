<div class="h-100vh min-w-350 max-w-400">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
        <h3>Configure</h3>
        <div class="icon ic-close  ic-sm cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="px-24 scrollbar scroll-hide h-100-60">
        <div class="bg-light-pearl mt-20 br-6 flex-between break-all bg-profile">
            <div class="flex-column pt-20 pl-10 pb-20">
                <div *ngIf="selectedTemplate" class="fw-semi-bold fv-sm-caps">
                    Template Name
                </div>
                <div class="fw-700 text-small mt-2">{{ selectedTemplate.name }}</div>
            </div>
        </div>
        <!-- Assign User -->
        <div class="bg-light-pearl my-20 br-6" [ngClass]="expandedSections['user'] ? 'pb-8' : ''"
            (click)="toggleExpand('user')">
            <div class="flex-between">
                <div class="d-flex pt-20 px-8 py-20 align-center">
                    <label title="Assign User" class="bg-blue-800 icon-badge">
                        <span class="icon ic-assign-to m-auto ic-xxs"></span>
                    </label>
                    <h5 class="ml-8 fw-semi-bold">Assign User</h5>
                </div>
                <div class="arrow-icon mr-16">
                    <span class="icon ic-black-100 ic-x-xs"
                        [ngClass]="expandedSections['user'] ? 'ic-triangle-down' : 'ic-triangle-up rotate-90'">
                    </span>
                </div>
            </div>
            <div *ngIf="expandedSections['user']" class="bg-white mx-8 p-8" (click)="$event.stopPropagation()">
                <div class="label-req mt-0">Assign to</div>
                <ng-select [virtualScroll]="true" [items]="allUserList" class="bg-white" bindLabel="fullName"
                    ResizableDropdown bindValue="id" placeholder="ex. Mounika Pampana" [formControl]="assignedToUsers"
                    [multiple]="true" appSelectAll [closeOnSelect]="false">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container">
                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                [checked]="item$.selected">
                            <span class="checkmark"></span>
                            <span class="text-truncate-1 break-all"> {{ item.fullName }}</span>
                        </div>
                    </ng-template>
                </ng-select>
                <div class="flex-end mt-4">
                    <h6 class="text-black-10 fw-semi-bold text-decoration-underline cursor-pointer"
                        (click)="toggleExpand('user', $event)">
                        {{ 'BUTTONS.cancel' | translate }}
                    </h6>
                    <button class="btn-coal w-55px ml-12" (click)="updateUsers($event);toggleExpand('user', $event)">
                        {{ 'BUTTONS.save' | translate }}
                    </button>
                </div>
            </div>
        </div>
        <!-- Assign Project -->
        <div class="bg-light-pearl my-20 br-6" [ngClass]="expandedSections['project'] ? 'pb-8' : ''"
            (click)="toggleExpand('project')">
            <div class="flex-between">
                <div class="d-flex pt-20 px-8 py-20 align-center">
                    <label title="Assign Project" class="bg-violet icon-badge">
                        <span class="icon ic-building-secondary m-auto ic-xxs"></span>
                    </label>
                    <h5 class="ml-8 fw-semi-bold">Assign Project</h5>
                </div>
                <div class="arrow-icon mr-16">
                    <span class="icon ic-black-100 ic-x-xs"
                        [ngClass]="expandedSections['project'] ? 'ic-triangle-down' : 'ic-triangle-up rotate-90'">
                    </span>
                </div>
            </div>
            <div *ngIf="expandedSections['project']" class="bg-white mx-8 p-8" (click)="$event.stopPropagation()">
                <div class="label mt-0">Project</div>
                <ng-select [virtualScroll]="true" [items]="allProjectsList" class="bg-white" bindLabel="name"
                    ResizableDropdown bindValue="id" placeholder="ex. ABC project" [formControl]="project"
                    [multiple]="true" appSelectAll [closeOnSelect]="false">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container">
                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                [checked]="item$.selected">
                            <span class="checkmark"></span>
                            <span class="text-truncate-1 break-all"> {{ item.name }}</span>
                        </div>
                    </ng-template>
                </ng-select>
                <div class="flex-end mt-4">
                    <h6 class="text-black-10 fw-semi-bold text-decoration-underline cursor-pointer"
                        (click)="toggleExpand('project', $event)">
                        {{ 'BUTTONS.cancel' | translate }}
                    </h6>
                    <button class="btn-coal w-55px ml-12" (click)="updateProject();toggleExpand('project', $event)">
                        {{ 'BUTTONS.save' | translate }}
                    </button>
                </div>
            </div>
        </div>
        <!-- Assign Agency -->
        <div class="bg-light-pearl my-20 br-6" [ngClass]="expandedSections['agency'] ? 'pb-8': ''"
            (click)="toggleExpand('agency')">
            <div class="flex-between">
                <div class="d-flex pt-20 px-8 py-20 align-center">
                    <label title="Assign Agency" class="bg-brown icon-badge">
                        <span class="icon ic-suitcase m-auto ic-xxs"></span></label>
                    <h5 class="ml-8 fw-semi-bold">Assign Agency</h5>
                </div>
                <div class="arrow-icon mr-16">
                    <span class="icon ic-black-100 ic-x-xs"
                        [ngClass]="expandedSections['agency'] ? 'ic-triangle-down' : 'ic-triangle-up rotate-90'"></span>
                </div>
            </div>
            <div *ngIf="expandedSections['agency']" class="bg-white mx-8 p-8" (click)="$event.stopPropagation()">
                <div class="label mt-0">Agency Name</div>
                <ng-select [virtualScroll]="true" [items]="agencyNameList" bindLabel="agencyName" bindValue="agencyName"
                    ResizableDropdown [formControl]="agencyName" class="bg-white"
                    placeholder="ex. Mounika pampana"></ng-select>
                <div class="flex-end mt-4">
                    <h6 class="text-black-10 fw-semi-bold text-decoration-underline cursor-pointer"
                        (click)="toggleExpand('agency', $event)">{{
                        'BUTTONS.cancel' | translate }}</h6>
                    <button class="btn-coal w-55px ml-12" (click)="updateAgentName();toggleExpand('agency', $event)">
                        {{ 'BUTTONS.save' | translate }}</button>
                </div>
            </div>
        </div>
        <!-- Track Campaign -->
        <div class="bg-light-pearl my-20 br-6" [ngClass]="expandedSections['campaign'] ? 'pb-8': ''"
            (click)="toggleExpand('campaign')">
            <div class="flex-between">
                <div class="d-flex pt-20 px-8 py-20 align-center">
                    <label title="Assign Agency" class="bg-orange-450 icon-badge">
                        <span class="icon ic-horn-solid m-auto ic-xxs"></span></label>
                    <h5 class="ml-8 fw-semi-bold">Track campaign</h5>
                </div>
                <div class="arrow-icon mr-16">
                    <span class="icon ic-black-100 ic-x-xs"
                        [ngClass]="expandedSections['campaign'] ? 'ic-triangle-down' : 'ic-triangle-up rotate-90'"></span>
                </div>
            </div>
            <div *ngIf="expandedSections['campaign']" class="bg-white mx-8 p-8" (click)="$event.stopPropagation()">
                <form [formGroup]=" trackCampaignForm">
                    <div>
                        <label class="label-req mt-16">Campaign name</label>
                        <form-errors-wrapper [control]="trackCampaignForm.controls['name']" label="campaign">
                            <ng-select [virtualScroll]="true" [items]="campaignList" class="bg-white" ResizableDropdown
                                placeholder="{{ 'GLOBAL.select' | translate }}/Create Campaign" [addTag]="true"
                                addTagText="Create New Campaign" formControlName="name" [multiple]="false"
                                [closeOnSelect]="true">
                            </ng-select>
                        </form-errors-wrapper>
                    </div>
                    <div>
                        <label class="label mt-16">Channel Partner</label>
                        <div class="form-group">
                            <ng-select [virtualScroll]="true" [items]="channelPartnerList" class="bg-white"
                                ResizableDropdown placeholder="Select channel partner" formControlName="channelPartner"
                                [multiple]="false" [closeOnSelect]="true">
                            </ng-select>
                        </div>
                    </div>
                    <div>
                        <label class="label mt-16">Assign Source</label>
                        <div class="form-group">
                            <ng-select [virtualScroll]="true" [items]="leadSources" bindValue="displayName"
                                bindLabel="displayName" class="bg-white" ResizableDropdown placeholder="Select source"
                                formControlName="source" [multiple]="false" [closeOnSelect]="true"
                                (change)="onSelectSource($event)">
                            </ng-select>
                        </div>
                    </div>
                    <div>
                        <label class="label mt-16">Assign Sub-Source</label>
                        <div class="form-group">
                            <ng-select [virtualScroll]="true" [items]="subSources" class="bg-white" ResizableDropdown
                                placeholder="Select subsource" formControlName="subSource" [multiple]="false"
                                [closeOnSelect]="true">
                            </ng-select>
                        </div>
                    </div>
                    <div>
                        <label class="label mt-16">Assign tags</label>
                        <div class="form-group">
                            <ng-select [virtualScroll]="true" [items]="leadTags" bindLabel="name" class="bg-white"
                                ResizableDropdown placeholder="Select tags" formControlName="tags" [multiple]="true" appSelectAll
                                [closeOnSelect]="false">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container">
                                        <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                            [checked]="item$.selected">
                                        <span class="checkmark"></span>{{item?.name}}
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div *ngIf="trackCampaignForm?.controls?.['tags']?.value?.length">
                        <h6 class="text-dark-gray mt-10">Selected</h6>
                        <div class="d-flex flex-wrap">
                            <div *ngFor="let tags of trackCampaignForm?.controls?.['tags']?.value"
                                class="w-25 flex-col p-4 mt-16 align-center">
                                <img [appImage]="tags?.activeImagePath ? tags?.activeImagePath: ''" alt="Tags"
                                    [type]="'defaultAvatar'" class="obj-cover" width="30px" height="30px">
                                <div class="text-wrap">
                                    <h6 class="text-center fw-400 mt-2">{{tags?.name}}</h6>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="flex-end mt-8">
                        <h6 class="text-black-10 fw-semi-bold text-decoration-underline cursor-pointer"
                            (click)="toggleExpand('campaign', $event)">{{
                            'BUTTONS.cancel' | translate }}</h6>
                        <button class="btn-coal w-55px ml-12" (click)="updateTrackCampaign($event)">
                            {{ 'BUTTONS.save' | translate }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>