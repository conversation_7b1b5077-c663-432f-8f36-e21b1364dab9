<div class="w-100 h-100-182 scrollbar">
  <div *ngIf="rowData?.length || showFilters || searchTerm">
    <ng-container *ngIf="canBulkUpload?.includes('Projects') || canAdd?.includes('Projects')">
      <div class="flex-end mb-4 px-24">
        <div class="btn-full-dropdown btn-w-100" *ngIf="canBulkUpload.includes('Projects')">
          <div class="position-absolute top-9 left-9 ip-top-11 align-center z-index-2"><span
              class="ic-tracker icon ic-xxs"></span>
            <span class="ml-8 ip-d-none">Tracker</span>
          </div>
          <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" [(ngModel)]="selectedTrackerOption"
            (click)="openProjectTracker()">
            <ng-option (click)="selectedTrackerOption = null" value="bulkUpload"
              *ngIf="canBulkUpload.includes('Projects')">
              <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
              {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
          </ng-select>
        </div>
        <div *ngIf="canAdd?.includes('Projects')" (click)="openCenterModal()" class="btn-left-dropdown ml-10">
          <span class="ic-add icon ic-xxs"></span>
          <span class="ml-8 ip-d-none">Add new unit</span>
        </div>
        <div class="btn-right-dropdown btn-w-30 black-100">
          <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" [(ngModel)]="selectedOption"
            (click)="openProjectBulkUpload()">
            <ng-option (click)="selectedOption = null" value="bulkUpload" *ngIf="canBulkUpload?.includes('Projects')">
              <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
              {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
          </ng-select>
        </div>
      </div>
    </ng-container>
  </div>
  <div *ngIf="rowData?.length || showFilters || searchTerm" class="px-24 mb-10 reports">
    <div class="align-center bg-white w-100 border-gray tb-align-center-unset tb-flex-col">
      <div class="align-center flex-grow-1 no-validation border-end tb-br-0">
        <div class="position-relative flex-grow-1">
          <div class="align-center w-100 px-10 py-12">
            <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
            <input type="text" (keydown)="onSearch($event)" (input)="isEmptyInput($event)" placeholder="type to search"
              [(ngModel)]="searchTerm" class="border-0 outline-0 w-100" />
          </div>
          <small class="text-muted text-nowrap ph-d-none mr-10 position-absolute right-0 bottom-0">
            ({{ 'LEADS.lead-search-prompt' | translate }})</small>
        </div>
      </div>
      <div class="tb-br-top align-center ip-flex-col ip-align-center-unset">
        <div class="d-flex w-100 py-12">
          <div class="px-10 align-center cursor-pointer border-end tb-flex-grow-1 ph-w-40px ph-flex-grow-unset"
            (click)="openAdvFiltersModal()">
            <div class="icon ic-filter-solid ic-xxs ic-black mr-10"></div>
            <span class="fw-600 text-nowrap">{{'PROPERTY.advanced-filters' | translate}}</span>
          </div>
        </div>
        <div class="d-flex ip-br-top">
          <div class="align-center position-relative cursor-pointer d-flex border-end">
            <span class="position-absolute left-15 z-index-2 fw-600 text-sm">
              {{ 'BUTTONS.manage-columns' | translate }}</span>
            <div class="show-hide-gray w-140">
              <ng-select [virtualScroll]="true" class="bg-white" [items]="columns" [multiple]="true" appSelectAll
                [searchable]="false" [closeOnSelect]="false" [ngModel]="defaultColumns"
                (change)="onColumnsSelected($event)">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container" [title]="item.label"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                      class="checkmark"></span>{{item.label}}</div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="bg-coal text-white px-10 py-12 ip-w-30px  align-center cursor-pointer"
            (click)="onSetColumnDefault()">
            <span class="ip-d-none">{{ 'GLOBAL.default' | translate }}</span> <span
              class="ic-refresh d-none ip-d-block"></span>
          </div>
          <div class="show-dropdown-white align-center position-relative ip-br-0">
            <span class="fw-600 position-absolute left-5 z-index-2"> <span>
                {{ 'GLOBAL.show' | translate }}</span> {{ 'GLOBAL.entries' | translate }}</span>
            <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" class="w-150"
              (change)="assignCount()" [(ngModel)]="selectedPageSize" [searchable]="false">
              <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                {{pageSize}}</ng-option>
            </ng-select>
          </div>
        </div>
      </div>
    </div>

    <!-- --------------------- filter -------------------------- -->
    <div class="bg-white px-4 py-8">
      <div class="tb-w-100-60" [ngClass]="showLeftNav ? 'w-100-190' : 'w-100-90'">
        <ng-container *ngIf="showFilters">
          <div class="bg-secondary flex-between">
            <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
              <div class="d-flex" *ngFor="let filter of appliedFilter | keyvalue">
                <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                  *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                  {{unitInfoFiltersKeyLabel[filter.key] || filter.key}}: {{
                  filter.key === 'UnitSubType' ? getUnitSubType(value) :
                  filter.key === 'MaxArea' || filter.key === 'MinArea' ? getUnitArea(value) :
                  filter.key === 'MaxCarpetArea' || filter.key === 'MinCarpetArea' ? getUnitCarpetArea(value) :
                  filter.key === 'MaxBuiltupArea' || filter.key === 'MinBuiltupArea' ? getUnitBuiltupArea(value) :
                  filter.key === 'MaxSuperBuiltupArea' || filter.key === 'MinSuperBuiltupArea' ?
                  getUnitSuperBuiltupArea(value) :
                  filter.key === 'UnitType' ? getUnitType(value) :
                  value }}
                  <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                    (click)="onRemoveFilter(filter.key, value)"></span>
                </div>
              </div>
            </drag-scroll>
            <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
              (click)="reset(true);filterFunction();">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}
            </div>
          </div>
        </ng-container>
      </div>
    </div>
    <!-- -------------------------- ENd filter ------------------------ -->
    <div *ngIf="rowData?.length" class="reports">
      <ag-grid-angular class="ag-theme-alpine w-100" [rowData]="rowData" [columnDefs]="colDefs"
        [gridOptions]="gridOptions" (gridReady)="onGridReady($event)" [pagination]="true"
        [suppressPaginationPanel]="true">
      </ag-grid-angular>
      <div class="my-6 mr-20 flex-end">
        <div class="mr-10" *ngIf="totalCount">{{ 'GLOBAL.showing' | translate }}
          {{totalCount ? currOffset*pageSize + 1 : 0}}
          {{ 'GLOBAL.to-small' | translate }}
          {{currOffset*pageSize + rowData?.length}}
          {{ 'GLOBAL.of-small' | translate }} {{totalCount}} {{ 'GLOBAL.entries-small' | translate}}
        </div>
        <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]='getPages(totalCount,pageSize)'
          (pageChange)="onPageChange($event)">
        </pagination>
      </div>
    </div>
  </div>
  <div *ngIf="!rowData?.length" class="h-100 w-100 px-20">
    <div class="flex-center-col h-100">
      <img src="../../../../assets/images/projects/no-unit-info.svg" alt="No unit found" class="mt-20">
      <h4 class="text-dark-gray my-20">No unit available</h4>
      <div *ngIf="canBulkUpload?.includes('Projects') || canAdd?.includes('Projects')" class="flex-center">
        <div class="btn-full-dropdown btn-w-100" *ngIf="canBulkUpload.includes('Projects')">
          <div class="position-absolute top-9 left-9 ip-top-11 align-center z-index-2">
            <span class="ic-tracker icon ic-xxs"></span>
            <span class="ml-8 ip-d-none">Tracker</span>
          </div>
          <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" [(ngModel)]="selectedTrackerOption"
            (click)="openProjectTracker()">
            <ng-option (click)="selectedTrackerOption = null" value="bulkUpload"
              *ngIf="canBulkUpload.includes('Projects')">
              <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
              {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }} Tracker</ng-option>
          </ng-select>
        </div>
        <div (click)="canAdd?.includes('Projects') ? openCenterModal() : ''" class="btn-left-dropdown ml-10"
          id="btnAddNewUnit">
          <span class="ic-add icon ic-xxs"></span>
          <span class="ml-8 ip-d-none">Add new unit</span>
        </div>
        <div class="btn-right-dropdown btn-w-30 black-100">
          <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" [(ngModel)]="selectedOption"
            (click)="openProjectBulkUpload()">
            <ng-option (click)="selectedOption = null" value="bulkUpload" *ngIf="canBulkUpload?.includes('Projects')">
              <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
              {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
          </ng-select>
        </div>
      </div>
    </div>
  </div>
</div>
<div *ngIf="rowData?.length" class="flex-end px-20 py-16 bg-white box-shadow-10 tb-mr-20">
  <u class="mr-20 text-black-200 text-large fw-600 cursor-pointer" (click)="manageProject()">Cancel</u>
  <button class="btn-coal" (click)="saveAndNext()">Go To Next</button>
</div>