<div class="mt-10" *ngIf="sortedUniqueNoOfBHK?.length > 0">
    <drag-scroll class="scrollbar scroll-hide tb-w-100-80 ph-w-100-40">
        <div class="d-flex white-box">
            <ng-container *ngFor="let bhk of sortedUniqueNoOfBHK; let i = index">
                <h6 class="activation" [ngClass]="{'active': selectedBhkIndex === i}" (click)="selectBhk(i)">
                    {{ bhk === 'Others' ? 'Others' :getBHKDisplayString(bhk) }}
                </h6>
            </ng-container>
        </div>
    </drag-scroll>
    <drag-scroll class="scrollbar scroll-hide tb-w-100-80 ph-w-100-40">
        <div class="d-flex border-bottom mt-20 unitArea">
            <ng-container *ngFor="let unitArea of projectUnit?.unitTypes; let i = index">
                <div class="activation mr-30" [ngClass]="{'actived': selectedUnitAreaIndex === i}"
                    *ngIf="!isUnitSoldOut(i) && ((sortedUniqueNoOfBHK[selectedBhkIndex] === 'Others' && (!unitArea.noOfBHK || unitArea.noOfBHK === 0)) || unitArea.noOfBHK === sortedUniqueNoOfBHK[selectedBhkIndex])"
                    (click)="selectUnitArea(i)">{{ unitArea?.name }}
                </div>
            </ng-container>
        </div>
    </drag-scroll>
    <div class="d-flex w-100 mt-20 ip-flex-col">
        <div class="w-30 ip-w-100">
            <div class="br-4 py-20 px-16 bg-white mr-30 ip-mr-0">
                <h4 class="fw-semi-bold text-black-200">Floor Details</h4>
                <div class="border-bottom">
                    <div class="my-16 align-center">
                        <div class="dot dot-lg bg-ash mr-12">
                            <div class="icon ic-coal ic-cube ic-xxs"></div>
                        </div>
                        <div>
                            <h6 class="text-light-gray">Unit type
                            </h6>
                            <h5 class="fw-700 mt-10">{{
                                projectUnit?.unitTypes[selectedUnitAreaIndex]?.unitType?.displayName ?
                                projectUnit?.unitTypes[selectedUnitAreaIndex]?.unitType?.displayName: '--' }} </h5>
                        </div>
                    </div>
                </div>
                <div class="border-bottom">
                    <div class="my-16 align-center">
                        <div class="dot dot-lg bg-ash mr-12">
                            <div class="icon ic-coal ic-diamond ic-xxs"></div>
                        </div>
                        <div>
                            <h6 class="text-light-gray">Built-up area
                            </h6>
                            <h5 class="fw-700 mt-10"> {{ projectUnit.unitTypes[selectedUnitAreaIndex]?.buildUpArea ?
                                projectUnit.unitTypes[selectedUnitAreaIndex]?.buildUpArea:'--'}}
                                {{ getAreaUnit(projectUnit.unitTypes[selectedUnitAreaIndex]?.buildUpAreaId,
                                areaSizeUnits)?.unit }}
                            </h5>
                        </div>
                    </div>
                </div>
                <div class="my-16 align-center">
                    <div class="dot dot-lg bg-ash mr-12">
                        <div class="icon ic-coal ic-cash ic-xxs"></div>
                    </div>
                    <div>
                        <h6 class="text-light-gray">Price
                        </h6>
                        <h5 class="fw-700 mt-10"> {{ projectUnit.unitTypes[selectedUnitAreaIndex]?.price ?
                            formatBudget(projectUnit.unitTypes[selectedUnitAreaIndex]?.price,
                            projectUnit.unitTypes[selectedUnitAreaIndex]?.currency) : '--'
                            }}
                        </h5>
                    </div>
                </div>
                <h5 [ngClass]="!globalSettingsDetails?.shouldEnableEnquiryForm ? 'd-none' : ''"
                    class="bg-coal br-4 fw-semi-bold py-8 text-white flex-center cursor-pointer"
                    (click)="showEnquire(projectUnit?.unitTypes[selectedUnitAreaIndex]?.id)">Enquire Now</h5>
            </div>
        </div>
        <div class="flex-col ip-w-100 ph-mt-4 w-70">
            <div class="flex-center activation gap-2">
                <h6 class="tab-item cursor-pointer"
                    [ngClass]="activeTab === 'images' ? 'border-bottom-black text-black-200 fw-600' : 'border-bottom text-light-gray fw-400'"
                    (click)="switchTab('images')">Images
                </h6>
                <h6 *ngIf="projectUnit?.unitTypes[selectedUnitAreaIndex]?.unitInfoGalleries?.length"
                    class="tab-item cursor-pointer"
                    [ngClass]="activeTab === 'documents' ? 'border-bottom-black text-black-200 fw-600' : 'border-bottom text-light-gray fw-400'"
                    (click)="switchTab('documents')">
                    Documents</h6>
            </div>
            <div class="mt-10 border">
                <div [ngSwitch]="activeTab">
                    <ng-container *ngSwitchCase="'images'">
                        <ng-container
                            *ngIf="projectUnit?.unitTypes[selectedUnitAreaIndex]?.images?.length else noimage">
                            <div class="position-relative p-20">
                                <div class="flex-center position-relative">
                                    <img [type]="'leadrat'"
                                        [appImage]="isIncludes3BucketUrl(projectUnit?.unitTypes[selectedUnitAreaIndex]?.images[selectedIndex]?.imageFilePath)"
                                        alt="" class="w-100 h-250 br-4 position-relative">
                                    <div class="bg-blur position-absolute flex-center w-100 br-4">
                                        <img [type]="'leadrat'"
                                            [appImage]="isIncludes3BucketUrl(projectUnit?.unitTypes[selectedUnitAreaIndex]?.images[selectedIndex]?.imageFilePath)"
                                            alt="" class="h-250 scrollbar">
                                    </div>
                                </div>
                                <div class="position-absolute left-20 top-150 cursor-pointer" (click)="previous()"
                                    *ngIf="projectUnit?.unitTypes[selectedUnitAreaIndex]?.images?.length > 1">
                                    <div class="dot dot-xl bg-black opacity-5"> </div><span
                                        class="icon ic-xxxs ic-arrow-left position-absolute top-10 left-10"></span>
                                </div>
                                <div class="position-absolute right-20 top-150 cursor-pointer" (click)="next()"
                                    *ngIf="projectUnit?.unitTypes[selectedUnitAreaIndex]?.images?.length > 1">
                                    <div class="dot dot-xl bg-black opacity-5"> </div><span
                                        class="icon ic-xxxs ic-arrow-right position-absolute top-10 left-10"></span>
                                </div>
                            </div>
                        </ng-container>
                    </ng-container>
                    <ng-container *ngSwitchCase="'documents'">
                        <div class="p-20"
                            *ngIf="projectUnit?.unitTypes[selectedUnitAreaIndex]?.unitInfoGalleries?.length">
                            <brochures [projectInfo]="projectUnit?.unitTypes[selectedUnitAreaIndex]" [isUnit]="true">
                            </brochures>
                        </div>
                    </ng-container>
                </div>
                <ng-template #noimage>
                    <div class="flex-center w-100">
                        <div class="m-20 w-300">
                            <img src="../../../../assets/images/house.svg" alt="" class="obj-fill w-100 br-4">
                        </div>
                    </div>
                </ng-template>
            </div>
        </div>
    </div>
</div>