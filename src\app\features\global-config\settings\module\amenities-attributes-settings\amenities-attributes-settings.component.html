<div class="p-20" *ngIf="canView">
    <div class="flex-between">
        <div class="align-center">
            <div routerLink='/global-config/property-project-settings'
                class="icon ic-triangle-down ic-coal cursor-pointer ic-xxxs rotate-90 mr-16">
            </div>
            <div class="border br-20 bg-white align-center user px-8 py-4 ">
                <div class="px-16 text-light-gray ip-px-8 align-center br-20 cursor-pointer h-28 fw-semi-bold"
                    [ngClass]="{'text-white fw-700 bg-black-100' : selectedSection == 'Amenities'}"
                    (click)="onSectionChange('Amenities') ;hideModal()">
                    <span>Amenities</span>
                </div>
                <div class="px-16 text-light-gray ip-px-8 align-center br-20 cursor-pointer h-28 fw-semi-bold"
                    [ngClass]="{'text-white fw-700 bg-black-100' : selectedSection == 'Attribute'}"
                    (click)="onSectionChange('Attribute') ;hideModal()">
                    <span>Attributes</span>
                </div>
            </div>
        </div>
        <div class="br-4 bg-coal cursor-pointer p-10" [ngClass]="{'pe-none' : !canUpdate}"
            (click)="canUpdate ? openAddModal(addModal): ''"><span class="ic-close rotate-45 icon ic-xxs mx-8">
            </span><span class="text-white">Add <span class="ph-d-none">
                    New {{selectedSection ===
                    "Amenities" ? 'Amenity' : 'Attribute'}}</span></span>
        </div>
    </div>
    <div class="bg-white">
        <div class="w-100 border-gray mt-20">
            <form autocomplete="off" class="align-center w-100 py-10 px-12 no-validation">
                <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
                <input placeholder="type to search" name="search" class="border-0 outline-0 w-100"
                    (input)="search($event)" [(ngModel)]="searchTerm" autocomplete="off" id="inpSearchQrForm">
            </form>
        </div>
        <div *ngIf="selectedSection == 'Amenities'">
            <ng-container *ngIf="noAmenitiesFound && !amenitiesLoading; else amenitiesList">
                <div class="flex-center-col h-100-200">
                    <img src="assets/images/layered-cards.svg" alt="No amenities found">
                    <div class="header-3 fw-600 text-center">No Amenities Found</div>
                </div>
            </ng-container>
            <ng-template #amenitiesList>
                <ng-container *ngIf="!amenitiesLoading; else skeleton">
                    <div class="px-20 pb-10 h-100-189 flex-wrap scrollbar">
                        <ng-container *ngFor="let category of filteredAmenities">
                            <ng-container *ngIf="category?.amenities?.length">
                                <h4 class="fw-semi-bold pt-20 mb-4 text-decoration-underline">
                                    {{category?.categoryName}}
                                </h4>
                                <div class="d-flex flex-wrap flex-grow-1">
                                    <ng-container *ngFor="let amenity of category?.amenities">
                                        <div class="w-20 tb-w-25 ip-w-50 ph-w-100 position-relative tag-card">
                                            <div [ngClass]="amenity?.isActive ? 'border-bottom-black' : 'border'"
                                                class="box-shadow-40 d-flex bg-white h-80 mt-10 mr-10 br-4 p-8">
                                                <!-- <div class="d-flex shadow-hover-sm border-white-hover"> -->
                                                <label [ngClass]="{'pe-none' : !canUpdate}"
                                                    class="checkbox-container mb-4">
                                                    <input type="checkbox" [checked]="amenity.isActive"
                                                        [ngClass]="{'pe-none' : !canUpdate}"
                                                        (change)="canUpdate ? onAmenityChecked($event, amenity): ''">
                                                    <span class="checkmark"></span>
                                                </label>
                                                <div class="flex-center-col w-100 mr-6">
                                                    <img *ngIf="amenity?.imageURL && amenity?.inActiveImageURL; else dummy"
                                                        [src]="amenity?.isActive ? amenity?.imageURL : amenity?.inActiveImageURL"
                                                        class="obj-fill" width="23px" height="15px">
                                                    <h5 [title]="amenity?.amenityDisplayName"
                                                        [ngClass]="amenity?.isActive ? 'text-black-100 fw-semi-bold': ' text-light-gray fw-400'"
                                                        class="mt-4 text-truncate-1 break-all text-center">
                                                        {{amenity?.amenityDisplayName}}
                                                    </h5>
                                                    <ng-template #dummy>
                                                        <span class="icon ic-black ic-sm">
                                                            {{getFirstCharacter(amenity?.amenityDisplayName)}}</span>
                                                    </ng-template>
                                                </div>
                                            </div>
                                            <div
                                                class="d-flex position-absolute bg-white top-16 right-12 h-60 w-90 flex-center action-option opacity-0">
                                                <div title="Edit" class="bg-blue-850 icon-badge ml-0"
                                                    [ngClass]="{'pe-none' : !canUpdate}"
                                                    (click)="canUpdate ? openEditModal(amenity,addModal): ''"> <span
                                                        class="icon ic-pen-solid ic-white m-auto ic-xxxs"></span></div>
                                                <div class="border m-6 h-10"></div>
                                                <div title="Delete" class="bg-red-350 icon-badge ml-0"
                                                    [ngClass]="{'pe-none' : !canUpdate}"
                                                    (click)="canUpdate ? delete(amenity): ''">
                                                    <span class="icon ic-trash ic-white m-auto ic-xxxs"></span>
                                                </div>
                                            </div>
                                            <!-- </div> -->
                                        </div>
                                    </ng-container>
                                </div>
                            </ng-container>
                        </ng-container>
                    </div>
                </ng-container>
            </ng-template>
        </div>
        <div *ngIf="selectedSection == 'Attribute'">
            <ng-container *ngIf="!attributesLoading; else skeleton">
                <div class="px-20 pb-10 h-100-189 flex-wrap scrollbar">
                    <div class="d-flex flex-wrap flex-grow-1">
                        <ng-container *ngFor="let attribute of filteredAttributes">
                            <div class="w-20 tb-w-25 ip-w-33 ph-w-100 position-relative tag-card">
                                <div [ngClass]="attribute?.isActive ? 'border-bottom-black' : 'border'"
                                    class="box-shadow-40 d-flex bg-white h-80 mt-10 mr-10 br-4 p-8">
                                    <!-- <div class="d-flex shadow-hover-sm border-white-hover"> -->
                                    <label [ngClass]="{'pe-none' : !canUpdate}" class="checkbox-container mb-4">
                                        <input type="checkbox" [checked]="attribute.isActive"
                                            [ngClass]="{'pe-none' : !canUpdate}"
                                            (change)="canUpdate ? onAttributeChecked($event, attribute): ''">
                                        <span class="checkmark"></span>
                                    </label>
                                    <div class="flex-center-col w-100 mr-4">
                                        <img *ngIf="attribute?.activeImageURL && attribute?.inActiveImageURL; else dummy"
                                            [src]="attribute?.isActive ? attribute?.activeImageURL: attribute?.inActiveImageURL"
                                            class="obj-fill" width="23px" height="15px">
                                        <div [title]="attribute?.attributeDisplayName" [ngClass]="attribute?.isActive ? 'text-black-100 fw-semi-bold': ' text-light-gray fw-400'"
                                            class="mt-4 text-truncate-1 break-all text-center">
                                            {{attribute?.attributeDisplayName}}
                                        </div>
                                        <ng-template #dummy>
                                            <span class="icon ic-black ic-sm">
                                                {{getFirstCharacter(attribute?.attributeDisplayName)}}</span>
                                        </ng-template>
                                    </div>
                                </div>
                                <div
                                    class="d-flex position-absolute bg-white top-16 right-12 h-60 w-90 flex-center action-option opacity-0">
                                    <div title="Edit" class="bg-blue-850 icon-badge ml-0"
                                        [ngClass]="{'pe-none' : !canUpdate}"
                                        (click)="canUpdate ? openEditModal(attribute,addModal): ''"> <span
                                            class="icon ic-pen-solid ic-white m-auto ic-xxxs"></span></div>
                                    <div class="border m-6 h-10"></div>
                                    <div title="Delete" class="bg-red-350 icon-badge ml-0"
                                        [ngClass]="{'pe-none' : !canUpdate}"
                                        (click)="canUpdate ? delete(attribute): ''">
                                        <span class="icon ic-trash ic-white m-auto ic-xxxs"></span>
                                    </div>
                                </div>
                                <!-- </div> -->
                            </div>
                        </ng-container>
                    </div>
                    <ng-container *ngIf="!filteredAttributes.length">
                        <div class="flex-center-col h-100-200">
                            <img src="assets/images/no-lead-found.svg" alt="No attributes found">
                            <div class="header-3 fw-600 text-center">No Attributes Found</div>
                        </div>
                    </ng-container>
                </div>
            </ng-container>
        </div>
    </div>
</div>
<ng-template #addModal>
    <div class="max-w-350 min-w-350">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h5 *ngIf="selectedSection === 'Amenities'">{{isEditModal ? 'Update Amenity' :'Add Amenity'}}</h5>
            <h5 *ngIf="selectedSection === 'Attribute'">{{isEditModal ? 'Update Attribute' :'Add Attribute'}}</h5>

            <div class="icon ic-close ic-white ic-sm cursor-pointer" (click)="hideModal()"></div>
        </div>
        <div class="p-10 bg-light-slate">
            <form [formGroup]="selectedSection === 'Amenities' ? amenitiesForm : attributesForm">
                <div class="bg-white px-10 py-16 mb-10 ng-select-sm">
                    <label for="iconName" class="label-req mt-0">
                        Icon Name</label>
                    <form-errors-wrapper label="Icon Name"
                        [control]="selectedSection === 'Amenities' ? amenitiesForm.controls['name'] : attributesForm.controls['name']">
                        <input type="text" formControlName="name" id="name" data-automate-id="name" autocomplete="off"
                            placeholder="ex. Building" class="h-36" maxlength="50" />
                        <p *ngIf="selectedSection === 'Amenities' || selectedSection === 'Attribute'"
                            class="position-absolute right-4 bottom-0">
                            {{
                            (selectedSection === 'Amenities' ? amenitiesForm.controls['name']?.value?.length
                            : attributesForm.controls['name']?.value?.length)
                            }}/50
                        </p>
                        <div class="error-message"
                            *ngIf="(selectedSection === 'Amenities' && doesAmenityNameExist && amenitiesForm.controls.name.status === 'VALID') ||
                                   (selectedSection === 'Attribute' && doesAttrNameExist && attributesForm.controls.name.status === 'VALID')">
                            {{ selectedSection === 'Amenities' ? 'Amenity' : 'Attribute' }} already exists
                        </div>
                    </form-errors-wrapper>
                    <ng-container *ngIf="selectedSection === 'Amenities'">
                        <label for="category" class="label-req mt-2">
                            Category</label>
                        <form-errors-wrapper label="Category" [control]="amenitiesForm.controls['category']">
                            <ng-select formControlName="category"
                                [ngClass]="{'pe-none blinking': amenitiesCategoryLoading}" placeholder="Basic"
                                [items]="amenitiesCategory" [virtualScroll]="true">
                            </ng-select>
                        </form-errors-wrapper>
                    </ng-container>
                    <label for="propertyType" class="label-req mt-2">
                        Property Type</label>
                    <form-errors-wrapper label="Property type"
                        [control]="selectedSection === 'Amenities' ? amenitiesForm.controls['propertyType'] : attributesForm.controls['propertyType']">
                        <ng-select placeholder="Residential" name="propertyType" [items]="propertyTypeList"
                            class="ng-select-xs" bindValue="displayName" bindLabel="displayName" ResizableDropdown
                            [multiple]="true" appSelectAll [searchable]="true" [closeOnSelect]="false"
                            formControlName="propertyType">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span>{{item?.displayName}}</div>
                            </ng-template>
                        </ng-select>
                    </form-errors-wrapper>
                </div>
            </form>
            <div class="bg-white px-10 py-16">
                <div class="align-center flex-between">
                    <div for="icons" class="label mt-0">
                        Select Icons</div>
                    <div class="flex-between no-validation px-10 py-4 border-gray br-16 bg-white">
                        <input type="text" placeholder="type to search" class="border-0 outline-0 w-100"
                            [(ngModel)]="iconSearchTerm" (input)="onSearchIcons($event?.target?.value)" />
                        <span class="search icon ic-search-solid ic-xxxs ic-slate-90"></span>
                    </div>
                </div>
                <div [ngClass]="selectedSection === 'Amenities' ? 'h-100-433': 'h-100-360'">
                    <div class="scrollbar scrollbar-hide mt-10 w-100 d-flex flex-wrap" [ngClass]="{
                       'max-h-100-433': selectedSection === 'Amenities',
                       'max-h-100-360': selectedSection !== 'amenities',
                       'pe-none blinking': isIconsListLoading
                        }">
                        <span *ngFor="let icon of filteredIconsList;" class="cursor-pointer w-16pr"
                            (click)="selectedIcon = icon">
                            <div class="w-40 h-40 flex-center obj-fill"
                                [ngClass]="icon?.id===selectedIcon?.id ? ' bg-light-slate br-4 ': ''">
                                <img [src]="icon?.id===selectedIcon?.id ? icon?.activeImagePath : icon?.inActiveImagePath"
                                    [alt]="icon?.name" class="w-20px h-20px" [title]="icon?.name">
                            </div>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="w-100 modal-footer bg-white justify-end position-sticky z-index-1021">
            <h6 class="text-black-10 fw-semi-bold text-decoration-underline cursor-pointer" (click)="hideModal()">{{
                'BUTTONS.cancel' | translate
                }}</h6>
            <button class="btn-coal ml-12" (click)="canUpdate ? (isEditModal ? update() : create()): ''">{{
                'BUTTONS.save' | translate
                }} {{selectedSection === 'Amenities' ? 'Amenity': 'Attribute'}}</button>
        </div>
    </div>
</ng-template>
<ng-template #skeleton>
    <div class="px-20 pb-10 h-100-189 flex-wrap scrollbar pe-none blinking">
        <ng-container *ngFor="let heading of [1, 2, 3]">
            <h4 class="fw-semi-bold pt-20 mb-4">
                <span
                    class="bg-grey">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            </h4>
            <div class="d-flex flex-wrap flex-grow-1">
                <ng-container *ngFor="let card of [1, 2, 3, 4, 5]">
                    <div class="box-shadow-40 w-20 tb-w-25 ip-w-50 ph-w-100 position-relative">
                        <div class="d-flex bg-white h-80 mt-10 mr-10 br-4 p-8">
                            <span
                                class="bg-grey w-100">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                        </div>
                        <div
                            class="d-flex position-absolute bg-white top-16 right-12 h-60 w-90 flex-center action-option opacity-0">
                            <span
                                class="bg-grey">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                        </div>
                    </div>
                </ng-container>
            </div>
        </ng-container>
    </div>
</ng-template>