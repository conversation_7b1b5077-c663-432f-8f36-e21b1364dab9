import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { skipWhile, takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { FetchAllAmenities, FetchAllAttributes } from 'src/app/reducers/amenities-attributes/amenities-attributes.action';
import { getAllAmenities, getAllAttributes, getAmenitiesLoading, getAttributesLoading } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';

@Component({
  selector: 'custom-amenities-attributes',
  templateUrl: './custom-amenities-attributes.component.html',
})
export class CustomAmenitiesAttributesComponent implements OnInit, OnDestroy {
  @Input() propertyTypeInput: number = 0;
  @Input() userSelectedAmenities: Array<string> = [];
  @Input() userSelectedAttributes: Array<string> = [];
  @Output() selectedAmenities = new EventEmitter<any>();
  @Output() selectedAdditionalAttr = new EventEmitter<any>();

  private stopper: EventEmitter<void> = new EventEmitter<void>();
  selectedSection: string = 'Amenities';
  allAmenities: any[];
  filteredAmenities: any[];
  allAttributes: any[];
  filteredAttributes: any;
  amenitiesLoading: boolean;
  amenitiesCategory: any[];
  attributesLoading: boolean;
  allSelected: boolean = false;
  searchTerm: string = '';
  amenitiesSearchTerm: string;
  attributesSearchTerm: string;

  get noAmenitiesFound(): boolean {
    return this.filteredAmenities?.every(category => category?.amenities?.length === 0);
  }
  constructor(
    private store: Store<AppState>,
    private modalRef: BsModalRef,

  ) { }

  ngOnInit(): void {
    this.store.dispatch(new FetchAllAmenities());
    this.store.dispatch(new FetchAllAttributes());

    this.store
      .select(getAmenitiesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.amenitiesLoading = isLoading;
      })

    this.store
      .select(getAllAmenities)
      .pipe(skipWhile(() => this.amenitiesLoading), takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.amenitiesCategory = [...data].map((category) => category?.categoryName);
        const activeAmenities = data
          .map((category: any) => ({
            ...category,
            amenities: category.amenities.map((amenity: any) => ({
              ...amenity,
              selected: this.userSelectedAmenities.includes(amenity?.id)
            })).filter((amenity: any) =>
              amenity.isActive && amenity.propertyType.includes(this.propertyTypeInput)
            )
              .sort((a: any, b: any) =>
                a.amenityDisplayName.localeCompare(b.amenityDisplayName)
              )
          }))
          .sort((a: any, b: any) => a.categoryName.localeCompare(b.categoryName))
          .filter((category: any) => category.amenities.length > 0);
        this.allAmenities = activeAmenities;
        this.filteredAmenities = activeAmenities;
        this.emitSelection();
      });

    this.store
      .select(getAllAttributes)
      .pipe(skipWhile(() => this.attributesLoading), takeUntil(this.stopper))
      .subscribe((data: any) => {
        const activeAttribute = data
          .map((attr: any) => ({
            ...attr,
            selected: this.userSelectedAttributes.includes(attr?.id)
          })).filter((attr: any) =>
            attr.isActive && attr?.attributeType === 'Additional' && attr.basePropertyType.includes(this.propertyTypeInput)
          )
          .sort((a: any, b: any) =>
            a.attributeDisplayName.localeCompare(b.attributeDisplayName)
          )

        this.allAttributes = activeAttribute
        this.filteredAttributes = activeAttribute
      })

    this.store
      .select(getAttributesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.attributesLoading = isLoading;
      })
  }

  onSelectAllChange(category: any, event: any) {
    const isChecked = event.target.checked;

    category.amenities = category.amenities.map((amenity: any) => ({
      ...amenity,
      selected: isChecked
    }));

    this.filteredAmenities = this.filteredAmenities.map((cat: any) =>
      cat.categoryName === category.categoryName ? category : cat
    );

    this.emitSelection();
  }

  isAllSelected(category: any): boolean {
    return category.amenities.every((amenity: any) => amenity.selected);
  }

  onAmenitySelectionChange(amenity: any, event: any) {
    const updatedAmenity = { ...amenity, selected: event.target.checked };
    this.updateFilteredAmenities(updatedAmenity);

    this.emitSelection();
  }

  emitSelection() {
    const selectedAmenityIds = this.filteredAmenities
      .flatMap((category: any) => category.amenities)
      .filter((amenity: any) => amenity.selected)
      .map((amenity: any) => amenity.id);

    this.selectedAmenities.emit(selectedAmenityIds);
  }

  onAttributeSelectionChange(
    featureId: string,
    isChecked: boolean,
    displayName: string
  ) {
    const findAttribute = (attrDisplayName: string) =>
      this.filteredAttributes.find(
        (attr: any) => attr.attributeDisplayName === attrDisplayName
      );

    const removeAttribute = (attrId: string) => {
      this.userSelectedAttributes = this.userSelectedAttributes.filter(
        (attr) => attr !== attrId
      );
    };

    const addAttribute = (attrId: string) => {
      this.userSelectedAttributes.push(attrId);
    };

    switch (displayName) {
      case 'Pet Friendly':
        const petsNotAllowedAttr = findAttribute('Pets Not Allowed');
        if (petsNotAllowedAttr) {
          if (isChecked) {
            removeAttribute(petsNotAllowedAttr.id);
          }
        }
        isChecked ? addAttribute(featureId) : removeAttribute(featureId);
        break;

      case 'Pets Not Allowed':
        const petFriendlyAttr = findAttribute('Pet Friendly');
        if (petFriendlyAttr) {
          if (isChecked) {
            removeAttribute(petFriendlyAttr.id);
          }
        }
        isChecked ? addAttribute(featureId) : removeAttribute(featureId);
        break;

      case 'Family Only':
        const bachelorFriendlyAttr = findAttribute('Bachelor And Couple Friendly');
        if (bachelorFriendlyAttr) {
          if (isChecked) {
            removeAttribute(bachelorFriendlyAttr.id);
          }
        }
        isChecked ? addAttribute(featureId) : removeAttribute(featureId);
        break;

      case 'Bachelor And Couple Friendly':
        const familyOnlyAttr = findAttribute('Family Only');
        if (familyOnlyAttr) {
          if (isChecked) {
            removeAttribute(familyOnlyAttr.id);
          }
        }
        isChecked ? addAttribute(featureId) : removeAttribute(featureId);
        break;

      default:
        isChecked ? addAttribute(featureId) : removeAttribute(featureId);
        break;
    }
    this.emitAttributeSelection();
  }


  allAttributeSelectionCheck(): boolean {
    return this.filteredAttributes.length > 0 &&
      this.filteredAttributes.every((attr: any) => this.userSelectedAttributes.includes(attr.id));
  }

  changeAllAttributeSelection(event: any): void {
    const isChecked = event.target.checked;

    if (isChecked) {
      this.userSelectedAttributes = this.filteredAttributes.map((attr: any) => attr.id);
    } else {
      this.userSelectedAttributes = [];
    }
    this.emitAttributeSelection();
  }

  updateFilteredAmenities(updatedAmenity: any) {
    this.filteredAmenities = this.filteredAmenities.map((category: any) => ({
      ...category,
      amenities: category.amenities.map((amenity: any) =>
        amenity.id === updatedAmenity.id ? updatedAmenity : amenity
      )
    }));
  }

  emitAttributeSelection() {
    this.selectedAdditionalAttr?.emit(this.userSelectedAttributes);
  }

  onSelectAll(event: any) {
    this.allSelected = event.target.checked;
    this.filteredAttributes = this.filteredAttributes.map((attribute: any) => ({
      ...attribute,
      isSelected: this.allSelected
    }));
  }

  onAttributeChecked(event: any, attribute: any) {
    attribute.isSelected = event.target.checked;
    this.allSelected = this.filteredAttributes.every((attr: any) => attr.isSelected);
  }

  search($event: any) {
    const searchTerm = $event.target.value.trim();
    if (this.selectedSection === 'Amenities') {
      this.amenitiesSearchTerm = searchTerm;
    } else if (this.selectedSection === 'Attribute') {
      this.attributesSearchTerm = searchTerm;
    }

    this.applySearch();
  }

  getFirstCharacter(name: string) {
    return name?.charAt(0).toUpperCase();
  }

  applySearch() {
    const searchTerm =
      this.selectedSection === 'Amenities'
        ? this.amenitiesSearchTerm || ''
        : this.attributesSearchTerm || '';

    const normalizedSearchTerm = searchTerm.toLowerCase().trim();
    if (!normalizedSearchTerm) {
      if (this.selectedSection === 'Amenities') {
        this.filteredAmenities = [...this.allAmenities];
      } else if (this.selectedSection === 'Attribute') {
        this.filteredAttributes = [...this.allAttributes];
      }
      return;
    }
    const searchWords = normalizedSearchTerm.split(/\s+/).filter(word => word.length > 0);

    if (this.selectedSection === 'Amenities') {
      this.filteredAmenities = this.allAmenities?.map((category: any) => {
        const filteredData = category?.amenities?.filter((amenity: any) => {
          const amenityName = (amenity?.amenityDisplayName || amenity?.amenityName || '').toLowerCase();
          return searchWords.every(word => amenityName.includes(word));
        });
        return { ...category, amenities: [...filteredData] };
      });
    } else if (this.selectedSection === 'Attribute') {
      this.filteredAttributes = this.allAttributes?.filter((attribute: any) => {
        const attributeName = (attribute?.attributeDisplayName || attribute?.attributeName || '').toLowerCase();
        return searchWords.every(word => attributeName.includes(word));
      });
    }
  }

  onSectionChange(newSection: string) {
    this.selectedSection = newSection;

    if (this.selectedSection === 'Amenities') {
      this.searchTerm = this.amenitiesSearchTerm;
    } else if (this.selectedSection === 'Attribute') {
      this.searchTerm = this.attributesSearchTerm;
    }

    this.applySearch();
  }

  hideModal() {
    this.modalRef.hide();
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
