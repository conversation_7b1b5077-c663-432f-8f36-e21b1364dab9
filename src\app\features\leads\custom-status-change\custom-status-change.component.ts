import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';

import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { BehaviorSubject, combineLatest, Subscription } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  skipWhile,
  take,
  takeUntil,
} from 'rxjs/operators';

import {
  EMPTY_GUID,
  LEAD_STATUS_REASONS,
  UPDATE_STATUS,
  UPDATE_STATUS_PAST_TENSE,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import { LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  formatBudget,
  getTimeZoneDate,
  onlyNumbers,
  onPickerOpened,
  patchFormControlValue,
  patchTimeZoneWithTime,
  setTimeZoneDateWithTime,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { LeadAppointmentComponent } from 'src/app/features/leads/lead-appointment/lead-appointment.component';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchInvoiceById } from 'src/app/reducers/invoice/invoice.actions';
import { getBookingData } from 'src/app/reducers/invoice/invoice.reducer';
import {
  FetchBulkOperation,
  UpdateLeadStatus,
  UpdateMultipleLead,
} from 'src/app/reducers/lead/lead.actions';
import {
  getLeadStatusIsLoading,
  getMultipleLeadStatusIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { LeadPreviewChanged } from 'src/app/reducers/loader/loader.actions';
import {
  getPermissions,
  getPermissionsIsLoading,
} from 'src/app/reducers/permissions/permissions.reducers';
import { FetchProjectById } from 'src/app/reducers/project/project.action';
import {
  getIsProjectByIdLoading,
  getProjectsIDWithName,
  getProjectsIDWithNameIsLoading,
  getSelectedProjectById,
} from 'src/app/reducers/project/project.reducer';
import {
  FetchPropertyById,
  FetchPropertyWithIdNameList,
} from 'src/app/reducers/property/property.actions';
import {
  getPropertyListDetails,
  getPropertyListDetailsIsLoading,
  getPropertyWithIdNameList,
} from 'src/app/reducers/property/property.reducer';
import { FetchLocationsWithGoogle } from 'src/app/reducers/site/site.actions';
import { getLocationsWithGoogleApi } from 'src/app/reducers/site/site.reducer';
import {
  CustomField,
  CustomStatus,
  getCustomStatusList,
  getCustomStatusListIsLoading,
} from 'src/app/reducers/status/status.reducer';
import { FetchReportingManagerDetails } from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getAdminsAndReporteesIsLoading,
  getManagerDetails,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { BulkOperationTrackerComponent } from 'src/app/shared/components/bulk-operation-tracker/bulk-operation-tracker.component';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';
import { BookingFormComponent } from '../booking-form/booking-form.component';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
@Component({
  selector: 'custom-status-change',
  templateUrl: './custom-status-change.component.html',
})
export class CustomStatusChangeComponent
  implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');
  @Input() leadInfo: any;
  @Input() canShowStatusPopupInPreview: boolean = false;
  @Input() canUpdateStatus: boolean = false;
  @Input() isLeadPreview: boolean = false;
  @Input() isLastLead: boolean = false;
  @Input() closeLeadPreviewModal: any;
  @Input() whatsAppComp: boolean = false;
  updateForm: FormGroup;
  params: any;

  dispSubStatus: any = LEAD_STATUS_REASONS;
  leadStatus: any = UPDATE_STATUS;
  currentLeadStatus: any = UPDATE_STATUS_PAST_TENSE;

  updateLeadStates: any[] = [];
  callBackReason: any[] = [];
  notInterestedReason: any[] = [];
  placesList: any[] = [];
  minDate: Date;
  maxDate: Date;
  allLeadStatus: any;
  leadAuditHistory: any;
  selectedStatus: CustomStatus;
  reasons: Array<string>;
  selectedReason: any;
  portfolioLink: string;
  navigateLink: string = '';
  whatsAppShare: string = '';
  defaultCurrency: string = '';
  soldPriceInWords: string = '';
  budgetInWords: string = '';
  leadSource: string;
  hideStatus: boolean = false;
  isVisitDone: boolean = false;
  didCustDenyOtp: boolean = false;
  isBulkUpdate: boolean = false;
  disableStatus: boolean;
  showLocationSearch: boolean = false;
  projectList: Array<any> = [];
  propertyList: Array<string> = [];
  currencyList: any[] = [];
  leadStatusIsLoading: boolean = false;
  multipleLeadsIsLoading: boolean = false;
  propertyListIsLoading: boolean = true;
  projectListIsLoading: boolean = true;
  isNotesMandatory: boolean;
  isProjectMandatory: boolean;
  elementHeight: any;
  assignedToUserId: string;
  canAssignLead: boolean = false;
  primaryUserList: Array<Object> = [];
  secondaryUserList: Array<Object> = [];
  primaryAgentList: Array<Object> = [];
  secondaryAgentList: Array<Object> = [];
  isUserListLoading: boolean = true;
  deactiveUsers: Array<Object> = [];
  isLeadStatusLoading: boolean = false;
  canUpdateBookedLead: boolean = false;
  isDualOwnershipEnabled: boolean;
  isReadMore: boolean = false;
  customStatusList: CustomStatus[];
  defaultControls: any;
  isCustomStatusListLoading: boolean = true;
  manualLocationsList: any[] = [];
  customStatusListFiltered: any = [];
  @ViewChild('statusForm') statusForm: ElementRef;
  @ViewChild('noUnitFound') noUnitFound: TemplateRef<any>;
  @ViewChild('trackerInfoModal') trackerInfoModal: any;
  EMPTY_GUID = EMPTY_GUID;
  moment = moment;
  patchFormControlValue = patchFormControlValue;
  formatBudget = formatBudget;
  onlyNumbers = onlyNumbers;
  getTimeZoneDate = getTimeZoneDate;
  isShowUnitInfoField: boolean = false;
  selectedProject: any = {};
  isSelectedPropertyOrProject: string;
  unitInfo: any;
  isUnitInfoDataLoading: boolean;
  miniagreementValueInWords: string;
  isShowBookingFormBtn: boolean = false;
  currentPath: string;
  getMiniBookingFormDatas: Subscription;
  private isProjectSubscription: Subscription;
  userDetails: any;
  userBasicDetails: any;
  isSaveAndNext: boolean = false;
  bookedAndInvoiceStatus: string[] = [];
  onPickerOpened = onPickerOpened
  currentDate: Date = new Date();
  trackerModalOpen: boolean = false;
  isPastDateSelectionEnabled: boolean = true;

  get canShowStatus(): boolean {
    return !this.hideStatus && !this.canShowStatusPopupInPreview;
  }

  get isUnassignedLead(): boolean {
    return this.leadInfo?.assignTo == EMPTY_GUID;
  }

  isBookedLead(): boolean {
    return (
      !this.canUpdateBookedLead &&
      this.leadInfo?.some(
        (lead: any) =>
          lead?.status?.shouldUseForBooking
      )
    );
  }

  isSelectedOnlySomeBooked(): boolean {
    if (!this.leadInfo?.length) {
      return false;
    }

    const allBookedOrInvoiced = this.leadInfo.every(
      (lead: any) =>
        lead.status?.shouldUseForBooking ||
        lead.status.shouldUseForInvoice
    );

    const someBookedOrInvoiced = this.leadInfo.some(
      (lead: any) =>
        lead.status?.shouldUseForBooking ||
        lead.status.shouldUseForInvoice
    );

    return (
      allBookedOrInvoiced || (someBookedOrInvoiced && !allBookedOrInvoiced)
    );
  }

  isSelectedAllBookedLead(): boolean {
    const leadInfo = this.leadInfo.length ? this.leadInfo : [];
    return leadInfo?.every((lead: any) => lead?.status?.shouldUseForBooking);
  }

  getMaxDate() {
    return new Date();
  }

  constructor(
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private formBuilder: FormBuilder,
    private _store: Store<AppState>,
    private cdr: ChangeDetectorRef,
    private _leadPreviewComponent: LeadPreviewComponent,
    private _notificationsService: NotificationsService,
    private shareDataService: ShareDataService,
    private router: Router
  ) {
    this.maxDate = new Date();
    // this.minDate = new Date();
    // this.minDate.setDate(this.minDate.getDate());
    this._store.dispatch(new FetchPropertyWithIdNameList());
    this._store.dispatch(new FetchLocationsWithGoogle());
  }
  async ngOnInit() {
    this.isProjectSubscription = this.shareDataService.isProjectUnit$.subscribe(() => {
      this.modalRef = this.modalService.show(this.noUnitFound, {
        class: 'modal-500 top-modal ip-modal-unset',
        ignoreBackdropClick: true,
      });
    });
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;

    this._store.select(getCustomStatusList)
      .pipe(takeUntil(this.stopper))
      .subscribe((customStatus: any) => {
        customStatus?.forEach((status: any) => {
          if (status?.shouldUseForBooking || status?.shouldUseForInvoice) {
            if (!this.bookedAndInvoiceStatus.includes(status?.displayName)) {
              this.bookedAndInvoiceStatus.push(status?.displayName);
            }
          }
        });
        this.bookedAndInvoiceStatus.sort((a: string, b: string) => a.localeCompare(b));
        this.customStatusList = customStatus;
        let shouldUseForBooking = this.isBulkUpdate
          ? this.leadInfo.every((lead: any) => lead?.status?.shouldUseForBooking)
          : this.leadInfo?.status?.shouldUseForBooking;

        if (this.isSelectedAllBookedOrInvoicedLead()) {
          this.customStatusListFiltered = this.customStatusList?.filter((status: any) =>
            status.shouldUseForBookingCancel
          );
        } else if (shouldUseForBooking) {
          this.customStatusListFiltered = this.customStatusList?.filter((status: any) =>
            (status?.shouldUseForBookingCancel && !status?.shouldBeHidden) || this.leadInfo?.status.id === status.id
            // status?.shouldUseForBooking
          );
        } else if (this.currentPath === '/invoice') {
          this.customStatusListFiltered = this.customStatusList?.filter((status: any) =>
            status?.shouldUseForBookingCancel || status.shouldUseForInvoice
          );
        } else {
          this.customStatusListFiltered = this.customStatusList?.filter((status: any) =>
            !status?.shouldUseForBookingCancel && !status?.shouldUseForInvoice && !status?.shouldBeHidden
          );
        }
      });


    this._store
      .select(getCustomStatusListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isCustomStatusListLoading = isLoading;
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Leads.UpdateBookedLead')) {
          this.canUpdateBookedLead = true;
        }
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isDualOwnershipEnabled = data?.isDualOwnershipEnabled;
        this.isPastDateSelectionEnabled = data?.isPastDateSelectionEnabled
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)
        this.minDate = this.currentDate
      });

    this.getMiniBookingFormDatas = this.shareDataService.getMiniBookingformData()
      .subscribe((data: any) => {
        this.makeRequired(data?.status);
        this.patchMiniBookDataIfBooked(data?.id);
        if (data?.status?.shouldUseForBooking) {
          this.customStatusListFiltered = this.customStatusList?.filter((status: any) =>
            status?.shouldUseForBookingCancel || status?.shouldUseForBooking
          );
          const status: any = this.customStatusListFiltered?.filter((status: any) =>
            status?.shouldUseForBooking
          )?.[0];
          this.makeRequired(status);
          this.showReasons(status, data?.id);
        }

        if (data?.status?.shouldUseForInvoice) {
          this.customStatusListFiltered = this.customStatusList?.filter((status: any) =>
            status?.shouldUseForBookingCancel || status.shouldUseForInvoice
          );
          const status: any = this.customStatusListFiltered?.filter((status: any) =>
            status?.shouldUseForInvoice
          )?.[0];
          this.makeRequired(status);
          this.showReasons(status, data?.id);
        }
      });

    if (
      location.href.includes('/invoice') ||
      this.leadInfo?.status?.shouldUseForBooking
    ) {
      const status: any = this.customStatusListFiltered?.filter((status: any) =>
        this.leadInfo?.status?.shouldUseForBooking
          ? status?.shouldUseForBooking
          : status?.shouldUseForInvoice
      )?.[0];
      this.makeRequired(status);
      this.showReasons(status, this.leadInfo?.id);
      return;
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes?.leadInfo) {
      if (this.leadInfo?.status?.shouldUseForBooking)
        this.customStatusListFiltered = this.customStatusList?.filter(
          (status: any) => {
            return status?.shouldUseForBookingCancel;
          }
        );
      else if (this.leadInfo?.status?.shouldUseForBookingCancel) {
        this.customStatusListFiltered = this.customStatusList?.filter(
          (status: any) => {
            return !status?.shouldUseForBookingCancel;
          }
        );
      }
      this.initialize();
    }
  }

  initializeUpdateForm(): void {
    let isPrimaryUserActive = this.primaryAgentList?.filter(
      (user: any) => user?.id === this.leadInfo?.assignTo
    )?.length;
    let isSecondaryUserActive = this.secondaryAgentList?.filter(
      (user: any) => user?.id === this.leadInfo?.secondaryUserId
    )?.length;
    this.defaultControls = {
      bookedUnderName: null,
      ScheduledDate: null,
      Projects: null,
      bookedDate: null,
      agreementValue: null,
      projectProperty: null,
      chosenProperty: null,
      chosenProject: null,
      chosenUnit: null,
      currency: null,
      notes: null,
      leadStatus: null,
      reason: '',
      assignedToUserId: [
        this.leadInfo?.assignTo !== EMPTY_GUID && isPrimaryUserActive
          ? this.leadInfo?.assignTo
          : null,
      ],
      secondaryAssignTo:
        this.leadInfo.secondaryUserId !== EMPTY_GUID && isSecondaryUserActive
          ? this.leadInfo.secondaryUserId
          : null,
    };
    this.updateForm = this.formBuilder.group(this.defaultControls);
  }

  clearManualLocation(location: any) {
    this.manualLocationsList = this.manualLocationsList.filter(
      (manualLocation: any) =>
        JSON.stringify(manualLocation) != JSON.stringify(location)
    );
  }

  addMoreLocation() {
    const { locality, city, state }: any = this.updateForm.value;
    if (!city?.trim() && !state?.trim() && !locality?.trim()) {
      return;
    }
    const filteredLocation = this.manualLocationsList.filter(
      (location: any) => {
        return (
          city?.trim() == location?.city &&
          locality?.trim() == location?.locality &&
          state?.trim() == location?.state
        );
      }
    );

    if (filteredLocation?.length) return;
    this.manualLocationsList.push({
      city: city?.trim(),
      locality: locality?.trim(),
      state: state?.trim(),
    });

    this.removeLocation('changeLocation');
  }

  removeLocation(type: any) {
    switch (type) {
      case 'changeLocation':
        this.updateForm.patchValue({
          locality: null,
          city: null,
          state: null,
        });
        break;
    }
  }

  initialize(): void {
    this.isBulkUpdate = Array.isArray(this.leadInfo);
    this.initializeUpdateForm();
    this.allLeadStatus = JSON.parse(localStorage.getItem('masterleadstatus'));
    this.updateLeadStates =
      this.allLeadStatus?.filter((status: any) => {
        return ![
          'Meeting Done',
          'Site Visit Done',
          'New',
          'Pending',
          'Site Visit Not Done',
          'Meeting Not Done',
        ].includes(status.displayName);
      }) || [];

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.currencyList =
          data.countries && data.countries.length > 0
            ? data.countries[0].currencies
            : null;
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
        this.isProjectMandatory =
          data?.leadProjectSetting?.isProjectMandatoryOnBooking;
        this.isNotesMandatory =
          data?.leadNotesSetting?.isNotesMandatoryOnUpdateLead;
        if (this.isNotesMandatory) {
          this.updateForm.controls['notes']?.setValidators([
            Validators.required,
          ]);
        } else {
          this.updateForm.controls['notes']?.clearValidators();
        }
      });

    this.updateForm
      ?.get('leadExpectedBudget')
      ?.valueChanges.subscribe((val) => {
        this.budgetInWords = formatBudget(
          val,
          this.leadInfo?.enquiry?.currency || this.defaultCurrency
        );
      });

    this._store
      .select(getLocationsWithGoogleApi)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchStr: string) => searchStr.length > 2)
      )
      .subscribe((searchStr: string) => {
        this._store.dispatch(new FetchLocationsWithGoogle(searchStr));
      });

    this._store
      .select(getProjectsIDWithName)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          ?.slice()
          ?.sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this._store
      .select(getProjectsIDWithNameIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.projectListIsLoading = data;
      });

    this._store
      .select(getPropertyWithIdNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.propertyList = data.slice().sort((a: any, b: any) => {
          const nameA = a.name || '';
          const nameB = b.name || '';
          return nameA.localeCompare(nameB);
        });
      });

    this._store
      .select(getPropertyListDetailsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.propertyListIsLoading = data;
      });

    const adminsWithReportees$ = this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper));

    const adminsWithReporteesIsLoading$ = this._store
      .select(getAdminsAndReporteesIsLoading)
      .pipe(takeUntil(this.stopper));

    const allUsers$ = this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper));

    const allUsersIsLoading$ = this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper));

    const permissions$ = this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper));

    const permissionsIsLoading$ = this._store
      .select(getPermissionsIsLoading)
      .pipe(takeUntil(this.stopper));

    combineLatest({
      adminsWithReportees: adminsWithReportees$,
      adminsWithReporteesIsLoading: adminsWithReporteesIsLoading$,
      allUsers: allUsers$,
      allUsersIsLoading: allUsersIsLoading$,
      permissions: permissions$,
      permissionsIsLoading: permissionsIsLoading$,
    }).subscribe(
      ({
        adminsWithReportees,
        adminsWithReporteesIsLoading,
        allUsers,
        allUsersIsLoading,
        permissions,
        permissionsIsLoading,
      }) => {
        let userList;
        if (permissions?.includes('Permissions.Leads.Assign')) {
          this.canAssignLead = true;
        }
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          userList = allUsers;
        } else {
          userList = adminsWithReportees;
        }

        let activeUsers = userList?.filter((user: any) => user.isActive);
        this.deactiveUsers = userList?.filter((user: any) => !user.isActive);
        this.primaryUserList = assignToSort(
          activeUsers,
          this.leadInfo?.assignTo
        );
        this.primaryAgentList = assignToSort(
          activeUsers,
          this.leadInfo?.assignTo
        );
        this.secondaryUserList = assignToSort(
          activeUsers,
          this.leadInfo?.secondaryUserId
        );
        this.secondaryUserList = assignToSort(
          activeUsers,
          this.leadInfo?.secondaryUserId
        );
        this.primaryAgentList = this.primaryUserList?.filter(
          (el: any) => !this.leadInfo?.secondaryUserId?.includes(el?.id)
        );
        this.secondaryAgentList = this.secondaryUserList?.filter(
          (el: any) => !this.leadInfo?.assignTo?.includes(el?.id)
        );
        this.updateForm
          .get('assignedToUserId')
          .valueChanges.subscribe((val: any) => {
            this.secondaryAgentList = this.secondaryUserList.filter(
              (el: any) => !val?.includes(el?.id)
            );
          });

        this.updateForm
          .get('secondaryAssignTo')
          .valueChanges.subscribe((val: any) => {
            this.primaryAgentList = this.primaryUserList.filter(
              (el: any) => !val?.includes(el?.id)
            );
          });

        this.isUserListLoading =
          adminsWithReporteesIsLoading &&
          allUsersIsLoading &&
          permissionsIsLoading;
      }
    );

    if (!this.isBulkUpdate) {
      this.leadSource = LeadSource[this.leadInfo?.enquiry?.leadSource];
    }
    // this.conditionalStatusRendering();
  }

  ngAfterViewInit(): void {
    const element = document.getElementById('bulk-status-table');
    if (element) {
      this.elementHeight = 'unset';
      const calculatedHeight = `${this.elementHeight}`;
      this.statusForm.nativeElement.style.height = calculatedHeight;
    }
    this.cdr.detectChanges();
  }

  dateValidator(control: { value: any }) {
    const selectedDate = control.value;

    if (selectedDate) {
      const currentDate = new Date();
      const selectedDateTime = new Date(selectedDate);
      currentDate.setSeconds(0, 0);
      selectedDateTime.setSeconds(0, 0);
      if (selectedDateTime > currentDate) {
        return { invalidDate: true };
      }
    }

    return null;
  }

  openAppointmentPopup() {
    let initialState: any = {
      data: this.leadInfo,
      closeModal: () => {
        appointmentModalRef.hide();
      },
      closeLeadPreviewModal: this.closeLeadPreviewModal,
    };
    const appointmentModalRef = this.modalService.show(
      LeadAppointmentComponent,
      {
        initialState,
        class: 'right-modal modal-550 ip-modal-unset',
      }
    );
  }

  showReasons(status: any, id: any) {
    const invoiceId = (id !== undefined && id !== null && id !== '') ? id : '';
    if (
      (this.leadInfo?.status?.shouldUseForInvoice ||
        this.leadInfo?.status?.shouldUseForBooking ||
        status?.shouldUseForInvoice) && status?.actionName !== 'Booking Cancel'
    ) {
      this.patchMiniBookDataIfBooked(invoiceId);
    }

    if (this.currentPath === '/invoice') {
      if (status?.shouldUseForInvoice) {
        this.isShowBookingFormBtn = true;
      } else if (status?.shouldUseForBookingCancel) {
        this.isShowBookingFormBtn = false;
        this.clearBookingFormValidations();
      }
    } else {
      if (status?.shouldUseForBooking) {
        this.isShowBookingFormBtn = true;
      } else {
        this.isShowBookingFormBtn = false;
        this.clearBookingFormValidations();
      }
    }

    patchFormControlValue(this.updateForm, 'leadStatus', status?.id);
    if (status?.childTypes?.length) {
      this.callBackReason = status.childTypes;
      this.hideStatus = true;
    }
    this.makeRequired(status);
    this.selectedStatus = status;
    let childCount = this.customStatusList.filter(
      (statusObj: any) => statusObj?.id === status?.id
    )?.[0]?.childTypes?.length;
    if (childCount) {
      toggleValidation(VALIDATION_SET, this.updateForm, 'reason', [
        Validators.required,
      ]);
    } else {
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'reason');
    }
  }

  clearBookingFormValidations() {
    const fieldsToClear = [
      'bookedUnderName',
      'bookedDate',
      'agreementValue',
      'chosenProperty',
      'chosenProject',
      'chosenUnit'
    ];
    fieldsToClear.forEach(field => toggleValidation(VALIDATION_CLEAR, this.updateForm, field));
  }

  addFormControls(
    formValues: { [key: string]: any },
    validators: { [key: string]: any } = {}
  ): void {
    Object.keys(this.updateForm.controls).forEach((controlName) => {
      if (!this.defaultControls.hasOwnProperty(controlName)) {
        this.updateForm.removeControl(controlName);
      }
    });

    for (const controlName in formValues) {
      if (formValues.hasOwnProperty(controlName)) {
        this.updateForm.addControl(
          controlName,
          this.formBuilder.control(formValues[controlName], [])
        );
      }
    }

    for (const controlName in validators) {
      if (validators.hasOwnProperty(controlName)) {
        const validatorsArray = this.getValidators(validators[controlName]);
        this.updateForm.get(controlName)?.setValidators(validatorsArray);
      }
    }
  }

  reasonChanged(reason: CustomStatus) {
    reason?.shouldUseForBooking ? '' : this.makeRequired(reason, true);
  }

  getValidators(validators: string[]): ValidatorFn[] {
    const validatorsArray: ValidatorFn[] = [];
    validators?.forEach((validator: string) => {
      switch (validator) {
        case 'required':
          validatorsArray.push(Validators.required);
          break;
        case 'futureDate':
          validatorsArray.push(this.validateScheduleTime(this.currentDate));
          break;
        default:
          break;
      }
    });
    return validatorsArray;
  }

  validateScheduleTime(Date: any): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const currentTime = new Date(Date);
      const selectedTime = new Date(control?.value);

      const selectedHours = selectedTime?.getHours();
      const selectedMinutes = selectedTime?.getMinutes();

      const currentDate = new Date(Date);
      const scheduleDate = new Date(control?.value);

      currentDate.setHours(0, 0, 0, 0);
      scheduleDate.setHours(0, 0, 0, 0);

      if (
        currentDate.getTime() == scheduleDate.getTime() &&
        (selectedHours < currentTime.getHours() ||
          (selectedHours === currentTime.getHours() &&
            selectedMinutes <= currentTime.getMinutes()))
      ) {
        return { invalidTime: true };
      }

      return null;
    };
  }

  makeRequired(status: CustomStatus, isSubStatus: boolean = false) {
    if (!status?.childTypes?.length && !isSubStatus)
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'reason');
    else toggleValidation(VALIDATION_SET, this.updateForm, 'reason');

    const formValues: { [key: string]: any } = {};
    const validators: { [key: string]: any } = {};

    // this should be refactored
    if (status?.shouldUseForBooking || status?.shouldUseForInvoice) {
      const customFields: CustomField[] = [];
      [
        ['Projects', null, []],
        ['bookedUnderName', null, ['required']],
        ['bookedDate', null, ['required']],
        ['agreementValue', null, []],
        ['projectProperty', 'Property', []],
        ['chosenProperty', null, ['required']],
        ['chosenProject', null, [this.isProjectMandatory ? 'required' : '']],
        ['chosenUnit', null, ['required']],
        [
          'currency',
          this.leadInfo?.enquiry?.currency || this.defaultCurrency,
          [],
        ],
      ].forEach((field: any) => {
        customFields.push({
          isRequired: false,
          value: field?.[1],
          field: {
            lastModifiedOn: 'string',
            createdOn: 'string',
            createdBy: 'string',
            lastModifiedBy: 'string',
            id: 'string',
            name: field?.[0],
            orderRank: 0,
            module: 'string',
            notes: 'any',
          },
          validators: field[2],
        });
      });
      status = {
        ...status,
        customFields,
      };
    }

    status?.customFields?.forEach((field: CustomField) => {
      formValues[field.field.name] = null;
      validators[field.field.name] = field?.validators;
    });
    this.addFormControls(formValues, validators);
    if (status?.shouldUseForBooking || status?.shouldUseForInvoice) {
      this.updateForm
        .get('currency')
        .setValue(this.leadInfo?.enquiry?.currency || this.defaultCurrency);
      this.updateForm.get('projectProperty').setValue('Property');
      this.updateForm.get('bookedDate').setValue(this.currentDate);

      this.updateForm.get('currency').valueChanges.subscribe((val) => {
        this.miniagreementValueInWords = formatBudget(
          this.updateForm.value.agreementValue ||
          this.updateForm?.get('agreementValue').value,
          val
        );
      });
      this.updateForm.get('agreementValue').valueChanges.subscribe((val) => {
        this.miniagreementValueInWords = formatBudget(
          val,
          this.updateForm.value.currency
        );
      });
      this.updateForm.get('assignedToUserId').valueChanges.subscribe((val) => {
        if (val) {
          this.selectedProject.assignedToUserId = val;
          this._store.dispatch(new FetchReportingManagerDetails(val));
          this._store.select(getManagerDetails).pipe().subscribe((data: any) => {
            this.userDetails = data;
          });
        }
      });
      this.updateForm.get('secondaryAssignTo').valueChanges.subscribe((val) => {
        if (val) {
          this.selectedProject.secondaryAssignTo = val;
        }
      });
    } else {
      let project = this.leadInfo?.projects?.map((data: any) => data.name);
      this.updateForm.get('Projects')?.setValue(project);
    }
    this.updateForm?.get('soldPriceCurrency')?.valueChanges.subscribe((val) => {
      this.soldPriceInWords = formatBudget(
        this.leadInfo?.soldPrice || this.updateForm.value.soldPrice,
        val
      );
    });

    this.updateForm?.get('soldPrice')?.valueChanges.subscribe((val) => {
      this.soldPriceInWords = formatBudget(
        val,
        this.updateForm.value.soldPriceCurrency ||
        this.leadInfo?.soldPriceCurrency
      );
    });

    this.updateForm
      ?.get('leadExpectedBudget')
      ?.valueChanges.subscribe((val) => {
        this.budgetInWords = formatBudget(
          val,
          this.leadInfo?.enquiry?.currency || this.defaultCurrency
        );
      });

    if (status?.shouldUseForBooking) {
      this.updateForm
        .get('soldPriceCurrency')
        ?.setValue(this.leadInfo?.enquiry?.currency || this.defaultCurrency);
    }
  }

  deselectStatuses() {
    this.updateForm.get('ScheduledDate').setErrors(null)
    this.updateForm.controls['reason'].setValue('');
    this.updateForm.controls['leadStatus'].setValue('');
    this.selectedStatus = null;
    this.hideStatus = false;
    this.selectedReason = null;
    this.isShowBookingFormBtn = false;
    this.clearBookingFormValidations();
  }

  updateStatus(isSaveAndNext: boolean = false) {
    this.isSaveAndNext = isSaveAndNext;
    Object.keys(this.updateForm.controls).forEach((field) => {
      const control = this.updateForm.get(field);
      if (control && control.invalid) {
        console.log(`Invalid field: ${field}`);
      }
    });
    this.selectedProject.agreementValue = this.updateForm?.value?.agreementValue;
    const invalidLeadNameMessage = 'Lead Name is invalid, Please Rename to continue';
    if (!this.isBulkUpdate) {
      if (!this.leadInfo?.name?.trim()) {
        this._notificationsService?.warn(invalidLeadNameMessage);
        return;
      }
    } else {
      for (const lead of this.leadInfo) {
        if (!lead?.name?.trim()) {
          this._notificationsService?.warn(invalidLeadNameMessage);
          return;
        }
      }
    }
    if (this.updateForm.invalid) {
      validateAllFormFields(this.updateForm);
      return;
    }
    if (this.navigateLink && this.updateForm.value.shareContent !== 'Properties') {
      window.location.href = this.navigateLink;
    } else if (this.whatsAppShare) {
      window.open(this.whatsAppShare, '_blank');
    }
    // Prepare the payload for the update
    const userData = this.updateForm.value;
    let postPonedStatusId = '';
    this.addMoreLocation();
    const addressPayload = this.prepareAddressPayload(userData);
    const isAddressPayloadEmpty = !addressPayload?.length;
    const payload = this.createPayload(userData, addressPayload, isAddressPayloadEmpty, postPonedStatusId);
    // Handle project or property selections
    if (this.selectedProject) {
      this.handleProjectPropertySelection(payload);
    }
    this.assignUsers(userData, payload);
    // Handle lead status based on current status
    this.handleLeadStatus(payload);
    // Dispatch the appropriate action based on whether it's a bulk update or not
    if (!this.isBulkUpdate) {
      this.handleSingleUpdate(payload);
    } else {
      this.handleBulkUpdate(userData, addressPayload, isAddressPayloadEmpty, postPonedStatusId);
    }
    // Update local lead info and clean up if necessary
    this.updateLeadInfo(userData, payload);
    if (!this.isBulkUpdate) {
      this.cleanStatusForm();
    }
    // Proceed to the next step if save and next
    if (!isSaveAndNext) {
      if (!this.isBulkUpdate) {
        this._store.select(getLeadStatusIsLoading).pipe(
          skipWhile((isLoading) => isLoading),
          take(1)
        ).subscribe((isLoading: boolean) => {
          if (!isLoading) {
            this.modalRef.hide();
          }
        });
      } else {
        this.modalRef.hide();
      }
      return;
    }
    this._leadPreviewComponent.nextData();
  }
  // Helper methods to keep the main method clean and readable
  prepareAddressPayload(userData: any): any {
    return (
      (this.showLocationSearch ? userData?.locationId : this.manualLocationsList) || this.leadInfo?.locationId
    )?.map((location: any) => ({
      locationId: location?.id ?? location?.id,
      placeId: location?.placeId ?? location?.placeId,
      subLocality: (location?.enquiredLocality ?? location?.enquiredLocality) || (location?.locality ?? location?.locality),
      city: (location?.enquiredCity ?? location?.enquiredCity) || (location?.city ?? location?.city),
      state: (location?.enquiredState ?? location?.enquiredState) || (location?.state ?? location?.state),
    }));
  }
  createPayload(userData: any, addressPayload: any, isAddressPayloadEmpty: boolean, postPonedStatusId: string): any {
    return {
      id: this.leadInfo.id,
      leadStatusId: userData.reason || userData.leadStatus || this.leadInfo.leadStatusId,
      scheduledDate: setTimeZoneDateWithTime(userData.ScheduledDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) || undefined,
      bookedDate: setTimeZoneDateWithTime(userData.bookedDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) || undefined,
      rating: userData.rating || this.leadInfo.rating,
      notes: userData.notes ? userData.notes : this.leadInfo.notes,
      IsNotesUpdated: userData.notes ? true : false,
      bookedUnderName: userData?.bookedUnderName,
      agreementValue: userData?.agreementValue,
      soldPrice: userData.soldPrice,
      soldPriceCurrency: userData.soldPriceCurrency,
      unmatchedBudget: userData.leadExpectedBudget || this.leadInfo.unmatchedBudget || undefined,
      purchasedFrom: userData.purchasedFromWhom || this.leadInfo.purchasedFrom,
      addresses: isAddressPayloadEmpty ? null : addressPayload,
      postponedDate: userData.reason === postPonedStatusId ? setTimeZoneDateWithTime(userData.ScheduledDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) : setTimeZoneDateWithTime(userData?.revertDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) || undefined,
      propertiesList: userData?.Properties ? userData?.Properties : this.leadInfo?.properties?.map((proper: any) => proper.title) || [],
      projectsList: userData?.Projects ? userData?.Projects : this.leadInfo?.projects?.map((proje: any) => proje.name) || [],
      projectIds: userData?.chosenProject ? [userData.chosenProject] : null,
      unitTypeId: userData?.chosenUnit ?? null,
      propertyIds: userData?.chosenProperty ? [userData.chosenProperty] : null,
      currency: userData.currency,
    };
  }
  handleProjectPropertySelection(payload: any): void {
    if (!payload.propertiesList) payload.propertiesList = [];
    if (!payload.projectsList) payload.projectsList = [];

    if (this.selectedProject.isprojectproperty === 'Property') {
      payload.propertiesList = [...new Set([...payload.propertiesList, this.selectedProject.selectedpropertyname])];
    } else if (this.selectedProject.isprojectproperty === 'Project') {
      payload.projectsList = [...new Set([...payload.projectsList, this.selectedProject.selectedpropertyname])];
    }
  }
  assignUsers(userData: any, payload: any): void {
    payload.assignTo = userData.assignedToUserId || this.leadInfo?.assignTo || EMPTY_GUID;
    payload.secondaryUserId = userData.secondaryAssignTo || this.leadInfo.secondaryUserId || EMPTY_GUID;
  }
  handleLeadStatus(payload: any): void {
    if (
      this.leadInfo?.status?.status === 'meeting_scheduled' ||
      this.leadInfo?.status?.status === 'site_visit_scheduled'
    ) {
      payload.isFullyCompleted = true;
      payload.projects = this.leadInfo?.projects?.map((project: any) => typeof project === 'string' ? project : project?.name);
      payload.properties = this.leadInfo?.properties?.map((property: any) => property?.title);
    }
  }

  handleSingleUpdate(payload: any): void {
    this.leadStatusIsLoading = true;
    this._store.dispatch(new UpdateLeadStatus(payload, this.leadInfo.id, false, false));
    if (!this.canShowStatusPopupInPreview) {
      this._store.dispatch(new LeadPreviewChanged());
    }
    // Close modal when response is received
    this._store.select(getLeadStatusIsLoading).pipe(
      skipWhile((isLoading) => isLoading),
      take(1)
    ).subscribe((isLoading: boolean) => {
      this.leadStatusIsLoading = false;
      if (!isLoading) {
        if (!this.isSaveAndNext) {
          this.modalRef.hide();
        }
      }
    });
  }

  handleBulkUpdate(userData: any, addressPayload: any, isAddressPayloadEmpty: boolean, postPonedStatusId: string): void {
    this.multipleLeadsIsLoading = true;
    if (!userData.leadStatus) {
      this.modalService.hide();
      return;
    }
    const notBookedLeads = this.leadInfo.filter((lead: any) => !lead.status?.shouldUseForBooking);
    const leadItems = this.isSelectedOnlySomeBooked() ? notBookedLeads : this.leadInfo;
    const bulkPayload = {
      ...this.createBulkPayload(leadItems, userData, addressPayload, isAddressPayloadEmpty, postPonedStatusId),
    };
    this._store.dispatch(new UpdateMultipleLead(bulkPayload, true));
    this._store.select(getMultipleLeadStatusIsLoading).pipe(
      skipWhile((isLoading) => isLoading),
      take(1)
    ).subscribe((isLoading: boolean) => {
      this.multipleLeadsIsLoading = isLoading;
      this.modalService.hide();
    });
  }
  createBulkPayload(lead: any, userData: any, addressPayload: any, isAddressPayloadEmpty: boolean, postPonedStatusId: string): any {
    const ids = lead.map((leadData: any) => leadData.id);
    const ArrayLeadIds = ids.flat();
    const payloadObj: any = {
      LeadIds: ArrayLeadIds,
      leadStatusId: userData?.reason || userData?.leadStatus,
      rating: userData?.rating,
      notes: userData?.notes,
      scheduledDate: userData.ScheduledDate || undefined,
      bookedDate: userData?.bookedDate || null,
      revertDate: userData?.revertDate || null,
      unmatchedBudget: userData?.leadExpectedBudget || null,
      purchasedFrom: userData?.purchasedFromWhom,
      bookedUnderName: userData.bookedUnderName,
      agreementValue: userData.agreementValue || null,
      soldPrice: userData.soldPrice,
      soldPriceCurrency: userData.soldPriceCurrency,
      postponedDate: userData?.reason === postPonedStatusId ? userData.ScheduledDate : userData?.revertDate || undefined,
      addresses: isAddressPayloadEmpty ? null : addressPayload,
      propertiesList: userData?.Properties ? userData?.Properties : [],
      projectsList: userData?.Projects ? userData?.Projects : [],
      projectIds: userData?.chosenProject ? [userData.chosenProject] : null,
      propertyIds: userData?.chosenProperty ? [userData.chosenProperty] : null,
      unitTypeId: userData?.chosenUnit ?? null,
    };

    if (!payloadObj.propertiesList) payloadObj.propertiesList = [];
    if (!payloadObj.projectsList) payloadObj.projectsList = [];

    if (this.selectedProject) {
      if (this.selectedProject.isprojectproperty === 'Property') {
        payloadObj.propertiesList = [...new Set([...payloadObj.propertiesList, this.selectedProject.selectedpropertyname])];
      } else if (this.selectedProject.isprojectproperty === 'Project') {
        payloadObj.projectsList = [...new Set([...payloadObj.projectsList, this.selectedProject.selectedpropertyname])];
      }
    }
    lead.forEach((leadData: any) => {
      if (leadData?.status?.status === 'meeting_scheduled' || leadData?.status?.status === 'site_visit_scheduled') {
        payloadObj.isFullyCompleted = true;
        // payloadObj.projects = leadData?.projects?.map((project: any) => project?.name);
        // payloadObj.properties = leadData?.properties?.map((property: any) => property?.title);
      }
      payloadObj.assignTo = userData.assignedToUserId || null;
      payloadObj.secondaryUserId = userData.secondaryAssignTo || null;
    });
    const intervalId = setInterval(() => {
      if (this.modalService.getModalsCount() === 0) {
        const numberOfLeads = payloadObj?.LeadIds?.length;
        if (numberOfLeads >= 50) {
          this.modalRef = this.modalService.show(
            this.trackerInfoModal,
            Object.assign(
              {},
              {
                class: 'modal-400 top-modal ph-modal-unset',
                ignoreBackdropClick: true,
                keyboard: false,
              }
            )
          );
        }
      }
      clearInterval(intervalId);
    }, 2000);
    return payloadObj;
  }

  updateLeadInfo(userData: any, payload: any): void {
    this._store.select(getLeadStatusIsLoading).pipe(
      skipWhile((isLoading) => isLoading),
      take(1)
    ).subscribe((isLoading: boolean) => {
      let masterLeadStatus = JSON.parse(localStorage.getItem('masterleadstatus'));
      masterLeadStatus = this.customStatusList;
      const id = userData.reason || userData.leadStatus || this.leadInfo.leadStatusId;
      const status = masterLeadStatus.find((item: any) => item.id === id || item.childTypes.some((child: any) => child.id === id));
      this.leadInfo = {
        ...this.leadInfo,
        status: {
          ...this.leadInfo.status,
          displayName: status.displayName || this.leadInfo.status.displayName
        },
        rating: userData.rating,
        projects: userData.projectsList,
        properties: userData.propertiesList,
        notes: userData.notes,
        scheduledDate: userData.ScheduledDate,
        assignTo: userData.assignedToUserId || this.leadInfo.assignTo || EMPTY_GUID,
        secondaryUserId: userData.secondaryAssignTo || this.leadInfo.secondaryUserId || EMPTY_GUID
      };
      // Handle sub-status display name
      let hasSubStatus = false;
      for (let reason of this.callBackReason) {
        if (this.updateForm?.controls?.['reason']?.value === reason?.id && this.leadInfo?.status?.childType) {
          this.leadInfo.status.childType = {
            ...this.leadInfo.status.childType,
            displayName: reason.displayName
          };
          hasSubStatus = true;
          break;
        }
      }
      if (!hasSubStatus) {
        this.leadInfo.status.childType = {
          ...this.leadInfo.status.childType,
          displayName: ''
        };
      }
    });
  }

  openBulkUpdatedStatus() {
    this._store.dispatch(new FetchBulkOperation(1, 10, 'lead'));

    this.modalRef = this.modalService.show(BulkOperationTrackerComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState: {
        moduleType: 'lead',
      },
    });
  }


  cleanStatusForm(): void {
    this.deselectStatuses();

    // this.updateForm.controls['scheduledDate'].setValue('');
    // this.updateForm.controls['notes'].setValue('');
    // this.updateForm.controls['rating'].setValue('');
    // this.updateForm.controls['projectsList'].setValue([]);
    // this.updateForm.controls['propertiesList'].setValue([]);
  }

  updateSelectedReason(reason: any) {
    let customFields = [...reason?.customFields]?.sort(
      (a: any, b: any) => a?.field?.orderRank - b?.field?.orderRank
    );
    reason = {
      ...reason,
      customFields,
    };
    this.selectedReason = reason;
  }

  closeModal() {
    this.isVisitDone = false;
    this.modalRef.hide();
    this.updateForm.reset();
    this.hideStatus = false;
    this.selectedReason = null;
  }

  fullBookingFormModal() {
    if (this.updateForm.invalid) {
      validateAllFormFields(this.updateForm);
      return;
    }
    if (this.updateForm.value.notes) {
      this.selectedProject.notes = this.updateForm.value.notes;
    }
    this.isShowBookingFormBtn = false;
    this.updateStatus(true);
    this._store
      .select(getManagerDetails)
      .pipe(take(1))
      .subscribe((data: any) => {
        this.userDetails = data;
        let initialState: any = {
          selectedProject: this.selectedProject,
          leadInfo: this.leadInfo,
          userDetails: this.userDetails,
          miniBookDate: this.updateForm.get('bookedDate')?.value,
        };
        this.modalRef = this.modalService.show(BookingFormComponent, {
          class: 'right-modal modal-550 ip-modal-unset',
          initialState,
        });
        this.modalRef.onHide.subscribe(() => {
          this.isShowBookingFormBtn = true;
        });
      });
  }

  switchTabProjectProperty(value: any) {
    if (value === 'Property') {
      this.isShowUnitInfoField = false;
      this.updateForm.controls['projectsList']?.setValue(null);
      this.updateForm.controls['chosenProject']?.setValue(null);
      toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProperty', [
        Validators.required,
      ]);
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');
    } else {
      this.isShowUnitInfoField = true;
      this.updateForm.controls['chosenProperty']?.setValue(null);
      if (this.isProjectMandatory) {
        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProject', [
          Validators.required,
        ]);
        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenUnit', [
          Validators.required,
        ]);
      }
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProperty');
    }
  }

  onInputAgreementValue(value: number) {
    this.selectedProject.agreementValue = value;
  }

  onPropertyChange(id: string) {
    toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');
    toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');
    this.isSelectedPropertyOrProject = 'Property';
    this.isShowUnitInfoField = false;
    if (id) {
      this._store.dispatch(new FetchPropertyById(id));
    }
    this._store
      .select(getPropertyListDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data) {
          this.selectedProject.selectedpropertyname = data.title;
          this.selectedProject.buildername = data.ownerDetails?.name;
          this.selectedProject.sealablearea = data.dimension?.saleableArea;
          this.selectedProject.carpetarea = data.dimension?.carpetArea;
          this.selectedProject.brokerageCurrency =
            data.monetaryInfo?.brokerageCurrency;
          this.selectedProject.brockragecharge = data.monetaryInfo?.brokerage;
          this.selectedProject.isprojectproperty =
            this.isSelectedPropertyOrProject;
        }
      });
  }

  onProjectChange(id: string) {
    if (id) {
      this._store.dispatch(new FetchProjectById(id));
      if (this.isProjectMandatory) {
        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenUnit', [
          Validators.required,
        ]);
        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProject', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');
        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');
      }
    }
    toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProperty');
    this.isSelectedPropertyOrProject = 'Project';
    this.isShowUnitInfoField = true;
    this.updateForm.get('chosenUnit')?.reset();
    combineLatest([
      this._store.select(getIsProjectByIdLoading),
      this._store.select(getSelectedProjectById),
    ])
      .pipe(takeUntil(this.stopper))
      .subscribe(([isLoading, projectData]: [boolean, any]) => {
        this.isUnitInfoDataLoading = isLoading;
        if (projectData) {
          this.unitInfo = projectData.unitTypes;
          this.selectedProject = {
            selectedpropertyname: projectData.name,
            buildername: projectData.builderDetail?.name,
            brockragecharge: projectData.monetaryInfo?.brokerage,
            brokerageCurrency: projectData.monetaryInfo?.brokerageCurrency,
            isprojectproperty: this.isSelectedPropertyOrProject,
            allprojectdata: projectData,
          };
        }
      });
  }

  onChoosenUnitChange(event: any) {
    this.unitInfo?.map((data: any) => {
      if (data.id === event?.id) {
        this.selectedProject.carpetarea = data?.carpetArea;
        this.selectedProject.sealablearea = data?.superBuildUpArea;
        this.selectedProject.unitname = data?.name;
      }
    });
  }

  patchMiniBookDataIfBooked(id: string) {
    id && this._store.dispatch(new FetchInvoiceById(id));
    this._store.select(getBookingData)?.subscribe((bookingDetails: any) => {
      if (!bookingDetails) return;
      const {
        projects,
        properties,
        unitType,
        bookedUnderName,
        agreementValue,
        notes,
        currency,
      } = bookingDetails;
      const hasProjects = Array.isArray(projects) && projects.length > 0;
      const hasProperties = Array.isArray(properties) && properties.length > 0;
      if (hasProjects) this.onProjectChange(projects[0].id);
      if (hasProperties) this.onPropertyChange(properties[0].id);
      const validAgreementValue = agreementValue > 0 ? agreementValue : null;
      this.selectedProject.agreementValue = validAgreementValue;
      this.updateForm.patchValue({
        reason: this.leadInfo?.status?.childType?.id,
        leadStatus: this.leadInfo?.status?.id,
        bookedDate: patchTimeZoneWithTime(bookingDetails.bookedDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
        bookedUnderName,
        agreementValue: validAgreementValue,
        chosenProperty: hasProperties ? properties[0]?.id : null,
        chosenProject: hasProjects ? projects[0]?.id : null,
        chosenUnit: unitType?.id,
        notes,
        currency,
        assignedToUserId: this.leadInfo?.assignTo,
        secondaryAssignTo: this.leadInfo?.secondaryUserId !== EMPTY_GUID ? this.leadInfo?.secondaryUserId : null,
      });
      this.selectedReason = this.leadInfo?.status?.childType?.displayName;
      const projectProperty = hasProjects ? 'Project' : hasProperties ? 'Property' : 'Property';
      this.updateForm?.get('projectProperty')?.setValue(projectProperty);

      if (projectProperty === 'Project') {
        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProperty');
      } else if (projectProperty === 'Property') {
        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');
        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');
      }

      if (unitType?.id) this.onChoosenUnitChange(unitType.id);
      this.miniagreementValueInWords = formatBudget(agreementValue, currency || this.defaultCurrency);
    });
  }

  isSelectedAllBookedOrInvoicedLead(): boolean {
    if (!Array.isArray(this.leadInfo)) {
      return false;
    }
    if (this.leadInfo.length === 0) {
      return false;
    }
    const allBookedOrInvoiced = this.leadInfo.every(
      (lead: any) =>
        lead?.status?.shouldUseForBooking ||
        lead?.status?.shouldUseForInvoice
    );
    return allBookedOrInvoiced;
  }

  checkBookedUnderName(event: any) {
    const inputValue = event.target.value.trim();
    if (inputValue) {
      this.updateForm.controls['bookedUnderName'].setValue(inputValue);
      this.updateForm.controls['bookedUnderName'].setErrors(null);
    } else {
      this.updateForm.controls['bookedUnderName'].setErrors({ whitespace: true });
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
    this.isProjectSubscription.unsubscribe();
  }
}
