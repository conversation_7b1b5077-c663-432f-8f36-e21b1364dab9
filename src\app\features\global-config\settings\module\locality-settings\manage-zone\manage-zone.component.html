<div class="d-flex ph-flex-col">
    <div class="align-center w-33 tb-w-50 ph-w-100">
        <div class="mr-10 fw-600 text-large text-nowrap">{{'GLOBAL.select' | translate}}
            {{'LOCATION.city'|translate }}</div>
        <div class="ng-select-sm w-100 mr-40 ip-mr-20 ph-mr-0">
            <ng-select [virtualScroll]="true" [multiple]="true" appSelectAll [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" ResizableDropdown [items]="allCityData"
                [(ngModel)]="appliedFilter.cityIds" bindValue="id" bindLabel="name" (change)="zoneFilterFunction()">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                            class="checkmark"></span>{{item.name}}
                    </div>
                </ng-template>
            </ng-select>
        </div>
    </div>
</div>
<div class="align-center bg-white w-100 border-gray mt-20">
    <div class="align-center px-10 border-end flex-grow-1 no-validation py-12">
        <ng-container>
            <div class="align-center w-100">
                <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
                <input placeholder="type to search" autocomplete="off" (keydown)="onSearch($event)"
                    (input)="isEmptyInput($event)" name="search" [(ngModel)]="searchTerm"
                    class="border-0 outline-0 w-100">
            </div>
            <small class="text-muted text-nowrap ph-d-none">
                ({{ 'LEADS.lead-search-prompt' | translate}})</small>
        </ng-container>
    </div>
    <div class="show-dropdown-white align-center position-relative">
        <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
                {{ 'GLOBAL.show' | translate}}</span> {{ 'GLOBAL.entries' | translate }}</span>
        <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" class="w-150 tb-w-120px"
            [(ngModel)]="selectedPageSize" ResizableDropdown (change)="assignCount()" [searchable]="false">
            <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                {{pageSize}}</ng-option>
        </ng-select>
    </div>
</div>
<div *ngIf="allZoneData?.items?.length else noData" class="locality">
    <ag-grid-angular #agGrid class="ag-theme-alpine" [pagination]="true" [paginationPageSize]="pageSize + 1"
        [gridOptions]="gridOptions" [rowData]="allZoneData.items" [suppressPaginationPanel]="true"
        [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true" (gridReady)="onGridReady($event)"
        (filterChanged)="onFilterChanged($event)">
    </ag-grid-angular>
    <div class="justify-center" *ngIf="gridApi?.getSelectedNodes()?.length">
        <div class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 z-index-2 ip-flex-col">
            <div>
                <h4 class="mr-20 fw-600 text-nowrap">{{gridApi?.getSelectedNodes()?.length}}
                    {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' | translate}}
                </h4>
                <div class="text-red-350 text-decoration-underline fw-semi-bold cursor-pointer ip-mb-10"
                    (click)="deselectOptions()">{{ 'GLOBAL.deselect' | translate }}</div>
            </div>
            <div class="flex-center flex-wrap">
                <button class="btn-bulk" [disabled]="gridApi?.getSelectedNodes()?.length < 2" *ngIf="canBulkAssignment"
                    (click)="userAssignment()">Assign to user(s)</button>
                <button class="btn-bulk-red" *ngIf="canBulkDelete" [disabled]="gridApi?.getSelectedNodes()?.length < 2"
                    (click)="deleteZones()">{{ 'LEADS.bulk' | translate }} {{'BUTTONS.delete' | translate }}</button>
            </div>
        </div>
    </div>
</div>
<ng-container *ngIf="allZoneData?.totalCount">
    <div class="mt-20 flex-end">
        <div class="mr-10">{{ 'GLOBAL.showing' | translate }}
            {{(currOffset * pageSize) + 1}} {{ 'GLOBAL.to-small' | translate }}
            {{allZoneData?.items?.length > 1 ? currOffset*pageSize + allZoneData?.items?.length :
            currOffset*pageSize + allZoneData?.items?.length}}
            {{'GLOBAL.of-small' | translate}} {{allZoneData.totalCount}} {{'GLOBAL.entries-small' | translate}}
        </div>
        <pagination [offset]=currOffset [limit]="1" [range]="1" [size]='getPages(allZoneData?.totalCount,pageSize)'
            (pageChange)="onPageChange($event)">
        </pagination>
    </div>
</ng-container>
<ng-template #noData>
    <div class="flex-col flex-center h-100-270">
        <img src="assets/images/layered-cards.svg" alt="No data found" width="160" height="140">
        <div class="fw-semi-bold text-xl text-mud">No Zone Found!</div>
    </div>
</ng-template>