<div *ngIf="!filterSimilarProp?.length && isMSSimilarPropertiesLoading" [ngClass]="{'gray-scale': isPropertySoldOut}">
    <div class="loader-shimmer-banner shimmer-animation gray"></div>
</div>
<ng-container *ngIf="filterSimilarProp?.length">
    <div class="bg-linear-dark-blue text-white brtl-10 brbl-10 tb-ml-40 ph-ml-0 custom-bg">
        <div class="pt-10 pl-16">
            <h5 class="fw-600">More Properties</h5>
            <drag-scroll class="d-flex scrollbar scroll-hide">
                <div *ngFor="let property of filterSimilarProp" class="w-unset">
                    <a [href]="'/external/property-preview/'+userName+'/'+property.serialNo" target="_blank">
                        <div class="h-200 w-180 m-4 p-10 position-relative">
                            <div class="position-relative">
                                <div class="bg-black-linear-down">
                                    <img [appImage]="getCoverImage(property?.imageUrls)" type="property"
                                        class="obj-cover br-6" width="100%" height="100"
                                        [ngClass]="{'gray-scale': property.status === 1 || property.enquiredFor === 'Sold'}">
                                </div>
                                <div class="position-absolute bottom-5 text-xxs w-100 text-center">
                                    <span class="fw-300">Posted on:</span>
                                    <span class="fw-600">
                                        {{property?.createdOn | date:'dd/MM/yyyy'}}</span>
                                </div>
                                <div class="position-absolute top-8 nleft-8 flex-col">
                                    <img *ngIf="property.tagInfo?.isFeatured == true" [type]="'leadrat'"
                                        [appImage]="images.leftHighlightTag" class="mb-4">
                                    <img [type]="'leadrat'"
                                        [appImage]="getEnquiredFor(property?.enquiredFor!) == 'Sale' ? images.buyTag : getEnquiredFor(property?.enquiredFor!) == 'Rent' ? images.rentTag : ''"
                                        width="40" height="16">
                                </div>
                                <div *ngIf="property.status === 1 || property.isDeleted"
                                    class="position-absolute w-100 h-100 flex-center z-index-1002">
                                    <img src="assets/images/soldout.svg" alt="Sold Out" class="w-50 pulse-animation">
                                </div>
                            </div>
                            <div>
                                <div class="mt-4 text-truncate-1 break-all text-accent-green text-sm"
                                    *ngIf="property?.title">"{{property.title}}"</div>
                                <h5 class="text-nowrap text-truncate-1 break-all fw-semi-bold mt-4">
                                    <span *ngIf="property?.noOfBHKs">
                                        {{isListing ? getBRDisplayString(property?.noOfBHKs) :
                                        getBHKDisplayString(property?.noOfBHKs)}}
                                    </span> {{property?.propertyType?.displayName}}
                                </h5>
                                <div class="text-sm mt-8 align-center w-100">
                                    <div class="w-50 align-center" *ngIf="property?.monetaryInfo?.expectedPrice">
                                        <span class="icon ic-circle-rupee ic-accent-green ic-xxs"></span>
                                        <span
                                            class="text-nowrap text-truncate-1 break-all ml-4" [title]="property?.enquiredFor === 'Rent' ? 'Rent Amount' : 'Total Price'">{{formatBudget(property?.monetaryInfo?.expectedPrice,property?.monetaryInfo?.currency
                                            || defaultCurrency)}}</span>
                                    </div>
                                    <div class="w-50 align-center" *ngIf="property?.dimension?.area">
                                        <span class="icon ic-square-feet ic-accent-green ic-xxs"></span>
                                        <span class="text-nowrap text-truncate-1 break-all ml-4">
                                            {{property?.dimension?.area}}
                                            {{getAreaUnit(property.dimension?.areaUnitId, areaSizeUnits)?.unit}}</span>
                                    </div>
                                </div>
                                <div class="text-sm mt-8 align-center w-100"
                                    *ngIf="property?.address && !isEmptyObject(property?.address)">
                                    <span class="icon ic-location-mark ic-accent-green ic-xxs"></span>
                                    <span
                                        class="text-nowrap text-truncate-1 break-all ml-4">{{getLocationDetailsByObj(property?.address)}}</span>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="opacity-0" inView (inView)="onInView($event)">
                    .
                </div>
                <span *ngIf="isMSSimilarPropertiesLoading" class="count mt-16 mr-12">
                    <div class="loader-shimmer-banner shimmer-animation gray h-100px w-180"></div>
                </span>
            </drag-scroll>
        </div>
    </div>
</ng-container>