<!-- web -->
<ng-container *ngIf="propertyInfo else loading">
  <div class="prop-ms">
    <div class="m-auto max-w-1260 web-bg-pattern position-relative">
      <!-- <div class="position-absolute top-0 left-0">
        <img alt="pattern" class="w-130 h-140" src="assets/images/5-4-stars-pattern.svg">
      </div> -->
      <div [ngClass]="!globalSettingsDetails?.shouldEnableEnquiryForm ? 'justify-center' : 'justify-between'"
        class="h-661 w-100 min-h-100 tb-h-unset tb-flex-col">
        <!-- (scroll)="onScroll($event)" -->
        <div
          class="flex-grow-1 scrollbar scroll-hide max-w-770 min-w-770 w-770 pr-30 pl-40 my-40 ph-px-16 tb-w-unset tb-max-w-unset tb-min-w-unset tb-mb-0 position-relative">
          <div *ngIf="isPropertySoldOut" class="position-absolute w-100 h-100 flex-center">
            <img src="assets/images/soldout.svg" alt="Sold Out"
              class="position-absolute w-50 max-w-50 top-45pr left-40pr ph-top-30 ph-left-45 pulse-animation">
          </div>
          <div class="position-relative">
            <div id="carouselIndicators1" class="carousel slide carousel-fade" data-bs-ride="carousel">
              <div class="carousel-inner br-10">
                <div class="carousel-item" data-bs-interval="2000" *ngFor="let slide of slides; let i = index"
                  [ngClass]="{'active': i == 0 }">
                  <div class="flex-center position-relative"
                    (click)="propertyImages.length ? openImage(image, mbleImage, i)  : ''">
                    <img [appImage]="s3BucketUrl+slide.src" [type]="'property'" alt="property image"
                      class="w-700 position-relative h-300" [ngClass]="{'gray-scale': isPropertySoldOut}">
                    <div class="w-700 bg-blur position-absolute flex-center cursor-pointer"
                      *ngIf="propertyImages?.length">
                      <img [appImage]="s3BucketUrl+slide.src" [type]="'property'" alt="property image"
                        class="max-w-700 h-300" [ngClass]="{'gray-scale': isPropertySoldOut}">
                    </div>
                  </div>
                  <div class="carousel-counter web" *ngIf="propertyImages?.length > 1">{{i+1}}/{{slides.length}}
                  </div>
                </div>
              </div>
              <div *ngIf="propertyImages?.length > 1">
                <button class="carousel-control-prev" type="button" data-bs-target="#carouselIndicators1"
                  data-bs-slide="prev">
                  <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#carouselIndicators1"
                  data-bs-slide="next">
                  <span class="carousel-control-next-icon" aria-hidden="true"></span>
                </button>
              </div>
            </div>
            <div class="justify-end position-absolute nright-11 top-20 z-index-1001"
              *ngIf="propertyInfo?.tagInfo?.isFeatured == true">
              <img [type]="'leadrat'" [appImage]="images.rightHighlightTag" class="w-80 h-24" alt="highlight">
            </div><span (click)="isShareVisible = !isShareVisible"
              class="icon ic-share ic-lg position-absolute p-20 br-8 bg-black-50 cursor-pointer top-12 left-10 z-index-1001"></span>
            <ng-container *ngIf="isShareVisible">
              <div class="mask mt-30 ml-12 z-index-1021">
                <span class="icon ic-whatsapp ic-lg position-absolute p-20 br-8 bg-black-50 cursor-pointer"
                  (click)="shareInfo('whatsApp')"></span>
                <span class="icon ic-envelope ic-lg position-absolute p-20 br-8 bg-black-50 cursor-pointer mt-50"
                  (click)="shareInfo('email')"></span>
              </div>
            </ng-container>
            <div
              class="position-absolute bottom-0 brtr-10 brbl-10 bg-linear-black py-4 px-12 text-white z-index-1001 fw-600 text-nowrap">
              <span class="fw-300 text-sm">Posted on</span>
              {{getTimeZoneDate(propertyInfo?.createdOn, userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear')}}
            </div>
          </div>
          <div class="sticky-nav" [ngClass]="{'is-sticky': isSticky}">
            <property-details [propertyInfo]="propertyInfo" [isPropertySoldOut]="isPropertySoldOut"></property-details>
          </div>
          <property-attributes [propertyInfo]="propertyInfo"
            [attributesSelection]="attributesSelection">property-details
          </property-attributes>
          <about-property [propertyInfo]="propertyInfo" [isPropertySoldOut]="isPropertySoldOut"
            [areaSizeUnits]="areaSizeUnits"></about-property>
          <ng-container *ngIf="amenities?.length || additionalAttrData?.length">
            <div class="border br-20 bg-white align-center user px-8 py-4 w-160 mt-12">
              <div class="px-10 text-light-gray ip-px-8 align-center br-20 cursor-pointer h-28 fw-semi-bold"
                [ngClass]="{'text-white fw-700 bg-black-100' : selectedSection == 'Amenities'}"
                (click)="selectedSection = 'Amenities';hideModal()">
                <span>Amenities</span>
              </div>
              <div class="px-10 text-light-gray ip-px-8 align-center br-20 cursor-pointer h-28 fw-semi-bold"
                [ngClass]="{'text-white fw-700 bg-black-100' : selectedSection == 'Attribute'}"
                (click)="selectedSection = 'Attribute';hideModal()">
                <span>Attributes</span>
              </div>
            </div>
            <!-- <div class="d-flex">
              <h3 class="text-mud mt-20 fw-semi-bold ph-br-mud">{{ 'PROPERTY.STEPS.amenities' | translate }}</h3>
            </div> -->
            <selected-amenities-list [selectedSection]="selectedSection" [isPropertySoldOut]="isPropertySoldOut"
              [propertyInfo]="propertyInfo" [presentAmenities]="amenities"></selected-amenities-list>
          </ng-container>
          <div *ngIf="propertyInfo?.videos?.length">
            <h3 class="text-mud mt-20 fw-semi-bold ph-br-mud">Video</h3>
            <span class="down-border"></span>
          </div>
          <property-videos [propertyInfo]="propertyInfo" [isPropertySoldOut]="isPropertySoldOut"></property-videos>
        </div>
        <div *ngIf="globalSettingsDetails?.shouldEnableEnquiryForm"
          class="min-w-490 tb-min-w-unset my-40 tb-mt-0 tb-mb-100 scrollbar scroll-hide">
          <div class="w-450 tb-d-none ph-mx-16">
            <enquiry-form [serialNo]="propertyInfo?.serialNo" [isPropertySoldOut]="isPropertySoldOut"></enquiry-form>
          </div>
          <div class="mt-30 w-490 tb-w-unset ph-ml-16"  *ngIf="globalSettingsDetails?.showMoreMicrositeProperties">
            <similar-properties [areaSizeUnits]="areaSizeUnits" [serialNo]="propertyInfo?.serialNo"
              [isPropertySoldOut]="isPropertySoldOut"></similar-properties>
          </div>
          <div class="tb-d-block d-none w-100 position-fixed bottom-0 p-20 bg-white brtl-10 brtr-10">
            <h4 class="bg-black-100 text-white fw-600 justify-center py-16 br-10"
              [ngClass]="{'pe-none' : !isMicrositeFeatureEnabled || isPropertySoldOut}" (click)="showEnquire()">
              Enquire Now
            </h4>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>

<ng-template #loading>
  <div class="flex-center h-100">
    <application-loader></application-loader>
  </div>
</ng-template>
<ng-template #image>
  <div class="flex-center-col">
    <div class="align-center mt-20" *ngIf="(propertyImages?.length && propertyImages[0]!='')">
      <span class="dot dot-lg-xxl cursor-pointer bg-accent-green mr-20" (click)="onPreviousClick()"
        *ngIf="propertyImages.length > 1">
        <span class="icon ic-arrow-left icon-white ic-md" id="clkLeftImage" data-automate-id="clkLeftImage"></span>
      </span>
      <div class="flex-center position-relative">
        <span class="icon ic-close-secondary ic-xl position-absolute ntop-30 nright-30 cursor-pointer"
          (click)="modalRef.hide()"></span>
        <img [appImage]="s3BucketUrl+slides[currentSlide].src" [type]="'property'" alt="property image"
          class="w-1000 position-relative h-500" [ngClass]="{'gray-scale': isPropertySoldOut}">
        <div class="w-1000 bg-blur position-absolute flex-center">
          <img [appImage]="s3BucketUrl+slides[currentSlide].src" [type]="'property'" alt="property image"
            class="max-w-1000 h-500" [ngClass]="{'gray-scale': isPropertySoldOut}">
        </div>
        <div *ngIf="isPropertySoldOut" class="position-absolute w-100 h-100 flex-center z-index-1002">
          <img src="assets/images/soldout.svg" alt="Sold Out"
            class="position-absolute w-50 max-w-50 top-45pr left-40pr pulse-animation">
        </div>
      </div>
      <span class="dot dot-lg-xxl cursor-pointer bg-accent-green ml-20" (click)="onNextClick()"
        *ngIf="propertyImages.length > 1">
        <span class="icon ic-arrow-right icon-white ic-md" id="clkRightImage" data-automate-id="clkRightImage"></span>
      </span>
    </div>
    <div class="d-flex flex-wrap">
      <div *ngFor="let propertyImage of propertyImages; let ind = index;">
        <img [appImage]="s3BucketUrl+propertyImage?.src" [type]="'property'" width="90" height="90"
          class="obj-cover m-10 cursor-pointer" alt="property image" (click)="currentSlide = ind"
          [ngClass]="{'gray-scale': isPropertySoldOut}">
      </div>
    </div>
  </div>
</ng-template>

<ng-template #mbleImage>
  <div class="flex-center-col">
    <ng-container *ngIf="(propertyImages?.length && propertyImages[0]!='')">
      <span class="icon ic-close-secondary mb-8" (click)="modalRef.hide()"></span>
      <div class="position-relative w-100">
        <img [appImage]="s3BucketUrl+slides[currentSlide].src" [type]="'property'" class="w-100" alt="property image"
          [ngClass]="{'gray-scale': isPropertySoldOut}">
        <div class="position-absolute top-50 left-10"><span *ngIf="propertyImages.length > 1"
            (click)="onPreviousClick()" class="icon ic-chevron-left ic-xl" id="clkLeftImage"
            data-automate-id="clkLeftImage"></span></div>
        <div class="position-absolute top-50 right-10">
          <span *ngIf="propertyImages.length > 1" (click)="onNextClick()" class="icon ic-chevron-right ic-xl"
            id="clkRightImage" data-automate-id="clkRightImage"></span>
        </div>
        <div *ngIf="isPropertySoldOut" class="position-absolute w-100 h-100 flex-center">
          <img src="assets/images/soldout.svg" alt="Sold Out" class="w-50 pulse-animation">
        </div>
      </div>
    </ng-container>
  </div>
</ng-template>