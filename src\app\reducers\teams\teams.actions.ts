import { Action } from '@ngrx/store';
import { UserFilter } from 'src/app/core/interfaces/property.interface';

export enum TeamsActionTypes {
  ADD_ROLE = '[ROLES] Add Role',
  FETCH_ROLES_LIST = '[ROLES] Fetch Roles List',
  FETCH_MANAGE_USER_ROLES_LIST = '[ROLES] Fetch Manage User Roles List',
  FETCH_ROLES_LIST_SUCCESS = '[ROLES] Fetch Roles List Success',
  FETCH_ROLES_BY_ID = '[ROLES] Fetch Roles By Id',
  FETCH_ROLES_BY_ID_SUCCESS = '[ROLES] Fetch Roles By Id Success',
  DELETE_ROLE = '[ROLES] Delete Role',
  FETCH_ROLES_PERMISSION_BY_ID = '[ROLES] Fetch Roles Permission By Id',
  FETCH_ROLES_PERMISSION_BY_ID_SUCCESS = '[ROLES] Fetch Roles Permission By Id Success',
  UPDATE_ROLES_PERMISSION_BY_ID = '[ROLES] Update Roles Permission By Id',
  UPDATE_ROLES_PERMISSION_BY_ID_SUCCESS = '[ROLES] Update Roles Permission By Id Success',
  FETCH_ALL_EXISTING_PERMISSIONS = '[PERMISSION] Fetch All Existing Permission',
  FETCH_ALL_EXISTING_PERMISSIONS_SUCCESS = '[PERMISSION] Fetch All Existing Permission Success',
  FETCH_USERS_LIST = '[USERS] Fetch Users List',
  FETCH_USERS_LIST_SUCCESS = '[USERS] Fetch Users List Success',
  FETCH_USERS_LIST_FOR_REASSIGNMENT = '[USERS] Fetch Users List For Reassignment',
  FETCH_USERS_LIST_FOR_REASSIGNMENT_SUCCESS = '[USERS] Fetch Users List For Reassignment Success',
  ADD_USER = '[USERS] Add User',
  FETCH_USER_BY_ID = '[USERS] Fetch Users By Id',
  FETCH_USER_BY_ID_SUCCESS = '[USERS] Fetch Users By Id Success',
  UPDATE_FILTER_PAYLOAD = '[USERS] Update Filter Payload',
  FETCH_USER_PROFILE = '[USERS] Fetch User Profile',
  FETCH_USER_PROFILE_SUCCESS = '[USERS] Fetch User Profile Success',
  DELETE_USER = '[USERS] Delete User',
  DELETE_USER_SUCCESS = '[USERS] Delete User Success',
  BULK_TOGGLE_USER_STATUS = '[USERS] Bulk Toggle User Status',
  BULK_TOGGLE_USER_STATUS_SUCCESS = '[USERS] Bulk Toggle User Status Success',
  UPDATE_USER_ROLE = '[USERS] Update User Role',
  ADD_DESIGNATION = '[DESIGNATION] Add Designation',
  DELETE_DESIGNATION = '[DESIGNATION] Delete Designation',
  FETCH_DESIGNATION_LIST = '[DESIGNATION] Fetch Designation List',
  FETCH_DESIGNATION_LIST_SUCCESS = '[DESIGNATION] Fetch Designation List Success',
  ADD_DEPARTMENT = '[DEPARTMENT] Add Department',
  DELETE_DEPARTMENT = '[DEPARTMENT] Delete Department',
  FETCH_DEPARTMENT_LIST = '[DEPARTMENT] Fetch Department List',
  FETCH_DEPARTMENT_LIST_SUCCESS = '[DEPARTMENT] Fetch Department List Success',
  FETCH_GENERAL_MANAGER_LIST = '[USER] Fetch General Manager List',
  FETCH_GENERAL_MANAGER_LIST_SUCCESS = '[USER] Fetch General Manager List Success',
  UPDATE_USER = '[USERS] Update User',
  UPDATE_BULK_USERS = '[USERS] Update Bulk Users',
  BULK_UPDATE_PERMISSIONS = '[PERMISSIONS] Bulk Update Permissions',
  UPDATE_BULK_USERS_SUCCESS = '[USERS] Update Bulk Users Success',
  UPDATE_USER_PROFILE = '[USERS] Update User Profile',
  DOES_USERNAME_EXISTS = '[USERS] DOES USERNAME EXISTS',
  DOES_USERNAME_EXISTS_SUCCESS = '[USERS] DOES USERNAME EXISTS SUCCESS',
  DOES_EMAIL_EXISTS = '[USERS] DOES EMAIL EXISTS',
  DOES_EMAIL_EXISTS_SUCCESS = '[USERS] DOES EMAIL EXISTS SUCCESS',
  DOES_PHONENO_EXISTS = '[USERS] DOES PHONENO EXISTS',
  DOES_PHONENO_EXISTS_SUCCESS = '[USERS] DOES PHONENO EXISTS SUCCESS',
  USER_EXCEL_UPLOAD = '[USERS] Upload Users Excel File',
  USER_EXCEL_UPLOAD_SUCCESS = '[USERS] Upload Users Excel File Success',
  UPLOAD_USER_DOCUMENTS = '[USERS] Upload User Documents',
  DELETE_USER_DOCUMENTS = '[USERS] Delete User Documents',
  DELETE_USER_ASSIGNMENT = '[USERS] Delete User Assignment',
  UPDATE_IMG = '[PROFILE] Update Image',
  FETCH_USER_BASIC_DETAILS_BY_ID = '[USERS] Fetch User Basic Details By Id',
  FETCH_USER_BASIC_DETAILS_BY_ID_SUCCESS = '[USERS] Fetch User Assignments By Id Success',
  FETCH_USER_ASSIGNMENTS_BY_ID = '[USERS] Fetch User Assignments By Id',
  FETCH_USER_ASSIGNMENTS_BY_ID_SUCCESS = '[USERS] Fetch User Basic Details By Id Success',
  TOGGLE_AUTOMATION = '[USERS] Toggle Automation',
  FETCH_REPORTEES = '[USERS] Fetch Reportees',
  FETCH_REPORTEES_SUCCESS = '[USERS] Fetch Reportees Success',
  FETCH_ADMINS_AND_REPORTEES = '[USERS] Fetch Admins and Reportees',
  FETCH_ADMINS_AND_REPORTEES_SUCCESS = '[USERS] Fetch Admins and Reportees Success',
  FETCH_ONLY_REPORTEES = '[USERS] Fetch Only Reportees',
  FETCH_ONLY_REPORTEES_SUCCESS = '[USERS] Fetch Only Reportees Success',
  FETCH_ONLY_REPORTEES_WITH_INACTIVE = '[USERS] Fetch Only Reportees With Reportees',
  FETCH_ONLY_REPORTEES_WITH_INACTIVE_SUCCESS = '[USERS] Fetch Only Reportees With Reportees Success',
  FETCH_BULK_USER_LEAD_COUNT = '[USERS] Fetch bulk user lead count',
  FETCH_BULK_USER_LEAD_COUNT_SUCCESS = '[USERS] Fetch bulk user lead count Success',
  FETCH_IVR_SETTING_LIST = '[USERS] Fetch IVR Setting User List',
  FETCH_IVR_SETTING_LIST_SUCCESS = '[USERS] Fetch IVR Setting User List Success',
  FETCH_IVR_SETTING_LIST_ID = '[USERS] Fetch IVR Setting User List By ID',
  FETCH_IVR_SETTING_LIST_ID_SUCCESS = '[USERS] Fetch IVR Setting User List By ID Success',
  UPDATE_IVR_SETTING = '[USERS] Update IVR Setting User List',
  FETCH_ONLY_ADMINS = '[USERS] Fetch Only Admins',
  FETCH_ONLY_ADMINS_SUCCESS = '[USERS] Fetch Only Admins Success',
  BULK_TOGGLE_MFA = '[USERS] Bulk Toggle MFA',
  BULK_TOGGLE_MFA_SUCCESS = '[USERS] Bulk Toggle MFA Success',
  EXPORT_USERS = '[USERS] Export Users',
  EXPORT_USERS_SUCCESS = '[USERS] Export Users Success',
  EXPORT_USERS_TRACKER = '[USERS] Export Users Tracker',
  EXPORT_USERS_TRACKER_SUCCESS = '[USERS] Export Users Tracker Success',
  FETCH_WITHOUT_ADMINS = '[USERS] Fetch Without Admins',
  FETCH_WITHOUT_ADMINS_SUCCESS = '[USERS] Fetch Without Admins Success',
  DELETE_USER_ID = '[USERS] Delete User by Id',
  DELETE_USER_ID_SUCCESS = '[USERS] Delete User by Id Success',
  FETCH_REPORTING_MANAGER_DETAILS = '[USERS] Fetch reporting manager details',
  FETCH_REPORTING_MANAGER_DETAILS_SUCCESS = '[USERS] Fetch reporting manager details success',
  FETCH_TEAM_LIST = '[TEAMS] Fetch Team List',
  FETCH_TEAM_LIST_SUCCESS = '[TEAMS] Fetch Team List Success',
  ADD_TEAMS = '[TEAMS] Add Teams',
  UPDATE_TEAMS = '[TEAMS] Update Teams',
  DELETE_TEAMS = '[TEAMS] Delete Teams',
  FETCH_TEAM_MEMBERS = '[TEAMS] Fetch Team Members',
  FETCH_TEAM_MEMBERS_SUCCESS = '[TEAMS] Fetch Team Members Success',
  FETCH_UNASSIGNED_USERS = '[TEAMS] Fetch Unassigned Users',
  FETCH_UNASSIGNED_USERS_SUCCESS = '[TEAMS] Fetch Unassigned Users Success',
  EXPORT_TEAMS = '[TEAMS] Export Teams',
  EXPORT_TEAMS_SUCCESS = '[TEAMS] Export Teams Success',
  DELETE_TEAM_MEMBER = '[TEAMS] Delete Team Members',
  DELETE_TEAM_MEMBER_SUCCESS = '[TEAMS] Delete Team Members Success',
  FETCH_ALL_TEAMS = '[TEAMS] Fetch all Teams',
  FETCH_ALL_TEAMS_SUCCESS = '[TEAMS] Fetch all Teams Success',
  ADD_RETENTION = '[TEAMS] Add Retention',
  FETCH_RETENTION_LIST = '[TEAMS] Fetch Retention List',
  FETCH_RETENTION_LIST_SUCCESS = '[TEAMS] Fetch Retention List Success ',
  TEAMS_FILTER_PAYLOAD = '[USERS] Teams Filter Payload',
  TEAMS_MEMBER_FILTER_PAYLOAD = '[USERS] Teams Member Filter Payload',
  UPDATE_RETENTION = '[TEAMS] Update Retention',
  // UPDATE_RETENTION_SUCCESS ='[TEAMS] Update Retention Success',
  DELETE_RETENTION = '[TEAMS] Delete Retention',
  // DELETE_RETENTION_SUCCESS = '[TEAMS] Delete Retention Success',
  EXPORT_TEAMS_TRACKER = '[TEAMS] Export Teams Tracker',
  EXPORT_TEAMS_TRACKER_SUCCESS = '[TEAMS] Export Teams Tracker Success',
  DELETE_ASSIGNED_USER = '[USERS] Delete Assigned User',
  DELETE_ASSIGNED_USER_SUCCESS = '[USERS] Delete Assigned User Success',
  FETCH_USER_ASSIGNED_DATA_BY_ID = '[USERS] Fetch User Assigned data By Id',
  FETCH_USER_ASSIGNED_DATA_BY_ID_SUCCESS = '[USERS] Fetch User Assigned data By Id Success',
  FETCH_DELETED_USER_LIST = '[USERS] Fetch Deleted User List',
  FETCH_DELETED_USER_LIST_SUCCESS = '[USERS] Fetch Deleted User List Success',
  FETCH_USERS_BY_DESIGNATION = '[USER] Fetch Users By Designation',
  FETCH_USERS_BY_DESIGNATION_SUCCESS = '[USER] Fetch Users By Designation Success',
  UPLOAD_MAPPED_COLUMNS = '[USER] Upload Mapped Columns',
  FETCH_EXCEL_UPLOADED_LIST = '[USER] Fetch Excel Uploaded List',
  FETCH_EXCEL_UPLOADED_LIST_SUCCESS = '[USER] Fetch Excel Uploaded List Success',
  DELETE_ROLES = '[USER] Delete Roles',
  DELETE_ROLES_SUCCESS = '[USER] Delete Roles Success',
  FETCH_WHATSAPP_SETTING_LIST = '[USERS] Fetch Whatsapp Setting User List',
  FETCH_WHATSAPP_SETTING_LIST_SUCCESS = '[USERS] Fetch Whatsapp Setting User List Success',
  UPDATE_WHATSAPP_SETTING = '[USERS] Update Whatsapp Setting User List',
  UPDATE_USER_SEARCH = '[USERS] Update User Search',
  UPDATE_USER_SEARCH_SUCCESS = '[USERS] Update User Search Success',
  FETCH_RECENT_SEARCH = '[USERS] Fetch Recent Search',
  FETCH_RECENT_SEARCH_SUCCESS = '[USERS] Fetch Recent Search Success',
  TOGGLE_GEO_FENCING = '[USERS] Toggle Geo Fencing',
  ADD_GEO_FENCING = '[USERS] Add Geo Fencing',
  ADD_GEO_FENCING_SUCCESS = '[USERS] Add Geo Fencing Success',
  UPDATE_GEO_FENCING = '[USERS] Update Geo Fencing',
  UPDATE_GEO_FENCING_SUCCESS = '[USERS] Update Geo Fencing Success',
  FETCH_GEO_FENCING_LIST = '[USERS] Fetch Geo Fencing List',
  FETCH_GEO_FENCING_LIST_SUCCESS = '[USERS] Fetch Geo Fencing List Success',
  FETCH_ROTATION_LIST = '[TEAMS] Fetch Rotation List',
  FETCH_ROTATION_LIST_SUCCESS = '[TEAMS] Fetch Rotation List Success',
}
export class AddRole implements Action {
  readonly type: string = TeamsActionTypes.ADD_ROLE;
  constructor(public payload: any) { }
}
export class FetchRolesList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ROLES_LIST;
  constructor(public payload?: any) { }
}
export class FetchManageUserRolesList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_MANAGE_USER_ROLES_LIST;
  constructor() { }
}
export class FetchRolesListSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ROLES_LIST_SUCCESS;
  constructor(public response: any[]) { }
}
export class FetchRolesById implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ROLES_BY_ID;
  constructor(public roleId: string) { }
}
export class FetchRolesByIdSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ROLES_BY_ID_SUCCESS;
  constructor(public response: any[]) { }
}
export class DeleteRole implements Action {
  readonly type: string = TeamsActionTypes.DELETE_ROLE;
  constructor(
    public roleId: string,
    public pageNumber: any,
    public pageSize: any,
    public searchName: any
  ) { }
}

export class DeleteRoles implements Action {
  readonly type: string = TeamsActionTypes.DELETE_ROLES;
  constructor(public roleId: string, public payload: string[]) { }
}

export class DeleteRolesSuccess implements Action {
  readonly type: string = TeamsActionTypes.DELETE_ROLES_SUCCESS;
  constructor(public response: any[]) { }
}
export class FetchRolePermissionById implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ROLES_PERMISSION_BY_ID;
  constructor(public roleId: string) { }
}

export class FetchRolePermissionByIdSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ROLES_PERMISSION_BY_ID_SUCCESS;
  constructor(public response: any) { }
}
export class UpdateRolePermissionById implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_ROLES_PERMISSION_BY_ID;
  constructor(public roleId: string, public payload: any) { }
}

export class UpdateRolePermissionByIdSuccess implements Action {
  readonly type: string =
    TeamsActionTypes.UPDATE_ROLES_PERMISSION_BY_ID_SUCCESS;
  constructor(public response: any[]) { }
}
export class FetchAllExistingPermission implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ALL_EXISTING_PERMISSIONS;
  constructor() { }
}

export class FetchAllExistingPermissionSuccess implements Action {
  readonly type: string =
    TeamsActionTypes.FETCH_ALL_EXISTING_PERMISSIONS_SUCCESS;
  constructor(public response: any = {}) { }
}

// --------------------User Actions-------------------------

export class FetchUsersList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USERS_LIST;
  constructor() { }
}
export class FetchUsersListSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USERS_LIST_SUCCESS;
  constructor(public response: any = []) { }
}
export class UpdateFilterPayload implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_FILTER_PAYLOAD;
  constructor(public filter: UserFilter) { }
}
export class FetchUsersListForReassignment implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USERS_LIST_FOR_REASSIGNMENT;
  constructor() { }
}
export class FetchUsersListForReassignmentSuccess implements Action {
  readonly type: string =
    TeamsActionTypes.FETCH_USERS_LIST_FOR_REASSIGNMENT_SUCCESS;
  constructor(public response: any = []) { }
}
export class AddUser implements Action {
  readonly type: string = TeamsActionTypes.ADD_USER;
  constructor(public payload: any) { }
}

export class UpdateUser implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_USER;
  constructor(public userId: string, public payload: any) { }
}
export class UpdateBulkUsers implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_BULK_USERS;
  constructor(public payload: any) { }
}
export class UpdateBulkUsersSuccess implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_BULK_USERS_SUCCESS;
  constructor() { }
}

export class UpdateUserProfile implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_USER_PROFILE;
  constructor(
    public userId: string,
    public payload: any,
    public shouldFetchUserById: boolean = false
  ) { }
}
//active and deactivate
export class DeleteUser implements Action {
  readonly type: string = TeamsActionTypes.DELETE_USER;
  constructor(public payload: any) { }
}
export class DeleteUserSuccess implements Action {
  readonly type: string = TeamsActionTypes.DELETE_USER_SUCCESS;
  constructor() { }
}
// export class DeleteUserId implements Action {
//   readonly type: string = TeamsActionTypes.DELETE_USER_ID;
//   constructor(public userId: string) { }
// }
// export class DeleteUserIdSuccess implements Action {
//   readonly type: string = TeamsActionTypes.DELETE_USER_ID_SUCCESS;
//   constructor() { }
// }
export class BulkToggleUserStatus implements Action {
  readonly type: string = TeamsActionTypes.BULK_TOGGLE_USER_STATUS;
  constructor(public payload: any, public userStatus: string) { }
}
export class BulkToggleUserStatusSuccess implements Action {
  readonly type: string = TeamsActionTypes.BULK_TOGGLE_USER_STATUS_SUCCESS;
  constructor() { }
}
export class FetchUserById implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USER_BY_ID;
  constructor(public userId: string) { }
}
export class FetchUserByIdSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USER_BY_ID_SUCCESS;
  constructor(public response: any = {}) { }
}
export class FetchUserProfile implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USER_PROFILE;
  constructor(public userId: string) { }
}
export class FetchUserProfileSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USER_PROFILE_SUCCESS;
  constructor(public response: any = {}) { }
}
export class UpdateUserRoleById implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_ROLES_PERMISSION_BY_ID;
  constructor(public roleId: string) { }
}

//   export class UpdateRolePermissionByIdSuccess implements Action {
//     readonly type: string =
//       TeamsActionTypes.UPDATE_ROLES_PERMISSION_BY_ID_SUCCESS;
//     constructor(public response: any[]) {}
//   }

export class UploadUserDocuments implements Action {
  readonly type: string = TeamsActionTypes.UPLOAD_USER_DOCUMENTS;
  constructor(public payload: any, public userId: string) { }
}

export class DeleteUserDocuments implements Action {
  readonly type: string = TeamsActionTypes.DELETE_USER_DOCUMENTS;
  constructor(public id: string) { }
}
export class BulkUpdatePermissions implements Action {
  readonly type: string = TeamsActionTypes.BULK_UPDATE_PERMISSIONS;
  constructor(public payload: any) { }
}

export class FetchGeneralManagerList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_GENERAL_MANAGER_LIST;
  constructor() { }
}
export class FetchGeneralManagerListSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_GENERAL_MANAGER_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

// ---------Designation Actions

export class FetchDesignationsList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_DESIGNATION_LIST;
  constructor() { }
}
export class FetchDesignationsListSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_DESIGNATION_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}
export class AddDesignation implements Action {
  readonly type: string = TeamsActionTypes.ADD_DESIGNATION;
  constructor(public designation: string) { }
}
export class DeleteDesignation implements Action {
  readonly type: string = TeamsActionTypes.DELETE_DESIGNATION;
  constructor(public designationId: string) { }
}

// ---------Department Actions

export class FetchDepartmentsList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_DEPARTMENT_LIST;
  constructor() { }
}
export class FetchDepartmentsListSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_DEPARTMENT_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}
export class AddDepartment implements Action {
  readonly type: string = TeamsActionTypes.ADD_DEPARTMENT;
  constructor(public department: string) { }
}
export class DeleteDepartment implements Action {
  readonly type: string = TeamsActionTypes.DELETE_DESIGNATION;
  constructor(public designationId: string) { }
}
export class DoesUsernameExists implements Action {
  readonly type: string = TeamsActionTypes.DOES_USERNAME_EXISTS;
  constructor(public userName: string) { }
}
export class DoesUsrnameExistsSuccess implements Action {
  readonly type: string = TeamsActionTypes.DOES_USERNAME_EXISTS_SUCCESS;
  constructor(public response: boolean) { }
}
export class DoesEmailExists implements Action {
  readonly type: string = TeamsActionTypes.DOES_EMAIL_EXISTS;
  constructor(public email: string) { }
}
export class DoesEmailExistsSuccess implements Action {
  readonly type: string = TeamsActionTypes.DOES_EMAIL_EXISTS_SUCCESS;
  constructor(public response: boolean) { }
}
export class DoesPhoneNoExists implements Action {
  readonly type: string = TeamsActionTypes.DOES_PHONENO_EXISTS;
  constructor(public phone: string) { }
}
export class DoesPhoneNoExistsSuccess implements Action {
  readonly type: string = TeamsActionTypes.DOES_PHONENO_EXISTS_SUCCESS;
  constructor(public response: boolean) { }
}
export class UserExcelUpload implements Action {
  readonly type = TeamsActionTypes.USER_EXCEL_UPLOAD;
  constructor(public file: File) { }
}
export class UserExcelUploadSuccess implements Action {
  readonly type = TeamsActionTypes.USER_EXCEL_UPLOAD_SUCCESS;
  constructor(public resp: any) { }
}
export class UpdateImg implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_IMG;
  constructor(public url: string) { }
}

export class FetchUserBasicDetailsById implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USER_BASIC_DETAILS_BY_ID;
  constructor(public userId: string) { }
}
export class FetchUserBasicDetailsByIdSuccess implements Action {
  readonly type: string =
    TeamsActionTypes.FETCH_USER_BASIC_DETAILS_BY_ID_SUCCESS;
  constructor(public response: any = {}) { }
}

export class FetchUserAssignmentsById implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USER_ASSIGNMENTS_BY_ID;
  constructor(public userId: string = '') { }
}
export class FetchUserAssignmentsByIdSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USER_ASSIGNMENTS_BY_ID_SUCCESS;
  constructor(public response: any = {}) { }
}

export class ToggleAutomation implements Action {
  readonly type: string = TeamsActionTypes.TOGGLE_AUTOMATION;
  constructor(public userIds: Array<String> = []) { }
}

export class FetchReportees implements Action {
  readonly type: string = TeamsActionTypes.FETCH_REPORTEES;
  constructor() { }
}

export class FetchReporteesSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_REPORTEES_SUCCESS;
  constructor(public response: any = []) { }
}

export class FetchAdminsAndReportees implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ADMINS_AND_REPORTEES;
  constructor() { }
}

export class FetchAdminsAndReporteesSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ADMINS_AND_REPORTEES_SUCCESS;
  constructor(public response: any = []) { }
}

export class FetchOnlyReportees implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ONLY_REPORTEES;
  constructor() { }
}

export class FetchOnlyReporteesSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ONLY_REPORTEES_SUCCESS;
  constructor(public response: any = []) { }
}

export class FetchOnlyReporteesWithInactive implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ONLY_REPORTEES_WITH_INACTIVE;
  constructor() { }
}

export class FetchOnlyReporteesWithInactiveSuccess implements Action {
  readonly type: string =
    TeamsActionTypes.FETCH_ONLY_REPORTEES_WITH_INACTIVE_SUCCESS;
  constructor(public response: any = []) { }
}

export class DeleteUserAssignment implements Action {
  readonly type: string = TeamsActionTypes.DELETE_USER_ASSIGNMENT;
  constructor(public payload: string) { }
}

export class FetchBulkUserLeadCount implements Action {
  readonly type: string = TeamsActionTypes.FETCH_BULK_USER_LEAD_COUNT;
  constructor(public payload: any = []) { }
}

export class FetchBulkUserLeadCountSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_BULK_USER_LEAD_COUNT_SUCCESS;
  constructor(public response: any = []) { }
}

export class FetchIVRSettingList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_IVR_SETTING_LIST;
  constructor() { }
}

export class FetchIVRSettingListSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_IVR_SETTING_LIST_SUCCESS;
  constructor(public response: any = []) { }
}

export class FetchIVRSettingListById implements Action {
  readonly type: string = TeamsActionTypes.FETCH_IVR_SETTING_LIST_ID;
  constructor(public id: string) { }
}

export class FetchIVRSettingListByIdSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_IVR_SETTING_LIST_ID_SUCCESS;
  constructor(public response: any = []) { }
}

export class UpdateIVRSettingList implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_IVR_SETTING;
  constructor(public payload: any) { }
}

export class FetchOnlyAdmins implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ONLY_ADMINS;
  constructor() { }
}

export class FetchOnlyAdminsSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ONLY_ADMINS_SUCCESS;
  constructor(public response: any = []) { }
}

export class BulkToggleMFA implements Action {
  readonly type: string = TeamsActionTypes.BULK_TOGGLE_MFA;
  constructor(public payload: any) { }
}
export class BulkToggleMFASuccess implements Action {
  readonly type: string = TeamsActionTypes.BULK_TOGGLE_MFA_SUCCESS;
  constructor() { }
}

export class ExportUsers implements Action {
  readonly type: string = TeamsActionTypes.EXPORT_USERS;
  constructor(public payload: any) { }
}

export class ExportUsersSuccess implements Action {
  readonly type: string = TeamsActionTypes.EXPORT_USERS_SUCCESS;
  constructor(public resp: string = '') { }
}

export class FetchUserExportTracker implements Action {
  readonly type: string = TeamsActionTypes.EXPORT_USERS_TRACKER;
  constructor(public pageNumber: number, public pageSize: number) { }
}

export class FetchUserExportTrackerSuccess implements Action {
  readonly type: string = TeamsActionTypes.EXPORT_USERS_TRACKER_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchWithoutAdmins implements Action {
  readonly type: string = TeamsActionTypes.FETCH_WITHOUT_ADMINS;
  constructor() { }
}

export class FetchWithoutAdminsSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_WITHOUT_ADMINS_SUCCESS;
  constructor(public response: any = []) { }
}

export class FetchReportingManagerDetails implements Action {
  readonly type: string = TeamsActionTypes.FETCH_REPORTING_MANAGER_DETAILS;
  constructor(public id: string) { }
}

export class FetchReportingManagerDetailsSuccess implements Action {
  readonly type: string =
    TeamsActionTypes.FETCH_REPORTING_MANAGER_DETAILS_SUCCESS;
  constructor(public managerDetails: any) { }
}
// ---------Team Actions

export class FetchTeamList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_TEAM_LIST;
  constructor(public payload: any) { }
}

export class FetchTeamListSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_TEAM_LIST_SUCCESS;
  constructor(public response: any) { }
}

export class AddTeams implements Action {
  readonly type: string = TeamsActionTypes.ADD_TEAMS;
  constructor(public payload: any) { }
}

export class UpdateTeams implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_TEAMS;
  constructor(public payload: any) { }
}

export class DeleteTeams implements Action {
  readonly type: string = TeamsActionTypes.DELETE_TEAMS;
  constructor(public ids: string[]) { }
}

export class FetchTeamMember implements Action {
  readonly type: string = TeamsActionTypes.FETCH_TEAM_MEMBERS;
  constructor(public payload: any) { }
}

export class FetchTeamMemberSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_TEAM_MEMBERS_SUCCESS;
  constructor(public response: any) { }
}

export class FetchUnassignedUser implements Action {
  readonly type: string = TeamsActionTypes.FETCH_UNASSIGNED_USERS;
  constructor() { }
}

export class FetchUnassignedUserSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_UNASSIGNED_USERS_SUCCESS;
  constructor(public response: any = []) { }
}

export class ExportTeams implements Action {
  readonly type: string = TeamsActionTypes.EXPORT_TEAMS;
  constructor(public payload: any) { }
}

export class ExportTeamsSuccess implements Action {
  readonly type: string = TeamsActionTypes.EXPORT_TEAMS_SUCCESS;
  constructor(public resp: string = '') { }
}

export class DeleteMember implements Action {
  readonly type: string = TeamsActionTypes.DELETE_TEAM_MEMBER;
  constructor(public payload: any) { }
}

export class DeleteMemberSuccess implements Action {
  readonly type: string = TeamsActionTypes.DELETE_TEAM_MEMBER_SUCCESS;
  constructor(public resp: string = '') { }
}

export class FetchAllTeams implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ALL_TEAMS;
  constructor() { }
}

export class FetchAllTeamsSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ALL_TEAMS_SUCCESS;
  constructor(public resp: any = []) { }
}

export class AddRetention implements Action {
  readonly type: string = TeamsActionTypes.ADD_RETENTION;
  constructor(public payload: any) { }
}

export class FetchRetentionList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_RETENTION_LIST;
  constructor() { }
}

export class FetchRetentionListSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_RETENTION_LIST_SUCCESS;
  constructor(public resp: any = []) { }
}
export class TeamsFilterPayload implements Action {
  readonly type: string = TeamsActionTypes.TEAMS_FILTER_PAYLOAD;
  constructor(public payload: any) { }
}

export class TeamsMemberFilterPayload implements Action {
  readonly type: string = TeamsActionTypes.TEAMS_MEMBER_FILTER_PAYLOAD;
  constructor(public payload: any) { }
}

export class UpdateRetention implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_RETENTION;
  constructor(public payload: any) { }
}

export class DeleteRetention implements Action {
  readonly type: string = TeamsActionTypes.DELETE_RETENTION;
  constructor(public id: string) { }
}
export class FetchTeamExportTracker implements Action {
  readonly type: string = TeamsActionTypes.EXPORT_TEAMS_TRACKER;
  constructor(public pageNumber: number, public pageSize: number) { }
}

export class FetchTeamExportTrackerSuccess implements Action {
  readonly type: string = TeamsActionTypes.EXPORT_TEAMS_TRACKER_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class DeleteAssignedUser implements Action {
  readonly type: string = TeamsActionTypes.DELETE_ASSIGNED_USER;
  constructor(public payload: any) { }
}

export class DeleteAssignedUserSuccess implements Action {
  readonly type: string = TeamsActionTypes.DELETE_ASSIGNED_USER_SUCCESS;
  constructor() { }
}

export class FetchUserAssignedDataById implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USER_ASSIGNED_DATA_BY_ID;
  constructor(public userId: string = '') { }
}
export class FetchUserAssignedDataByIdSuccess implements Action {
  readonly type: string =
    TeamsActionTypes.FETCH_USER_ASSIGNED_DATA_BY_ID_SUCCESS;
  constructor(public response: any = {}) { }
}

export class FetchDeletedUserList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_DELETED_USER_LIST;
  constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchDeletedUserListSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_DELETED_USER_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchUsersByDesignation implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USERS_BY_DESIGNATION;
  constructor() { }
}
export class FetchUsersByDesignationSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_USERS_BY_DESIGNATION_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class UploadMappedColumns implements Action {
  readonly type: string = TeamsActionTypes.UPLOAD_MAPPED_COLUMNS;
  constructor(public payload: any) { }
}

export class FetchUserExcelUploadedList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_EXCEL_UPLOADED_LIST;
  constructor(public pageNumber: number, public pageSize: number) { }
}

export class FetchUserExcelUploadedSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_EXCEL_UPLOADED_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchWhatsappSettingList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_WHATSAPP_SETTING_LIST;
  constructor() { }
}

export class FetchWhatsappSettingListSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_WHATSAPP_SETTING_LIST_SUCCESS;
  constructor(public response: any = []) { }
}

export class UpdateWhatsappSettingList implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_WHATSAPP_SETTING;
  constructor(public payload: any) { }
}

export class FetchRecentSearch implements Action {
  readonly type: string = TeamsActionTypes.FETCH_RECENT_SEARCH;
  constructor(public moduleType: string) { }
}

export class FetchRecentSearchSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_RECENT_SEARCH_SUCCESS;
  constructor(public response: any = []) { }
}

export class UpdateUserSearch implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_USER_SEARCH;
  constructor(public payload: any) { }
}

export class UpdateUserSearchSuccess implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_USER_SEARCH_SUCCESS;
  constructor(public response: any = []) { }
}

export class ToggleGeoFencing implements Action {
  readonly type: string = TeamsActionTypes.TOGGLE_GEO_FENCING;
  constructor(public payload: any) { }
}

export class AddGeoFencing implements Action {
  readonly type: string = TeamsActionTypes.ADD_GEO_FENCING;
  constructor(public payload: any) { }
}

export class AddGeoFencingSuccess implements Action {
  readonly type: string = TeamsActionTypes.ADD_GEO_FENCING_SUCCESS;
  constructor(public response: any) { }
}

export class UpdateGeoFencing implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_GEO_FENCING;
  constructor(public payload: any) { }
}

export class UpdateGeoFencingSuccess implements Action {
  readonly type: string = TeamsActionTypes.UPDATE_GEO_FENCING_SUCCESS;
  constructor(public response: any) { }
}

export class FetchGeoFencingList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_GEO_FENCING_LIST;
  constructor(public userId: string) { }
}

export class FetchGeoFencingListSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_GEO_FENCING_LIST_SUCCESS;
  constructor(public response: any = []) { }
}

export class FetchRotationList implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ROTATION_LIST;
  constructor() { }
}

export class FetchRotationListSuccess implements Action {
  readonly type: string = TeamsActionTypes.FETCH_ROTATION_LIST_SUCCESS;
  constructor(public response: any = []) { }
}
