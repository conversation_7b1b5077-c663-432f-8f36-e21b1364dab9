import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { combineLatest, takeUntil } from 'rxjs';

import {
  BHK_NO,
  BHK_TYPE,
  FURNISH_STATUS,
  GENDER,
  MARITAL_STATUS,
  NUMBER_10,
  OFFERING_TYPE,
  PURPOSE_LIST,
  TRANSACTION_TYPE_LIST,
} from 'src/app/app.constants';
import { DataDateType, EnquiryType, Profession } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { DataTopFilter } from 'src/app/core/interfaces/data-management.interface';
import {
  assignToSort,
  changeCalendar,
  formatBudget,
  generateFloorOptions,
  getBHKDisplayString,
  getBRDisplayString,
  onlyN<PERSON><PERSON>,
  onlyNumbersWithDecimal,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { FetchAgencyNameList } from 'src/app/reducers/Integration/integration.actions';
import {
  getAgencyNameList,
  getAgencyNameListIsLoading,
} from 'src/app/reducers/Integration/integration.reducer';
import {
  ClearData,
  FetchAllData,
  FetchDataAltCountryCode,
  FetchDataCities,
  FetchDataClusterName,
  FetchDataCommunities,
  FetchDataCountries,
  FetchDataCountryCode,
  FetchDataCurrency,
  FetchDataCustomStatusFilter,
  FetchDataLandLine,
  FetchDataLocalities,
  FetchDataLocations,
  FetchDataNationality,
  FetchDataSourceList,
  FetchDataStates,
  FetchDataSubCommunities,
  FetchDataSubSourceList,
  FetchDataTopFilters,
  FetchDataTowerNames,
  FetchDataUnitName,
  FetchUploadTypeNameList,
  UpdateDataFilterPayload,
} from 'src/app/reducers/data/data-management.actions';
import {
  DataManagementFilters,
  getchDataCommunities,
  getDataAltCountryCode,
  getDataAltCountryCodeLoading,
  getDataCities,
  getDataCitiesIsLoading,
  getDataClusterName,
  getDataClusterNameLoading,
  getDataCommunitiesIsLoading,
  getDataCountries,
  getDataCountriesIsLoading,
  getDataCountryCode,
  getDataCountryCodeLoading,
  getDataCurrencyList,
  getDataCurrencyListIsLoading,
  getDataFiltersPayload,
  getDataLandLine,
  getDataLandLineLoading,
  getDataLocalities,
  getDataLocalitiesIsLoading,
  getDataLocations,
  getDataLocationsIsLoading,
  getDataNationality,
  getDataNationalityLoading,
  getDataPostalCode,
  getDataPostalCodeLoading,
  getDataSourceList,
  getDataStates,
  getDataStatesIsLoading,
  getDataSubCommunities,
  getDataSubCommunitiesIsLoading,
  getDataSubSourceList,
  getDataSubSourceListIsLoading,
  getDataTowerNames,
  getDataTowerNamesIsLoading,
  getDataUnitName,
  getDataUnitNameLoading,
  getUploadTypeNameList,
  getUploadTypeNameListIsLoading,
} from 'src/app/reducers/data/data-management.reducer';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchCampaignList,
  FetchChannelPartnerList,
  FetchPropertyList,
} from 'src/app/reducers/lead/lead.actions';
import {
  getCampaignList,
  getCampaignListIsLoading,
  getChannelPartnerList,
  getChannelPartnerListIsLoading,
  getProjectList,
  getPropertyList,
  getPropertyListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import {
  getAreaUnitIsLoading,
  getAreaUnits,
} from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getReportees,
  getReporteesIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { TrackingService } from 'src/app/services/shared/tracking.service';
@Component({
  selector: 'manage-data-advance-filters',
  templateUrl: './manage-data-advance-filters.component.html',
})
export class ManageDataAdvanceFiltersComponent implements OnInit, OnDestroy {
  @ViewChild('possessionFilter') possessionFilter: any;
  @Input() filterData: any;
  advanceFilterForm: FormGroup;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  dateTypeList: Array<string> = Object.keys(DataDateType).slice(9).filter(type => type !== 'Possession Date');
  professions: { value: number; label: string | Profession }[] = Object.values(
    Profession
  )
    ?.slice(1, 10)
    ?.map((value, index) => ({
      value: index + 1,
      label: value,
    }));
  enquiryType: Array<any> = TRANSACTION_TYPE_LIST;
  propertyType: Array<string> = JSON.parse(
    localStorage.getItem('propertyType')
  );
  propertySubTypes: any[] = [];
  currency: any[] = [];
  defaultCurrency: string;
  noOfBhk: Array<string> = BHK_NO;
  bhkType: Array<string> = BHK_TYPE;
  states: any[] = [];
  statesIsLoading: boolean = false;
  filtersPayload: DataManagementFilters;
  dateType: string;
  filterDate: any;
  allUsers: any[] = [];
  reportees: any[] = [];
  statusList: any[] = [];
  sourceList: any[] = [];
  topFilters: DataTopFilter[] = [];
  subSourceList: any[] = [];
  propertyList: any[] = [];
  projectList: any[] = [];
  areaSizeUnits: any[] = [];
  agencyNameList: string[] = [];
  locations: string[];
  SubCommunities: string[];
  Communities: string[];
  TowerNames: string[];
  cities: string[];
  dataCurrencyList: any[];
  Localities: string[];
  isSourcesLoading: boolean = false;

  isSubSourceListLoading: boolean = true;
  isPropertyListLoading: boolean = true;
  isAreaSizeUnitsLoading: boolean = true;
  isAgencyNameListLoading: boolean = true;
  isDataCurrencyListLoading: boolean = true;
  isCitiesLoading: boolean = true;
  isLocationLoading: boolean = true;
  allSubSourceList: any;
  reporteesIsLoading: boolean = true;
  isChannelPartnerListLoading: boolean = true;
  channelPartnerList: Array<any>;
  onPickerOpened = onPickerOpened;
  formatBudget = formatBudget;
  getBHKDisplayString = getBHKDisplayString;
  getBRDisplayString = getBRDisplayString;
  floorOptions: string[] = generateFloorOptions();
  campaignList: any;
  isCampaignListLoading: boolean;
  userData: any;
  currentDate: Date = new Date();
  towerNames: string[];
  communitiesIsLoading: boolean = true;
  countries: string[];
  postalCodeList: string[];
  postalCodeIsLoading: boolean = true;
  landLineList: string[];
  landLineIsLoading: boolean = true;
  countriesIsLoading: boolean = true;
  subCommunities: string[];
  communities: string[];
  subCommunitiesIsLoading: boolean = true;
  localitesIsLoading: boolean = true;
  towerNamesIsLoading: boolean = true;
  globalSettingsData: any;
  // onFilterApplied: any;
  furnishStatus = FURNISH_STATUS;
  offerType: Array<{ displayName: string; value: number }> = OFFERING_TYPE;
  numbers: Array<{ display: string; value: number }> = NUMBER_10;
  numbers10: Array<{ display: string; value: number }> = NUMBER_10.slice(1);
  zonesIsLoading: boolean = true;
  zones: string[];
  isReassignmentIsLoading: any;
  localities: any;
  onlyNumbers = onlyNumbers;
  onlyNumbersWithDecimal = onlyNumbersWithDecimal;
  nationalities: any[] = [];
  nationalitiesIsLoading: boolean;
  clusterNames: any[] = [];
  isDataClusterLoading: boolean;
  unitNames: any[] = [];
  isDataUnitNameLoading: boolean;
  purposeList: Array<{ displayName: string; value: number }> = PURPOSE_LIST;
  carpetAreaValidations: boolean = true;
  buildUpAreaValidations: boolean = true;
  saleableValidation: boolean = true;
  netValidation: boolean = true;
  areaValidation: boolean = true;
  minBudgetValidation: boolean = true;
  maxBudgetValidation: boolean = true;
  uploadTypeList: any[] = [];
  isUploadTypeNameListIsLoading: boolean = false;
  canViewAllUsers: boolean = false;
  permission: Set<unknown>;
  countryCodes: any[] = [];
  isDataAltCountryCodeLoading: boolean;
  altCountryCodes: any[] = [];
  isDataCountryCodeLoading: boolean;
  genders: any[] = GENDER;
  maritalStatus: any[] = MARITAL_STATUS;
  @ViewChild('dt2') dt2: any;
  
  get maxDate() {
    const currentDate = new Date();
    currentDate.setDate(currentDate.getDate());
    return currentDate;
  }

  constructor(
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private fb: FormBuilder,
    public trackingService: TrackingService
  ) { }

  ngOnInit(): void {
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        this.permission = new Set(permissions);
        this.canViewAllUsers =
          this.permission.has('Permissions.Users.ViewForFilter') &&
          this.permission.has('Permissions.Prospects.ViewAllProspects');
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this.initializeForm();
    // this.advanceFilterForm.get('FromMinBudget').valueChanges.subscribe(() => this.minBudgetCheck());
    // this.advanceFilterForm.get('FromMaxBudget').valueChanges.subscribe(() => this.maxBudgetCheck());
    this.advanceFilterForm
      .get('Currency')
      ?.valueChanges.subscribe((newCurrency) => {
        this.advanceFilterForm
          .get('Currency')
          ?.setValue(newCurrency, { emitEvent: false });
      });
    this.trackingService.trackFeature('Web.Data.Page.FilterBy.Visit');
    this.advanceFilterForm.valueChanges.subscribe((formValues) => {
      this.trackFormChanges(formValues);
    });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data;
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
        this.currency =
          data.countries && data.countries.length > 0
            ? data.countries[0].currencies.map((cur: any) => cur.symbol)
            : null;
        this.advanceFilterForm
          .get('Currency')
          .setValue(
            this.advanceFilterForm.get('Currency').value ?? this.defaultCurrency
          );
      });

    this.propertyType?.forEach((type: any) => {
      this.propertySubTypes = this.propertySubTypes.concat(type.childTypes);
    });
    this._store.dispatch(new FetchCampaignList());
    this._store.dispatch(new FetchUploadTypeNameList());

    this._store
      .select(getCampaignList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.campaignList = item
          ?.filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getUploadTypeNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.uploadTypeList = data || [];
      });

    this._store
      .select(getUploadTypeNameListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isUploadTypeNameListIsLoading = isLoading;
      });

    this._store
      .select(getCampaignListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isCampaignListLoading = isLoading;
      });
    this._store.dispatch(new FetchChannelPartnerList());
    this._store
      .select(getChannelPartnerList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.channelPartnerList = item
          ?.filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getChannelPartnerListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isChannelPartnerListLoading = isLoading;
      });

    this._store
      .select(getDataFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: DataManagementFilters) => {
        this.filtersPayload = filters;
        this.advanceFilterForm.patchValue({
          ...filters,
          Currency: filters.Currency ?? this.defaultCurrency,
          ...(this.filterData && Object.keys(this.filterData).length ? this.filterData?.filterCriteria : {})
        });
        this.dateType = DataDateType[filters.DateType];
        this.filterDate = [
          patchTimeZoneDate(
            filters?.FromDate,
            this.userData?.timeZoneInfo?.baseUTcOffset
          ),
          patchTimeZoneDate(
            filters?.ToDate,
            this.userData?.timeZoneInfo?.baseUTcOffset
          ),
        ];

        // Possession filter values are now handled by the shared component
      });

    this._store.dispatch(new FetchAllSources());
    this._store.dispatch(new FetchDataSourceList());
    this._store.dispatch(new FetchDataSubSourceList());
    this._store.dispatch(new FetchDataCountryCode());
    this._store.dispatch(new FetchDataAltCountryCode());

    const allSources$ = this._store.select(getAllSources);
    const dataSourceList$ = this._store.select(getDataSourceList);
    combineLatest([allSources$, dataSourceList$])
      .pipe(takeUntil(this.stopper))
      .subscribe(([leadSource, dataSource]) => {
        if (leadSource && dataSource) {
          const matched = dataSource
            .filter((data: any) =>
              leadSource.some((lead: any) => lead?.value === data?.value && lead?.isEnabled))
            .map((data: any) => {
              const matchedLead = leadSource.find((lead: any) => lead?.value === data?.value);
              return {
                ...data,
                isEnabled: matchedLead?.isEnabled ?? false,
                isDefault: matchedLead?.isDefault ?? false
              };
            });
          const sorted = matched.sort((a: any, b: any) => {
            if (a.isEnabled === b.isEnabled) {
              return a.displayName.localeCompare(b.displayName);
            }
            return a.isEnabled ? -1 : 1;
          });
          this.sourceList = sorted;
          this.updateSubSource()
        }
      });

    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });

    this._store
      .select(getDataSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.allSubSourceList = data;
        this.updateSubSource()
      });

    this._store
      .select(getDataSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isSubSourceListLoading = isLoading;
      });

    this._store.dispatch(new FetchPropertyList());
    this._store
      .select(getPropertyList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.propertyList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getPropertyListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isPropertyListLoading = isLoading;
      });

    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store.dispatch(new FetchAreaUnitList());
    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this._store
      .select(getAreaUnitIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isAreaSizeUnitsLoading = isLoading;
      });

    this._store.dispatch(new FetchAgencyNameList());
    this._store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agencyNameList = item
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getAgencyNameListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isAgencyNameListLoading = isLoading;
      });

    this._store.dispatch(new FetchDataCurrency());
    this._store
      .select(getDataCurrencyList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.dataCurrencyList = data;
      });

    this._store
      .select(getDataCurrencyListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isDataCurrencyListLoading = isLoading;
      });

    this._store.dispatch(new FetchDataLocations());
    this._store
      .select(getDataLocations)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.locations = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getDataLocationsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isLocationLoading = isLoading;
      });

    this._store.dispatch(new FetchDataCities());
    this._store
      .select(getDataCities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.cities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store.dispatch(new FetchDataStates());
    this._store
      .select(getDataStates)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.states = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getDataStatesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.statesIsLoading = data;
      });

    this._store
      .select(getDataCitiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isCitiesLoading = isLoading;
      });
    this._store.dispatch(new FetchDataCountries());

    this._store
      .select(getDataCountries)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countries = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getDataCountriesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.countriesIsLoading = data;
      });
    // this._store.dispatch(new FetchDataPostalCode());

    this._store
      .select(getDataPostalCode)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.postalCodeList = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getDataPostalCodeLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.postalCodeIsLoading = data;
      });

    this._store.dispatch(new FetchDataLandLine());
    this._store
      .select(getDataLandLine)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.landLineList = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getDataLandLineLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.landLineIsLoading = data;
      });
    // this._store.dispatch(new FetchDataZones());
    // this._store
    //   .select(getDataZones)
    //   .pipe(takeUntil(this.stopper))
    //   .subscribe((data: any) => {
    //     this.zones = data
    //       .filter((data: any) => data)
    //       .slice()
    //       .sort((a: any, b: any) => a.localeCompare(b));
    //   });
    // this._store
    //   .select(getDataZonesIsLoading)
    //   .pipe(takeUntil(this.stopper))
    //   .subscribe((data: boolean) => {
    //     this.zonesIsLoading = data;
    //   });

    this._store.dispatch(new FetchDataLocalities());
    this._store
      .select(getDataLocalities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.localities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getDataLocalitiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.localitesIsLoading = data;
      });
    this._store
      .select(getDataCountryCode)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryCodes = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getDataCountryCodeLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isDataCountryCodeLoading = data;
      });

    this._store
      .select(getDataAltCountryCode)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.altCountryCodes = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getDataAltCountryCodeLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isDataAltCountryCodeLoading = data;
      });
    if (this.globalSettingsData?.isCustomLeadFormEnabled) {
      this._store.dispatch(new FetchDataSubCommunities());
      this._store.dispatch(new FetchDataCommunities());
      this._store.dispatch(new FetchDataTowerNames());
      this._store.dispatch(new FetchDataNationality());
      this._store.dispatch(new FetchDataUnitName());
      this._store.dispatch(new FetchDataClusterName());
    }

    this._store
      .select(getDataUnitName)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.unitNames = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getDataUnitNameLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isDataUnitNameLoading = data;
      });

    this._store
      .select(getDataClusterName)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.clusterNames = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getDataClusterNameLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isDataClusterLoading = data;
      });

    this._store
      .select(getDataNationality)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.nationalities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getDataNationalityLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.nationalitiesIsLoading = data;
      });

    this._store
      .select(getDataSubCommunities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.subCommunities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getDataSubCommunitiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.subCommunitiesIsLoading = data;
      });
    this._store
      .select(getchDataCommunities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.communities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getDataCommunitiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.communitiesIsLoading = data;
      });
    this._store
      .select(getDataTowerNames)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.towerNames = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getDataTowerNamesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.towerNamesIsLoading = data;
      });

    this._store.dispatch(new FetchUsersListForReassignment());
    this._store.dispatch(new FetchReportees());

    this._store
      .select(getReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.reportees = data;
        this.reportees = this.reportees?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.reportees = assignToSort(this.reportees, '');
      });

    this._store
      .select(getReporteesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.reporteesIsLoading = data;
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((users: any) => {
        this.allUsers = users?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allUsers = assignToSort(this.allUsers, '');
      });

    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isReassignmentIsLoading = isLoading;
      });
  }

  onDataFilterChange(filter: DataTopFilter): void {
    this.advanceFilterForm.patchValue({
      ProspectVisiblity: filter?.enumValue,
    });
  }

  dateChange(): void {
    if (this.dateType && this.filterDate?.[0]) {
      const fromDate = setTimeZoneDate(
        this.filterDate?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      );
      const toDate = setTimeZoneDate(
        this.filterDate?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      );
      this.advanceFilterForm.patchValue({
        DateType: DataDateType[this.dateType as keyof typeof DataDateType],
        FromDate: fromDate,
        ToDate: toDate,
      });
    }
    if (this.dateType) {
      this.trackingService.trackFeature(
        `Web.Data.Filter.${this.dateType.replace(/\s+/g, '')}.Click`
      );
    } else if (this.filterDate?.[0]) {
      this.trackingService.trackFeature(`Web.Data.Filter.CalenderRange.Click`);
    }
  }

  applyAdvancedFilter() {
    if (
      !this.minBudgetValidation ||
      !this.maxBudgetValidation ||
      !this.buildUpAreaValidations ||
      !this.carpetAreaValidations ||
      !this.saleableValidation ||
      !this.areaValidation ||
      !this.netValidation
    )
      return;
    this.filtersPayload = {
      ...this.filtersPayload,
      ...this.advanceFilterForm.value,
      PageNumber: 1,
      EnquiryTypes: this.advanceFilterForm.get('EnquiryTypes')?.value?.map((enquiry: any) => EnquiryType[enquiry as keyof typeof EnquiryType]),
      DateOfBirth: this.advanceFilterForm.get('DateOfBirth')?.value ? setTimeZoneDate(this.advanceFilterForm.get('DateOfBirth')?.value, this.userData?.timeZoneInfo?.baseUTcOffset) : null,
      };
    if (
      !this.advanceFilterForm.get('FromMinBudget').value &&
      !this.advanceFilterForm.get('ToMinBudget').value &&
      !this.advanceFilterForm.get('FromMaxBudget').value &&
      !this.advanceFilterForm.get('ToMaxBudget').value
    ) {
      this.filtersPayload.Currency = null;
    }

    this._store.dispatch(new UpdateDataFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchDataCustomStatusFilter(this.filtersPayload)); // first Level Filter
    this._store.dispatch(new FetchDataTopFilters(this.filtersPayload)); // Second Level Filter
    this._store.dispatch(new FetchAllData(this.filtersPayload));
    this._store.dispatch(new ClearData());

    this.modalService.hide();
    this.trackingService.trackFeature(`Web.Data.Filter.Search.Click`);
  }

  updateSubSource(): void {
    const sourceIds = this.advanceFilterForm.get('SourceIds')?.value;
    if (sourceIds?.length) {
      this.subSourceList = [];
      const selectedSource = this.sourceList?.filter((source: any) =>
        sourceIds.includes(source.id));
      selectedSource.forEach((source: any) => {
        if (source?.displayName === '99 Acres') {
          const subSource = this.allSubSourceList['NinetyNineAcres'];
          if (Array.isArray(subSource)) {
            this.subSourceList = [...this.subSourceList, ...subSource];
          }
        } else {
          const formattedKey = source?.displayName?.replace(/\s+/g, '');
          const subSource = this.allSubSourceList[formattedKey] || this.allSubSourceList[source?.displayName];
          if (Array.isArray(subSource)) {
            this.subSourceList = [...this.subSourceList, ...subSource];
          }
        }
      });
    } else {
      let subSourceList: string[] = this.sourceList?.flatMap((lead: any) => {
        if (lead?.displayName === '99 Acres') {
          return this.allSubSourceList?.['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        const match = this.allSubSourceList[formattedKey] || this.allSubSourceList[lead?.displayName];
        return match ? match : [];
      }) || [];
      this.subSourceList = subSourceList
    }
  }

  onClearAllFilters() {
    this.advanceFilterForm.reset();
    this.advanceFilterForm.patchValue({
      Currency: this.defaultCurrency,
      ProspectVisiblity: this.filtersPayload?.ProspectVisiblity,
      PossesionType: null,
      FromPossesionDate: null,
      ToPossesionDate: null
    });
    this.filterDate = [null, null];
    this.dateType = DataDateType[0];
    if (this.possessionFilter) {
      this.possessionFilter.reset();
    }

    this.trackingService.trackFeature(`Web.Data.Filter.Reset.Click`);
  }

  initializeForm() {
    this.advanceFilterForm = this.fb.group({
      DateType: [0],
      FromDate: [null],
      ToDate: [null],
      ProspectVisiblity: [0],
      AssignTo: [null],
      AssignedFromIds: [null],
      CreatedByIds: [null],
      QualifiedByIds: [null],
      LastModifiedByIds: [null],
      ConvertedByIds: [null],
      DeletedByIds: [null],
      RestoredByIds: [null],
      StatusIds: [null],
      SourceIds: [null],
      SubSources: [null],
      Properties: [null],
      PropertyType: [null],
      PropertySubType: [null],
      Projects: [null],
      EnquiryTypes: [null],
      FromMinBudget: [null],
      ToMinBudget: [null],
      FromMaxBudget: [null],
      ToMaxBudget: [null],
      Currency: [null],
      NoOfBHKs: [null],
      BHKTypes: [null],
      Beds: [null],
      Baths: [null],
      Furnished: [null],
      Floors: [null],
      OfferTypes: [null],
      MinCarpetArea: [null],
      MaxCarpetArea: [null],
      CarpetAreaUnitId: [null],
      BuiltUpAreaUnitId: [null],
      MaxBuiltUpArea: [null],
      MinBuiltUpArea: [null],
      SaleableAreaUnitId: [null],
      MaxSaleableArea: [null],
      MinSaleableArea: [null],
      MinPropertyArea: [null],
      MaxPropertyArea: [null],
      PropertyAreaUnitId: [null],
      MinNetArea: [null],
      MaxNetArea: [null],
      NetAreaUnitId: [null],
      AgencyNames: [null],
      ChannelPartnerNames: [null],
      CampaignNames: [null],
      CompanyNames: [null],
      Designations: [null],
      Profession: [null],
      Locations: [null],
      SubCommunities: [null],
      Communities: [null],
      TowerNames: [null],
      Nationality: [null],
      ClusterName: [null],
      Purposes: [null],
      Cities: [null],
      States: [null],
      IsWithTeam: [false],
      Countries: [null],
      PostalCodes: [null],
      // Zones: [null],
      Localities: [null],
      ReferralEmail: [null],
      ReferralContactNo: [null],
      ReferralName: [null],
      LandLine: [null],
      UnitNames: [null],
      CountryCode: [null],
      AltCountryCode: [null],
      ClosingManagers: [null],
      SourcingManagers: [null],
      UploadTypeName: [null],
      PossesionType: [null],
      FromPossesionDate: [null],
      ToPossesionDate: [null],
      MaritalStatus: [null],
      Gender: [null],
      DateOfBirth: [null],
    });
  }

  getFormValue(controlName: string) {
    return this.advanceFilterForm.get(controlName).value;
  }

  trackFormChanges(formValues: any) {
    Object.keys(formValues).forEach((key) => {
      if (formValues[key]) {
        this.trackerFilterFeatures(key);
      }
    });
  }

  trackerFilterFeatures(value: any) {
    if (value !== 'ProspectVisiblity') {
      this.trackingService.trackFeature(
        `Web.Data.Filter.${value.replace(/\s+/g, '')}.Click`
      );
    }
  }

  trackerFeatures(visibility: any) {
    this.trackingService.trackFeature(
      `Web.Data.Filter.${visibility.replace(/\s+/g, '')}.Click`
    );
  }

  validateCarpetArea(): void {
    const minArea = this.getFormValue('MinCarpetArea');
    const maxArea = this.getFormValue('MaxCarpetArea');

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.carpetAreaValidations = false;
    } else {
      this.carpetAreaValidations = true;
    }
  }

  minBudgetCheck(): void {
    const minBudget = this.getFormValue('FromMinBudget');
    const maxBudget = this.getFormValue('ToMinBudget');
    if (minBudget !== null && maxBudget !== null && maxBudget < minBudget) {
      this.minBudgetValidation = false;
    } else {
      this.minBudgetValidation = true;
    }
  }

  maxBudgetCheck(): void {
    const minBudget = this.getFormValue('FromMaxBudget');
    const maxBudget = this.getFormValue('ToMaxBudget');

    if (minBudget !== null && maxBudget !== null && maxBudget < minBudget) {
      this.maxBudgetValidation = false;
    } else {
      this.maxBudgetValidation = true;
    }
  }

  validateBuildUpArea(): void {
    const minArea = this.getFormValue('MinBuiltUpArea');
    const maxArea = this.getFormValue('MaxBuiltUpArea');

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.buildUpAreaValidations = false;
    } else {
      this.buildUpAreaValidations = true;
    }
  }

  saleableAreaValidation(): void {
    const mBudget = this.getFormValue('MinSaleableArea');
    const maxBudget = this.getFormValue('MaxSaleableArea');

    if (mBudget && maxBudget && maxBudget < mBudget) {
      this.saleableValidation = false;
    } else {
      this.saleableValidation = true;
    }
  }

  netAreaValidation(): void {
    const minArea = this.getFormValue('MinNetArea');
    const maxArea = this.getFormValue('MaxNetArea');

    if (minArea && maxArea && maxArea < minArea) {
      this.netValidation = false;
    } else {
      this.netValidation = true;
    }
  }
  propertyAreaValidation(): void {
    const minArea = this.getFormValue('MinPropertyArea');
    const maxArea = this.getFormValue('MaxPropertyArea');

    if (minArea && maxArea && maxArea < minArea) {
      this.areaValidation = false;
    } else {
      this.areaValidation = true;
    }
  }

  onPossessionFilterChange(event: { possessionType: number | null; fromDate: string | null; toDate: string | null }): void {
    this.advanceFilterForm.patchValue({
      PossesionType: event.possessionType,
      FromPossesionDate: event.fromDate,
      ToPossesionDate: event.toDate
    });
  }

  onResetDateFilter() {
    this.dateType = 'All';
    this.filterDate = [];
    this.advanceFilterForm.patchValue({
      DateType: DataDateType[this.dateType as keyof typeof DataDateType],
      FromDate: null,
      ToDate: null,
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
