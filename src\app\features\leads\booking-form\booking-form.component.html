<div class="bg-white br-8 shadow p-20 text-center position-absolute z-index w-300 h-250 right-25pr border"
  *ngIf="showSelectedImgPopup">
  <div *ngIf="isPdfFile(ImageUrl?.filePath)" class="w-100 h-100">
    <iframe [src]="ImageUrl?.filePath" class="w-100 h-100"></iframe>
  </div>
  <img *ngIf="!isPdfFile(ImageUrl?.filePath)" [type]="'leadrat'" [appImage]="ImageUrl?.filePath"
    [alt]="ImageUrl?.photoName" class="w-100 h-100">
</div>
<div class="align-center mt-10 ml-20">
  <div class="d-flex cursor-pointer" (click)="modalRef.hide()">
    <span class="icon ic-person ic-black ic-xs ic-circle-chevron-left mr-8" (click)="modalRef.hide()"></span>
    <h4 class="fw-600">{{ selectedFormTab ? 'Booking details' : 'Brokerage info'}}</h4>
  </div>
</div>
<div class="bg-secondary my-16 p-16 br-4 text-mud text-sm m-10">
  <h4 class="fw-600 text-coal text-truncate-1 break-all">
    {{leadInfo?.name ? leadInfo?.name : '--'}}
  </h4>
  <div class="align-center mt-8">
    <ng-container>
      <span class="align-center">
        <span class="icon ic-Call mr-6 ic-slate-90 ic-xxxs"></span>
        <span>{{leadInfo?.contactNo ? leadInfo?.contactNo : '--'}}</span>
      </span>
    </ng-container>
    <span class="align-center text-truncate-1 break-all ml-10">
      <span class="icon ic-mail ic-slate-90 ic-xxs mr-6 ml-2"></span>
      <span>{{leadInfo?.email ? leadInfo?.email : '--'}}</span>
    </span>
  </div>
  <div class="align-center text-truncate-1 break-all mt-8">
    <span class="icon ic-location-circle ic-slate-90 ic-xxs mr-6"></span>
    <drag-scroll class="scrollbar scroll-hide">
      <div>{{addresses}}
      </div>
    </drag-scroll>
  </div>
  <div class="align-center ip-w-100-70 text-nowrap scrollbar scroll-hide mt-8">
    <span class="icon ic-apartment ic-slate-90 ic-xxs mr-6"></span>
    <drag-scroll class="scrollbar scroll-hide">
      <div class="align-center text-nowrap">
        <span class="align-center" *ngFor="let enquiry of leadInfo?.enquiry?.enquiryTypes || ['']; let last = last">
          <span class="text-accent-green text-sm fw-600">{{enquiry
            ? EnquiryType[enquiry] : "--"}}</span>
          <span *ngIf="!last" class="dot dot-xxs bg-accent-green mx-6"></span>
        </span>
        <ng-container *ngIf="leadInfo?.enquiry?.propertyTypes?.length">
          <span class="dot dot-xxs bg-dark-700 mx-6"></span>
          <ng-container *ngIf="leadInfo?.enquiry?.propertyTypes?.length; else noSubtype">
            <ng-container *ngFor="let type of leadInfo?.enquiry?.propertyTypes">
              <span *ngIf="type?.childType?.displayName">
                {{ type.childType.displayName }}
              </span>
            </ng-container>
          </ng-container>
        </ng-container>
        <ng-container *ngIf="leadInfo?.enquiry?.propertyTypes?.length">
          <span class="dot dot-xxs bg-dark-700 mx-6"></span>
          <span>{{ leadInfo.enquiry.propertyTypes?.[0]?.displayName }}</span>
        </ng-container>
        <ng-container *ngIf="leadInfo?.enquiry?.propertyTypes?.length">
          <span class="dot dot-xxs bg-dark-700 mx-6"></span>
          <ng-container *ngIf="leadInfo?.enquiry?.propertyTypes?.length; else noSubtype">
            <ng-container *ngFor="let type of leadInfo?.enquiry?.propertyTypes">
              <span *ngIf="type?.childType?.displayName">
                {{ type.childType.displayName }}
              </span>
            </ng-container>
          </ng-container>
          <ng-template #noSubtype>
            <h6>--</h6>
          </ng-template>
        </ng-container>
        <ng-container *ngFor="let noOfBHK of leadInfo?.enquiry?.bhKs">
          <span class="dot dot-xxs bg-dark-700 mx-6"></span>
          <span>{{ globalSettingsData?.isCustomLeadFormEnabled ? getBRDisplayString(noOfBHK) :
            getBHKDisplayString(noOfBHK) }}</span>
        </ng-container>
        <ng-container *ngFor="let bhkType of leadInfo?.enquiry?.bhkTypes">
          <span class="dot dot-xxs bg-dark-700 mx-6"></span>
          <span>{{ BHKType[bhkType] }}</span>
        </ng-container>
        <ng-container *ngIf="leadInfo?.enquiry?.lowerBudget || leadInfo?.enquiry?.upperBudget">
          <span class="dot dot-xxs bg-dark-700 mx-6"></span>
          <span>{{ formatBudget(leadInfo?.enquiry?.lowerBudget, leadInfo?.enquiry?.currency || defaultCurrency)
            }}</span>
          <span *ngIf="leadInfo?.enquiry?.lowerBudget && leadInfo?.enquiry?.upperBudget">-
            {{ formatBudget(leadInfo?.enquiry?.upperBudget, leadInfo?.enquiry?.currency || defaultCurrency) }}</span>
        </ng-container>
      </div>
    </drag-scroll>
  </div>
  <div class="w-100 d-flex mt-6 ip-w-100-70 text-nowrap scrollbar scroll-hide">
    <div class="align-center">
      <div class="text-sm text-nowrap">
        <div class="fw-semi-bold mb-4">Selected {{ selectedProject.isprojectproperty }}</div>
        <div class="text-black-200">
          {{ selectedProject.selectedpropertyname ? selectedProject.selectedpropertyname : '--' }}
        </div>
      </div>
    </div>
    <div class="border-left mx-16"></div>
    <div class="align-center">
      <div class="text-sm text-nowrap">
        <div class="fw-semi-bold mb-4">
          {{ selectedProject.isprojectproperty === 'Project' ? 'Unit name' : 'Unit number' }}
        </div>
        <div class="text-black-200">
          {{ selectedProject.isprojectproperty === 'Project' ? selectedProject.unitname :
          selectedProject.unitNo ? selectedProject.unitNo : "--" }}
        </div>
      </div>
    </div>
    <div class="border-left mx-16"></div>
    <div class="align-center">
      <div class="text-sm text-nowrap">
        <div class="fw-semi-bold mb-4">Carpet area</div>
        <div class="text-black-200">{{ selectedProject.carpetarea ? selectedProject.carpetarea : '--' }}</div>
      </div>
    </div>
    <div class="border-left mx-16"></div>
    <div class="align-center">
      <div class="text-sm text-nowrap">
        <div class="fw-semi-bold mb-4">Saleable area</div>
        <div class="text-black-200">{{selectedProject.sealablearea ? selectedProject.sealablearea : "--"}}</div>
      </div>
    </div>
    <div class="border-left mx-16"></div>
    <div class="align-center">
      <div class="text-sm text-nowrap">
        <div class="fw-semi-bold mb-4">Builder name</div>
        <div class="text-black-200">{{ toCamelCaseWithCapitals(selectedProject.buildername) }}</div>
      </div>
    </div>
  </div>
</div>
<div class="nav flex-start gap-4 ml-10 p-10 field-label" *ngIf="canViewBrokerageInfo">
  <a class="nav-item  m-0 p-0" [ngClass]="completeBookingForm.invalid && 'pe-none'" [class.active]="selectedFormTab"
    (click)="switchFormTab('Booking details')" [class.text-accent-green]="selectedFormTab"
    [class.field-label-underline-green]="selectedFormTab">Booking details</a>
  <a class="nav-item m-0 p-0 ml-10" [ngClass]="completeBookingForm.invalid && 'pe-none'"
    [class.active]="!selectedFormTab" (click)="switchFormTab('Brokerage info')"
    [class.text-accent-green]="!selectedFormTab" [class.field-label-underline-green]="!selectedFormTab">Brokerage
    info</a>
</div>
<form [formGroup]="completeBookingForm">
  <div class="h-100-333 scrollbar" *ngIf="selectedFormTab">
    <div class="flex-between flex-wrap px-16 w-100">
      <div class="w-50 ph-w-100">
        <div class="mr-10 ph-mr-0">
          <form-errors-wrapper>
            <div class="field-label">Booking Date</div>
            <input [owlDateTime]="dt1" [max]="currentDate" [owlDateTimeTrigger]="dt1" readonly id="inpBookDateTime"
              formControlName="bookingDate" data-automate-id="inpBookDateTime" placeholder="ex. 5/03/2025, 12:00 pm">
            <owl-date-time #dt1 [hour12Timer]="'true'" (afterPickerOpen)="onPickerOpened(currentDate)"
              [startAt]="completeBookingForm.controls['bookingDate'].value ? null : currentDate"></owl-date-time>
          </form-errors-wrapper>
        </div>
      </div>
      <div class="w-50 ph-w-100">
        <div class="field-label">Team Head</div>
        <div class="form-group">
          <ng-select [virtualScroll]="true" *ngIf="!propertyListIsLoading else fieldLoader" id="teamHead"
            ResizableDropdown data-automate-id="teamHead" dropdownPosition="bottom" placeholder="ex. Abid"
            formControlName="teamHead" class="bg-white position-relative align-center ">
            <ng-option *ngFor="let user of allUsers" [value]="user.id">
              {{user.fullName}} <span class="error-message-custom top-13 right-40"
                *ngIf="!user?.isActive">(Disabled)</span></ng-option>
          </ng-select>
        </div>
      </div>
      <div class="field-label">Upload Documents</div>
      <div class="w-100 flex-between ph-flex-col bg-slate br-6 p-4">
        <input (input)="setDocumentName($event.target.value)" id="documentsupload" formControlName="documentName"
          class="w-200 ph-w-100" type="text" placeholder="add document file name here..." autocomplete="off">
        <div (click)="triggerFileInput()" class="btn-coal"
          [ngClass]="{'disabled': !completeBookingForm.get('documentName')?.value}">Upload Image</div>
        <input #fileInput type="file" (change)="uploadImage($event)" />
      </div>
      <div class="w-100 d-flex flex-column position-relative">
        <p *ngIf="isShowDocumentError && !completeBookingForm.get('documentName')?.value"
          class="text-xxs text-danger position-absolute ml-16">document name is required field.</p>
        <p *ngIf="isShowFilneExist" class="text-xxs text-danger position-absolute ml-16">filename already exists.</p>
        <div class="d-flex flex-wrap mt-12">
          <div class="align-center bg-white" *ngFor="let doc of documentList; let index=index">
            <div class="mr-10 ph-mr-0 flex-center">
              <div class="rounded-circle flex-center p-4 w-18 h-18">
                <img *ngIf="doc.extensionName ==='pdf'" class="w-100" src="../../../../assets/images/pdf-image.svg"
                  alt="">
                <span *ngIf="doc.extensionName !=='pdf'" class="icon ic-xxs ic-black ic-degree-camera "></span>
              </div>
              <a class="fw-600 text-sm mr-4 text-accent-green cursor-pointer" (mouseenter)="onMouseEnter(doc)"
                (mouseleave)="onMouseLeave(doc)" [title]="doc.photoName">
                {{ doc.photoName?.length > 8 ? doc.photoName.slice(0, 8) + '..' : doc.photoName }}.{{doc.extensionName}}
              </a>
              <div [title]="'Delete'" class="rounded-circle cursor-pointer flex-center p-4 w-16 h-16 bg-red-750">
                <span class="icon ic-x-xs ic-delete" (click)="removeImage(doc, index)"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="w-100">
        <h6 class="field-label">Booking done by</h6>
        <div class="flex-between flex-wrap w-100 mb-20 mt-10">
          <div class="w-50 ph-w-100">
            <div class="mr-10 ph-mr-0">
              <div class="field-label mt-0">Primary</div>
              <div class="form-group">
                <ng-select [virtualScroll]="true" *ngIf="!propertyListIsLoading else fieldLoader" id="primaryOwner"
                  ResizableDropdown data-automate-id="primaryOwner" dropdownPosition="bottom"
                  formControlName="primaryOwner" placeholder="ex. Abid" class="bg-white position-relative">
                  <ng-option *ngFor="let user of allUsers" [value]="user.id">
                    {{user.fullName}}</ng-option>
                </ng-select>
              </div>
            </div>
          </div>
          <div class="w-50 ph-w-100 ph-mt-10">
            <div class="field-label mt-0">Secondary</div>
            <div class="form-group">
              <ng-select [virtualScroll]="true" *ngIf="!propertyListIsLoading else fieldLoader" id="chosenProperty"
                ResizableDropdown data-automate-id="chosenProperty" dropdownPosition="bottom"
                formControlName="secondaryOwner" [readonly]="!completeBookingForm.get('primaryOwner').value"
                placeholder="ex. Arif" class="bg-white position-relative" [items]="allSecondaryUsers"
                bindLabel="fullName" bindValue="id">

              </ng-select>
            </div>
          </div>
          <div class="field-rupees-tag w-50 ph-w-100">
            <div class="mr-10 ph-mr-0">
              <div class="field-label-req">Agreement Value</div>
              <div class="position-relative budget-dropdown">
                <form-errors-wrapper [control]="completeBookingForm.controls['agreementValue']" label="agreement value">
                  <input type="number" formControlName="agreementValue"
                    id="agreementValue" data-automate-id="agreementValue" placeholder="ex. 4000000">
                  <div class="no-validation">
                    <ng-container *ngIf="currencyList?.length > 1 ; else showCurrencySymbol">
                      <ng-select formControlName="currency" (change)="changeCurrency($event)"
                        class="ml-4 mt-4 position-absolute top-0 manage-dropdown" ResizableDropdown>
                        <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                          <span [title]="curr.currency">
                            {{curr.currency}}
                          </span>
                        </ng-option>
                      </ng-select>
                    </ng-container>
                    <ng-template #showCurrencySymbol>
                      <h5 class="rupees px-12 py-8 fw-600 m-4">
                        {{leadInfo?.enquiry?.currency || defaultCurrency }}</h5>
                    </ng-template>
                  </div>
                </form-errors-wrapper>
                <div *ngIf="completeBookingForm.controls['agreementValue'].value"
                  class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                  {{agreementValueInWords}}</div>
              </div>
            </div>
          </div>
          <div class="field-rupees-tag w-50 ph-w-100">
            <div class="field-label">Car Parking Charges</div>
            <div class="position-relative budget-dropdown">
              <form-errors-wrapper [control]="completeBookingForm.controls['carParkingCharges']"
                label="car parking charges">
                <input type="number" formControlName="carParkingCharges" (wheel)="$event.preventDefault()"
                  id="carParkingCharges" data-automate-id="carParkingCharges" placeholder="ex. 4000000"
                  (input)="updateTotalSoldPrice()">
                <div class="no-validation">
                  <ng-container *ngIf="currencyList?.length > 1 ; else showCurrencySymbol">
                    <ng-select formControlName="currency" (change)="changeCurrency($event)"
                      class="ml-4 mt-4 position-absolute top-0 manage-dropdown" ResizableDropdown>
                      <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                        <span [title]="curr.currency">
                          {{curr.currency}}
                        </span>
                      </ng-option>
                    </ng-select>
                  </ng-container>
                </div>
              </form-errors-wrapper>
              <div *ngIf="completeBookingForm.controls['carParkingCharges'].value"
                class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                {{carParkingChargesInWords}}</div>
            </div>
          </div>
          <div class="field-rupees-tag w-50 ph-w-100">
            <div class="mr-10 ph-mr-0">
              <div class="field-label">Add-on Charges</div>
              <div class="position-relative budget-dropdown">
                <form-errors-wrapper [control]="completeBookingForm.controls['addOnCharges']" label="add-on charges">
                  <input type="number" formControlName="addOnCharges" id="addOnCharges"
                    (wheel)="$event.preventDefault()" data-automate-id="addOnCharges" placeholder="ex. 4000000"
                    (input)="updateTotalSoldPrice()">
                  <div class="no-validation">
                    <ng-container *ngIf="currencyList?.length > 1 ; else showCurrencySymbol">
                      <ng-select formControlName="currency" (change)="changeCurrency($event)"
                        class="ml-4 mt-4 position-absolute top-0 manage-dropdown" ResizableDropdown>
                        <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                          <span [title]="curr.currency">
                            {{curr.currency}}
                          </span>
                        </ng-option>
                      </ng-select>
                    </ng-container>
                  </div>
                </form-errors-wrapper>
                <div *ngIf="completeBookingForm.controls['addOnCharges'].value"
                  class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                  {{addOnChargesInWords}}</div>
              </div>
            </div>
          </div>
          <div class="field-rupees-tag w-50 ph-w-100">
            <div class="field-label">Total Sold Price</div>
            <div class="position-relative budget-dropdown">
              <form-errors-wrapper [control]="completeBookingForm.controls['totalSoldPrice']" label="total sold price">
                <input type="number" formControlName="totalSoldPrice" (wheel)="$event.preventDefault()"
                  id="totalSoldPrice" data-automate-id="totalSoldPrice" placeholder="ex. 4000000"
                  (input)="updateBalanceAmount()">
                <div class="no-validation">
                  <ng-container *ngIf="currencyList?.length > 1 ; else showCurrencySymbol">
                    <ng-select formControlName="currency" (change)="changeCurrency($event)"
                      class="ml-4 mt-4 position-absolute top-0 manage-dropdown" ResizableDropdown>
                      <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                        <span [title]="curr.currency">
                          {{curr.currency}}
                        </span>
                      </ng-option>
                    </ng-select>
                  </ng-container>
                </div>
              </form-errors-wrapper>
              <div *ngIf="completeBookingForm.controls['totalSoldPrice'].value"
                class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                {{totalSoldPriceInWords}}</div>
            </div>
          </div>
          <div class="field-rupees-tag w-50 ph-w-100 ph-mt-20">
            <div class="mr-10 ph-mr-0">
              <div class="field-label">Token Amount Paid</div>
              <div class="position-relative budget-dropdown">
                <form-errors-wrapper [control]="completeBookingForm.controls['tokenAmountPaid']"
                  label="token amount paid">
                  <input type="number" formControlName="tokenAmountPaid" (wheel)="$event.preventDefault()"
                    id="tokenAmountPaid" data-automate-id="tokenAmountPaid" placeholder="ex. 4000000"
                    (input)="updateBalanceAmount()">
                  <div class="no-validation">
                    <ng-container *ngIf="currencyList?.length > 1 ; else showCurrencySymbol">
                      <ng-select formControlName="currency" (change)="changeCurrency($event)"
                        class="ml-4 mt-4 position-absolute top-0 manage-dropdown" ResizableDropdown>
                        <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                          <span [title]="curr.currency">
                            {{curr.currency}}
                          </span>
                        </ng-option>
                      </ng-select>
                    </ng-container>
                  </div>
                </form-errors-wrapper>
                <div *ngIf="completeBookingForm.controls['tokenAmountPaid'].value"
                  class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                  {{tokenAmountPaidInWords}}</div>
              </div>
            </div>
          </div>
          <div class="w-50 ph-w-100">
            <div class="field-label">Payment Mode</div>
            <div class="form-group">
              <ng-select [virtualScroll]="true" [closeOnSelect]="true" ResizableDropdown formControlName="paymentMode"
                placeholder="select" [multiple]="false" [items]="PaymentModeList">
              </ng-select>
            </div>
          </div>
          <div class="d-flex ph-flex-col w-100">
            <div class="flex-column field-rupees-tag w-50 ph-w-100">
              <div class="mr-10">
                <div class="justify-between align-end"> 
                  <div class="field-label">Discount</div>
                  <label class="checkbox-container mb-4 ml-4">
                    <input type="checkbox" formControlName="discountUnit" (change)="updateDiscountType()" />
                    <span class="checkmark"></span>%
                  </label>
                </div>
                <div class="d-flex field-rupees-tag">
                  <div class="position-relative w-60pr">
                    <div class="mr-10 ph-mr-10">
                      <div class="position-relative budget-dropdown">
                        <form-errors-wrapper>
                          <input type="number" formControlName="discount" (wheel)="$event.preventDefault()"
                            id="discount" data-automate-id="discount" placeholder="ex. 40000"
                            (input)="updateDiscount($event.target.value)">
                          <div class="no-validation">
                            <ng-select formControlName="discountCurrency" ResizableDropdown
                              class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                              <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                                <span [title]="curr.currency">
                                  {{curr.currency}}
                                </span>
                              </ng-option>
                              <ng-option value="%">
                                <span title="percentage">%</span>
                              </ng-option>
                            </ng-select>
                          </div>
                        </form-errors-wrapper>
                        <div *ngIf="completeBookingForm.get('discount').hasError('invalid')"
                          class="text-danger position-absolute right-5 nbottom-11 text-xxxs">
                          {{ completeBookingForm.get('discount').getError('invalid') }}
                        </div>
                        <div *ngIf="completeBookingForm.controls['discount'].value"
                          class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                          {{ discountInWords.length > 20 ? discountInWords.slice(0, 20) + '...' : discountInWords }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="form-group w-40pr">
                    <form-errors-wrapper [control]="completeBookingForm.controls['cashback']" label="This">
                      <ng-select [virtualScroll]="true" ResizableDropdown dropdownPosition="bottom"
                        placeholder="cashback" class="bg-white position-relative" formControlName="cashback"
                        (change)="onChangeDiscount()">
                        <ng-option *ngFor="let cashback of ['Cashback','Direct adjustment'] ; let index=index"
                          [value]="index+1">
                          <span [title]="cashback">{{cashback}}</span>
                        </ng-option>
                      </ng-select>
                    </form-errors-wrapper>
                  </div>
                </div>
              </div>
            </div>
            <div class="field-rupees-tag w-50 ph-w-100">
              <div class="field-label">Balance Amount</div>
              <div class="position-relative budget-dropdown">
                <form-errors-wrapper [control]="completeBookingForm.controls['balanceAmount']" label="balance amount">
                  <input type="number" formControlName="balanceAmount" (wheel)="$event.preventDefault()"
                    id="balanceAmount" data-automate-id="balanceAmount" placeholder="ex. 4000000">
                  <div class="no-validation">
                    <ng-container *ngIf="currencyList?.length > 1 ; else showCurrencySymbol">
                      <ng-select formControlName="currency" class="ml-4 mt-4 position-absolute top-0 manage-dropdown"
                        ResizableDropdown>
                        <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                          <span [title]="curr.currency">
                            {{curr.currency}}
                          </span>
                        </ng-option>
                      </ng-select>
                    </ng-container>
                  </div>
                </form-errors-wrapper>
                <div *ngIf="completeBookingForm.controls['balanceAmount'].value"
                  class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                  {{balanceAmountInWords}}</div>
              </div>
            </div>
          </div>
          <div class="w-50 ph-w-100">
            <div class="mr-10 ph-mr-0">
              <div class="field-label">Type of Payment Selected</div>
              <div class="form-group">
                <form-errors-wrapper [control]="completeBookingForm.controls['typeOfPaymentSelected']"
                  label="payment type">
                  <ng-select [virtualScroll]="true" [closeOnSelect]="true" ResizableDropdown
                    formControlName="typeOfPaymentSelected" placeholder="select" [multiple]="false"
                    [items]="PaymentTypeList">
                  </ng-select>
                </form-errors-wrapper>
              </div>
            </div>
          </div>
          <div class="w-100">
            <div class="field-label">{{'TASK.notes' | translate}}</div>
            <form-errors-wrapper [control]="completeBookingForm.controls['notes']" label="{{'TASK.notes' | translate}}">
              <textarea rows="2" id="txtUpdateStatusNotes" data-automate-id="txtUpdateStatusNotes"
                formControlName="notes" placeholder="ex. I want to say "></textarea>
            </form-errors-wrapper>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer  border-top bg-white position-sticky bottom-0" *ngIf="!canViewBrokerageInfo">
    <button type="button" class="btn-gray" (click)="modalRef.hide()">Cancel</button>
    <button type="submit" class="btn-coal" (click)="saveAndMoveToInvoice()"
      [ngClass]="{'w-150': canViewInvoice, 'w-130': !canViewInvoice}">
      {{canViewInvoice?'Save & move to invoice' : 'Save & Close'}}</button>
  </div>
  <div class="modal-footer  border-top bg-white position-sticky bottom-0"
    *ngIf="canViewBrokerageInfo && selectedFormTab ">
    <button type="button" class="btn-gray" (click)="modalRef.hide()">Cancel</button>
    <button type="submit" class="btn-coal w-130" (click)="saveAndMoveBrockrage()">Save & Go to Next</button>
  </div>
</form>
<!-- --------------- Brokerage info form ------------------------ -->
<form [formGroup]="brokerageInfoForm">
  <div *ngIf="!selectedFormTab">
    <div class="h-100-333 scrollbar">
      <div class="flex-between flex-wrap px-16 pr-10 w-100 mb-20">
        <div class="field-rupees-tag w-50 ph-w-100">
          <div class="mr-10 ph-mr-0">
            <div class="field-label">Agreement Value</div>
            <div class="position-relative budget-dropdown">
              <form-errors-wrapper [control]="brokerageInfoForm.controls['agreementValueFromBrokerageinfo']"
                label="agreement value">
                <input type="number" formControlName="agreementValueFromBrokerageinfo" [readonly]="true"
                  (wheel)="$event.preventDefault()" id="agreementValueFromBrokerageinfo"
                  data-automate-id="agreementValueFromBrokerageinfo" placeholder="ex. 4000000"
                  (input)="onTypingInAgreement()">
                <h5 class="rupees px-12 py-8 fw-600 m-4">
                  {{brokerageInfoForm.value.currency || defaultCurrency }}</h5>
              </form-errors-wrapper>
              <div *ngIf="brokerageInfoForm.controls['agreementValueFromBrokerageinfo'].value"
                class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                {{agreementValueFromBrokerageinfoInWords}}</div>
            </div>
          </div>
        </div>
        <div class="field-rupees-tag w-50 ph-w-100">
          <div class="field-label">Total Sold Price</div>
          <div class="position-relative budget-dropdown">
            <form-errors-wrapper [control]="brokerageInfoForm.controls['totalSoldPriceFromBrokerageinfo']"
              label="total sold price">
              <input type="number" (wheel)="$event.preventDefault()" formControlName="totalSoldPriceFromBrokerageinfo"
                id="totalSoldPriceFromBrokerageinfo" data-automate-id="totalSoldPriceFromBrokerageinfo"
                placeholder="ex. 4000000" (input)="onTypingInSoldPrice()">
              <h5 class="rupees px-12 py-8 fw-600 m-4">
                {{brokerageInfoForm.value.currency || defaultCurrency }}</h5>
            </form-errors-wrapper>
            <div *ngIf="brokerageInfoForm.controls['totalSoldPriceFromBrokerageinfo'].value"
              class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
              {{totalSoldPriceFromBrokerageinfoInWords}}</div>
          </div>
        </div>
        <div class="field-rupees-tag w-50 ph-w-100">
          <div class="mr-10 ph-mr-0">
            <div class="justify-between align-end"> 
              <div class="field-label">Brokerage Charges</div>
              <label class="checkbox-container mb-4 ml-4">
                <input type="checkbox" [ngModel]="isbrokerageChargesInPercentage"
                  (change)="updateBrockerageChargesType($event)" formControlName="brokerageChargesUnit" />
                <span class="checkmark"></span>%
              </label>
            </div>
            <div class="position-relative budget-dropdown">
              <form-errors-wrapper [control]="brokerageInfoForm.controls['brokerageCharges']" label="brokerage charges">
                <input type="number" (wheel)="$event.preventDefault()" formControlName="brokerageCharges"
                  id="brokerageCharges" data-automate-id="brokerageCharges" placeholder="ex. 4000000"
                  [max]="isbrokerageChargesInPercentage ? 99 : null " (input)="onTypingInBrokerage($event)">
                <div class="no-validation">
                  <ng-select formControlName="brokerageChargesCurrency" (change)="changeBrockerageCurrency($event)"
                    ResizableDropdown class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                    <ng-option *ngFor="let curr of [brokerageInfoForm.value.currency || defaultCurrency,'%']"
                      [value]="curr">
                      <span [title]="curr">
                        {{curr}}
                      </span>
                    </ng-option>
                  </ng-select>
                </div>
              </form-errors-wrapper>
              <div *ngIf="brokerageInfoForm.controls['brokerageCharges'].value"
                class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                {{brokerageChargesInWords}}</div>
            </div>
          </div>
        </div>
        <div class="flex-column field-rupees-tag w-50 ph-w-100">
          <div class="field-label">Net Brokerage Amount</div>
          <div class="d-flex">
            <div class="position-relative"
              [ngClass]="{ 'w-60pr': isbrokerageChargesInPercentage, 'w-100': selectedProject?.brokerageCurrency !== '%' || !isbrokerageChargesInPercentage}">
              <div class="position-relative budget-dropdown">
                <div [ngClass]="{'mr-10 ph-mr-0': isbrokerageChargesInPercentage}">
                  <form-errors-wrapper [control]="brokerageInfoForm.controls['netbrokerageAmount']" label="this">
                    <input type="number" (wheel)="$event.preventDefault()" formControlName="netbrokerageAmount"
                      id="netbrokerageAmount" data-automate-id="netbrokerageAmount" placeholder="ex. 4000000"
                      (input)="ischangeNetBorkerage($event)">
                    <h5 class="rupees px-12 py-8 fw-600 m-4">
                      {{brokerageInfoForm.value.currency || defaultCurrency }}</h5>
                  </form-errors-wrapper>
                  <div *ngIf="brokerageInfoForm.controls['netbrokerageAmount'].value"
                    class="position-absolute right-15 nbottom-0 text-accent-green fw-semi-bold text-sm">
                    {{netbrokerageAmountInWords}}</div>
                </div>
              </div>
            </div>
            <div class="form-group w-40pr" *ngIf="isbrokerageChargesInPercentage">
              <form-errors-wrapper [control]="brokerageInfoForm.controls['brokerageAmountValue']" label="this">
                <ng-select [virtualScroll]="true" id="amount" data-automate-id="amount" ResizableDropdown
                  formControlName="brokerageAmountValue" dropdownPosition="bottom" placeholder="Agg. value"
                  class="bg-white position-relative" (change)="onAmountValueChange($event)">
                  <ng-option *ngFor="let amount of ['Agreement Value','Sold price']; let index = index"
                    [value]="index + 1" [appTooltip]="amount">
                    <span [title]="amount">{{amount}}</span>
                  </ng-option>
                </ng-select>
              </form-errors-wrapper>
            </div>
          </div>
        </div>
        <div class="field-rupees-tag w-50 ph-w-100">
          <div class="mr-10 ph-mr-0">
            <div class="field-label">GST</div>
            <div class="position-relative budget-dropdown">
              <form-errors-wrapper [control]="brokerageInfoForm.controls['gst']" label="gst must be less than 99. it">
                <input type="number" (wheel)="$event.preventDefault()" formControlName="gst" [max]="99" id="gst"
                  data-automate-id="gst" placeholder="ex. 10%" (input)="onTypingInGST($event)"
                  (change)="onAmountValueChange($event.target.value)">
                <h5 class="rupees px-12 py-8 fw-600 m-4">%</h5>
              </form-errors-wrapper>
              <!-- <div *ngIf="brokerageInfoForm.get('gst').hasError('invalid')" class="text-danger position-absolute right-5 text-xs">
                {{ brokerageInfoForm.get('gst').getError('invalid') }}
              </div> -->
              <div *ngIf="brokerageInfoForm.controls['gst'].value"
                class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                {{gstInWords}}</div>
            </div>
          </div>
        </div>
        <div class="field-rupees-tag w-50 ph-w-100">
          <div class="field-label">Total Brokerage</div>
          <div class="position-relative budget-dropdown">
            <form-errors-wrapper [control]="brokerageInfoForm.controls['totalBrokerage']" label="total brokerage">
              <input type="number" formControlName="totalBrokerage" id="totalBrokerage"
                (wheel)="$event.preventDefault()" data-automate-id="totalBrokerage" placeholder="ex. 4000000"
                (input)="ischangetotalBrokerage($event)">
              <h5 class="rupees px-12 py-8 fw-600 m-4">
                {{brokerageInfoForm.value.currency || defaultCurrency }}</h5>
            </form-errors-wrapper>
            <div *ngIf="brokerageInfoForm.controls['totalBrokerage'].value"
              class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
              {{totalBrokerageInWords}}</div>
          </div>
        </div>
        <div class="w-50 ph-w-100">
          <div class="mr-10 ph-mr-0">
            <div class="field-label">Referral Name</div>
            <form-errors-wrapper>
              <input type="text" id="referralname" data-automate-id="referralname" formControlName="referralname"
                placeholder="ex. abid">
            </form-errors-wrapper>
          </div>
        </div>
        <div class="w-50 ph-w-100">
          <div class="field-label">Referral Number</div>
          <form-errors-wrapper [control]="brokerageInfoForm.controls['referralnumber']" label="referral number">
            <ngx-mat-intl-tel-input #referralNoInput *ngIf="hasInternationalSupport"
              [preferredCountries]="preferredCountries" [enablePlaceholder]="true" [enableSearch]="true"
              formControlName="referralnumber" class="referralNoInput no-validation">
            </ngx-mat-intl-tel-input>
            <ngx-mat-intl-tel-input #referralNoInput *ngIf="!hasInternationalSupport"
              [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries" [enablePlaceholder]="true"
              [enableSearch]="true" formControlName="referralnumber" class="referralNoInput no-validation">
            </ngx-mat-intl-tel-input>
          </form-errors-wrapper>
        </div>
        <div class="field-rupees-tag w-50 ph-w-100">
          <div class="mr-10 ph-mr-0">
            <div class="justify-between align-end"> 
              <div class="field-label">Referral Commission</div>
              <label class="checkbox-container mb-4 ml-4">
                <input type="checkbox" [ngModel]="isCommissionPercentage" (change)="updateCommissionType($event)"
                  formControlName="referralCommissionUnit" />
                <span class="checkmark"></span>%
              </label>
            </div>
            <div class="position-relative budget-dropdown">
              <form-errors-wrapper [control]="brokerageInfoForm.controls['referralcommission']"
                label="referral commission">
                <input type="number" (wheel)="$event.preventDefault()" formControlName="referralcommission"
                  id="referralcommission" data-automate-id="referralcommission" placeholder="ex. 4000000"
                  [max]="isCommissionPercentage ? 99 : null " (input)="getTotalComission($event.target.value)">
                <div class="no-validation">
                  <ng-select formControlName="referalComCurrency" ResizableDropdown
                    class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                    <ng-option *ngFor="let curr of [brokerageInfoForm.value.currency || defaultCurrency,'%']"
                      [value]="curr">
                      <span [title]="curr">
                        {{curr}}
                      </span>
                    </ng-option>
                  </ng-select>
                </div>
              </form-errors-wrapper>
              <div *ngIf="brokerageInfoForm.controls['referralcommission'].value"
                class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                {{referralcommissionInWords}}</div>
              <div *ngIf="brokerageInfoForm.get('referralcommission').hasError('invalid')"
                class="text-danger position-absolute right-5 nbottom-0 text-xxxs">
                {{ brokerageInfoForm.get('referralcommission').getError('invalid') }}
              </div>
            </div>
          </div>
        </div>
        <div class="field-rupees-tag w-50 ph-w-100">
          <div class="field-label">Brokerage Earned</div>
          <div class="position-relative budget-dropdown">
            <form-errors-wrapper [control]="brokerageInfoForm.controls['brokerageEarned']" label="brokerage earned">
              <input type="number" formControlName="brokerageEarned" id="brokerageEarned"
                (wheel)="$event.preventDefault()" data-automate-id="brokerageEarned" placeholder="ex. 4000000">
              <h5 class="rupees px-12 py-8 fw-600 m-4">
                {{brokerageInfoForm.value.currency || defaultCurrency }}</h5>
            </form-errors-wrapper>
            <div *ngIf="brokerageInfoForm.controls['brokerageEarned'].value"
              class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
              {{brokerageEarnedInWords}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer border-top bg-white position-sticky bottom-0 p-10" *ngIf="currentPath == '/invoice'">
      <button type="button" class="btn-gray" (click)="modalRef.hide()">Cancel</button>
      <button type="button" class="btn-coal" (click)="goToBookingFormBack()">Back</button>
      <button type="submit" class="btn-coal" (click)="brokerageInfoSaveClose()">Save & close</button>
    </div>
    <div class="modal-footer border-top bg-white position-sticky bottom-0"
      *ngIf="currentPath =='/whatsApp/whatsApp-inbox' || currentPath == '/leads/manage-leads'">
      <button type="button" class="btn-gray" (click)="modalRef.hide()">Cancel</button>
      <button type="button" class="btn-coal" (click)="goToBookingFormBack()">Back</button>
      <button type="submit" class="btn-coal" (click)="saveAndMoveToInvoice(true)">Save & move to
        invoice</button>
    </div>
  </div>
</form>
<ng-template #imageUploadLimit>
  <div class="p-20">
    <h3 class="text-center">Only {{numberOfImageCanUpload}} images can be uploaded..!</h3>
    <div class="mt-20 flex-end">
      <button type="button" class="btn-green" (click)="modalRef.hide()">OK</button>
    </div>
  </div>
</ng-template>

<ng-template #confirmModal>
  <div class="p-20">
    <h3 class="text-black-100 fw-semi-bold mb-20">Are you sure you want to save?</h3>
    <div class="text-black-200 p-10 bg-light-pearl text-large br-4">
      Note: You've modified
      <ng-container *ngIf="isChangenetBrokerageAmountValue && isChangetotalBrokerageAmountValue">
        net brokerage and total brokerage amount
      </ng-container>
      <ng-container *ngIf="isChangenetBrokerageAmountValue && !isChangetotalBrokerageAmountValue">
        net brokerage amount
      </ng-container>
      <ng-container *ngIf="!isChangenetBrokerageAmountValue && isChangetotalBrokerageAmountValue">
        total brokerage amount
      </ng-container>
    </div>
    <div class="flex-end mt-30">
      <button class="btn-gray mr-20" (click)="isClickYesOrNo(false)" id="clkSettingsNo"
        data-automate-id="clkSettingsNo">
        {{ 'GLOBAL.no' | translate }}</button>
      <button class="btn-green" (click)="isClickYesOrNo(true)" id="clkSettingsYes" data-automate-id="clkSettingsYes">
        {{ 'GLOBAL.yes' | translate }}</button>
    </div>
  </div>
</ng-template>