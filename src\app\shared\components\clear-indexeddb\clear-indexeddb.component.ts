import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { ClearIndexedDB } from 'src/app/reducers/indexeddb/indexeddb.actions';
import { getFiltersPayload } from 'src/app/reducers/property/property.reducer';

@Component({
  selector: 'app-clear-indexeddb',
  template: `
    <button class="btn btn-info btn-sm px-6 text-white" (click)="clearIndexedDB()">
      Sync
    </button>
  `,
  styles: [],
})
export class ClearIndexedDbComponent implements OnInit {
  constructor(private store: Store<AppState>) { }

  ngOnInit(): void { }

  clearIndexedDB(): void {
    // Get the current filter payload from the store
    this.store
      .select(getFiltersPayload)
      .subscribe((payload) => {
        console.log('Current filter payload for clearing IndexedDB:', payload);
        // Dispatch the ClearIndexedDB action with the current payload
        this.store.dispatch(new ClearIndexedDB(payload));
      })
      .unsubscribe(); // Unsubscribe immediately after getting the value
  }
}
