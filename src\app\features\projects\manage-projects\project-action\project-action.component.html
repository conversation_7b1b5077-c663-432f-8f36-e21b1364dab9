<ng-container *ngIf="params.data.isArchived">
    <div class="d-flex">
        <div title="Restore" class="bg-blue-450 icon-badge" (click)="openArchiveModal(params.data)"><span
                class="icon ic-update m-auto ic-xxs" id="clkRestoreLead" data-automate-id="clkEditLead"></span></div>
        <div title="Delete Permanently" *ngIf="canPermanentDelete" class="bg-light-red icon-badge"
            id="clkArchiveProject" data-automate-id="clkArchiveProject" (click)="deletePermanently(params.data)"><span
                class="icon ic-delete m-auto ic-xxs"></span></div>
    </div>
</ng-container>

<div class="d-flex mr-10" *ngIf="!params.data.isArchived">
    <div *ngIf="canEdit" title="Edit" class="bg-accent-green icon-badge" (click)="editProject(params.data)">
        <span class="icon ic-pen m-auto ic-xxs" id="clkEditProject" data-automate-id="clkEditProject"></span>
    </div>
    <div *ngIf="canDelete" title="Delete" class="bg-light-red icon-badge" id="clkDeleteProject"
        (click)="openDeleteProjectModal(params.data)" data-automate-id="clkDeleteProject"><span
            class="icon ic-delete m-auto ic-xxs"></span></div>
    <share-external [data]="params.data" [moduleName]="'project'" [key]="'share-project'"
        [mailCount]="params.data?.contactRecords?.Email"
        [whatsAppCount]="params.data?.contactRecords?.WhatsApp"></share-external>
    <div *ngIf="canAssign && canViewForFilter" title="Assign To" class="bg-blue-800 icon-badge" id="clkAssignProject"
        data-automate-id="clkAssignProject" (click)="openAssignmentModal(assign)"><span
            class="icon ic-assign-to m-auto ic-xxs"></span>
    </div>
    <div *ngIf="params.data?.serialNo" title="Copy Microsite URL" class="bg-brown icon-badge" (click)="copyUrl()"><span
            class="icon ic-copy-clipboard m-auto ic-xxxs"></span></div>
</div>

<ng-template #assign>
    <form class="h-100vh text-coal">
        <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
            <h3 class="fw-semi-bold">{{ 'SIDEBAR.project' | translate }} {{ 'LEADS.assignment' | translate }}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide();"></div>
        </div>
        <div class="p-16 scrollbar h-100-108">
            <div class="bg-light-pearl mt-20 br-6 flex-between">
                <div class="flex-column pt-10 pl-10 pb-20">
                    <div class="fw-semi-bold fv-sm-caps"> {{ 'SIDEBAR.project' | translate }}
                        {{'GLOBAL.name' | translate}}</div>
                    <div class="fw-700 text-large">{{params.data?.name}}</div>
                </div>
                <div><img src="../../../../assets/images/profile.svg" class="mt-8" /></div>
            </div>
            <div class="field-label"> {{'SETTINGS.select-user' | translate}}
            </div>
            <div class="ng-select-sm-gray">
                <!-- <ng-select [virtualScroll]="true" [items]="canAssignToAny ? allActiveUsers : activeUsers"
                    [multiple]="true" [closeOnSelect]="false" bindLabel="fullName" bindValue="id" name="assignedUser"
                    [formControl]="assignedUser" placeholder="ex. Mounika Pampana" class="bg-white">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                class="checkmark"></span>{{item.firstName}}
                            {{item.lastName}}</div>
                    </ng-template>
                </ng-select> -->
                <div class="border br-20 bg-white align-center user w-max-content mt-10">
                    <ng-container *ngFor="let option of leadAssignmentOptions">
                        <div *ngIf="option == 'Configuration' || (option != 'Configuration' && ((!canAllowSecondaryUsers && (assignedUser?.length || assignedDuplicateUser?.length)) || ((canAllowSecondaryUsers && canEnableAllowSecondaryUsers) && (assignedPrimaryUsers?.length || assignedSecondaryUsers?.length || assignedDuplicateUser?.length))))"
                            class="activation" [ngClass]="{'active' : selectedSectionLeadAssignment == option}"
                            (click)="selectedSectionLeadAssignment = option; setListSelection();">
                            <span>{{option}}</span>
                        </div>
                    </ng-container>
                </div>
                <ng-container *ngIf="selectedSectionLeadAssignment === 'Configuration' else selectedUsers">
                    <div class="mt-10 mb-4 d-flex w-100 flex-wrap cursor-pointer">
                        <div class="w-50"
                            title="This feature will assign a duplicate of the Incoming Lead to a block of  selected user(s) at once."
                            (click)="canEnableAllowDuplicates ? resetIntegrationForm() : openConfirmModal(changePopup, 'allowDuplicateLeads');toggleAssignedUserValidation()">
                            <label class="mb-4 checkbox-container text-gray"
                                [ngClass]="{'pe-none': !canEnableAllowDuplicates}">
                                <input type="checkbox" [(ngModel)]="canAllowDuplicates"
                                    [ngModelOptions]="{standalone: true}" (change)="toggleAssignedUserValidation()" />
                                <span class="checkmark"></span>
                                <span class="line-break" [ngClass]="{'text-coal': canEnableAllowDuplicates}">Assign
                                    leads as duplicate(s)</span>
                            </label>
                        </div>
                        <div class="w-50"
                            title="This feature will assign a secondary Owner to a Incoming Lead to a block of  selected user(s) in Sequential Manner."
                            (click)="canEnableAllowSecondaryUsers ? resetIntegrationForm() : openConfirmModal(changePopup, 'allowSecondaryUsers');toggleAssignedUserValidation()">
                            <label class="mb-4 checkbox-container text-gray"
                                [ngClass]="{'pe-none': !canEnableAllowSecondaryUsers}">
                                <input type="checkbox" [(ngModel)]="canAllowSecondaryUsers"
                                    [ngModelOptions]="{standalone: true}" />
                                <span class="checkmark"></span>
                                <span class="line-break" [ngClass]="{'text-coal': canEnableAllowSecondaryUsers}">Assign
                                    to secondary user</span>
                            </label>
                        </div>
                    </div>
                    <form [formGroup]="integrationDuplicateForm" class="prevent-text-select" autocomplete="off">
                        <div [ngClass]="canAllowDuplicates ? 'field-label-req' : 'field-label'"
                            *ngIf="!canAllowSecondaryUsers">
                            {{'SETTINGS.select-user' | translate}}
                        </div>
                        <form-errors-wrapper [control]="integrationDuplicateForm?.controls?.['assignedUser']"
                            label="{{'SETTINGS.select-user' | translate}}">
                            <ng-select *ngIf="!canAllowSecondaryUsers" ResizableDropdown
                                [ngClass]="{'pe-none blinking' : (!canAssignToAny && isActiveUsersLoading) || (canAssignToAny && isAllActiveUsersLoading)}"
                                [items]="canAssignToAny ? allActiveUsers : activeUsers" bindLabel="fullName"
                                bindValue="id" [multiple]="true" groupBy="selectedAllGroup" [selectableGroup]="true"
                                [closeOnSelect]="false" [clearSearchOnAdd]="true" name="assignedUser"
                                (change)="sameAsSelectedUsers = false; handleSelectAll(true)"
                                placeholder="ex. Mounika Pampana" class="bg-white" [(ngModel)]="assignedUser"
                                formControlName="assignedUser">
                                <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span>Select All</div>
                                </ng-template>
                                <ng-template ng-label-tmp let-item="item">
                                    {{(item?.firstName || item?.lastName) ? (item.firstName+ " " +item.lastName) :
                                    "All"}}
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container" (click)="lastClickedOption = item">
                                        <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                            [checked]="item$.selected">
                                        <span class="checkmark"></span><span class="text-truncate-1 break-all">
                                            {{item.firstName}} {{item.lastName}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </form-errors-wrapper>
                        <ng-container *ngIf="canAllowDuplicates && canEnableAllowDuplicates && !canAllowSecondaryUsers">
                            <div class="field-label-req"> Select Lead Duplicate User(s)
                            </div>
                            <form-errors-wrapper [control]="integrationDuplicateForm.controls['assignedDuplicateUser']"
                                label="Select Lead Duplicate User(s)">
                                <ng-select [required]="true" [items]="canAssignToAny ? allActiveUsers : activeUsers"
                                    ResizableDropdown bindLabel="fullName" bindValue="id" [multiple]="true"
                                    groupBy="selectedAllGroup" [selectableGroup]="true" [closeOnSelect]="false"
                                    [clearSearchOnAdd]="true" (change)="sameAsSelectedUsers = false; handleSelectAll();"
                                    name="assignedDuplicateUser" placeholder="ex. Mounika Pampana" class="bg-white"
                                    [(ngModel)]="assignedDuplicateUser" formControlName="assignedDuplicateUser">
                                    <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span>Select All</div>
                                    </ng-template>
                                    <ng-template ng-label-tmp let-item="item">
                                        {{(item?.firstName || item?.lastName) ? (item.firstName+ " " +item.lastName) :
                                        "All"}}
                                    </ng-template>
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container" (click)="lastClickedOption = item">
                                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                                [checked]="item$.selected">
                                            <span class="checkmark"></span><span class="text-truncate-1 break-all">
                                                {{item.firstName}} {{item.lastName}}</span>
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </form-errors-wrapper>
                            <div class="d-flex">
                                <label class="checkbox-container mt-10" (click)="sameAsSelectedUsersClicked()">
                                    <input type="checkbox" [(ngModel)]="sameAsSelectedUsers"
                                        [ngModelOptions]="{standalone: true}" />
                                    <span class="checkmark"></span>
                                    <span class="line-break text-sm ip-pl-10">Same as <span
                                            class="fw-600 text-coal">Selected
                                            User(s)</span></span>
                                </label>
                            </div>
                            <div class="d-flex">
                                <label class="checkbox-container mt-10">
                                    <input type="checkbox" [(ngModel)]="canAssignSequentially"
                                        [ngModelOptions]="{standalone: true}" />
                                    <span class="checkmark"></span>
                                    <span class="line-break text-sm ip-pl-10">Assign duplicate leads sequentially</span>
                                </label>
                            </div>
                        </ng-container>
                    </form>

                    <ng-container *ngIf="canAllowSecondaryUsers && canEnableAllowSecondaryUsers">
                        <form [formGroup]="integrationDualOwnerForm" class="prevent-text-select" autocomplete="off">
                            <div class="field-label-req"> Select Primary Owner(s)
                            </div>
                            <form-errors-wrapper
                                [control]="integrationDualOwnerForm?.controls?.['assignedPrimaryUsers']"
                                label="'Select Primary Owner(s)'">
                                <ng-select [required]="true" [items]="canAssignToAny ? allActiveUsers : activeUsers"
                                    ResizableDropdown bindLabel="fullName" bindValue="id" [multiple]="true"
                                    groupBy="selectedAllGroup" [selectableGroup]="true" [closeOnSelect]="false"
                                    [clearSearchOnAdd]="true" name="assignedPrimaryUsers"
                                    formControlName="assignedPrimaryUsers" placeholder="ex. Mounika Pampana"
                                    class="bg-white" [(ngModel)]="assignedPrimaryUsers"
                                    (change)="sameAsPrimaryUsers = false; handleSelectAll()">
                                    <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span>Select All</div>

                                    </ng-template>
                                    <ng-template ng-label-tmp let-item="item">
                                        {{(item?.firstName || item?.lastName) ? (item.firstName+ " " +item.lastName) :
                                        "All"}}
                                    </ng-template>
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container" (click)="lastClickedOption = item">
                                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                                [checked]="item$.selected">
                                            <span class="checkmark"></span><span class="text-truncate-1 break-all">
                                                {{item.firstName}} {{item.lastName}}</span>
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </form-errors-wrapper>
                            <div class="field-label-req"> Select Secondary Owner(s)
                            </div>
                            <form-errors-wrapper
                                [control]="integrationDualOwnerForm?.controls?.['assignedSecondaryUsers']"
                                label="Select Secondary Owner(s)">
                                <ng-select [required]="true" [items]="canAssignToAny ? allActiveUsers : activeUsers"
                                    ResizableDropdown bindLabel="fullName" bindValue="id" [multiple]="true"
                                    groupBy="selectedAllGroup" [selectableGroup]="true" [closeOnSelect]="false"
                                    [clearSearchOnAdd]="true" name="assignedSecondaryUsers"
                                    formControlName="assignedSecondaryUsers" placeholder="ex. Mounika Pampana"
                                    class="bg-white" [(ngModel)]="assignedSecondaryUsers"
                                    (change)="sameAsPrimaryUsers = false; handleSelectAll()">
                                    <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span>Select All</div>

                                    </ng-template>
                                    <ng-template ng-label-tmp let-item="item">
                                        {{(item?.firstName || item?.lastName) ? (item.firstName+ " " +item.lastName) :
                                        "All"}}
                                    </ng-template>
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container" (click)="lastClickedOption = item">
                                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                                [checked]="item$.selected">
                                            <span class="checkmark"></span><span class="text-truncate-1 break-all">
                                                {{item.firstName}} {{item.lastName}}</span>
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </form-errors-wrapper>
                            <div class="d-flex">
                                <label class="mt-10 checkbox-container" (click)="sameAsSelectedUsersClicked(true)">
                                    <input type="checkbox" [(ngModel)]="sameAsPrimaryUsers"
                                        [ngModelOptions]="{standalone: true}" />
                                    <span class="checkmark"></span>
                                    <span class="line-break text-sm ip-pl-10">Same as <span
                                            class="fw-600 text-coal">Primary
                                            User(s)</span></span>
                                </label>
                            </div>
                            <ng-container *ngIf="canAllowDuplicates && canEnableAllowDuplicates">
                                <div class="field-label-req"> Select Lead Duplicate User(s)
                                </div>
                                <form-errors-wrapper
                                    [control]="integrationDualOwnerForm?.controls?.['assignedDuplicateUser']"
                                    label="Select Lead Duplicate User(s)">
                                    <ng-select [required]="true" [items]="canAssignToAny ? allActiveUsers : activeUsers"
                                        ResizableDropdown bindLabel="fullName" bindValue="id" [multiple]="true"
                                        groupBy="selectedAllGroup" [selectableGroup]="true" [closeOnSelect]="false"
                                        [clearSearchOnAdd]="true" name="assignedDuplicateUser"
                                        placeholder="ex. Mounika Pampana" class="bg-white"
                                        formControlName="assignedDuplicateUser" [(ngModel)]="assignedDuplicateUser"
                                        (change)="sameAsAbove = false; handleSelectAll()">
                                        <ng-template ng-optgroup-tmp let-item="item" let-item$="item$"
                                            let-index="index">
                                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                    class="checkmark"></span>Select All</div>
                                        </ng-template>
                                        <ng-template ng-label-tmp let-item="item">
                                            {{(item?.firstName || item?.lastName) ? (item.firstName+ " " +item.lastName)
                                            : "All"}}
                                        </ng-template>
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <div class="checkbox-container" (click)="lastClickedOption = item">
                                                <input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected">
                                                <span class="checkmark"></span>{{item.firstName}} {{item.lastName}}
                                            </div>
                                        </ng-template>
                                    </ng-select>
                                </form-errors-wrapper>
                                <div class="d-flex">
                                    <label class="mt-10 checkbox-container"
                                        (click)="sameAsPrimarySecondaryUsersClicked()">
                                        <input type="checkbox" [(ngModel)]="sameAsAbove"
                                            [ngModelOptions]="{standalone: true}" />
                                        <span class="checkmark"></span>
                                        <span class="line-break text-sm ip-pl-10">Same as above</span>
                                    </label>
                                </div>
                                <div class="flex-between">
                                    <div class="align-center"
                                        *ngFor="let userType of ['Primary User(s)', 'Secondary User(s)']; let i = index">
                                        <div class="form-check form-check-inline align-center btn pl-0 py-0 mr-0">
                                            <input type="radio" id="userType{{ i }}" data-automate-id="userType"
                                                name="userType" [checked]="userType === selectedUserType"
                                                [value]="userType" (change)="sameAsAbove = false"
                                                [(ngModel)]="selectedUserType" [ngModelOptions]="{standalone: true}"
                                                class="radio-check-input mr-10" />
                                            <label class="fw-600 text-secondary cursor-pointer text-large"
                                                for="userType{{ i }}">
                                                {{ userType }}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex">
                                    <label class="checkbox-container mt-10">
                                        <input type="checkbox" [(ngModel)]="canAssignSequentially"
                                            [ngModelOptions]="{standalone: true}" />
                                        <span class="checkmark"></span>
                                        <span class="line-break text-sm ip-pl-10">Assign duplicate leads
                                            sequentially</span>
                                    </label>
                                </div>
                            </ng-container>
                        </form>
                    </ng-container>

                </ng-container>
                <ng-template #selectedUsers>
                    <div class="mt-10" *ngIf="assignedUser?.length && !canAllowDuplicates && !canAllowSecondaryUsers">
                        <div class="d-flex">
                            <div class="cursor-pointer mt-12 text-accent-green">Selected User(s)
                            </div>
                        </div>
                        <div class="mt-12">
                            <div class="flex-between mb-12" *ngFor="let user of assignedUser; let i = index">
                                <div class="align-center">
                                    <div class="dot dot-xl bg-pearl-90 mr-6">
                                        <span class="fw-semi-bold text-normal text-white text-uppercase">{{
                                            user ?
                                            getAssignedToDetails(user, canAssignToAny ? allUserList
                                            :userList)?.firstName[0] +
                                            getAssignedToDetails(user, canAssignToAny ? allUserList
                                            :userList)?.lastName[0] :
                                            '--'}}</span>
                                    </div>
                                    <div class="fw-semi-bold text-large text-coal">{{getAssignedToDetails(user,
                                        canAssignToAny ? allUserList : userList, true) || '--'}}</div>
                                </div>
                                <div>
                                    <span (click)="removeUserFromSelection(user)" class="bg-light-red icon-badge">
                                        <span class="icon ic-delete m-auto ic-xxs"></span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="canAllowDuplicates && !canAllowSecondaryUsers">
                        <div class="d-flex">
                            <div class="cursor-pointer mr-20 mt-12" *ngIf="assignedUser?.length"
                                (click)="originalDuplicateListToggle('original')"
                                [ngClass]="{'text-accent-green field-label-left-underline-green': listSelection == 'original'}">
                                Original
                                User(s)
                            </div>
                            <div class="cursor-pointer mt-12" *ngIf="assignedDuplicateUser?.length"
                                (click)="originalDuplicateListToggle('duplicate')"
                                [ngClass]="{'text-accent-green field-label-left-underline-green': listSelection == 'duplicate'}">
                                Duplicate User(s)
                            </div>
                        </div>
                        <div class="mt-12">
                            <div class="flex-between mb-12" *ngFor="let user of filteredUsers; let i = index">
                                <div class="align-center">
                                    <div class="dot dot-xl bg-pearl-90 mr-6">
                                        <span class="fw-semi-bold text-normal text-white text-uppercase">{{
                                            user ?
                                            getAssignedToDetails(user, canAssignToAny ? allUserList
                                            :userList)?.firstName[0] +
                                            getAssignedToDetails(user, canAssignToAny ? allUserList
                                            :userList)?.lastName[0] :
                                            '--'}}</span>
                                    </div>
                                    <div class="fw-semi-bold text-large text-coal">{{getAssignedToDetails(user,
                                        canAssignToAny ? allUserList : userList, true) || '--'}}</div>
                                </div>
                                <div>
                                    <span (click)="removeUserFromSelection(user)" class="bg-light-red icon-badge">
                                        <span class="icon ic-delete m-auto ic-xxs"></span></span>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex">

                        </div>
                    </div>
                    <div *ngIf="primarySeondaryUsers?.length && canAllowSecondaryUsers">
                        <div class="d-flex">
                            <div class="cursor-pointer mr-20 mt-12" *ngIf="assignedPrimaryUsers?.length"
                                (click)="originalDuplicateListToggle('primary')"
                                [ngClass]="{'text-accent-green field-label-left-underline-green': listSelection == 'primary'}">
                                Primary
                            </div>
                            <div class="cursor-pointer mr-20 mt-12" *ngIf="assignedSecondaryUsers?.length"
                                (click)="originalDuplicateListToggle('secondary')"
                                [ngClass]="{'text-accent-green field-label-left-underline-green': listSelection == 'secondary'}">
                                Secondary
                            </div>
                            <div class="cursor-pointer mt-12"
                                *ngIf="canAllowDuplicates && assignedDuplicateUser?.length"
                                (click)="originalDuplicateListToggle('duplicate')"
                                [ngClass]="{'text-accent-green field-label-left-underline-green': listSelection == 'duplicate'}">
                                Duplicate User(s)
                            </div>
                        </div>
                        <div class="mt-12">
                            <div class="flex-between mb-12" *ngFor="let user of primarySeondaryUsers; let i = index">
                                <div class="align-center">
                                    <div class="dot dot-xl bg-pearl-90 mr-6">
                                        <span class="fw-semi-bold text-normal text-white text-uppercase">{{
                                            user ?
                                            getAssignedToDetails(user, canAssignToAny ? allUserList
                                            :userList)?.firstName[0] +
                                            getAssignedToDetails(user, canAssignToAny ? allUserList
                                            :userList)?.lastName[0] :
                                            '--'}}</span>
                                    </div>
                                    <div class="fw-semi-bold text-large text-coal">{{getAssignedToDetails(user,
                                        canAssignToAny ? allUserList : userList, true) || '--'}}</div>
                                </div>
                                <div>
                                    <span (click)="removeUserFromSelection(user)" class="bg-light-red icon-badge">
                                        <span class="icon ic-delete m-auto ic-xxs"></span></span>
                                </div>
                            </div>
                        </div>
                        <!-- Check this -->
                        <div class="d-flex"></div>
                    </div>
                </ng-template>
                <div class="flex-end px-20 py-10 box-shadow-20 w-100 position-absolute left-0 bottom-0">
                    <div class="fw-600 text-black-200 text-decoration-underline cursor-pointer" (click)="closeModal()">
                        {{ 'BUTTONS.cancel' | translate }}
                    </div>
                    <div class="border-right mx-12 h-16"></div>
                    <div class="br-4 bg-coal py-10 px-20 text-white cursor-pointer" (click)="assignAccount()">{{
                        'BUTTONS.save' |
                        translate }}</div>
                </div>

                <ng-template #changePopup>
                    <div class="p-20">
                        <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
                        <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div>
                        <div class="flex-end mt-30">
                            <button class="btn-gray mr-20" (click)="closePopup()" id="clkSettingsCancel"
                                data-automate-id="clkSettingsCancel">
                                cancel</button>

                            <button class="btn-green px-12 text-nowrap min-w-fit-content" (click)="goToGlobalConfig()"
                                id="clkSettingsYes" data-automate-id="clkSettingsYes">
                                Go to Lead Settings
                            </button>
                        </div>
                    </div>
                </ng-template>
            </div>
        </div>
    </form>
</ng-template>