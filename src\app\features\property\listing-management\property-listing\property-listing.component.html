<div class="bg-coal w-100 px-20 py-12 text-white brtl-10 brtr-10 flex-between">
    <h3 class="fw-semi-bold">{{ action ==='list' ? 'Property Listing' : action === 'delist' ? 'Property Delisting' :
        'Modify Listing'}}</h3>
    <a class="ic-close-secondary ic-close-modal tb-ic-close-secondary" (click)="closeModal()"></a>
</div>
<ng-container *ngIf="data?.length else noFound">
    <div class="flex-between p-12">
        <h5 class="fw-600">Selected Property(s)</h5>
        <form [formGroup]="listForm" autocomplete="off">
            <div class="d-flex mr-20" *ngIf="action !== 'modify'">
                <div class="align-center p-8 ml-12">
                    <h6 class="text-sm text-dark-gray">Select Portal: </h6>
                    <div class="w-130 ml-4 ng-select-sm  br-20 no-validation">
                        <form-errors-wrapper [control]="listForm.controls['portal']" label="portal">
                            <ng-select [virtualScroll]="true" [multiple]="true" appSelectAll class="bg-white"
                                placeholder="{{ 'GLOBAL.select' | translate }}" [searchable]="false" ResizableDropdown
                                [items]="bulkOperation ? listingSource :  data[0]?.shouldVisisbleOnListing ?  data[0]?.listingSources : listingSource"
                                class="w-150" formControlName="portal" bindLabel="displayName" bindValue="id"
                                [closeOnSelect]="false">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.displayName}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="bg-white p-12 scrollbar table-scrollbar ip-w-100-15">
        <table class="table standard-table no-vertical-border">
            <thead>
                <tr class="w-100 text-nowrap">
                    <th class="w-110">Property Name</th>
                    <th class="w-100px">
                        Permit Number</th>
                    <th class="w-80">Agent Name</th>
                    <th class="w-100px">Broker Number</th>
                    <th class="w-80">Portal</th>
                    <th class="w-160">Address</th>
                    <th *ngIf="bulkOperation" class="w-80">{{ 'GLOBAL.actions' | translate }}</th>
                </tr>
            </thead>
            <tbody class="text-secondary fw-semi-bold max-h-100-380">
                <ng-container *ngFor="let listing of data">
                    <tr class="w-100">
                        <td class="w-110">
                            <div [title]="listing.title" class="text-truncate w-100">
                                {{ listing.title }}</div>
                        </td>
                        <td class="w-100px">
                            <ng-container *ngIf="listing?.uaeEmirate === 1; else notRequired">
                              <ng-container *ngIf="listing?.dldPermitNumber || listing?.dtcmPermit; else noPermitMessage">
                                <div class="text-truncate w-100px">
                                  {{ listing?.dldPermitNumber || listing?.dtcmPermit }}
                                </div>
                              </ng-container>
                              <ng-template #noPermitMessage>
                                <div title="Required" class="text-truncate w-100px text-red">
                                  Required
                                </div>
                              </ng-template>
                            </ng-container>
                            <ng-template #notRequired>
                              <div class="text-truncate w-100px">--</div>
                            </ng-template>
                          </td>                                                
                        <td class="w-80">
                            <div class="text-truncate-1 break-all" [title]="getUser(listing?.assignedTo)">{{
                                getUser(listing?.assignedTo)}}</div>
                        </td>
                        <td class="w-100px">
                            <ng-container *ngIf="listing?.uaeEmirate === 1; else notRequired">
                              <div class="text-truncate-1 break-all"
                                   [title]="getLicense(listing?.assignedTo)"
                                   [ngClass]="{'text-red': getLicense(listing?.assignedTo) === 'Required'}">
                                {{ getLicense(listing?.assignedTo) }}
                              </div>
                            </ng-container>
                            <ng-template #notRequired>
                              <div class="text-truncate-1 break-all">--</div>
                            </ng-template>
                          </td>                          
                        <td class="w-80">
                            <div>{{ getListingSources(listing?.listingSources) }}</div>
                        </td>
                        <td class="w-160">
                            <ul *ngIf="listing?.listingSourceAddresses?.length; else noAddressMessage">
                                <li *ngFor="let address of listing.listingSourceAddresses"
                                    class="text-truncate-1 break-all">
                                    <strong>{{ address.listingSource?.displayName }} - </strong>
                                    <span [title]="getLocationDetailsByObj(address)">{{ getLocationDetailsByObj(address)
                                        }}</span>
                                </li>
                            </ul>
                            <ng-template #noAddressMessage>
                                <div [title]="'Required'" class="text-truncate w-100px text-red">
                                    Required
                                </div>
                            </ng-template>
                        </td>
                        <td *ngIf="bulkOperation" class="w-80">
                            <div class="align-center">
                                <div title="Delete" class="bg-light-red icon-badge"
                                    (click)="deselectProperty(listing?.id)">
                                    <span class="icon ic-trash ic-xxxs"></span>
                                </div>
                                <div title="Modify" class="btn-accent-green icon-badge" *ngIf="action === 'modify'"
                                    (click)="openAddPortal(listing,addPortal)">
                                    <span class="icon ic-pen ic-xxxs"></span>
                                </div>
                            </div>
                        </td>
                    </tr>
                </ng-container>
            </tbody>
        </table>
    </div>
</ng-container>
<div class="flex-end modal-footer bg-white box-shadow-3">
    <h6 *ngIf="action !== 'modify'" class="text-black-10 fw-semi-bold text-decoration-underline cursor-pointer"
        (click)="closeModal()">{{
        'BUTTONS.cancel' | translate }}</h6>
    <button class="btn-coal ml-20" *ngIf="action !== 'modify'" (click)="onPublish()">{{ action ==='list' ? 'Publish' :
        action === 'delist' ?
        'Delist' : 'Modify'}}</button>
</div>
<ng-template #noFound>
    <div class="flex-center h-100-210">
        <div class="flex-column">
            <ng-lottie [options]='noDataFound'></ng-lottie>
            <h3 class="header-5 fw-600 text-center">{{'PROFILE.no-property-found' | translate }} For {{ action ==='list'
                || action === 'modify'
                ? 'Listing' : 'Delisting' }}</h3>
        </div>
    </div>
</ng-template>
<ng-template #gridLoader>
    <div class="flex-center h-100 mt-60">
        <application-loader></application-loader>
    </div>
</ng-template>

<ng-template #addPortal>
    <h5 class="text-white fw-600 px-20 py-12 bg-black">Modify
    </h5>
    <div *ngIf="!selectedListing.dldPermitNumber && !selectedListing.dtcmPermit" class="ml-20 mt-20 error-text">Property
        does
        not have a permit number and cannot able to list</div>
    <form [formGroup]="listForm" autocomplete="off" class="pb-20 px-30">
        <div class="field-label-req">Modify Portal</div>
        <form-errors-wrapper [control]="listForm.controls['portal']" label="portal">
            <ng-select [virtualScroll]="true" [items]="listingSource" [multiple]="true" appSelectAll formControlName="portal"
                [closeOnSelect]="false"
                [ngClass]="{'disabled' : !selectedListing.dldPermitNumber && !selectedListing.dtcmPermit }"
                class="bg-white" ResizableDropdown placeholder="Select Portals" bindValue="id" bindLabel="displayName">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                            class="checkmark"></span><span class="text-truncate-1 break-all">{{item.displayName}}</span>
                    </div>
                </ng-template>
            </ng-select>
        </form-errors-wrapper>
        <div class="flex-end mt-30">
            <button class="btn-gray mr-20" id="addPortalCancel" data-automate-id="addPortalCancel"
                (click)="modalRef.hide()">{{
                'BUTTONS.cancel' | translate }}</button>
            <h6 class="text-white flex-center w-100px h-32 br-4 fw-600 cursor-pointer bg-black"
                (click)="onPortalUpdate()">
                Publish</h6>
        </div>
    </form>
</ng-template>