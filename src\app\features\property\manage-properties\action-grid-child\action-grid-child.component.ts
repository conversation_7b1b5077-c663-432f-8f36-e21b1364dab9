import {
  Component,
  <PERSON>E<PERSON>ter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, takeUntil } from 'rxjs';

import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAssignedToDetails,
  getMSUrl,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { PropertyListingComponent } from 'src/app/features/property/listing-management/property-listing/property-listing.component';
import {
  AddToList,
  DeList,
  FetchAllListing,
} from 'src/app/reducers/listing-site/listing-site.actions';
import { getListingSources } from 'src/app/reducers/listing-site/listing-site.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  ArchiveProperty,
  CloneProperty,
  DeleteProperty,
  FetchArchivedPropertyList,
  IncreaseShareCount,
  UpdateUserAssignment,
} from 'src/app/reducers/property/property.actions';
import {
  getAdminsAndReportees,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { PropertyService } from 'src/app/services/controllers/properties.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'manage-action-grid-reassign',
  templateUrl: './action-grid-child.component.html',
})
export class PropertiesActionGridComponent
  implements ICellRendererAngularComp, OnDestroy, OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public params: any;
  canEdit: boolean = false;
  canDelete: boolean = false;
  canPermanentDelete: boolean = false;
  canAssign: boolean = false;
  canCloneProperty: boolean = false;
  canAssignToAny: boolean = false;
  canViewOwnerInfo: boolean = false;
  getAssignedToDetails = getAssignedToDetails;
  activeUsers: any;
  allActiveUsers: any;
  inactiveUsers: any;
  assignedUser = new FormControl([]);
  listingOnBehalfUser = new FormControl(null)
  listForm: FormGroup;
  allUserList: any[] = [];
  userList: any[] = [];
  assignedUserDetails: string[] = [];
  currentPath: string;
  listingSource: any[];
  canListing: boolean;
  license: string | string[];
  allAssignUserList: any[] = [];
  isListingOnBehalf = new FormControl(false);
  listingTypeOptions = [
    { value: false, label: 'Lisiting By' },
    { value: true, label: 'Listing on Behalf' },
  ];
  deactiveUsers: any[] = [];
  allDeActiveUsers: any[] = [];
  assignUserList: any;
  constructor(
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    public router: Router,
    private _notificationService: NotificationsService,
    private _translateService: TranslateService,
    private store: Store<AppState>,
    private fb: FormBuilder,
    private propertyService: PropertyService,
    public trackingService: TrackingService
  ) {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        this.canEdit = permissions?.includes('Permissions.Properties.Update');
        this.canCloneProperty = permissions?.includes('Permissions.ListingIntegration.CloneProperty');
        this.canDelete = permissions?.includes('Permissions.Properties.Delete');
        this.canPermanentDelete = permissions?.includes('Permissions.Properties.PermanentDelete');

        if (permissions?.includes('Permissions.Properties.Assign')) {
          this.canAssign = true;
        }
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
        }
        if (permissions?.includes('Permissions.Properties.ViewOwnerInfo')) {
          this.canViewOwnerInfo = true;
        }
        if (permissions?.includes('Permissions.Properties.PublishProperty')) {
          this.canListing = true;
        }
      });

    this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.assignUserList = data;
        this.userList = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.userList = assignToSort(this.userList, '');
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
        this.deactiveUsers = data?.filter((user: any) => !user.isActive);
        this.deactiveUsers = this.deactiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.deactiveUsers = assignToSort(this.deactiveUsers, '');
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allAssignUserList = data;
        this.allUserList = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allUserList = assignToSort(this.allUserList, '');
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
        this.allDeActiveUsers = data?.filter((user: any) => !user.isActive);
        this.allDeActiveUsers = this.allDeActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allDeActiveUsers = assignToSort(this.allDeActiveUsers, '');
      });
  }

  ngOnInit(): void {
    this.isListingOnBehalf.valueChanges.subscribe(value => {
      const listingUser = Array.isArray(this.params.data?.listingOnBehalf) && this.params.data.listingOnBehalf[0]
        ? this.params.data?.listingOnBehalf[0]
        : null;

      this.assignedUserDetails = this.isListingOnBehalf.value === true
        ? (listingUser ? [listingUser] : [])
        : this.params.data?.assignedTo;
      this.assignedUser.patchValue(this.params.data?.assignedTo);
      if (Array.isArray(this.params.data?.listingOnBehalf) && this.params.data.listingOnBehalf.length > 0) {
        this.listingOnBehalfUser.patchValue(this.params.data?.listingOnBehalf[0]);
      } else {
        this.listingOnBehalfUser.patchValue(null);
      }
    });
    this.listForm = this.fb.group({
      portal: [null, [Validators.required]],
    });
    if (this.params?.data?.assignedTo) {
      this.license = this.getLicense(this.params.data.assignedTo);
    }

    this.store
      .select(getListingSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.listingSource = [...data];
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  refresh(): boolean {
    return false;
  }

  getLicense(assignedTo: any[] | undefined): string | string[] {
    if (!assignedTo || assignedTo.length === 0) {
      return 'Required';
    }

    const licenses = assignedTo?.map(userId => {
      const user = this.allUserList?.find((user: any) => user.id === userId);
      return user ? user?.licenseNo : null;
    })
      .filter((license): license is string => license !== null);

    return licenses.length ? licenses : 'Required';
  }

  listingProperty(action: string, data: any) {
    const initialState: any = {
      bulkOperation: false,
      action: action,
      data: [data],
    };

    this.modalRef = this.modalService.show(
      PropertyListingComponent,
      Object.assign({}, {
        class: 'modal-800 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      })
    );
  }

  getListingSources() {
    return this.params.data?.shouldVisisbleOnListing ? this.params?.data?.listingSources : this.listingSource;
  }

  editProperty(data: any) {
    const activePropertyId: string = this.params.data.id;
    if (this.currentPath === '/properties/manage-listing') {
      this.router.navigate(['/properties/edit-listing/' + activePropertyId], {
        state: { activePropertyId },
      });
    } else {
      this.router.navigate(['/properties/edit-property/' + activePropertyId], {
        state: { activePropertyId },
      });
      this.trackingService.trackFeature(`Web.Property.Actions.Edit.Click`);
    }
  }

  openDeleteModal(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.title,
      fieldType: 'property',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteProperty(this.params.data.id));
        }
      });
    }
  }

  openArchiveModal(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: data?.isArchived ? 'Restore' : 'Archive',
      title: data?.title,
      fieldType: 'property',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.trackingService.trackFeature(`Web.Property.Actions.Delete.Click`);
          this.store.dispatch(new ArchiveProperty(data?.id));
          if (data?.isArchived) {
            this._notificationService.success(`Property Restore successfully.`);
          } else {
            this._notificationService.success(`Property Archive successfully.`);
          }
        }
      });
    }
  }

  openAssignmentModal(assign: TemplateRef<any>) {
    this.modalRef = this.modalService.show(assign, {
      class: 'modal-350 right-modal',
      keyboard: false,
    });
    this.assignedUser.patchValue(this.params?.data?.assignedTo);
    this.listingOnBehalfUser.patchValue(this.params?.data?.listingOnBehalf?.[0]);
    this.isListingOnBehalf.patchValue(this.params?.data?.isListingOnBehalf ?? false);
    const listingUser = Array.isArray(this.params?.data?.listingOnBehalf) && this.params.data.listingOnBehalf[0]
      ? this.params.data.listingOnBehalf[0]
      : null;

    this.assignedUserDetails = this.isListingOnBehalf?.value === true
      ? (listingUser ? [listingUser] : [])
      : this.params?.data?.assignedTo || [];

    this.trackingService.trackFeature(`Web.Property.Actions.AssignTo.Click`);
  }
  assignAccount() {
    let payload: any = {
      assignedTo: this.assignedUser.value,
      propertyId: this.params.data?.id,
      isListingOnBehalf: this.isListingOnBehalf?.value,
      listingOnBehalf: this.listingOnBehalfUser?.value ? [this.listingOnBehalfUser?.value] : []
    };
    this.store.dispatch(new UpdateUserAssignment(payload));
    this.modalService.hide();
  }

  copyUrl(): void {
    navigator.clipboard?.writeText(getMSUrl(this.params.data?.serialNo));
    this._notificationService.success(
      this._translateService.instant('GLOBAL.link-copied')
    );
    this.trackingService.trackFeature(`Web.Property.Actions.Copy.Click`);
  }

  initiateCall() {
    let payloadCount: any = {
      ids: [this.params?.data?.id],
      contactType: 1,
    };
    this.store.dispatch(new IncreaseShareCount(payloadCount));
    this.store.dispatch(new LoaderHide());
  }

  deleteAssignUser(userId: string) {
    this.assignedUserDetails = this.assignedUserDetails.filter(
      (id: string) => id !== userId
    );
    if (this.isListingOnBehalf.value === true) {
      this.listingOnBehalfUser.setValue(this.assignedUserDetails?.[0] || null);
      this.assignedUser.setValue(this.params?.data?.assignedTo)
    } else {
      this.assignedUser.setValue(this.assignedUserDetails);
      this.listingOnBehalfUser.setValue(this.params?.data?.listingOnBehalf?.[0] || null);
    }
  }

  onUserSelect(event: any[]) {
    if (!Array.isArray(event)) {
      if (this.isListingOnBehalf.value === true) {
        this.assignedUserDetails = event ? [event] : [];
        this.listingOnBehalfUser.setValue(event);
        this.assignedUser.setValue(this.params?.data?.assignedTo);
      }
      return;
    }
    const selectedUserIds = new Set(
      event
        .filter(
          (user) => user.isActive || this.assignedUserDetails.includes(user.id)
        )
        .map((user) => user.id)
    );
    this.assignedUserDetails = Array.from(selectedUserIds);

    if (this.isListingOnBehalf.value === true) {
      this.listingOnBehalfUser.setValue(this.assignedUserDetails?.[0] || null);
      this.assignedUser.setValue(this.params?.data?.assignedTo);
    } else {
      this.assignedUser.setValue(this.assignedUserDetails);
      this.listingOnBehalfUser.setValue(this.params?.data?.listingOnBehalf?.[0] || null);
    }
  }

  onClear(item: any) {
    this.assignedUserDetails = this.assignedUserDetails.filter(
      (userId) => userId !== item.id
    );
    if (this.isListingOnBehalf.value === true) {
      this.listingOnBehalfUser.setValue(this.assignedUserDetails?.[0] || null);
      this.assignedUser.setValue(this.params?.data?.assignedTo)
    } else {
      this.assignedUser.setValue(this.assignedUserDetails);
      this.listingOnBehalfUser.setValue(this.params?.data?.listingOnBehalf?.[0] || null);
    }
  }

  onPortalUpdate() {
    if (!this.listForm.valid) {
      validateAllFormFields(this.listForm);
      return;
    }
    const portalValue = this.listForm.get('portal').value;

    if (portalValue) {
      let payload = { ids: [this.params.data.id], listingSourceIds: portalValue }
      const action = this.params.data?.shouldVisisbleOnListing
        ? new DeList(payload)
        : new AddToList(payload);
      this.store.dispatch(action);
    }
    this.modalRef.hide();
    this.listForm.reset();
  }

  deletePermanently(data: any) {
    let initialState: any = {
      message: data?.leadsCount
        ? `This property is associated with ${data?.leadsCount} lead${data?.leadsCount !== 1 ? 's' : ''}${data?.prospectCount ? ` and ${data?.prospectCount} data${data?.prospectCount !== 1 ? '' : ''}` : ''}. Do you still want to`
        : data?.prospectCount
          ? `This project is associated with ${data?.prospectCount} data${data?.prospectCount !== 1 ? '' : ''}. Do you still want to`
          : 'GLOBAL.user-confirmation',
      confirmType: 'permanently delete',
      title: data?.name,
      fieldType: 'property',
    };

    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.propertyService.deletePropertyPermanently([data?.id]).subscribe((res: any) => {
            if (res)
              if (this.currentPath === '/properties/manage-listing') {
                this.store.dispatch(new FetchAllListing());
              } else {
                this.store.dispatch(new FetchArchivedPropertyList());
              }
            this._notificationService.success(`Property deleted successfully.`);
            this.modalService.hide();
          })
        }
      });
    }
  }

  cloneProperty(data: any) {
    this.store.dispatch(new CloneProperty({
      propertyId: data.id,
    }));
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
