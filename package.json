{"name": "lr-black", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "node --max_old_space_size=8000 ./node_modules/@angular/cli/bin/ng build --configuration=production --output-hashing=all", "build:qa": "ng build --configuration=qa --output-hashing=all", "build:dev": "ng build --configuration=dev --output-hashing=all", "watch": "ng build --watch --configuration development", "test": "ng test", "postinstall": "node ./scripts/service-worker-workaround.js"}, "private": true, "dependencies": {"@angular-slider/ngx-slider": "^2.0.4", "@angular/animations": "^14.1.0", "@angular/cdk": "^14.2.7", "@angular/common": "^14.1.0", "@angular/compiler": "^14.1.0", "@angular/core": "^14.1.0", "@angular/fire": "^7.6.1", "@angular/forms": "^14.1.0", "@angular/google-maps": "^14.2.7", "@angular/material": "^14.2.7", "@angular/platform-browser": "^14.1.0", "@angular/platform-browser-dynamic": "^14.1.0", "@angular/router": "^14.1.0", "@angular/service-worker": "^14.1.0", "@canvasjs/angular-charts": "^1.0.3", "@cashfreepayments/cashfree-js": "^1.0.5", "@ckeditor/ckeditor5-angular": "^7.0.1", "@ckeditor/ckeditor5-build-classic": "^41.3.1", "@ckeditor/ckeditor5-core": "^41.3.1", "@ckeditor/ckeditor5-engine": "^41.3.1", "@ckeditor/ckeditor5-utils": "^41.3.1", "@ckeditor/ckeditor5-watchdog": "^41.3.1", "@danielmoncada/angular-datetime-picker": "^14.2.0", "@fingerprintjs/fingerprintjs": "^4.5.1", "@microsoft/signalr": "^8.0.0", "@ng-select/ng-select": "^9.0.2", "@ngrx/effects": "^14.3.1", "@ngrx/entity": "^14.3.1", "@ngrx/router-store": "^14.3.1", "@ngrx/store": "^14.3.1", "@ngrx/store-devtools": "^14.3.1", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@popperjs/core": "^2.11.6", "@types/facebook-js-sdk": "^3.3.6", "ag-grid-angular": "^28.2.0", "ag-grid-community": "^28.2.0", "angular-google-charts": "^2.2.3", "angular-idle-preload": "^3.0.0", "angular2-notifications": "^12.0.0", "bootstrap": "^5.2.2", "chart.js": "^4.4.0", "chartjs-plugin-annotation": "^2.1.0", "countries-list": "^3.1.1", "email-validator": "^2.0.4", "firebase": "^9.16.0", "jquery": "^3.6.1", "libphonenumber-js": "^1.10.44", "lottie-web": "^5.9.6", "moment": "^2.29.4", "ng-circle-progress": "^1.6.0", "ng2-charts": "^4.1.1", "ngx-bootstrap": "^9.0.0", "ngx-device-detector": "^4.0.0", "ngx-drag-scroll": "^14.0.1", "ngx-file-drop": "^14.0.1", "ngx-lottie": "^9.1.0", "ngx-mat-intl-tel-input": "^5.0.0", "ngx-webcam": "^0.4.1", "rxjs": "~7.5.0", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.1.0", "@angular/cli": "^14.2.12", "@angular/compiler-cli": "^14.1.0", "@types/google.visualization": "^0.0.69", "@types/jasmine": "~4.0.0", "jasmine-core": "~4.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~4.7.2", "webpack": "^5.79.0"}}