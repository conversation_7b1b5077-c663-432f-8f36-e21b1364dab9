import {
  Component,
  EventEmitter,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { WHATSAPP_SHARE_API } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  getTenantName,
  getTimeZoneDate,
  ProjectTemplateMsg,
  PropertyTemplateMsg,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { platformShareLinks } from 'src/app/core/utils/share.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  CommunicationCount,
  CommunicationMessage,
} from 'src/app/reducers/lead/lead.actions';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchUnitById,
  FetchUnitInfoById,
  IncreaseProjectShareCount,
  IncreaseProjectUnitShareCount,
} from 'src/app/reducers/project/project.action';
import {
  getUnitById,
  getUnitInfo,
} from 'src/app/reducers/project/project.reducer';
import { IncreaseShareCount } from 'src/app/reducers/property/property.actions';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { FetchTemplateModule } from 'src/app/reducers/template/template.actions';
import {
  getTemplatesModule,
  getTemplatesModuleIsLoading,
} from 'src/app/reducers/template/template.reducer';
import { TenantService } from 'src/app/services/controllers/tenant.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'share-external',
  templateUrl: './share-external.component.html',
})
export class ShareExternalComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() data: any;
  @Input() leadData: any = {};
  @Input() moduleName: any;
  @Input() key: any;
  @Input() type: any;
  @Input() contactPhone: any;
  @Input() contactMail: any;
  @Input() whatsAppCount: any;
  @Input() mailCount: any;
  @Input() closeModal: any;
  @Input() closeShareExternalComponent: any;
  @Input() alternatePhone: any;
  @Input() leadId: any;

  shareForm: FormGroup;
  whatsAppUrl: string;
  // dataFormat: string;
  userName: string =
    JSON.parse(localStorage.getItem('userDetails'))?.given_name +
    ' ' +
    JSON.parse(localStorage.getItem('userDetails'))?.family_name;
  bulkEmailIds: string[] = [];
  platformShareLinks = platformShareLinks;
  // currentDate = moment(new Date().toISOString());
  contactNumber: any;
  projectId: any;
  shareDetails: any;
  templates: [];
  unitTypes: [];
  unitInfoData: any = [];
  isTemplatesLoading: boolean = true;
  areaSizeUnits: any;
  selectedContact: any;
  tenantName: string = getTenantName();
  canViewOwner: boolean = false;
  defaultCurrency: string;
  userData: any;
  currentDate: Date = new Date();

  constructor(
    public options: ModalOptions,
    private store: Store<AppState>,
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    private tenantService: TenantService,
    private fb: FormBuilder,
    private sharedDataService: ShareDataService,
    public trackingService: TrackingService
  ) {
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
      });
  }

  ngOnInit() {
    this.shareForm = this.fb.group({
      selectedUnit: ['', Validators.required],
      selectedTemplateType: ['project'],
      callType: ['primary'],
      selectedTemplate: [''],
      message: [''],
    });

    this.modalService.onHide.subscribe(() => {
      this.shareForm.patchValue({
        selectedUnit: null,
        selectedTemplate: null,
        message: '',
      });
    });

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });

    this.store
      .select(getUnitInfo)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.unitTypes = data?.map((unit: any) => unit);
      });

    this.store
      .select(getUnitById)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.unitInfoData = data;
      });

    this.store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Properties.ViewOwnerInfo')) {
          this.canViewOwner = true;
        }
      });

    if (Array.isArray(this.data)) {
      this.bulkEmailIds = this.data?.map((item: any) => item?.email);
    }

    this.store
      .select(getTemplatesModuleIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isTemplatesLoading = isLoading;
      });

    this.shareForm.controls['selectedUnit'].valueChanges.subscribe(
      (val: string) => {
        this.getTemplateData();
        this.getUnitData();
      }
    );

    this.store
      .select(getTemplatesModule)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res != '' && res != undefined) {
          this.templates = res.templates
            .filter((data: any) => data)
            .slice()
            .sort((a: any, b: any) => a.title.localeCompare(b.title));
        }
      });
    this.selectedContact = this.contactPhone;

  }
  getTemplateData() {
    this.store
      .select(getTemplatesModule)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res != '' && res != undefined) {
          this.templates = res.templates
            .filter((data: any) => data)
            .slice()
            .sort((a: any, b: any) => a.title.localeCompare(b.title));
        }
      });
  }

  getUnitData() {
    this.store
      .select(getUnitById)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.unitInfoData = data;
      });
  }

  getTitle(): string {
    if (this.key === 'share-matching-projects') {
      if (this.data && this.data.length > 0) {
        return this.data
          .map((dataItem: any) => dataItem?.projectName)
          .join(', ');
      }
      return this.data?.projectName || '';
    }
    if (
      this.key === 'share-project-units' ||
      this.key === 'share-units' ||
      this.key === 'share-project' ||
      this.key === 'share-matching-lead' ||
      this.key === 'share-projects-unit' ||
      this.key === 'bulk-share-project'
    ) {
      if (this.data && this.data.length > 0) {
        return this.data.map((dataItem: any) => dataItem?.name).join(', ');
      }
      return this.data?.name || '';
    } else if (this.key === 'share-property') {
      return this.data?.title || '';
    } else if (this.data && this.data.length > 0) {
      return this.data.map((dataItem: any) => dataItem?.title).join(', ');
    } else {
      return '';
    }
  }

  getUnitName(): string {
    if (
      this.key === 'share-matching-projects' ||
      this.key === 'share-projects-unit'
    ) {
      if (this.data && this.data.length > 0) {
        return this.data
          .map((dataItem: any) => dataItem?.unitInfo?.name)
          .join(', ');
      }
      return this.data?.unitInfo?.name || '';
    }
    return '';
  }

  onTemplateChange() {
    const selectedTemplate = this.shareForm?.get('selectedTemplate')?.value;

    if (
      this.moduleName === 'project' ||
      this.key === 'share-matching-lead' ||
      this.key === 'share-units' ||
      this.key === 'share-matching-projects' ||
      this.key === 'share-project-units'
    ) {
      if (this.key === 'share-matching-projects') {
        if (Array.isArray(this.data)) {
          this.data = this.data.map((item) => ({
            ...item,
            name: item.projectName,
            address: item.location,
          }));
        } else {
          this.data = {
            ...this.data,
            name: this.data.projectName,
            address: this.data.location,
          };
        }
      }
      this.shareForm.patchValue({
        message: selectedTemplate?.message
          ? Array.isArray(this.data)
            ? this.data.map((item: any) => ProjectTemplateMsg(
              selectedTemplate?.message,
              item,
              this.areaSizeUnits,
              this.tenantName,
              selectedTemplate?.header,
              selectedTemplate?.footer,
              this.unitInfoData,
              this.key,
              this.userData,
              this.currentDate
            )).join('\n\n')
            : ProjectTemplateMsg(
              selectedTemplate?.message,
              this.data,
              this.areaSizeUnits,
              this.tenantName,
              selectedTemplate?.header,
              selectedTemplate?.footer,
              this.unitInfoData,
              this.key,
              this.userData,
              this.currentDate
            )
          : '',
      });
    } else {
      this.shareForm.patchValue({
        message: selectedTemplate?.message
          ? Array.isArray(this.data)
            ? this.data.map((item: any) => PropertyTemplateMsg(
              selectedTemplate?.message,
              item,
              this.areaSizeUnits,
              this.tenantName,
              selectedTemplate?.header,
              selectedTemplate?.footer,
              this.canViewOwner,
              this.defaultCurrency,
              this.userData,
              getTimeZoneDate(this.currentDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear')
            )).join('\n\n')
            : PropertyTemplateMsg(
              selectedTemplate?.message,
              this.data,
              this.areaSizeUnits,
              this.tenantName,
              selectedTemplate?.header,
              selectedTemplate?.footer,
              this.canViewOwner,
              this.defaultCurrency,
              this.userData,
              getTimeZoneDate(this.currentDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear')
            )
          : '',
      });
    }
  }

  onUnitChange(event: any): void {
    const selectedUnitId = event ? event.id : null;
    if (selectedUnitId) {
      this.store.dispatch(new FetchUnitById(selectedUnitId));
    }
  }

  setSelectedTemplateType(type: string) {
    const currentType = this.shareForm.get('selectedTemplateType')?.value;
    if (currentType !== type) {
      this.shareForm.patchValue({ selectedTemplateType: type });
      if (type === 'project') {
        this.store.dispatch(new FetchTemplateModule(5));
      } else if (type === 'unit') {
        this.store.dispatch(new FetchTemplateModule(10));
      }
    }
    this.shareForm.patchValue({
      selectedUnit: null,
      selectedTemplate: null,
      message: '',
    });
  }

  shareInfo(via?: string, contactType?: any, template?: TemplateRef<any>) {
    // this.store.dispatch(new FetchProjectDataById(this.data.id));    via === 'whatsApp' ? 'WhatsApp' : 'Email'
    if (this.moduleName === 'projectUnit') {
      this.trackingService.trackFeature(`Web.ProjectUnitInfo.Actions.${via === 'whatsApp' ? 'WhatsApp' : 'Email'}.Click`)
    } else if (this.moduleName === 'property') {
      this.trackingService.trackFeature(`Web.Property.Actions.${via === 'whatsApp' ? 'WhatsApp' : 'Email'}.Click`)
    } else {
      this.trackingService.trackFeature(`Web.Project.Actions.${via === 'whatsApp' ? 'WhatsApp' : 'Email'}.Click`)
    }
    if (this.closeShareExternalComponent) this.closeShareExternalComponent();
    if (this.closeModal) this.closeModal();
    if (
      this.key === 'share-matching-projects' ||
      this.key === 'share-project-units' ||
      this.key === 'share-units'
    ) {
      this.store.dispatch(new FetchTemplateModule(10));
    } else if (
      this.key === 'share-projects-unit' ||
      this.key === 'bulk-share-project' ||
      this.key === 'share-project' ||
      this.key === 'share-matching-lead'
    ) {
      this.store.dispatch(new FetchTemplateModule(5));
      if (
        this.key !== 'share-projects-unit' ||
        this.key !== 'bulk-share-project'
      ) {
        this.store.dispatch(new FetchUnitInfoById(this.data.id));
      }
    } else {
      this.store.dispatch(new FetchTemplateModule(6));
    }

    this.store.dispatch(new FetchAreaUnitList());
    this.modalRef = this.modalService.show(template, {
      class: 'modal-350 right-modal',
      keyboard: false,
    });
    this.shareDetails = {
      shareType: via,
      contactType: contactType,
    };
    this.shareForm.patchValue({
      selectedTemplate: null,
    });
  }

  sendMessage() {
    const selectedTemplateType = this.shareForm.get(
      'selectedTemplateType'
    )?.value;
    if (
      selectedTemplateType === 'unit' &&
      (this.key === 'share-project' || this.key === 'share-matching-lead')
    ) {
      if (!this.shareForm.valid) {
        validateAllFormFields(this.shareForm);
        return;
      }
    }
    if (this.alternatePhone || this.leadData?.alternateContactNo) {
      this.contactNumber =
        window.innerWidth <= 600
          ? this.selectedContact
          : this.selectedContact || null;
    } else if (this.contactPhone) {
      this.contactNumber =
        window.innerWidth <= 600
          ? this.contactPhone
          : this.contactPhone || null;
    }

    const dataFormat = this.shareForm.get('message')?.value;

    // this.whatsAppUrl = this.platformShareLinks(
    //   'whatsApp',
    //   dataFormat,
    //   this.userName,
    //   this.contactNumber || this.leadData?.contactNo
    // );

    if (this.shareDetails?.shareType === 'whatsApp') {
      window.open(
        `${WHATSAPP_SHARE_API}?phone=${this.contactNumber
          ? this.contactNumber
          : this.leadData?.contactNo
            ? this.leadData.contactNo
            : ''
        }&text=${dataFormat ? encodeURIComponent(dataFormat) : 'Hi'}`,
        '_blank'
      );
    }
    switch (this.shareDetails?.shareType) {
      case 'email':
        window.location.href = this.platformShareLinks(
          'email',
          dataFormat,
          this.userName,
          this.contactMail
        );
        break;
    }

    if (
      this.key === 'share-matching-lead' ||
      this.key == 'share-property' ||
      this.key == 'bulk-share-property' ||
      this.key === 'share-matching-projects' ||
      this.key === 'share-projects-unit'
    ) {
      let payloadCount: any = {};
      if (this.key == 'share-property' || this.key == 'bulk-share-property') {
        payloadCount.ids =
          (this.key == 'share-property' || 'bulk-share-property') &&
            this.data?.id
            ? [this.data?.id]
            : Array.isArray(this.data)
              ? this.data?.map((dataItem: any) => dataItem?.id)
              : this.data?.id;
        payloadCount.contactType = this.shareDetails?.contactType;
      }

      const payload: any = {
        id: this.leadData?.id || this.leadId,
        contactType: this.shareDetails?.contactType,
      };
      const leadPayload: any = {
        contactType: this.shareDetails?.contactType,
        leadId: this.leadData?.id || this.leadId,
      };
      if (this.leadData?.id || this.leadId) {
        this.store.dispatch(new CommunicationCount(payload?.id, payload));
        this.store.dispatch(new CommunicationMessage(leadPayload));
        if (
          this.key !== 'share-matching-lead' &&
          this.key !== 'share-matching-projects'
        ) {
          this.store.dispatch(new IncreaseShareCount(payloadCount));
        }
      } else {
        this.store.dispatch(new IncreaseShareCount(payloadCount));
      }
    }

    if (
      this.key === 'share-project' ||
      this.key == 'share-units' ||
      this.key == 'share-matching-lead' ||
      this.key === 'bulk-share-project' ||
      this.key === 'share-matching-projects' ||
      this.key === 'share-project-units'
    ) {
      const payload: any = {
        id: this.leadData?.id || this.leadId,
        contactType: this.shareDetails?.contactType,
      };

      if (
        this.key === 'share-matching-projects' ||
        this.key === 'share-units' ||
        this.key === 'share-matching-lead' ||
        this.key === 'bulk-share-project' ||
        this.key === 'share-project-units' ||
        this.key === 'share-project'
      ) {
        let projectPayloadCount: any = {
          ids: [],
          contactType: this.shareDetails?.contactType,
        };
        let projectInUnitPayloadCount: any = {
          ids: [],
          contactType: this.shareDetails?.contactType,
        };
        let unitPayloadCount: any = {
          ids: [],
          contactType: this.shareDetails?.contactType,
        };

        if (Array.isArray(this.data)) {
          projectPayloadCount.ids = this.data
            .map((dataItem: any) => dataItem?.id)
            .filter((id) => id !== undefined);
        } else if (this.data?.id) {
          projectPayloadCount.ids = [this.data?.id];
        }

        if (
          (this.key === 'share-project' && selectedTemplateType === 'unit') ||
          this.key === 'share-matching-lead'
        ) {
          const unitId = this.shareForm.get('selectedUnit')?.value?.id;
          if (unitId) {
            unitPayloadCount.ids = [unitId];
          }
        }

        if (!unitPayloadCount.ids.length) {
          if (Array.isArray(this.data)) {
            unitPayloadCount.ids = this.data
              .map((dataItem: any) => dataItem?.unitInfo?.id)
              .filter((id) => id !== undefined);
          } else if (this.data?.unitInfo?.id) {
            unitPayloadCount.ids = [this.data?.unitInfo?.id];
          }
        }

        if ((this.leadData?.id || this.leadId) && this.key !== 'share-units') {
          this.store.dispatch(new CommunicationCount(payload?.id, payload));
        }

        if (
          (this.key === 'share-matching-projects' ||
            this.key === 'share-project' ||
            this.key === 'bulk-share-project' ||
            this.key === 'share-matching-lead') &&
          projectPayloadCount.ids.length
        ) {
          this.store.dispatch(
            new IncreaseProjectShareCount(projectPayloadCount)
          );
        }

        if (this.key !== 'bulk-share-project' && unitPayloadCount.ids.length) {
          this.store.dispatch(
            new IncreaseProjectUnitShareCount(unitPayloadCount)
          );
        }

        if (this.key === 'share-units' || this.key === 'share-project-units') {
          this.store.dispatch(
            new IncreaseProjectUnitShareCount(projectPayloadCount)
          );
          this.projectId = this.sharedDataService.getProjectTitleId();
          if (this.projectId) {
            projectInUnitPayloadCount.ids = [this.projectId];
          }
          this.store.dispatch(
            new IncreaseProjectShareCount(projectInUnitPayloadCount)
          );
        }
      }
    }

    this.modalService.hide();
    this.shareForm.reset();
    this.store.dispatch(new LoaderHide());
  }

  isTemplateSelectDisabled(): boolean {
    const selectedTemplateType = this.shareForm.get(
      'selectedTemplateType'
    )?.value;
    const selectedUnit = this.shareForm.get('selectedUnit')?.value;

    return (
      this.key !== 'bulk-share-project' &&
      ((this.key === 'share-project' && selectedTemplateType === 'unit') ||
        (this.key === 'share-matching-lead' &&
          selectedTemplateType === 'unit')) &&
      !selectedUnit
    );
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
