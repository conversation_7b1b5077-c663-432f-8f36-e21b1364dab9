import {
  Component,
  EventE<PERSON>ter,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { firstValueFrom, skipWhile, Subject, switchMap, takeUntil } from 'rxjs';

import * as moment from 'moment';
import { AnimationOptions } from 'ngx-lottie';
import {
  PAGE_SIZE,
  REPORTS_DATE_TYPE,
  REPORT_FILTERS_KEY_LABEL,
  SHOW_ENTRIES,
  USER_VISIBILITY,
} from 'src/app/app.constants';
import {
  IntegrationSource,
  LeadSource,
  ReportDateType,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { ReportsFilter } from 'src/app/core/interfaces/reports.interface';
import {
  assignToSort,
  changeCalendar,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  getTotalCountForReports,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { FetchAgencyNameList } from 'src/app/reducers/Integration/integration.actions';
import {
  getAgencyNameList,
  getAgencyNameListIsLoading,
} from 'src/app/reducers/Integration/integration.reducer';
import {
  FetchLeadCities,
  FetchLeadCountries,
  FetchLeadStates,
  FetchProjectList,
  FetchSubSourceList,
} from 'src/app/reducers/lead/lead.actions';
import {
  getLeadCities,
  getLeadCitiesIsLoading,
  getLeadCountries,
  getLeadCountriesIsLoading,
  getLeadStates,
  getLeadStatesIsLoading,
  getProjectList,
  getProjectListIsLoading,
  getSubSourceList,
  getSubSourceListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchMeetingSiteVisitExportSuccess,
  FetchReportsMeetingSiteVisitLevel1,
  UpdateMeetingSiteVisitFilterPayload,
} from 'src/app/reducers/reports/reports.actions';
import {
  getMeetingSiteVisitFiltersPayload,
  getReportsMeetingSiteVisitLevel1ListIsLoading,
  getReportsMeetingSiteVisitLevel2ListIsLoading,
  getReportsMeetingSiteVisitList,
} from 'src/app/reducers/reports/reports.reducer';
import {
  FetchOnlyReporteesWithInactive,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getOnlyReporteesWithInactive,
  getOnlyReporteesWithInactiveIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { environment } from 'src/environments/environment';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'visit-meeting-report',
  templateUrl: './visit-meeting-report.component.html',
})
export class VisitMeetingReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  leadSources: Array<any> = [];
  public searchTermSubject = new Subject<string>();
  gridOptions: any;
  columnDropDown: { field: string; hide: boolean }[] = [];
  rowData: Array<any> = [];
  gridApi: any;
  gridColumnApi: any;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  currentView: 'table' | 'graph' = 'table';
  getPages = getPages;
  appliedFilter: any;
  filtersPayload: ReportsFilter;
  meetingsitevisitTotalCount: number;
  canExportAllUsers: boolean = false;
  canViewAllUsers: boolean = false;
  canViewReportees: boolean = false;
  canExportReportees: boolean = false;
  projectList: any;
  isDateFilter: string;
  subSourceList: any;
  agencyNameList: any;
  allSubSourceList: any;
  dateTypeList: Array<string> = REPORTS_DATE_TYPE.slice(0, 4);
  visibilityList: Array<Object> = USER_VISIBILITY.slice(0, 3);
  allUsers: Array<any> = [];
  onlyReportees: Array<any> = [];
  users: Array<any> = [];
  reportees: Array<any> = [];
  showLeftNav: boolean = true;
  isVisitMeetingReportL1Loading: boolean = true;
  isVisitMeetingReportL2Loading: boolean = true;
  isAllUsersLoading: boolean = true;
  isOnlyReporteesLoading: boolean = true;
  allSubSourceListIsLoading: boolean = true;
  isProjectListLoading: boolean = true;
  agencyNameListIsLoading: boolean = true;
  moment = moment;
  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;
  showFilters: boolean = false;
  cities: string[];
  citiesIsLoading: boolean = true;
  states: string[];
  statesIsLoading: boolean = true;
  countryList: any[];
  countryIsLoading: boolean = true;
  isSourcesLoading: boolean = true;

  userData: any;
  currentDate: Date = new Date();
  fromDateForMeetingOrVisit: any = new Date();
  toDateForMeetingOrVisit: any = new Date();
  toDate: any = new Date();
  fromDate: any = new Date();
  onPickerOpened = onPickerOpened;
  s3BucketUrl: string = environment.s3ImageBucketURL;
  globalSettingsData: any;
  filteredColumnDefsCache: any[] = [];

  @ViewChild('reportsGraph') reportsGraph: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    public router: Router,
    private modalService: BsModalService,
    private shareDataService: ShareDataService,
    private modalRef: BsModalRef
  ) {
    this.headerTitle.setTitle('Leads - Visit & Meeting Report');
    this.metaTitle.setTitle('CRM | Reports');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });
    this._store
      .select(getMeetingSiteVisitFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = { ...data, isNavigatedFromReports: true };
        this.pageSize = this.filtersPayload?.pageSize;
        const userStatus =
          this.filtersPayload?.userStatus === undefined
            ? 1
            : this.filtersPayload?.userStatus;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          userStatus: userStatus,
          withTeam: this.filtersPayload?.IsWithTeam,
          visibility: this.filtersPayload?.userStatus,
          users: this.filtersPayload?.UserIds,
          agencyNames: this.filtersPayload?.AgencyNames,
          search: this.filtersPayload?.SearchText,
          sources: this.filtersPayload?.Sources,
          subSources: this.filtersPayload?.SubSources,
          projects: this.filtersPayload?.Projects,
          dateType: ReportDateType[Number(this.filtersPayload?.dateType)],
          date: [
            patchTimeZoneDate(
              this.filtersPayload?.fromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              this.filtersPayload?.toDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ],
          dateForMeetingOrVisit: [
            patchTimeZoneDate(
              this.filtersPayload?.fromDateForMeetingOrVisit,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              this.filtersPayload?.toDateForMeetingOrVisit,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ],
          cities: this.filtersPayload?.Cities,
          states: this.filtersPayload?.States,
        };
      });
    this._store
      .select(getUsersListForReassignment)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.users = usersData;
          this.allUsers = usersData;
          this.allUsers = assignToSort(this.allUsers, '');
          return this._store
            .select(getUsersListForReassignmentIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
        if (!isLoading) {
          this.currentVisibility(1, false);
        }
      });

    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.reportees = usersData;
          this.onlyReportees = usersData;
          this.onlyReportees = assignToSort(this.onlyReportees, '');
          return this._store
            .select(getOnlyReporteesWithInactiveIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isOnlyReporteesLoading = isLoading;
        if (!isLoading) {
          this.currentVisibility(1, false);
        }
      });
    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProjectListLoading = isLoading;
      });

    this._store
      .select(getReportsMeetingSiteVisitList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const excludedKeys = [
          'droppedCount',
          'notInterestedCount',
          'meetingDoneUniqueCount',
          'meetingNotDoneUniqueCount',
          'siteVisitDoneUniqueCount',
          'siteVisitNotDoneUniqueCount',
        ];

        const calculateTotal = (item: any, excludedKeys: string[]): number => {
          return Object.entries(item).reduce((total, [key, value]) => {
            if (typeof value === 'number' && !excludedKeys?.includes(key)) {
              return total + value;
            }
            return total;
          }, 0);
        };

        const rowData = getTotalCountForReports(data?.items);
        this.rowData = rowData?.map((item: any) => ({
          ...item,
          totalCount: calculateTotal(item, excludedKeys),
        }));

        this.meetingsitevisitTotalCount = data?.totalCount;
      });

    this._store
      .select(getReportsMeetingSiteVisitLevel1ListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isVisitMeetingReportL1Loading = isLoading;
      });
    this._store
      .select(getReportsMeetingSiteVisitLevel2ListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isVisitMeetingReportL2Loading = isLoading;
      });
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExportAllUsers = permissionsSet.has(
          'Permissions.Reports.ExportAllUsers'
        );
        this.canViewAllUsers = permissionsSet.has(
          'Permissions.Reports.ViewAllUsers'
        );
        this.canExportReportees = permissionsSet.has(
          'Permissions.Reports.ExportReportees'
        );
        this.canViewReportees = permissionsSet.has(
          'Permissions.Reports.ViewReportees'
        );
        if (this.canViewAllUsers) {
          this._store.dispatch(new FetchUsersListForReassignment());
        }
        if (this.canViewReportees) {
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
      });
    this._store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agencyNameList = item
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getAgencyNameListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.agencyNameListIsLoading = isLoading;
      });
    this._store
      .select(getSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allSubSourceList = data;
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.updateSubSource()
      });
    this._store
      .select(getSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.allSubSourceListIsLoading = isLoading;
      });
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
    this.filterFunction();
    this.activeDate();
  }

  async ngOnInit() {
      this.globalSettingsData = await firstValueFrom(
        this._store
          .select(getGlobalSettingsAnonymous)
          .pipe(skipWhile((data) => !Object.keys(data).length))
      );
    this.initializeGridSettings();

    this._store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        if (leadSource) {
          const enabledSources = leadSource
            .filter((source: any) => source.isEnabled)
            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));
          this.leadSources = [...enabledSources];
        } else {
          this.leadSources = [];
        }
        this.updateSubSource()
      });

    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });

    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });

    this.filteredColumnDefsCache = this.gridOptions?.columnDefs?.filter(
      (col: any) => col.field !== 'Name'
    );

    this._store
      .select(getLeadCities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.cities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadCitiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.citiesIsLoading = data;
      });

    this._store
      .select(getLeadStates)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.states = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadStatesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.statesIsLoading = data;
      });

    this._store
      .select(getLeadCountries)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryList = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadCountriesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.countryIsLoading = data;
      });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Name',
        field: 'Name',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [
          params.data?.firstName,
          params.data?.lastName,
        ],
        minWidth: 180,
        cellRenderer: (params: any) => {
          if (this.isVisitMeetingReportL2Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<p class="py-16 text-truncate">${params.value[0]} ${params.value[1]}</p>`;
        },
      },
      // {
      //   headerName: 'Total',
      //   field: 'Total',
      //   filter: false,
      //   valueGetter: (params: any) => {
      //     if (!params.data) return ['--', '--'];

      //     const totalCount = params.data?.totalCount || 0;

      //     const keysToCount = [
      //       'meetingDoneUniqueCount',
      //       'meetingNotDoneUniqueCount',
      //       'siteVisitDoneUniqueCount',
      //       'siteVisitNotDoneUniqueCount',
      //     ];

      //     const uniqueCount = keysToCount.reduce((total, key) => {
      //       return total + (params.data[key] || 0);
      //     }, 0);

      //     return [totalCount, uniqueCount];
      //   },
      //   minWidth: 120,
      //   cellRenderer: (params: any) => {
      //     const totalCount = params.value[0] || '--';
      //     const uniqueCount = params.value[1] || '--';
      //     return `
      //       <a>
      //         <p>${totalCount}</p>
      //         <p class="text-truncate">
      //           <span class="text-dark-gray">unique count: </span>
      //           <span class="fw-600">${uniqueCount}</span>
      //         </p>
      //       </a>
      //     `;
      //   },
      // },
      {
        headerName: 'Meeting Scheduled',
        field: 'Meeting Scheduled',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.meetingScheduledCount,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 140,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL1Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p></a>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataNewTab('Meeting Scheduled', params);
              return;
            }
            this.getDataFromCell('Meeting Scheduled', event);
          }
        },
      },
      {
        headerName: 'Meeting Scheduled From History',
        field: 'Meeting Scheduled From History',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.meetingScheduledCountFromHistory,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 220,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL1Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p></a>`;
        },
        // cellClass: 'cursor-pointer',
        // onCellClicked: (event: any) => {
        //   const isCtrlClick = event?.event?.ctrlKey;
        //   const params = { value: event?.value, data: event?.data };
        //   if (event.data.projectTitle == 'Total') {
        //     return;
        //   } else if (event.value[0] != 0) {
        //     if (isCtrlClick) {
        //       this.getDataNewTab('Meeting Scheduled From History', params)
        //       return
        //     }
        //     this.getDataFromCell('Meeting Scheduled From History', event);
        //   }
        // },
      },
      {
        headerName: 'Meeting Done',
        field: 'Meeting Done',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.meetingDoneCount,
          params.data?.meetingDoneUniqueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL2Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab('All Leads', params, 'Meeting Done');
              return;
            }
            this.getMeetingCountFromCell('All Leads', event, 'Meeting Done');
          }
        },
      },
      {
        headerName: 'Meeting Not Done',
        field: 'Meeting Not Done',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.meetingNotDoneCount,
          params.data?.meetingNotDoneUniqueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 150,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL2Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab(
                'All Leads',
                params,
                'Meeting Not Done'
              );
              return;
            }
            this.getMeetingCountFromCell(
              'All Leads',
              event,
              'Meeting Not Done'
            );
          }
        },
      },
      {
        headerName:  this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',
        field:  this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.siteVisitScheduledCount,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 150,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL1Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p></a>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataNewTab(!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Scheduled' : 'Referral Scheduled', params);
              return;
            }
            this.getDataFromCell(!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Scheduled' : 'Referral Scheduled', event);
          }
        },
      },
      {
        headerName: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled From History' : 'Site Visit Scheduled From History',
        field: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled From History' : 'Site Visit Scheduled From History',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.siteVisitScheduledCountFromHistory,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 220,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL1Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p></a>`;
        },
        // cellClass: 'cursor-pointer',
        // onCellClicked: (event: any) => {
        //   const isCtrlClick = event?.event?.ctrlKey;
        //   const params = { value: event?.value, data: event?.data };
        //   if (event.data.projectTitle == 'Total') {
        //     return;
        //   } else if (event.value[0] != 0) {
        //     if (isCtrlClick) {
        //       this.getDataNewTab('Site Visit Scheduled From History', params)
        //       return
        //     }
        //     this.getDataFromCell('Site Visit Scheduled From History', event);
        //   }
        // },
      },
      {
        headerName:  !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
        field:  !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.siteVisitDoneCount,
          params.data?.siteVisitDoneUniqueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL2Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
              <div class="dot-elastic"></div>
            </ng-container></div>`;
          }
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab(
                'All Leads',
                params,
                'Site Visit Done'
              );
              return;
            }
            this.getMeetingCountFromCell('All Leads', event, 'Site Visit Done');
          }
        },
      },
      {
        headerName: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Not Done' : 'Referral Not Taken',
        field: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Not Done' : 'Referral Not Taken',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.siteVisitNotDoneCount,
          params.data?.siteVisitNotDoneUniqueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 150,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL2Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab(
                'All Leads',
                params,
                'Site Visit Not Done'
              );
              return;
            }
            this.getMeetingCountFromCell(
              'All Leads',
              event,
              'Site Visit Not Done'
            );
          }
        },
      },
      {
        headerName: 'Not Interested After Meeting Done',
        field: 'Not Interested After Meeting Done',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.notInterestedAfterMeetingDone,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 240,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL1Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab(
                'Not Interested',
                params,
                'Meeting Done'
              );
              return;
            }
            this.getMeetingCountFromCell(
              'Not Interested',
              event,
              'Meeting Done'
            );
          }
        },
      },
      {
        headerName: 'Not Interested After Site Visit Done',
        field: 'Not Interested After Site Visit Done',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.notInterestedAfterSiteVisitDone,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 240,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL1Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab(
                'Not Interested',
                params,
                'Site Visit Done'
              );
              return;
            }
            this.getMeetingCountFromCell(
              'Not Interested',
              event,
              'Site Visit Done'
            );
          }
        },
      },
      {
        headerName: 'Dropped After Meeting Done',
        field: 'Dropped After Meeting Done',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.droppedAfterMeetingDone,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 200,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL1Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab('Dropped', params, 'Meeting Done');
              return;
            }
            this.getMeetingCountFromCell('Dropped', event, 'Meeting Done');
          }
        },
      },
      {
        headerName: 'Dropped After Site Visit Done',
        field: 'Dropped After Site Visit Done',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.droppedAfterSiteVisitDone,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 200,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL1Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab('Dropped', params, 'Site Visit Done');
              return;
            }
            this.getMeetingCountFromCell('Dropped', event, 'Site Visit Done');
          }
        },
      },
      {
        headerName: 'Booked',
        field: 'Booked',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.bookedCount,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 90,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL1Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              const filters = { ...this.filtersPayload };
              if (filters?.IsWithTeam) filters.IsWithTeam = false;
              window?.open(
                `leads/manage-leads?leadReportGetData=true&assignTo=${params?.value?.[1]
                }&data=${encodeURIComponent(
                  JSON.stringify(params?.data)
                )}&operation=Booked&filtersPayload=${encodeURIComponent(
                  JSON.stringify({ ...this.filtersPayload, UserIds: null })
                )}`,
                '_blank'
              );
              return;
            }
            this.getDataFromCell('Booked', event);
          }
        },
      },
      {
        headerName: 'Booking Cancel',
        field: 'Booking Cancel',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.bookingCancelCount,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (this.isVisitMeetingReportL1Loading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total' || event.value[0] == 0) {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              const filters = { ...this.filtersPayload };
              if (filters?.IsWithTeam) filters.IsWithTeam = false;
              window?.open(
                `leads/manage-leads?leadReportGetData=true&assignTo=${params?.value?.[1]
                }&data=${encodeURIComponent(
                  JSON.stringify(params?.data)
                )}&operation=Booking Cancel&filtersPayload=${encodeURIComponent(
                  JSON.stringify({ ...filters })
                )}`,
                '_blank'
              );
              return;
            }
            this.getDataFromCell('Booking Cancel', event);
          }
        },
      },
    ];
    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.rowData = this.rowData;
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  getDataFromCell(operation: string, event: any) {
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.dateType = this.appliedFilter.dateType;
    this.gridOptionsService.status = operation;
    const filters = { ...this.filtersPayload };
    if (this.appliedFilter.dateForMeetingOrVisit?.[0]) {
      this.gridOptionsService.meetingStatus = [
        'Meeting Done',
        'Meeting Not Done',
        'Site Visit Done',
        'Site Visit Not Done',
      ];
      this.filtersPayload = {
        ...this.filtersPayload,
        fromDate: this.filtersPayload?.fromDate?.[0],
        toDate: this.filtersPayload?.toDate?.[0],
      };
    }
    if (filters?.IsWithTeam) filters.IsWithTeam = false;
    this.gridOptionsService.payload = { ...filters, UserIds: null };
    this.router.navigate(['leads/manage-leads']);
  }

  getDataNewTab(operation: string, params: any) {
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = false;
    }
    window?.open(
      `leads/manage-leads?visitAndMeetingReportGetData=true&data=${encodeURIComponent(
        JSON.stringify(params?.data)
      )}&operation=${operation}&filtersPayload=${encodeURIComponent(
        JSON.stringify({ ...filters, UserIds: null })
      )}`,
      '_blank'
    );
  }

  getMeetingCountFromCell(
    operation: string,
    event: any,
    meetingStatus: string
  ) {
    this.router.navigate(['leads/manage-leads']);
    let visitMeeting = [];
    visitMeeting.push(meetingStatus);
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.dateType = this.appliedFilter.dateType;
    this.gridOptionsService.status = operation;
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) filters.IsWithTeam = false;
    this.gridOptionsService.payload = { ...filters, UserIds: null };
    this.gridOptionsService.meetingStatus = visitMeeting;
  }

  getMeetingCountnewTab(operation: string, params: any, meetingStatus: string) {
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = false;
    }
    window?.open(
      `leads/manage-leads?leadReportGetMeetingCount=true&data=${encodeURIComponent(
        JSON.stringify(params?.data)
      )}&operation=${operation}&meetingStatus=${meetingStatus}&filtersPayload=${encodeURIComponent(
        JSON.stringify({ ...filters, UserIds: null })
      )}`,
      '_blank'
    );
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this._store.dispatch(
      new UpdateMeetingSiteVisitFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchReportsMeetingSiteVisitLevel1());
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(
      new UpdateMeetingSiteVisitFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchReportsMeetingSiteVisitLevel1());
    this.currOffset = 0;
  }

  currentVisibility(visibility: any, isTopLevelFilter: any) {
    this.appliedFilter.userStatus = visibility;
    this.appliedFilter.pageNumber = 1;
    if (isTopLevelFilter) {
      this.appliedFilter.users = null;
    }
    this.filterFunction();

    if (this.canViewAllUsers) {
      switch (visibility) {
        case 1:
          this.allUsers = this.users?.filter((user: any) => user.isActive);
          break;
        case 2:
          this.allUsers = this.users?.filter((user: any) => !user.isActive);
          break;
        case null:
          this.allUsers = this.users;
          break;
      }
      this.allUsers = assignToSort(this.allUsers, '');
    } else {
      switch (visibility) {
        case 1:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => user.isActive
          );
          break;
        case 2:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => !user.isActive
          );
          break;
        case null:
          this.onlyReportees = this.reportees;
          break;
      }
      this.onlyReportees = assignToSort(this.onlyReportees, '');
    }
  }

  filterFunction() {
    this.appliedFilter.pageNumber = 1;
    this.filtersPayload = {
      ...this.filtersPayload,
      Cities: this.appliedFilter?.cities,
      States: this.appliedFilter?.states,
      Countries: this.appliedFilter?.Countries,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      IsWithTeam: this.appliedFilter.withTeam,
      userStatus: this.appliedFilter.userStatus,
      UserIds: this.appliedFilter.users,
      Sources: this.appliedFilter.sources,
      SubSources: this.appliedFilter.subSources,
      Projects: this.appliedFilter.projects,
      SearchText: this.searchTerm,
      AgencyNames: this.appliedFilter.agencyNames,
      dateType: ReportDateType[this.appliedFilter.dateType],
      ReportPermission: this.canViewAllUsers ? 0 : 1,
      ExportPermission: this.canExportAllUsers ? 0 : 1,
      fromDate: setTimeZoneDate(
        this.appliedFilter?.date?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      toDate: setTimeZoneDate(
        this.appliedFilter.date?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      fromDateForMeetingOrVisit: setTimeZoneDate(
        this.appliedFilter?.dateForMeetingOrVisit?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      toDateForMeetingOrVisit: setTimeZoneDate(
        this.appliedFilter?.dateForMeetingOrVisit?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
    };
    this._store.dispatch(
      new UpdateMeetingSiteVisitFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchReportsMeetingSiteVisitLevel1());
    this.currOffset = 0;
    if (
      this.appliedFilter?.dateType?.length ||
      this.appliedFilter?.date?.[0]?.length ||
      this.appliedFilter.users?.length ||
      this.appliedFilter.projects?.length ||
      this.appliedFilter.subSources?.length ||
      this.appliedFilter.sources?.length ||
      this.appliedFilter.dateForMeetingOrVisit?.[0] ||
      this.appliedFilter.agencyNames?.length ||
      this.appliedFilter.cities?.length ||
      this.appliedFilter.Countries?.length ||
      this.appliedFilter.states?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
  }

  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = [
      'subSources',
      'projects',
      'agencyNames',
      'cities',
      'states',
    ];

    if (
      [
        'pageSize',
        'pageNumber',
        'visibility',
        'withTeam',
        'userStatus',
        'search',
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'dateForMeetingOrVisit' && values.length === 2) {
      if (key === 'dateForMeetingOrVisit' && values[0] !== null) {
        this.toDateForMeetingOrVisit = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDateForMeetingOrVisit = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDateForMeetingOrVisit = getTimeZoneDate(
          this.toDateForMeetingOrVisit,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDateForMeetingOrVisit = getTimeZoneDate(
          this.fromDateForMeetingOrVisit,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const dateString = `${formattedToDateForMeetingOrVisit} to ${formattedFromDateForMeetingOrVisit}`;
        return [dateString];
      } else {
        return null;
      }
    } else if (key === 'date' && values.length === 2) {
      if (values[0] !== null) {
        this.toDate = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDate = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDate = getTimeZoneDate(
          this.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDate = getTimeZoneDate(
          this.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    } else if (allowedKeys.includes(key)) {
      return values;
    }
    return values?.toString()?.split(',');
  }

  applyAdvancedFilter() {
    this.filterFunction();
    this.modalService.hide();
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.fullName}`;
    });
    return userName;
  }

  onRemoveFilter(key: string, value: string) {
    if (key === 'date' || key === 'dateType') {
      delete this.appliedFilter[key];
      const dependentKey = key === 'date' ? 'dateType' : 'date';
      if (this.appliedFilter[dependentKey]) {
        delete this.appliedFilter[dependentKey];
      }
    } else if (key === 'dateForMeetingOrVisit') {
      delete this.appliedFilter[key];
      this.isDateFilter = '';
    }
    else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter(
        (item: any, index: number) => {
          const matchIndex = this.appliedFilter[key]?.indexOf(value);
          return index !== matchIndex;
        }
      );
    }
    this.filterFunction();
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    this._store.dispatch(new FetchLeadCities());
    this._store.dispatch(new FetchLeadStates());
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchAgencyNameList());
    this._store.dispatch(new FetchSubSourceList());
    // this._store.dispatch(new FetchLeadCountries());
    this._store.dispatch(new FetchAllSources());
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }

  updateSubSource() {
    if (this.appliedFilter?.sources?.length) {
      this.subSourceList = [];
      this.appliedFilter?.sources.forEach((i: any) => {
        const source: any = LeadSource[i];
        const leadSource = IntegrationSource[source];
        if (leadSource === '99 Acres') {
          this.subSourceList.push.apply(
            this.subSourceList,
            this.allSubSourceList['NinetyNineAcres'] || []
          );
        } else {
          const formattedKey = leadSource?.replace(/\s+/g, '');
          if (Array.isArray(this.allSubSourceList[formattedKey])) {
            this.subSourceList.push.apply(
              this.subSourceList,
              this.allSubSourceList[formattedKey] || []
            );
          } else {
            this.subSourceList.push.apply(
              this.subSourceList,
              this.allSubSourceList[leadSource] || []
            );
          }
        }
      });
    } else {
      let subSourceList: string[] = this.leadSources?.flatMap((lead: any): string[] => {
        if (lead?.displayName === '99 Acres') {
          return this.allSubSourceList?.['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        let match = this.allSubSourceList[formattedKey];
        if (!match) {
          match = this.allSubSourceList[lead?.displayName];
        }
        if (!match && formattedKey?.toLowerCase() === '99acres') {
          match = this.allSubSourceList['NinetyNineAcres'];
        }
        return Array.isArray(match) ? match : [];
      }) || [];
      this.subSourceList = subSourceList
    }
  }

  onSelectSource(source: any) {
    if (source) {
      this.updateSubSources(source.displayName);
    } else {
      this.updateSubSources(null);
    }
  }

  updateSubSources(sourceName: string | null) {
    if (sourceName) {
      if (sourceName === '99 Acres') {
        this.subSourceList = this.allSubSourceList['NinetyNineAcres'] || [];
      } else {
        const formattedKey = sourceName.replace(/\s+/g, '');
        if (Array.isArray(this.allSubSourceList[formattedKey])) {
          this.subSourceList = this.allSubSourceList[formattedKey] || [];
        } else {
          this.subSourceList = this.allSubSourceList[sourceName] || [];
        }
      }
    } else {
      let subSourceList: string[] = this.leadSources?.flatMap((lead: any): string[] => {
        if (lead?.displayName === '99 Acres') {
          return this.allSubSourceList?.['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        let match = this.allSubSourceList[formattedKey];
        if (!match) {
          match = this.allSubSourceList[lead?.displayName];
        }
        if (!match && formattedKey?.toLowerCase() === '99acres') {
          match = this.allSubSourceList['NinetyNineAcres'];
        }
        return Array.isArray(match) ? match : [];
      }) || [];
      this.subSourceList = subSourceList
    }
  }

  onResetDateFilter() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateType: null,
      date: '',
    };
    this.filterFunction();
  }

  resetDate() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateForMeetingOrVisit: [null, null],
    };
    this.isDateFilter = '';
    this.filterFunction();
  }

  reset() {
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.pageSize,
      dateForMeetingOrVisit: [null, null],
    };
    this.isDateFilter = '';

    this.filterFunction();
  }

  filterByDate(type?: string) {
    let newDate = new Date();
    let date = new Date(newDate.setHours(0, 0, 0, 0));
    switch (type) {
      case 'today':
        this.isDateFilter = 'today';
        this.appliedFilter.dateForMeetingOrVisit[0] = new Date(date);
        this.appliedFilter.dateForMeetingOrVisit[1] = new Date(date);
        break;
      case 'yesterday':
        this.isDateFilter = 'yesterday';
        this.appliedFilter.dateForMeetingOrVisit[0] = new Date(date).setDate(
          new Date(date).getDate() - 1
        );
        this.appliedFilter.dateForMeetingOrVisit[1] = new Date(date).setDate(
          new Date(date).getDate() - 1
        );
        break;
      case 'sevenDays':
        this.isDateFilter = 'sevenDays';
        this.appliedFilter.dateForMeetingOrVisit[0] = new Date(date).setDate(
          new Date(date).getDate() - 6
        );
        this.appliedFilter.dateForMeetingOrVisit[1] = new Date(date);
        break;
      case 'custom':
        this.isDateFilter = 'custom';
        this.appliedFilter.dateForMeetingOrVisit[0] = null;
        this.appliedFilter.dateForMeetingOrVisit[1] = null;
        break;
    }
  }

  activeDate() {
    const fromDate = new Date(this.appliedFilter.dateForMeetingOrVisit[0]);
    const toDate = new Date(this.appliedFilter.dateForMeetingOrVisit[1]);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 6);

    if (
      fromDate.toDateString() === today.toDateString() &&
      toDate.toDateString() === today.toDateString()
    ) {
      this.isDateFilter = 'today';
    } else if (
      fromDate.toDateString() === yesterday.toDateString() &&
      toDate.toDateString() === yesterday.toDateString()
    ) {
      this.isDateFilter = 'yesterday';
    } else if (
      fromDate.toDateString() === sevenDaysAgo.toDateString() &&
      toDate.toDateString() === today.toDateString()
    ) {
      this.isDateFilter = 'sevenDays';
    } else if (this.appliedFilter.dateForMeetingOrVisit[0]) {
      this.isDateFilter = 'custom';
    }
  }

  exportMeetingReport() {
    this._store.dispatch(new FetchMeetingSiteVisitExportSuccess(''));
    this.filterFunction();

    let initialState: any = {
      payload: {
        ...this.filtersPayload,
        timeZoneId:
          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset:
          this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  toggleView() {
    this.currentView = this.currentView === 'graph' ? 'table' : 'graph';
  }

  exportGraphAsPDF() {
    if (this.reportsGraph && this.isGraphExportEnabled()) {
      this.reportsGraph.exportGraph();
    }
  }

  isGraphExportEnabled(): boolean {
    return this.currentView === 'graph' && 
           this.reportsGraph?.isChartReady && 
           !this.reportsGraph?.showSelectionMessage;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
