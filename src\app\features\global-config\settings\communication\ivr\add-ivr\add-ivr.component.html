<form [formGroup]="integrateForm">
    <div class="box-radio-lg">
        <div class="field-label header-4">{{ 'INTEGRATION.ivr-options' | translate }}</div>
        <div class="flex-center mt-10 ip-flex-col">
            <ng-container *ngFor="let ivrType of ivrTypeList">
                <input type="radio" class="btn-check" name="ivr" [id]="ivrType.name" autocomplete="off"
                    formControlName="ivr" [value]="ivrType.name">
                <label class="btn-outline fw-600 w-100 ip-mt-10" [for]="ivrType.name">
                    <div class="flex-center-col">
                        <h3 class="fw-600">{{ ivrType.name }}</h3>
                        <span class="mt-4 text-nowrap">{{ ivrType.desc }}</span>
                    </div>
                </label>
            </ng-container>
        </div>
    </div>
    <div class="field-label-req">Service Provider</div>
    <form-errors-wrapper label="Service Provider" [control]="integrateForm.controls['serviceProvider']">
        <input type="text" required formControlName="serviceProvider" name="serviceProvider" autocomplete="off"
            placeholder="ex. Servetel" />
    </form-errors-wrapper>
    <div for="inpIvrAcc" class="field-label-req">
        {{ 'INTEGRATION.account-name' | translate }}</div>
    <form-errors-wrapper label="{{ 'INTEGRATION.account-name' | translate }}"
        [control]="integrateForm.controls['accountName']">
        <input type="text" required formControlName="accountName" name="accountName" autocomplete="off"
            placeholder="ex. Manasa pampana" />
    </form-errors-wrapper>
    <label class="checkbox-container mt-8 ml-6 text-normal align-center">
        <input type="checkbox" formControlName="isVirtualNumberRequiredForOutbound">
        <span class="checkmark"></span>Virtual Number
    </label>
    <div *ngIf="integrateForm.controls['isVirtualNumberRequiredForOutbound'].value">
        <div for="inpVirtual" class="field-label-req">
            {{ 'INTEGRATION.virtual-number' | translate }}</div>
        <div formArrayName="ivrAssignmentDtos">
            <div class="mb-12" *ngFor="let control of ivrAssignmentDtos.controls; let i = index">
                <ng-container [formGroupName]="i">
                    <div class="align-center">
                        <form-errors-wrapper label="{{ 'INTEGRATION.virtual-number' | translate }}"
                            [control]="integrateForm.controls['virtualNumber']" class="flex-grow-1">
                            <input type="text" formControlName="virtualNumber" id="inpVirtual"
                                data-automate-id="inpVirtual" autocomplete="off" placeholder="ex. 9133XXXXXX"
                                (keydown)="onlyNumbers($event)" />
                        </form-errors-wrapper>
                        <div class="align-center border br-4 px-16 py-10 ml-12 w-110">
                            <div (click)="onToggleUsers(i)"
                                *ngIf="canAssign && integrateForm.controls['ivr'].value == 'Outbound'">
                                <div title="Assign To" class="bg-blue-800 icon-badge">
                                    <span class="icon ic-assign-to ic-xxxs"></span>
                                </div>
                            </div>
                            <div *ngIf="integrateForm.controls['ivr'].value !== 'Outbound'" (click)="onToggleAgency(i)"
                                title="Agency Name" class="bg-blue-850 icon-badge">
                                <span class="icon ic-suitcase ic-xxs"></span>
                            </div>
                            <div class="bg-violet icon-badge" (click)="onToggleProjectLocation(i)"
                                [title]="integrateForm.controls['ivr'].value == 'Outbound' ? 'Project' : 'Project & Location'">
                                <span class="icon ic-building-secondary ic-xxxs"></span>
                            </div>
                            <div title="Delete" class="bg-light-red icon-badge" (click)="onRemoveVN(i)">
                                <span class="icon ic-delete ic-xxxs"></span>
                            </div>
                        </div>
                    </div>
                    <div class="align-center flex-wrap">
                        <div class="w-50 px-12"
                            *ngIf="integrateForm.controls['ivr'].value == 'Outbound' && control.get('showUsers').value">
                            <div class="field-label"> {{'SETTINGS.select-user' | translate}}
                            </div>
                            <ng-select [virtualScroll]="true" [items]="canAssignToAny ? allActiveUsers : activeUsers"
                                [multiple]="true" appSelectAll [closeOnSelect]="false" bindLabel="fullName" bindValue="id"
                                name="assignedUser" formControlName="userIds" placeholder="ex. Mounika Pampana"
                                class="bg-white">
                                <ng-template ng-label-tmp let-item="item">
                                    {{item.firstName}} {{item.lastName}}
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span>{{item.firstName}}
                                        {{item.lastName}}</div>
                                </ng-template>
                            </ng-select>
                        </div>
                        <div class="w-50 px-12"
                            *ngIf="integrateForm.controls['ivr'].value !== 'Outbound' && control.get('showAgency').value">
                            <div class="field-label">Select Agency Name</div>
                            <ng-select [virtualScroll]="true" [items]="agencyNameList" [addTag]="true"
                                bindLabel="agencyName" bindValue="agencyName" formControlName="agencyName"
                                class="bg-white" addTagText="Create New Agency Name"
                                placeholder="ex. Mounika pampana"></ng-select>
                        </div>
                        <div class="w-50 px-12" *ngIf="control.get('showProjectLocation').value">
                            <div class="field-label">Select Project</div>
                            <ng-select [virtualScroll]="true" [items]="allProjectList" class="bg-white" bindLabel="name"
                                bindValue="id" placeholder="ex. ABC project" formControlName="projectId"></ng-select>
                        </div>
                        <div class="w-50 px-12"
                            *ngIf="integrateForm.controls['ivr'].value !== 'Outbound' && control.get('showProjectLocation').value">
                            <div class="field-label">Select {{'LOCATION.location' |
                                translate}}
                            </div>
                            <ng-select [virtualScroll]="true" [items]="placesList" class="bg-white" bindLabel="location"
                                bindValue="id" placeholder="ex. ABC location" formControlName="locationId"></ng-select>
                        </div>
                    </div>
                </ng-container>
            </div>
        </div>
        <div class="cursor-pointer align-center fw-700">
            <span class="icon ic-xs ic-add ic-accent-green"></span>
            <span class="text-accent-green" (click)="onAddVN()">add additional number</span>
        </div>
    </div>
    <div class="field-label-req">Payload Method Type</div>
    <form-errors-wrapper label="Payload Method Type" [control]="integrateForm.controls['payloadMethodType']">
        <ng-select class="bg-white" [virtualScroll]="true" placeholder="ex. GET"
            [items]="globalSettingsData?.methodTypes" formControlName="payloadMethodType"></ng-select>
    </form-errors-wrapper>
    <div class="field-label-req">Payload Content Type</div>
    <form-errors-wrapper label="Payload Content Type" [control]="integrateForm.controls['payloadContentType']">
        <ng-select class="bg-white" [virtualScroll]="true" placeholder="ex. application/json"
            [items]="globalSettingsData?.contentTypes" formControlName="payloadContentType"></ng-select>
    </form-errors-wrapper>
    <div class="mt-10">
        <div formArrayName="payloadForPushEndpoint">
            <div class="field-label-req">Payload Mapping</div>
            <div *ngFor="let control of payloadForPushEndpoint.controls; let i = index">
                <ng-container [formGroupName]="i">
                    <div class="d-flex mb-16">
                        <div class="w-50 me-2">
                            <form-errors-wrapper label="payload key" [control]="control.get('payloadKey')"
                                class="mb-12">
                                <ng-select class="bg-white" (change)="onPayloadOptionChanged()" [virtualScroll]="true"
                                    placeholder="key" bindLabel="displayName" bindValue="value" [items]="payloadOptions"
                                    formControlName="payloadKey"></ng-select>
                            </form-errors-wrapper>
                        </div>
                        <div class="w-50">
                            <form-errors-wrapper label="payload value" [control]="control.get('payloadValue')">
                                <input type="text" formControlName="payloadValue" id="payloadValue"
                                    (input)="uniqueValueCheck(i)" data-automate-id="payloadValue" autocomplete="off"
                                    placeholder="value">
                            </form-errors-wrapper>
                        </div>
                        <div class="align-center">
                            <div title="Delete" class="bg-light-red icon-badge" (click)="onRemovePayload(i)">
                                <span class="icon ic-delete ic-xxxs"></span>
                            </div>
                        </div>
                    </div>
                </ng-container>

            </div>
            <div class="cursor-pointer align-center fw-700"
                *ngIf="payloadOptions?.length && payloadForPushEndpoint?.length < tempVariables?.length">
                <span class="icon ic-xs ic-add ic-accent-green"></span>
                <span class="text-accent-green" (click)="onAddPayload()">add additional payload</span>
            </div>
        </div>
    </div>
    <label class="checkbox-container mt-10 ml-6 text-normal align-center">
        <input type="checkbox" formControlName="canIncludeApiKeyInHeader">
        <span class="checkmark"></span>Can Include API Key in Header
    </label>
    <div *ngIf="integrateForm.controls['ivr'].value === 'Outbound'" class="mt-10">
        <div class="field-label-req">
            IVR Outbound Configuration</div>
        <div class="mb-3">
            <div class="field-label-req">Method Type</div>
            <form-errors-wrapper label="Method Type" [control]="integrateForm.controls['methodType']">
                <ng-select class="bg-white" [virtualScroll]="true" placeholder="ex. GET"
                    [items]="globalSettingsData?.methodTypes" formControlName="methodType"></ng-select>
            </form-errors-wrapper>
            <div class="field-label-req">Content Type</div>
            <form-errors-wrapper label="Content Type" [control]="integrateForm.controls['contentType']">
                <ng-select class="bg-white" [virtualScroll]="true" placeholder="ex. application/json"
                    [items]="globalSettingsData?.contentTypes" formControlName="contentType"></ng-select>
            </form-errors-wrapper>
            <div class="field-label-req">Base URL</div>
            <form-errors-wrapper label="Base URL" [control]="integrateForm.controls['baseURL']">
                <input type="text" formControlName="baseURL" id="baseURL" data-automate-id="baseURL" autocomplete="off"
                    placeholder="ex. https://www.example.com">
            </form-errors-wrapper>
            <div class="field-label">Resources</div>
            <form-errors-wrapper label="value" [control]="integrateForm.controls['resources']">
                <input type="text" formControlName="resources" id="resources" data-automate-id="resources"
                    autocomplete="off" placeholder="Resources">
            </form-errors-wrapper>
        </div>
        <div formArrayName="queryParameters">
            <div class="field-label">
                Query Parameters</div>
            <div *ngFor="let control of queryParameters.controls; let i = index">
                <ng-container [formGroupName]="i">
                    <div class="d-flex mb-16">
                        <div class="w-50 me-2">
                            <form-errors-wrapper label="key" [control]="control.get('queryKey')">
                                <input type="text" formControlName="queryKey" id="queryKey" data-automate-id="queryKey"
                                    autocomplete="off" placeholder="Query key">

                            </form-errors-wrapper>
                        </div>
                        <div class="w-50">
                            <form-errors-wrapper label="value" [control]="control.get('queryValue')">
                                <ng-select class="bg-white" [virtualScroll]="true" placeholder="Query value"
                                    bindLabel="displayName" [items]="tempVariables" (change)="queryValueChange(i)"
                                    formControlName="queryValue" [addTag]="true"></ng-select>
                            </form-errors-wrapper>
                        </div>
                        <div class="align-center">
                            <div title="Delete" class="bg-light-red icon-badge" (click)="onRemoveQueryParams(i)">
                                <span class="icon ic-delete ic-xxxs"></span>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </div>
            <div class="cursor-pointer align-center fw-700">
                <span class="icon ic-xs ic-add ic-accent-green"></span>
                <span class="text-accent-green" (click)="onaddQueryParams()">add query parameters</span>
            </div>
        </div>
        <div formArrayName="headerVariables">
            <div class="field-label">
                Header Variables</div>
            <div *ngFor="let control of headerVariables.controls; let i = index">
                <ng-container [formGroupName]="i">
                    <div class="d-flex mb-16">
                        <div class="w-50 me-2">
                            <form-errors-wrapper label="key" [control]="control.get('headerKey')">
                                <input type="text" formControlName="headerKey" id="headerKey"
                                    data-automate-id="headerKey" autocomplete="off" placeholder="Header Key">
                            </form-errors-wrapper>
                        </div>
                        <div class="w-50">
                            <form-errors-wrapper label="value" [control]="control.get('headerValue')">
                                <ng-select class="bg-white" [virtualScroll]="true" placeholder="Header Value"
                                    bindLabel="displayName" [items]="tempVariables" (change)="headerValueChange(i)"
                                    formControlName="headerValue" [addTag]="true"
                                    (add)="control.get('headerValue').setValue($event.value)"></ng-select>
                            </form-errors-wrapper>
                        </div>
                        <div class="align-center">
                            <div title="Delete" class="bg-light-red icon-badge" (click)="onRemoveHeaderVariables(i)">
                                <span class="icon ic-delete ic-xxxs"></span>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </div>
            <div class="cursor-pointer align-center fw-700">
                <span class="icon ic-xs ic-add ic-accent-green"></span>
                <span class="text-accent-green" (click)="onaddHeaderVariables()">add header variables</span>
            </div>

        </div>
        <div formArrayName="bodyVariables">
            <div class="field-label">
                Body Variables</div>
            <div *ngFor="let control of bodyVariables.controls; let i = index">
                <ng-container [formGroupName]="i">
                    <div class="d-flex mb-16">
                        <div class="w-50 me-2">
                            <form-errors-wrapper label="key" [control]="control.get('bodyKey')">
                                <input type="text" formControlName="bodyKey" id="bodyKey" data-automate-id="bodyKey"
                                    autocomplete="off" placeholder="Body Key">
                            </form-errors-wrapper>
                        </div>
                        <div class="w-50">
                            <form-errors-wrapper label="value" [control]="control.get('bodyValue')">
                                <ng-select class="bg-white" [virtualScroll]="true" placeholder="Body Value"
                                    bindLabel="displayName" [items]="tempVariables" (change)="bodyValueChange(i)"
                                    formControlName="bodyValue" [addTag]="true"
                                    (add)="control.get('bodyValue').setValue($event.value)"></ng-select>
                            </form-errors-wrapper>
                        </div>
                        <div class="align-center">
                            <div title="Delete" class="bg-light-red icon-badge" (click)="onRemoveBodyVariables(i)">
                                <span class="icon ic-delete ic-xxxs"></span>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </div>
            <div class="cursor-pointer align-center fw-700">
                <span class="icon ic-xs ic-add ic-accent-green"></span>
                <span class="text-accent-green" (click)="onaddBodyVariables()">add body variables</span>
            </div>
        </div>
    </div>
    <label class="checkbox-container mt-10 ml-6 text-normal align-center">
        <input type="checkbox" formControlName="setPrimary">
        <span class="checkmark"></span>Set as Primary
    </label>

    <div class="flex-end mt-20">
        <button class="btn-coal" id="btnIvrDownload" data-automate-id="btnIvrDownload" (click)="downloadExcelFile()">
            {{ 'Download' }}</button>
        <button class="btn-gray ml-20" id="btnIvrCancel" (click)="closeAdd.emit()" data-automate-id="btnIvrCancel">
            {{ 'BUTTONS.cancel' | translate }}</button>
    </div>
</form>