<div class="h-100vh">
    <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
        <h3 class="fw-semi-bold">User Assignment</h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="my-16 px-16 scrollbar h-100-74">
        <div class="bg-light-pearl br-6 p-10">
            <div class="field-label mb-10 mt-0">{{entityData?.multipleAssign ? entityData?.moduleName :
                entityData?.module}}</div>
            <div class="flex-column scrollbar max-h-100-250">
                <ng-container *ngFor="let item of entityData?.name; let i = index">
                    <div class="p-12 bg-white border-bottom flex-between">
                        <div class="fw-600 text-sm  text-secondary">{{ item }} </div>
                        <div *ngIf="entityData?.multipleAssign" (click)="deleteItem(i)" class="bg-light-red icon-badge">
                            <span class="icon ic-cancel m-auto ic-xx-xs"></span>
                        </div>
                    </div>
                </ng-container>
            </div>
        </div>
        <div class="field-label"> {{'SETTINGS.select-user' | translate}}</div>
        <div class="ng-select-sm-gray">
            <ng-select [virtualScroll]="true" [items]="canAssignToAny ? allActiveUsers : activeUsers" [multiple]="true"
                appSelectAll ResizableDropdown [closeOnSelect]="false" bindLabel="fullName" bindValue="id"
                name="assignedUser" [formControl]="assignedUser" placeholder="ex. Mounika Pampana" class="bg-white">
                <ng-template ng-label-tmp let-item="item">
                    {{item.firstName}} {{item.lastName}}
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                            class="checkmark"></span>{{item.firstName}}
                        {{item.lastName}}</div>
                </ng-template>
            </ng-select>
        </div>
        <div class="flex-end mt-20">
            <button class="btn-gray mr-20" (click)="modalService.hide()">
                {{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal" (click)="userAssignment()">
                {{ 'BUTTONS.save' | translate }}</button>
        </div>
        <div class="mt-10" *ngIf="assignedUserDetails?.length">
            <div class="d-flex">
                <div class="field-label-underline">{{'GLOBAL.assigned-to'| translate}}
                </div>
            </div>
            <div class="flex-between my-12" *ngFor="let user of assignedUserDetails; let i = index">
                <div class="align-center">
                    <div class="dot dot-xl bg-pearl-90 mr-6">
                        <span class="fw-semi-bold text-normal text-white text-uppercase">{{
                            user ?
                            getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.firstName[0] +
                            getAssignedToDetails(user, canAssignToAny ? allUserList :userList)?.lastName[0] :
                            '--'}}</span>
                    </div>
                    <div class="fw-semi-bold text-large text-coal">{{getAssignedToDetails(user, canAssignToAny ?
                        allUserList : userList, true) || '--'}}</div>
                </div>
            </div>
        </div>
    </div>
</div>