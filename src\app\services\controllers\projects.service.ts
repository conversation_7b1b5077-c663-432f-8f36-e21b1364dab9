import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class ProjectsService extends BaseService<any> {
  serviceBaseUrl: string;

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'project';
  }

  getAllAssignedProjectsList(id: string): Observable<any[]> {
    return this.http.get<any[]>(
      `${env.baseURL}${env.apiURL}qrformtemplate/qr-template_projects?templateId=${id}`
    );
  }

  updateAssignedProjects(id: string, projects: any[]): Observable<any[]> {
    const url = `${env.baseURL}${env.apiURL}automation/qr-assignment`;
    return this.http.put<any[]>(url, {
      id: id,
      projectId: projects,
      source: 23,
    });
  }

  bulkDeleteProject(ids: string[]) {
    let headers: any = {
      body: {
        projectIds: ids,
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/bulk`, headers);
  }

  bulkRestoreProject(ids: string[]) {
    let headers: any = {
      ids: ids,
    };
    return this.http.put(`${this.serviceBaseUrl}/bulk/restore`, headers);
  }

  bulkDeleteProjectBlock(ids: string[]) {
    let headers: any = {
      body: {
        blockIds: ids,
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/block/bulk`, headers);
  }

  //only id and name
  getProjectIdsWithName() {
    return this.http.get(`${this.serviceBaseUrl}/idwithname`);
  }

  getAssignmentDetails(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/assignmentinfo/${id}`);
  }

  getUnitInfoList(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/unitinfo?id=${id}`);
  }

  getProjectCount(): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/count`);
  }

  getProjectDataById(id: any): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/${id}`);
  }

  addUnitType(data: any) {
    return this.http.post(`${this.serviceBaseUrl}/unittype`, data);
  }

  addProjectBlocks(data: any) {
    return this.http.post(`${this.serviceBaseUrl}/block`, data);
  }

  addProjectAmenities(data: any) {
    return this.http.post(`${this.serviceBaseUrl}/amenities`, data);
  }

  fetchProjectType() {
    return this.http.get(`${this.serviceBaseUrl}/customprojecttype`);
  }

  getMicrositeProject(payload: any) {
    return this.http.get(
      `${this.serviceBaseUrl}/microsite?serialNo=${payload}`
    );
  }

  getMicrositeUnit(payload: any) {
    return this.http.get(
      `${this.serviceBaseUrl}/microsite/units-blocks?serialNo=${payload}`
    );
  }

  getMicrositeAmentities(payload: any) {
    return this.http.get(
      `${this.serviceBaseUrl}/microsite/amenities?serialNo=${payload}`
    );
  }

  getProjectUnit(payload: any) {
    return this.http.get(
      `${this.serviceBaseUrl}/microsite/unit?serialNo=${payload}`
    );
  }

  getUnitInfoData(data: any): Observable<any> {
    const params = new HttpParams({
      fromObject: {
        PageNumber: data?.pageNumber?.toString() || '1',
        PageSize: data?.pageSize?.toString() || '10',
        ProjectId: data?.id?.toString() || '',
      },
    });
    return this.http.get(
      `${this.serviceBaseUrl}/unitinfos?${params.toString()}`
    );
  }

  getBuilderDetials(): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/builder/infos`);
  }

  getBlockData(data: any): Observable<any> {
    const params = new HttpParams({
      fromObject: {
        PageNumber: data?.pageNumber?.toString() || '1',
        PageSize: data?.pageSize?.toString() || '10',
        ProjectId: data?.id?.toString() || '',
      },
    });
    return this.http.get(`${this.serviceBaseUrl}/blocks?${params.toString()}`);
  }

  getProjectAmenities(id: any): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/amenities?id=${id}`);
  }

  doesProjectExists(contactNo: any) {
    return this.http.get(
      `${this.serviceBaseUrl}/name?projectName=${contactNo}`
    );
  }

  getLocations() {
    return this.http.get(`${this.serviceBaseUrl}/location`);
  }

  updateBasicDetails(resource: any, id: string) {
    return this.httpClient.put(
      `${this.APIUrl}/updatebasicdetails?${id}`,
      resource
    );
  }

  updateProjectBlock(resource: any) {
    return this.httpClient.put(`${this.APIUrl}/updateblockdetails`, resource);
  }

  deleteBlock(id: string | number): Observable<any> {
    return this.httpClient.delete(`${this.APIUrl}/block/${id}`);
  }

  updateProjectUnitInfo(resource: any) {
    return this.httpClient.put(`${this.APIUrl}/updateunittypes`, resource);
  }

  deleteUnitInfo(id: string | number): Observable<any> {
    return this.httpClient.delete(`${this.APIUrl}/unittype${id}`);
  }

  updateProjectAminity(resource: any) {
    return this.httpClient.put(`${this.APIUrl}/amenity`, resource);
  }

  updateProjectGallery(resource: any) {
    return this.httpClient.put(`${this.APIUrl}/gallery`, resource);
  }

  updateProjectToggleStatus(resource: any) {
    return this.httpClient.put(
      `${this.APIUrl}/toggle/status?id=${resource}`,
      resource
    );
  }

  updateUnitTypeToggleStatus(resource: any) {
    return this.httpClient.put(
      `${this.APIUrl}/toggle/unittypes?id=${resource}`,
      resource
    );
  }

  getCurrency() {
    return this.http.get(`${this.serviceBaseUrl}/currency`);
  }

  uploadExcel(selectedFile: File) {
    let formData = new FormData();
    formData.append('file', selectedFile);
    return this.http.post(`${this.serviceBaseUrl}/unit/excel`, formData);
  }

  uploadMappedColumns(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/unit/bulk`, payload);
  }

  getExcelUploadedList(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/unit/tracker?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  increaseProjectShareCount(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/contactrecordscount`, payload);
  }

  getGalleryDropDown() {
    return this.http.get(`${this.serviceBaseUrl}/gallery-dropdown`);
  }

  getProjectBasicDetailsById(id: any): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/getbasicdetails?id=${id}`);
  }

  getProjectGalleryById(id: any): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/getprojectgallery?id=${id}`);
  }

  increaseProjectUnitShareCount(payload: any) {
    return this.http.put(
      `${this.serviceBaseUrl}/unittype/contactrecordscount`,
      payload
    );
  }

  getUnitInfoById(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/unitinfo/${id}`);
  }

  deleteProjectPermanently(id: any) {
    const ids = {
      body: {
        ids: id,
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/delete`, ids);
  }

  archive(id: string) {
    return this.http.delete(`${this.serviceBaseUrl}/archive?id=${id}`);
  }

  getProjectExcelUploadedList(pageNumber: number, pageSize: number) {
    return this.http.get(`${this.serviceBaseUrl}/bulk/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`);
  }

  getExportProjectStatus(pageNumber: number, pageSize: number) {
    return this.http.get(`${this.serviceBaseUrl}/export/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`);
  }

  exportProject(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/export/batch`, payload);
  }

  uploadProjectMappedColumns(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/batch`, payload);
  }

  uploadProjectExcel(selectedFile: File) {
    let formData = new FormData();
    formData.append('file', selectedFile);
    return this.http.post(`${this.serviceBaseUrl}/excel`, formData);
  }

  getProjectWithGoogleLocation() {
    return this.http.get(`${this.serviceBaseUrl}/all/projects`);
  }

  getProjectAssignments(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/assignedusers?Id=${id}`);
  }

  getProjectsByIds(ids: string[]): Observable<any> {
    return this.http.post(`${this.serviceBaseUrl}/getmultipleprojectsbyids`, { ids: ids });
  }
}
