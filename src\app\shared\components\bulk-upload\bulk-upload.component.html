<div class="py-30 px-24 position-relative">
    <!-- <h5 class="fw-600 text-accent-green position-absolute right-30 ip-top-10 ip-right-5">How to import?</h5> -->
    <div class="flex-center-col">
        <div class="header-3 text-coal">Importing {{moduleName}} becomes more easier</div>
        <div class="flex-center mt-20">
            <img src="../../../../assets/images/file-upload.svg">
            <span class="w-170 ip-w-100px ph-w-70"
                [ngClass]="currentStep == 1 || currentStep == 2 ? 'border-dashed-bottom-2' : 'border-accent-green'"></span>
            <img src="../../../../assets/images/map-fields.svg"
                [ngClass]="{'gray-scale' : currentStep == 1 || currentStep == 2}">
            <span class="w-170 ip-w-100px ph-w-70"
                [ngClass]="currentStep !== 4 ? 'border-dashed-bottom-2' : 'border-accent-green'"></span>
            <img src="../../../../assets/images/review-import.svg" [ngClass]="{'gray-scale' : currentStep !== 4}">
        </div>
        <div class="flex-center">
            <div class="flex-center-col pl-16">
                <div class="fw-600 text-sm text-gray-90 mt-6">Step 1</div>
                <div class="fw-semi-bold text-large text-black-100 mt-2 text-center">
                    {{'PROJECTS_BLOCK_INFO.upload-file' | translate}}</div>
            </div>
            <span class="w-115 ip-w-45 ph-w-15"></span>
            <div class="flex-center-col pl-16">
                <div class="fw-600 text-sm text-gray-90 mt-6">Step 2</div>
                <div class="fw-semi-bold text-large text-black-100 mt-2 text-center">
                    {{'PROJECTS_BLOCK_INFO.map-the-fields' | translate }}
                </div>
            </div>
            <span class="w-115 ip-w-45 ph-w-15"></span>
            <div class="flex-center-col">
                <div class="fw-600 text-sm text-gray-90 mt-6">Step 3</div>
                <div class="fw-semi-bold text-large text-black-100 mt-2 text-center">{{'LEADS.review-import' | translate
                    }}
                </div>
            </div>
        </div>
    </div>
    <ng-container *ngIf="currentStep == 1 || currentStep == 2">
        <div class="mt-20 p-20 bg-white br-4">
            <div class="fw-700 header-3 text-black-100">{{'BULK_LEAD.importing-description' | translate }}</div>
            <div class="flex-between mt-20">
                <div class="d-flex">
                    <span class="dot dot-xxs bg-slate-250 mr-8 mt-6"></span>
                    <span class="text-nowrap fw-700 text-dark-gray text-large mr-10">step 1 :</span>
                    <a [href]="excelTemplatePath" (click)="trackDownload()" class="d-flex">
                        <div class="border-accent-green br-50 mr-4">
                            <span class="icon ic-down-to-line ic-accent-green ic-x-xs m-4"></span>
                        </div>
                        <span class="fw-700 text-accent-green text-large text-decoration-underline">Download
                            template</span>
                    </a>
                </div>
            </div>
            <div class="fw-semi-bold text-sm text-dark-gray mt-6 ml-60">
                {{'BULK_LEAD.download-description' | translate}}</div>
            <div class="border-bottom-slate-20 mt-12 ml-60"></div>
            <div class="d-flex mt-12">
                <span class="dot dot-xxs bg-slate-250 mr-8 mt-6"></span>
                <span class="text-nowrap fw-700 text-dark-gray text-large mr-10">step 2 :</span>
                <div>
                    <div class="fw-700 text-black-200 text-large">{{'BULK_LEAD.prepare-import-file'| translate }}</div>
                    <div class="fw-semi-bold text-sm text-dark-gray mt-6">change the dummy data in the sample file to
                        your {{moduleName}} details</div>
                </div>
            </div>
            <div class="border-bottom-slate-20 mt-12 ml-60"></div>
            <div class="d-flex mt-12">
                <span class="dot dot-xxs bg-slate-250 mr-8 mt-6"></span>
                <span class="text-nowrap fw-700 text-dark-gray text-large mr-10">step 3 :</span>
                <div>
                    <div class="fw-700 text-black-200 text-large">{{'BULK_LEAD.upload-your-file'| translate }}</div>
                    <div class="fw-semi-bold text-sm text-dark-gray mt-6">{{'BULK_LEAD.upload-description' | translate
                        }}</div>
                </div>
            </div>
        </div>
        <ng-container *ngIf="currentStep == 1">
            <div class="version-two">
                <browse-drop-upload [allowedFileType]="'excel'" [isExcelFile]="true"
                    (uploadedFile)="onFileSelection($event)"></browse-drop-upload>
            </div>
        </ng-container>
        <ng-container *ngIf="currentStep == 2">
            <div class="bg-white px-20 py-40">
                <div class="border-green-dashed-2 flex-center-col bg-green-150">
                    <div class="align-center-col py-40 text-black-200">
                        <div class="fw-semi-bold header-4  text-center">{{'BULK_LEAD.successfully-upload' | translate }}
                        </div>
                        <div class="mt-4 fw-700 header-3 text-truncate-1 break-all">{{selectedFile?.name}}</div>
                        <a class="align-center fw-600 text-large mt-10 text-lowercase">
                            <div class="text-red-450 mr-10 text-decoration-underline" (click)="currentStep = 1">
                                {{'BUTTONS.delete' | translate }}
                            </div>
                            <div class="text-aqua-750 text-decoration-underline" (click)="replaceFile()">
                                {{'BUTTONS.replace' | translate }}
                            </div>
                        </a>
                        <input type="file" #fileInput (change)="onFileSelection($event.target.files[0])" />
                    </div>
                </div>
            </div>
        </ng-container>
        <ng-container *ngIf="currentStep == 1 || currentStep == 2">
            <div class="border-bottom-slate-20"></div>
            <div class="d-flex flex-end p-10 bg-white">
                <a class="fw-600 text-large text-black-200 text-decoration-underline mr-20" (click)="navigateToHome()">
                    {{'BUTTONS.cancel' | translate}} Import</a>
                <span [ngClass]="{'pe-none opacity-6' : currentStep == 1}" class="btn-coal" (click)="uploadFile()">
                    {{'BUTTONS.proceed' | translate }}</span>
            </div>
        </ng-container>
    </ng-container>
    <ng-container *ngIf="currentStep == 3">
        <div class="bg-white p-20 br-4 text-black-100 mt-20">
            <div class="fw-700 header-4">Field mapping</div>
            <div class="d-flex flex-wrap fw-semi-bold text-large mt-4"><span class="text-nowrap">select the</span><span
                    class="fw-700 mx-2">CSV</span><span>or</span><span class="fw-700 mx-2">EXCEL</span><span
                    class="text-nowrap">fields that match
                    our</span><span class="text-accent-green mx-2 fw-700">{{getAppName()}}</span><span>fields.</span>
            </div>
        </div>
        <div class="d-flex tb-w-100-40 bg-coal scrollbar text-large fw-semi-bold text-white p-6">
            <div class="min-w-150">{{'GLOBAL.s-no' | translate}}</div>
            <div class="min-w-250"><span class="text-accent-green mr-2">{{getAppName()}}</span><span>fields</span></div>
            <div class="flex-grow-1 ml-40 ip-ml-0 mr-10"><span class="fw-700 mr-2">CSV</span><span>or</span><span
                    class="fw-700 mx-2">EXCEL</span><span>fields</span></div>
        </div>
        <div class="scrollbar tb-w-100-40 hmq-h-100-405 h-100-250 bg-white ng-select-xs">
            <div class="align-center py-10 px-16 w-100 text-large">
                <div class="min-w-150 text-dark-gray fw-700">1</div>
                <div class="min-w-300 fw-semi-bold text-black-100 flex-between mr-16">
                    <div class="field-label-clear-m-req">{{ 'BULK_LEAD.select-sheet' | translate }}</div>
                    <div class="ic-dashed-arrow"></div>
                </div>
                <div class="align-center flex-grow-1">
                    <div class="position-relative">
                        <ng-select [virtualScroll]="true" [items]="sheetNames" [(ngModel)]="selectedSheet"
                            ResizableDropdown (change)="onSheetSelection()" placeholder="Select sheet"
                            class="w-200 mr-10"></ng-select>
                        <div class="text-xxs error-message position-absolute fw-semi-bold nbottom-15 right-20"
                            *ngIf="!selectedSheet">
                            Sheet is a required field.</div>
                    </div>
                </div>
            </div>
            <ng-container *ngIf="moduleName == 'leads' else dataForm">
                <form [formGroup]="leadMappingForm">
                    <div class="border-bottom-slate-20"></div>
                    <div class="align-center py-10 px-16 w-100 text-large">
                        <div class="min-w-150 text-dark-gray fw-700">2</div>
                        <div class="min-w-300 fw-semi-bold text-black-100 flex-between mr-16">
                            <div class="field-label-clear-m-req">Name</div>
                            <div class="ic-dashed-arrow"></div>
                        </div>
                        <div class="align-center flex-grow-1">
                            <form-errors-wrapper [control]="leadMappingForm.controls['Name']" label="Name">
                                <ng-select [virtualScroll]="true" [items]="formKeys" formControlName='Name'
                                    ResizableDropdown placeholder="Select a field" class="w-200 mr-10"></ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="border-bottom-slate-20"></div>
                    <div class="align-center py-10 px-16 w-100 text-large">
                        <div class="min-w-150 text-dark-gray fw-700">3</div>
                        <div class="min-w-300 fw-semi-bold text-black-100 flex-between mr-16">
                            <div class="field-label-clear-m-req">Primary Number</div>
                            <div class="ic-dashed-arrow"></div>
                        </div>
                        <div class="align-center flex-grow-1">
                            <form-errors-wrapper [control]="leadMappingForm.controls['ContactNo']"
                                label="Primary Number">
                                <ng-select [virtualScroll]="true" [items]="formKeys" formControlName='ContactNo'
                                    ResizableDropdown placeholder="Select a field" class="w-200 mr-10"></ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <!-- Dynamic dropdown references -->
                    <ng-container *ngFor="let field of getBulkFields(leadBulkFields); let i = index">
                        <ng-container
                            *ngIf="(field.displayName !== 'Name' || field.mappingControlName !== 'Name') && (field.displayName !== 'Primary Number' || field.mappingControlName !== 'ContactNo')">
                            <div class="border-bottom-slate-20"></div>
                            <div class="align-center py-10 px-16 w-100 text-large">
                                <div class="min-w-150 text-dark-gray fw-700">{{i+2}}</div>
                                <div class="min-w-300 fw-semi-bold text-black-100 flex-between mr-16">
                                    <div class="field-label-clear-m">{{field.displayName}}</div>
                                    <div class="ic-dashed-arrow"></div>
                                </div>
                                <div class="align-center flex-grow-1">
                                    <ng-select [virtualScroll]="true" [items]="formKeys" ResizableDropdown
                                        [formControlName]="field.mappingControlName" placeholder="Select a field"
                                        class="w-200 mr-10"></ng-select>
                                </div>
                            </div>
                        </ng-container>
                    </ng-container>
                </form>
            </ng-container>
            <ng-template #dataForm>
                <form [formGroup]="dataMappingForm">
                    <div class="border-bottom-slate-20"></div>
                    <div class="align-center py-10 px-16 w-100 text-large">
                        <div class="min-w-150 text-dark-gray fw-700">2</div>
                        <div class="min-w-300 fw-semi-bold text-black-100 flex-between mr-16">
                            <div class="field-label-clear-m-req">Name</div>
                            <div class="ic-dashed-arrow"></div>
                        </div>
                        <div class="align-center flex-grow-1">
                            <form-errors-wrapper [control]="dataMappingForm.controls['Name']" label="Name">
                                <ng-select [virtualScroll]="true" [items]="formKeys" formControlName='Name'
                                    ResizableDropdown placeholder="Select a field" class="w-200 mr-10"></ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="border-bottom-slate-20"></div>
                    <div class="align-center py-10 px-16 w-100 text-large">
                        <div class="min-w-150 text-dark-gray fw-700">3</div>
                        <div class="min-w-300 fw-semi-bold text-black-100 flex-between mr-16">
                            <div class="field-label-clear-m-req">Primary Number</div>
                            <div class="ic-dashed-arrow"></div>
                        </div>
                        <div class="align-center flex-grow-1">
                            <form-errors-wrapper [control]="dataMappingForm.controls['ContactNo']"
                                label="Primary Number">
                                <ng-select [virtualScroll]="true" [items]="formKeys" formControlName='ContactNo'
                                    ResizableDropdown placeholder="Select a field" class="w-200 mr-10"></ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <ng-container *ngFor="let field of getBulkFields(dataBulkFields); let i = index">
                        <ng-container
                            *ngIf="(field.displayName !== 'Name' || field.mappingControlName !== 'Name') && (field.displayName !== 'Primary Number' || field.mappingControlName !== 'ContactNo')">
                            <div class="border-bottom-slate-20"></div>
                            <div class="align-center py-10 px-16 w-100 text-large">
                                <div class="min-w-150 text-dark-gray fw-700">{{i+2}}</div>
                                <div class="min-w-300 fw-semi-bold text-black-100 flex-between mr-16">
                                    <div class="field-label-clear-m">{{field.displayName}}</div>
                                    <div class="ic-dashed-arrow"></div>
                                </div>
                                <div class="align-center flex-grow-1">
                                    <ng-select [virtualScroll]="true" [items]="formKeys" ResizableDropdown
                                        [formControlName]="field.mappingControlName" placeholder="Select a field"
                                        class="w-200 mr-10"></ng-select>
                                </div>
                            </div>
                        </ng-container>
                    </ng-container>
                </form>
            </ng-template>
        </div>
        <div class="box-shadow-10 flex-end p-10 bg-white">
            <span class="fw-600 text-large text-black-200 text-decoration-underline mr-20 cursor-pointer"
                (click)="navigateToHome()">
                {{'BUTTONS.cancel' | translate}}</span>
            <span class="br-4 p-8 border mr-10 cursor-pointer" (click)="currentStep = 2">
                <span class="ic-chevron-left ic-light-gray ic-x-xs mr-8"></span>
                <span class="fw-600 text-large text-dark-gray">{{'BUTTONS.back' | translate}}</span></span>
            <span class="w-140 btn-coal" (click)="isValidForm()">Confirm Mapping</span>
        </div>
    </ng-container>
    <ng-template #validModal>
        <div *ngIf="isValidModal" class="p-20">
            <h3 class="text-black-100 text-center fw-semi-bold mb-10">Please choose the sheet and verify the mapped
                fields</h3>
            <div class="flex-end mt-30 flex-center">
                <button class="btn-gray" id="cancel" data-automate-id="cancel" (click)="modalRef.hide()">{{
                    'BUTTONS.cancel' | translate }}</button>
                <button class="btn-coal ml-20" id="ok" data-automate-id="ok" (click)="confirmSheet()">Verified</button>
            </div>
        </div>
    </ng-template>
    <ng-container *ngIf="currentStep == 4">
        <div class="bg-white p-20 mt-20 br-4 scrollbar h-100-290">
            <div class="fw-700 header-3 text-black-100">Review & finalize your Import</div>
            <div class="fw-semi-bold header-4 text-black-100">please review the complete process of {{moduleName}}
                importing and finish it.</div>
            <div class="align-center mt-20"> <span class="dot dot-xxs bg-slate-250 mr-8"></span><span
                    class="fw-700 text-black-200 text-large mr-10 text-nowrap">File uploaded:</span>
                <span
                    class="fw-600 text-large text-accent-green text-decoration-underline">{{selectedFile?.name}}</span>
            </div>
            <div class="border-bottom-slate-20 mt-8 ml-24"></div>
            <div class="mt-16">
                <div class="align-center">
                    <span class="dot dot-xxs bg-slate-250 mr-8"></span><span class="fw-700 text-large text-black-200">
                        field mapping done</span>
                </div>
            </div>
            <div class="border-bottom-slate-20 mt-12 ml-24"></div>
            <div class="mt-12">
                <div class="align-center">
                    <span class="dot dot-xxs bg-slate-250 mr-8"></span>
                    <span class="fw-700 text-large text-black-200">Assign these
                        {{moduleName}} to</span>
                </div>
                <span class="text-sm text-dark-gray fw-semi-bold mt-2 ml-12">The {{moduleName}} in your file record can
                    be assigned to any user(s).</span>
                <div class="ng-select-sm mt-10 ml-12 dashboard-dropdown">
                    <ng-select [virtualScroll]="true" [items]="canAssignToAny ? allActiveUsers : activeUsers "
                        ResizableDropdown [multiple]="true" appSelectAll [closeOnSelect]="false" bindLabel="Name" bindValue="id"
                        placeholder="Select User(s)" name="user" [(ngModel)]="selectedUserId"
                        class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <ng-template ng-label-tmp let-item="item">
                            {{item.firstName}} {{item.lastName}}
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.firstName}} {{item.lastName}}</div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <!-- <div class="border-bottom-slate-20 mt-8 ml-24"></div>
                <div class="align-center mt-20"> <span class="dot dot-xxs bg-slate-250 mr-8"></span><span
                    class="fw-700 text-black-200 text-large mr-10 text-nowrap">Actions:</span>
            </div>
           <div class="mt-4 ml-10 d-flex bg-white scrollbar ip-w-100-90 scroll-hide">
                <ng-container *ngFor="let option of filteredOptions">
                    <div class="align-center br-4 cursor-pointer p-6 mr-10 mt-6 border text-nowrap"
                        [ngClass]="{'border-black fw-600': selectedOption === option?.value}"
                        (click)="selectOption(option?.value)">
                        <span>{{ option?.label | translate }}</span>
                    </div>
                </ng-container>
            </div>
            <div class="mt-10 ml-10 flex-wrap">
                <ng-container *ngFor="let subOption of subOptions">
                    <div *ngIf="moduleName === 'leads' && (selectedOption === 2 || selectedOption === 3)">
                        <div>
                            <label class="form-check form-check-inline">
                                <input class="radio-check-input" type="radio" name="subOption"
                                    [(ngModel)]="selectedSubOption" [value]="subOption.value">
                                <div
                                    [ngClass]="selectedSubOption === subOption.value ? 'ml-2 fw-semi-bold text-large text-black-200': 'text-large fw-semi-bold ml-2 text-dark-gray'">
                                    {{ subOption.label }}</div>
                            </label>
                        </div>
                    </div>
                </ng-container>
            </div> -->
        </div>
        <div class="border-bottom-slate-20"></div>
        <div class="d-flex flex-end p-10 bg-white">
            <span class="fw-600 text-large text-black-200 text-decoration-underline mr-20 cursor-pointer"
                (click)="navigateToHome()">{{'BUTTONS.cancel' | translate}} Import</span>
            <span class="br-4 p-8 border mr-10 cursor-pointer" (click)="currentStep = 3"><span
                    class="ic-chevron-left ic-light-gray ic-x-xs mr-8"></span><span
                    class="fw-600 text-large text-dark-gray">{{'BUTTONS.back' | translate }}</span></span>
            <span class="btn-coal w-140"
                (click)="moduleName == 'leads' ? sendLeadMappingDetails(trackerInfoModal) : sendDataMappingDetails(trackerInfoModal)">Finish
                Importing</span>
        </div>
    </ng-container>
</div>
<ng-template #trackerInfoModal>
    <h5 class="px-20 py-16 fw-semi-bold bg-coal text-white text-capitalize">{{moduleName}} Upload Scheduled</h5>
    <div class="p-20 flex-center-col">
        <h4 class="text-black-100 fw-600 mb-10 text-center word-break line-break text-capitalize">{{moduleName}} upload
            has been scheduled.
        </h4>
        <h5 class="text-black-100 fw-semi-bold text-center word-break line-break">You can check
            <span class="cursor-pointer text-accent-green header-3 fw-600"
                (click)="openBulkUploadedStatusModal()">“Excel
                Upload Tracker”</span> to view upload status
        </h5>
        <button class="btn-coal mt-30" (click)="navigateToHome();modalService.hide()">
            {{'BULK_LEAD.got-it' | translate}}</button>
    </div>
</ng-template>