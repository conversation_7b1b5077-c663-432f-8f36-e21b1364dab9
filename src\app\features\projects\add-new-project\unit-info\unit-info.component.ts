import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { GridApi, GridOptions } from 'ag-grid-community';
import { BsModalService } from 'ngx-bootstrap/modal';
import { Subject, Subscription, takeUntil } from 'rxjs';

import {
  BHK_TYPE,
  PAGE_SIZE,
  SHOW_ENTRIES,
  UNIT_INFO_FILTERS_KEY_LABEL,
} from 'src/app/app.constants';
import { Facing, FurnishStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getBHKDisplayString, getPages } from 'src/app/core/utils/common.util';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import { AddUnitInfoComponent } from 'src/app/features/projects/add-new-project/unit-info/add-unit-info/add-unit-info.component';
import { UnitInfoActionsComponent } from 'src/app/features/projects/add-new-project/unit-info/unit-info-actions/unit-info-actions.component';
import { MatchingLeadsComponent } from 'src/app/features/property/matching-leads/matching-leads.component';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchAreaUnitList
} from 'src/app/reducers/master-data/master-data.actions';
import {
  getAreaUnits
} from 'src/app/reducers/master-data/master-data.reducer';
import {
  getAddPermissions,
  getBulkUploadPermissions,
} from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchProjectDataById,
  FetchProjectUnitExcelUploadedList,
  FetchUnitInfo,
} from 'src/app/reducers/project/project.action';
import {
  getProjectDataById,
  getUnitInfoData,
  getUnitInfoFiltersPayload,
} from 'src/app/reducers/project/project.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UnitInfoAdvanceFiltersComponent } from './unit-info-advance-filters/unit-info-advance-filters.component';

@Component({
  selector: 'unit-info',
  templateUrl: './unit-info.component.html',
})
export class UnitInfoComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  gridOptions: GridOptions;
  gridApi: GridApi;
  selectedOption: string;
  areaUnit: Array<any>;
  allUnitTypes: Array<any> = JSON.parse(localStorage.getItem('projectType') || '[]');
  canBulkUpload: any;
  canAdd: any;
  selectedDataId: any;
  selectedTrackerOption: string;
  totalCount: number;
  currOffset: number = 0;
  pageSize: number = PAGE_SIZE;
  appliedFilter: any = {};
  searchTerm: string;
  showFilters: boolean = false;
  currencyList: any[] = [];
  defaultCurrency: string;
  unitSubTypes: any = [];
  getPages = getPages;
  getBHKDisplayString = getBHKDisplayString;
  unitInfoFiltersKeyLabel = UNIT_INFO_FILTERS_KEY_LABEL;
  showLeftNav: boolean = true;
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: 10,
  };
  rowData: any[] = [];
  canExportAllUsers: boolean;
  canExportReportees: boolean;
  columns: any;
  defaultColumns: any;
  colDefs: any;
  public searchTermSubject = new Subject<string>();
  private storeSubscription: Subscription;
  gridColumnApi: any;
  masterAttributeList: any;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  selectedPageSize: number;
  basicDetailCurrency: any;
  globalSettings: any;
  projectData: any;

  constructor(
    private activatedRoute: ActivatedRoute,
    private gridOptionsService: GridOptionsService,
    private router: Router,
    private _store: Store<AppState>,
    private modalService: BsModalService,
    private sharedDataService: ShareDataService,
    public trackingService: TrackingService
  ) { }

  ngOnInit(): void {
    this.sharedDataService.shareBasicDetailCurrency$.subscribe((data) => {
      this.basicDetailCurrency = data;
    });
    this.callActivatedRoute();
    this.initializeGridSettings();

    this._store
      .select(getBulkUploadPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canBulkUpload: any) => {
        this.canBulkUpload = canBulkUpload;
      });

    this._store
      .select(getAddPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canAdd: any) => {
        this.canAdd = canAdd;
      });

    this._store.dispatch(new FetchAreaUnitList());
    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaUnit = units || [];
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettings = data;
        this.currencyList = data?.countries?.length
          ? data.countries[0].currencies
          : null;
        this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : null;
        this.initializeGridSettings();
      });

    this.unitSubTypes = [];
    this.allUnitTypes?.map((item: any) => {
      item.childTypes?.map((item: any) => {
        this.unitSubTypes.push(item);
      });
    });

    this._store
      .select(getUnitInfoFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = data;
        this.pageSize = this.filtersPayload?.pageSize;
        this.currOffset = this.filtersPayload?.pageNumber - 1;
        this.filterUnitInfo();
        this.searchTerm = this.filtersPayload?.userSearch;
      });

    this.sharedDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this.searchTermSubject.subscribe(() => {
      this.filtersPayload = {
        ...this.filtersPayload,
        pageNumber: 1,
      };
      this.filterFunction();
    });

    this.sharedDataService.updateSharedTabData(1);
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Availability',
        field: 'Availability',
        filter: false,
        valueGetter: (params: any) => [
          'Toggle Status',
          this.selectedDataId,
          this.currOffset + 1,
          this.pageSize,
        ],
        cellRenderer: UnitInfoActionsComponent,
        maxWidth: 100,
      },
      {
        headerName: 'Unit Name',
        field: 'unitName',
        valueGetter: (params: any) => [params.data?.name],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.data?.name}</p>`;
        },
      },
      {
        headerName: 'Unit Type',
        hide: false,
        field: 'unitType',
        valueGetter: (params: any) => [
          params.data?.unitType?.displayName ?? '--',
          params.data?.unitType?.childType?.displayName ?? '--',
          params.data?.noOfBHK ?? 0,
          params.data?.bhkType ?? 0,
        ],
        cellRenderer: (params: any) => {
          const displayName = params.value[0];
          const childTypeName = params.value[1];
          const noOfBHK =
            params.value[2] > 0 ? ', ' + params.value[2] + ' BHK' : '';
          const bhkType =
            params.value[3] > 0 ? ', ' + BHK_TYPE[params.value[3] - 1] : '';
          const isDataUnavailable =
            displayName === '--' && childTypeName === '--';
          return `<p class="text-truncate-1 break-all">${isDataUnavailable
            ? '--'
            : displayName +
            (childTypeName !== '--' ? ', ' + childTypeName : '') +
            noOfBHK +
            bhkType
            }</p>`;
        },
      },
      {
        headerName: 'Unit Area',
        hide: false,
        field: 'unitArea',
        valueGetter: (params: any) => [params.data?.area],
        cellRenderer: (params: any) => {
          const obj = this.areaUnit?.find(
            (item) => item.id === params.data?.areaUnitId
          );
          return `<p class="text-truncate-1 break-all">${params.data?.area ? params.data?.area : ''
            } 
          ${obj?.unit ? obj?.unit : '--'}</p>`;
        },
      },
      {
        headerName: 'Carpet Area',
        hide: true,
        field: 'carpetArea',
        valueGetter: (params: any) => params.data?.carpetArea,
        cellRenderer: (params: any) => {
          const obj = this.areaUnit?.find(
            (item) => item.id === params.data?.carpetAreaUnitId
          );
          const carpetArea = params.data?.carpetArea;
          const displayUnit = carpetArea > 0 ? obj?.unit : '';
          return `<p class="text-truncate-1 break-all">${carpetArea ? carpetArea : '--'
            } ${displayUnit}</p>`;
        },
      },
      {
        headerName: 'Built-up Area',
        hide: true,
        field: 'builtupArea',
        valueGetter: (params: any) => params.data?.buildUpArea,
        cellRenderer: (params: any) => {
          const obj = this.areaUnit?.find(
            (item) => item.id === params.data?.buildUpAreaId
          );
          const buildUpArea = params.data?.buildUpArea;
          const displayUnit = buildUpArea > 0 ? obj?.unit : '';
          return `<p class="text-truncate-1 break-all">${buildUpArea ? buildUpArea : '--'
            } ${displayUnit}</p>`;
        },
      },
      {
        headerName: 'Super Built-up Area',
        hide: true,
        field: 'superBuiltupArea',
        valueGetter: (params: any) => params.data?.superBuildUpArea,
        cellRenderer: (params: any) => {
          const obj = this.areaUnit?.find(
            (item) => item.id === params.data?.superBuildUpAreaUnit
          );
          const superBuildUpArea = params.data?.superBuildUpArea;
          const displayUnit = superBuildUpArea > 0 ? obj?.unit : '';
          return `<p class="text-truncate-1 break-all">${superBuildUpArea ? superBuildUpArea : '--'
            } ${displayUnit}</p>`;
        },
      },
      {
        headerName: 'Maintenance Cost',
        hide: true,
        field: 'maintenanceCost',
        valueGetter: (params: any) => params.data?.maintenanceCost,
        cellRenderer: (params: any) => {
          const currency = params?.data?.currency;
          const value = params.value;
          const displayCurrency = value ? ` ${currency}` : '';
          return `<p class="text-truncate-1 break-all">${displayCurrency}${value ? value : '--'
            }</p>`;
        },
      },
      {
        headerName: 'Price per unit area',
        hide: true,
        field: 'pricePerUnit',
        valueGetter: (params: any) => params.data?.pricePerUnit,
        cellRenderer: (params: any) => {
          const currency = params?.data?.currency;
          const value = params.value;
          const displayCurrency = value ? currency : '';
          return `<p class="text-truncate-1 break-all">${displayCurrency}${value ? value : '--'
            }</p>`;
        },
      },
      {
        headerName: 'Total Price',
        hide: false,
        field: 'totalPrice',
        valueGetter: (params: any) => ({
          price: params.data?.price || '',
          currency: params.data?.currency || '',
          taxationMode: params.data?.taxationMode || '',
        }),
        cellRenderer: (params: any) => {
          if (!params.value?.price) return `<p>--</p>`;
          const { price, currency, taxationMode } = params.value;
          let gstLabel = '';

          if (!this.globalSettings?.shouldEnablePropertyListing) {
            gstLabel = taxationMode === 1 ? '(Excl. GST)' : '(Incl. GST)';
          }

          return `<p class="text-truncate-1 break-all">${currency} ${price} ${gstLabel}</p>`;
        },
      },
      {
        headerName: 'Furnishing Status',
        hide: true,
        field: 'furnishingStatus',
        valueGetter: (params: any) => params.data?.furnishingStatus,
        cellRenderer: (params: any) => {
          const value = params.value;
          return `<p class="text-truncate-1 break-all">${value
            ? ['Unfurnished', 'Semi-Furnished', 'Furnished'][value - 1]
            : '--'
            }</p>`;
        },
      },
      {
        headerName: 'Facing',
        hide: true,
        field: 'facing',
        valueGetter: (params: any) => params.data?.facings,
        cellRenderer: (params: any) => {
          let facings = params.value?.map((element: any) => {
            return Facing[element];
          });
          return `<p class="text-truncate-1 break-all">${facings ? facings : '--'}</p>`;
        },
      },
      {
        headerName: 'Matching Leads',
        field: 'Matching Leads',
        hide: true,
        filter: false,
        valueGetter: (params: any) => ['ProjectsUnit', params?.data],
        cellRenderer: MatchingLeadsComponent,
      },
      {
        headerName: 'Actions',
        field: 'Actions',
        maxWidth: 165,
        minWidth: 165,
        filter: false,
        valueGetter: (params: any) => [
          'Actions',
          this.selectedDataId,
          this.currOffset + 1,
          this.pageSize,
          this.projectData,
        ],
        cellRenderer: UnitInfoActionsComponent,
      },
    ];
  }

  openProjectBulkUpload() {
    if (this.selectedOption === 'bulkUpload') {
      this.trackingService.trackFeature(`Web.ProjectUnitInfo.Button.BulkUpload.Click`)
      this.router.navigate([`/projects/bulk-upload`]);
    }
    this.selectedOption = '';
  }

  saveAndNext() {
    if (this.selectedDataId) {
      this.router.navigate([
        '/projects/edit-project/blocks-info/' + this.selectedDataId,
      ]);
    }
  }

  onPageChange(e: number) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: e + 1,
      pageSize: this.pageSize,
    };
    this._store.dispatch(new FetchUnitInfo(this.filtersPayload));
  }

  manageProject() {
    this.router.navigate(['/projects/manage-projects']);
  }

  openCenterModal() {
    this.trackingService.trackFeature(`Web.Project.Button.AddNewUnit.Click`)
    let initialState: any = {
      selectedDataId: this.selectedDataId,
      pageNumber: this.currOffset + 1,
      pageSize: this.pageSize,
      basicDetailCurrency: this.basicDetailCurrency,
    };
    this.modalService.show(AddUnitInfoComponent, {
      class: 'tb-modal-unset modal-1100',
      initialState,
    });
  }

  openProjectTracker() {
    if (this.selectedTrackerOption === 'bulkUpload') {
      let initialState: any = {
        fieldType: 'project-unit',
      };
      this.trackingService.trackFeature(`Web.ProjectUnitInfo.Button.BulkUpload.Click`)
      this._store.dispatch(new FetchProjectUnitExcelUploadedList(1, 10));
      this.modalService.show(ExcelUploadedStatusComponent, {
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      });
    }
    this.selectedTrackerOption = '';
  }

  callActivatedRoute() {
    this.activatedRoute.params.subscribe((params: any) => {
      if ((params || {}).id) {
        this.selectedDataId = params.id;
        this.sharedDataService.setProjectTitleId(params.id);
        this.filtersPayload = {
          ...this.filtersPayload,
          id: params.id,
        };
        this._store.dispatch(new FetchUnitInfo(this.filtersPayload));
        this._store.dispatch(new FetchProjectDataById(params.id));
        this._store
          .select(getProjectDataById)
          .pipe(takeUntil(this.stopper))
          .subscribe((data) => {
            if (data?.length) {
              if (!this.basicDetailCurrency) {
                this.basicDetailCurrency = data?.[0]?.monetaryInfo?.currency;
              }
              this.projectData = data?.[0];
              if (this.gridApi) {
                this.gridApi.refreshCells();
              }
            }
          });
      }
      this._store
        .select(getUnitInfoData)
        .pipe(takeUntil(this.stopper))
        .subscribe((data) => {
          this.rowData = data?.items ? data?.items : [];
          this.totalCount = data?.totalCount || null;
          this.pageSize = this.filtersPayload?.pageSize || 10;
          this.currOffset = this.filtersPayload?.pageNumber - 1 || 0;
        });
    });
  }

  filterUnitInfo() {
    this.appliedFilter = {
      ...this.appliedFilter,
      Search: this.filtersPayload?.Search,
      MinArea: this.filtersPayload?.MinArea,
      MaxArea: this.filtersPayload?.MaxArea,
      AreaUnitId: this.filtersPayload?.AreaUnitId,
      path: 'project/unitinfos',
      MinCarpetArea: this.filtersPayload?.MinCarpetArea,
      MaxCarpetArea: this.filtersPayload?.MaxCarpetArea,
      CarpetAreaUnitId: this.filtersPayload?.CarpetAreaUnitId,
      MinBuiltupArea: this.filtersPayload?.MinBuiltupArea,
      MaxBuiltupArea: this.filtersPayload?.MaxBuiltupArea,
      BuiltupAreaUnitId: this.filtersPayload?.BuiltupAreaUnitId,
      MinSuperBuiltupArea: this.filtersPayload?.MinSuperBuiltupArea,
      MaxSuperBuiltupArea: this.filtersPayload?.MaxSuperBuiltupArea,
      SuperBuiltupAreaUnitId: this.filtersPayload?.SuperBuiltupAreaUnitId,
      MaintenanceCost: this.filtersPayload?.MaintenanceCost,
      PricePerUnit: this.filtersPayload?.PricePerUnit,
      TotalPrice: this.filtersPayload?.TotalPrice,
      Currency: this.filtersPayload?.Currency,
      UnitType: this.filtersPayload?.UnitType,
      UnitSubType: this.filtersPayload?.UnitSubType,
      BHKs: this.filtersPayload?.BHKs,
      BHKTypes: this.filtersPayload?.BHKTypes,
      Facings: this.filtersPayload?.Facings?.map((data: any) => {
        return Facing[data];
      }),
      FurnishingStatuses: this.filtersPayload?.FurnishingStatuses?.map(
        (item: any) => {
          return FurnishStatus[item];
        }
      ),
      OrderBy: this.filtersPayload?.OrderBy,
      NoOfBalconies: this.filtersPayload?.NoOfBalconies,
      NoOfBathrooms: this.filtersPayload?.NoOfBathrooms,
      NoOfLivingrooms: this.filtersPayload?.NoOfLivingrooms,
      NoOfBedrooms: this.filtersPayload?.NoOfBedrooms,
      NoOfUtilites: this.filtersPayload?.NoOfUtilites,
      NoOfKitchens: this.filtersPayload?.NoOfKitchens,
      NoOfMaximumOccupants: this.filtersPayload?.NoOfMaximumOccupants,
    };
    if (
      this.appliedFilter?.MinArea ||
      this.appliedFilter?.MaxArea ||
      this.appliedFilter?.AreaUnitId ||
      this.appliedFilter?.PageNumber?.length ||
      this.appliedFilter?.MinCarpetArea ||
      this.appliedFilter?.MaxCarpetArea ||
      this.appliedFilter?.CarpetAreaUnitId ||
      this.appliedFilter?.MinBuiltupArea ||
      this.appliedFilter?.MaxBuiltupArea ||
      this.appliedFilter?.BuiltupAreaUnitId?.length ||
      this.appliedFilter?.MinSuperBuiltupArea ||
      this.appliedFilter?.MaxSuperBuiltupArea ||
      this.appliedFilter?.SuperBuiltupAreaUnitId?.length ||
      this.appliedFilter?.MaintenanceCost ||
      this.appliedFilter?.PricePerUnit ||
      this.appliedFilter?.TotalPrice ||
      this.appliedFilter?.Currency?.length ||
      this.appliedFilter?.UnitType?.length ||
      this.appliedFilter?.UnitSubType?.length ||
      this.appliedFilter?.BHKs?.length ||
      this.appliedFilter?.BHKTypes?.length ||
      this.appliedFilter?.Facings?.length ||
      this.appliedFilter?.FurnishingStatuses?.length ||
      this.appliedFilter?.NoOfBalconies ||
      this.appliedFilter?.NoOfBathrooms?.length ||
      this.appliedFilter?.NoOfLivingrooms?.length ||
      this.appliedFilter?.NoOfBedrooms?.length ||
      this.appliedFilter?.NoOfUtilites?.length ||
      this.appliedFilter?.NoOfKitchens?.length ||
      this.appliedFilter?.NoOfMaximumOccupants?.length ||
      this.appliedFilter?.floorNumber ||
      this.appliedFilter?.OrderBy
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
  }

  getArrayOfFilters(key: string, values: any) {
    if (
      [
        'pageSize',
        'pageNumber',
        'path',
        'AreaUnitId',
        'CarpetAreaUnitId',
        'BuiltupAreaUnitId',
        'SuperBuiltupAreaUnitId',
        'Search',
      ].includes(key) ||
      values?.length === 0 ||
      (key == 'maxPrice' && this.appliedFilter?.maxPrice == 0)
    )
      return [];
    return values?.toString()?.split(',');
  }

  openAdvFiltersModal() {
    this.trackingService.trackFeature(`Web.ProjectUnitInfo.AdvanceFilter.Click`)
    let initialState: any = {
      class: 'tb-modal-unset modal-1100',
      initialState: {
        appliedFilter: this.appliedFilter,
      },
    };
    const modalRef = this.modalService.show(
      UnitInfoAdvanceFiltersComponent,
      initialState
    );
    (
      modalRef.content as UnitInfoAdvanceFiltersComponent
    ).filterEmitter.subscribe((value: any) => {
      this.appliedFilter = { ...value.appliedFilter };
      this.filterFunction();
    });
  }

  applyAdvancedFilter() {
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: 1,
    };
    this.filterFunction();
    this.modalService.hide();
  }

  filterFunction() {
    this.filtersPayload = {
      ...this.filtersPayload,
      id: this.selectedDataId,
      path: 'project/unitinfos',
      Search: this.searchTerm,
      MinArea: this.appliedFilter?.MinArea,
      MaxArea: this.appliedFilter?.MaxArea,
      AreaUnitId: this.appliedFilter?.AreaUnitId,
      MinCarpetArea: this.appliedFilter?.MinCarpetArea,
      MaxCarpetArea: this.appliedFilter?.MaxCarpetArea,
      CarpetAreaUnitId: this.appliedFilter?.CarpetAreaUnitId,
      MinBuiltupArea: this.appliedFilter?.MinBuiltupArea,
      MaxBuiltupArea: this.appliedFilter?.MaxBuiltupArea,
      BuiltupAreaUnitId: this.appliedFilter?.BuiltupAreaUnitId,
      MinSuperBuiltupArea: this.appliedFilter?.MinSuperBuiltupArea,
      MaxSuperBuiltupArea: this.appliedFilter?.MaxSuperBuiltupArea,
      SuperBuiltupAreaUnitId: this.appliedFilter?.SuperBuiltupAreaUnitId,
      MaintenanceCost: this.appliedFilter?.MaintenanceCost,
      PricePerUnit: this.appliedFilter?.PricePerUnit,
      TotalPrice: this.appliedFilter?.TotalPrice,
      Currency: this.appliedFilter?.Currency,
      UnitType: this.appliedFilter?.UnitType,
      UnitSubType: this.appliedFilter?.UnitSubType,
      BHKs: this.appliedFilter?.BHKs,
      BHKTypes: this.appliedFilter?.BHKTypes,
      Facings: this.appliedFilter?.Facings?.map((data: any) => {
        return Facing[data];
      }),
      FurnishingStatuses: this.appliedFilter?.FurnishingStatuses?.map(
        (item: any) => {
          return FurnishStatus[item];
        }
      ),
      NoOfBalconies: this.appliedFilter?.NoOfBalconies,
      NoOfBathrooms: this.appliedFilter?.NoOfBathrooms,
      NoOfLivingrooms: this.appliedFilter?.NoOfLivingrooms,
      NoOfBedrooms: this.appliedFilter?.NoOfBedrooms,
      NoOfUtilites: this.appliedFilter?.NoOfUtilites,
      NoOfKitchens: this.appliedFilter?.NoOfKitchens,
      NoOfMaximumOccupants: this.appliedFilter?.NoOfMaximumOccupants,
      OrderBy: this.appliedFilter?.OrderBy,
    };
    this.filterUnitInfo();
    this._store.dispatch(new FetchUnitInfo(this.filtersPayload));
  }

  getUnitSubType(value: any) {
    return this.unitSubTypes.find((item: any) => item.id === value)
      ?.displayName;
  }

  getAreaUnit(id: string) {
    let areaUnit = '';
    this.areaUnit?.forEach((type: any) => {
      if (type.id === id) areaUnit = type.unit;
    });
    return areaUnit;
  }

  getUnitArea(area: number) {
    return (
      area + ' ' + (this.getAreaUnit(this.appliedFilter?.AreaUnitId) || '')
    );
  }

  getUnitCarpetArea(area: number) {
    return (
      area +
      ' ' +
      (this.getAreaUnit(this.appliedFilter?.CarpetAreaUnitId) || '')
    );
  }

  getUnitBuiltupArea(area: number) {
    return (
      area +
      ' ' +
      (this.getAreaUnit(this.appliedFilter?.BuiltupAreaUnitId) || '')
    );
  }

  getUnitSuperBuiltupArea(area: number) {
    return (
      area +
      ' ' +
      (this.getAreaUnit(this.appliedFilter?.SuperBuiltupAreaUnitId) || '')
    );
  }

  getUnitType(value: any) {
    return this.allUnitTypes.find((item: any) => item.id === value)
      ?.displayName;
  }

  changeUnitType(data: any) {
    this.unitSubTypes = [];
    if (!data?.length) {
      this.allUnitTypes?.map((item: any) => {
        item.childTypes?.map((item: any) => {
          this.unitSubTypes.push(item);
        });
      });
    } else {
      this.allUnitTypes?.map((item: any) => {
        if (data.includes(item?.id)) {
          item.childTypes?.map((item: any) => {
            this.unitSubTypes.push(item);
          });
        }
      });
    }
  }

  onRemoveFilter(key: any, value: any) {
    if (Array.isArray(this.appliedFilter[key])) {
      let indexToRemove = this.appliedFilter[key].indexOf(value);
      const list = [...this.appliedFilter[key]];
      list.splice(indexToRemove, 1);
      this.appliedFilter[key] = list;
      this.filterFunction();
      return;
    }
    const areaKeys = [
      'Area',
      'CarpetArea',
      'BuiltupArea',
      'SuperBuiltupArea',
    ];

    for (const areaKey of areaKeys) {
      if (key === `Min${areaKey}` || key === `Max${areaKey}`) {
        this.appliedFilter[`Min${areaKey}`] = null;
        this.appliedFilter[`Max${areaKey}`] = null;
        this.appliedFilter[`${areaKey}UnitId`] = null;
        this.filterFunction();
        return;
      }
    }
    if (key === 'Search') {
      this.searchTerm = '';
    }
    this.appliedFilter[key] = null;
    this.filterFunction();
  }

  reset() {
    for (let key in this.appliedFilter) {
      if (this.appliedFilter.hasOwnProperty(key)) {
        this.appliedFilter[key] = null;
      }
    }
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (this.searchTerm === '' || this.searchTerm === null) {
        return;
      }
      this.trackingService.trackFeature(`Web.ProjectUnitInfo.DataEntry.Search.DataEntry`)
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput(data: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  onGridReady(params: any) {
    this.gridApi = params?.api;
    this.gridColumnApi = params.columnApi;
    this.toggleColumns(params);
    if (this.projectData) {
      this.gridApi.refreshCells();
    }
  }

  toggleColumns(params: any): void {
    this.columns = params?.columnApi?.getColumns()?.map((column: any) => {
      return {
        label: column?.getColDef()?.headerName,
        value: column,
      };
    });
    this.columns = this.columns
      .slice(2, this.columns?.length - 1)
      .sort((a: any, b: any) => a?.label?.localeCompare(b?.label));
    this.defaultColumns = this.columns?.filter(
      (col: any) => col?.value?.getColDef()?.hide !== true
    );

    let columnState = JSON.parse(localStorage.getItem('unitInfoColumnState'));
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }

    let columnData = localStorage.getItem('unit-info-columns')?.split(',');

    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
  }

  onColumnsSelected(columns: any): void {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('unit-info-columns', colData?.toString());
    const cols = columns?.map((col: any) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col: any) => col.value),
      false
    );
    var columnState: any = this.gridColumnApi.getColumnState();
    localStorage.setItem('unitInfoColumnState', JSON.stringify(columnState));
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
  }

  decProperty(item: any) {
    let count = Number(this.appliedFilter[item] || 0);
    if (count > 0) {
      this.appliedFilter[item] = --count;
      if (count === 0) {
        this.appliedFilter[item] = null;
      }
    }
  }

  incProperty(item: any) {
    let count2 = Number(this.appliedFilter[item] || 0);
    this.appliedFilter[item] = ++count2;
  }

  updateFilter(item: any, value: any) {
    let parsedValue = parseInt(value, 10);
    if (!isNaN(parsedValue)) {
      this.appliedFilter[item] = parsedValue;
    } else {
      this.appliedFilter[item] = null;
    }
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this.gridOptions.paginationPageSize = this.pageSize;
    this.gridOptions.api?.paginationSetPageSize(this.selectedPageSize);
    this.gridApi.setRowData([]);
    this.currOffset = 0;
    this.filterFunction();
    this.trackingService.trackFeature(`Web.ProjectUnitInfo.Options.${this.pageSize}.Click`)
  }

  onSetColumnDefault() {
    this.trackingService.trackFeature(`Web.ProjectUnitInfo.Button.Default.Click`)
    this.defaultColumns = this.columns.filter(
      (col: any) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
  }
  ngOnDestroy() {
    if (this.storeSubscription) {
      this.storeSubscription.unsubscribe();
    }
    this.stopper.next();
    this.stopper.complete();
  }
}
