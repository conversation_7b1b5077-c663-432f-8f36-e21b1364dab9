.owl-dt-container {
  background-color: $primary-black;
  @extend .br-10, .header-4;

  .owl-dt-calendar-table .owl-dt-calendar-cell,
  .owl-dt-calendar-header,
  .owl-dt-calendar-control {
    @extend .text-white;
  }

  .owl-dt-timer-hour12 .owl-dt-timer-hour12-box {
    border: 1px solid $white;
    @extend .text-white;
  }

  .owl-dt-timer {
    @extend .text-white;
  }
}

.owl-dt-calendar-table .owl-dt-calendar-cell-out {
  color: $purple-200;
  opacity: 1;
}

.owl-dt-calendar-table .owl-dt-calendar-cell-in-range {
  background: #ff000033;
}

.owl-dt-calendar-table .owl-dt-calendar-cell-selected {
  background-color: $orange-500 !important;
}

.owl-dt-container-buttons {
  color: $dark-blue-100;
}

.owl-dt-control-arrow-button svg {
  fill: $white;
}

.owl-dt-calendar-table .owl-dt-calendar-cell-disabled>.owl-dt-calendar-cell-content:not(.owl-dt-calendar-cell-selected) {
  color: $slate-30;
  cursor: not-allowed;
}

.owl-dt-control {
  @extend .text-white;
}

.owl-dt-inline-container,
.owl-dt-popup-container {
  width: 250px !important;
}

.owl-dt-calendar-table .owl-dt-calendar-cell-selected.owl-dt-calendar-cell-today {
  box-shadow: unset;
}

.custom-today {
  background-color: $accent-green !important;
  border-color: $accent-green;
  border-radius: 10px;
}