import { Component, ElementRef, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { Chart, ChartTypeRegistry } from 'chart.js';
import { jsPDF } from 'jspdf';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { changeCalendar, getTimeZoneDate, setTimeZoneDateWithTime } from 'src/app/core/utils/common.util';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ReportsGraphStateService } from '../../../services/shared/reports-graph-state.service';

interface ChildColumn {
  field: string;
  headerName: string;
  parentName?: string;
  uniqueField?: string;
  displayName?: string;
  originalField?: string;
  valueGetter?: Function;
}

@Component({
  selector: 'report-graph',
  templateUrl: './reports-graph.component.html',
})
export class ReportGraphComponent implements OnChanges {
  @ViewChild('activityChart') chartCanvas: ElementRef;
  @Input() rowData: any[] = [];
  @Input() gridOptions: any;
  @Input() filteredColumnDefsCache: any[] = [];
  @Input() xAxisData: any;
  @Input() reportType: string = 'default';
  @Input() type: string = 'default';
  @Input() userData: any;
  @Output() exportTriggered = new EventEmitter<void>();

  parentColumns: any[] = [];
  childColumns: ChildColumn[] = [];
  selectedParent: any[] = [];
  selectedChildren: ChildColumn[] = [];
  selectedChartType: keyof ChartTypeRegistry = 'line';
  chart: Chart | null = null;
  isChartReady: boolean = false;
  noDataAvailable: boolean = false;
  showSelectionMessage: boolean = false;
  getTimeZoneDate = getTimeZoneDate;

  readonly chartTypes = [
    { label: 'Line Chart', value: 'line' },
    { label: 'Bar Chart', value: 'bar' },
    // { label: 'Radar Chart', value: 'radar' },
  ] as const;

  private destroy$ = new Subject<void>();
  currentDate: Date = new Date();
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  setTime: string;
  getTime: string;

  constructor(
    public modalService: BsModalService,
    private graphStateService: ReportsGraphStateService,
    private store: Store<AppState>,
    private modalRef: BsModalRef,
    private headerTitle: HeaderTitleService
  ) {
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.filteredColumnDefsCache && this.filteredColumnDefsCache?.length > 0) {
      this.initializeColumnsAndLoadSelections();
    }

    if (changes.rowData || changes.selectedChartType) {
      this.updateChartWithDelay();
    }
  }

  initializeColumnsAndLoadSelections(): void {
    this.setupColumns();

    if (this.graphStateService.isInitialized(this.reportType)) {
      this.loadSavedSelections();
    } else {
      this.initializeDefaultSelections();
    }

    this.updateChartWithDelay();
  }

  setupColumns(): void {
    this.parentColumns = [];
    this.childColumns = [];

    const hasParentColumns = this.filteredColumnDefsCache.some(col => col.children && col.children.length > 0);

    if (hasParentColumns) {
      this.parentColumns = this.filteredColumnDefsCache;
    } else {
      this.childColumns = this.filteredColumnDefsCache.map(col => ({
        ...col,
        parentName: col.headerName
      }));
    }
  }

  loadSavedSelections(): void {
    const savedSelections = this.graphStateService.getSelections(this.reportType);

    this.selectedParent = Array.isArray(savedSelections.selectedParent)
      ? savedSelections.selectedParent
      : savedSelections.selectedParent ? [savedSelections.selectedParent] : [];

    this.selectedChildren = savedSelections.selectedChildren;
    this.selectedChartType = savedSelections.selectedChartType as keyof ChartTypeRegistry;
    this.childColumns = savedSelections.childColumns;
    this.updateSelectionState();
  }

  initializeDefaultSelections(): void {
    const regularColumns = this.filteredColumnDefsCache.filter(col => !col.isParent);
    this.selectedParent = [];
    this.selectedChildren = [];

    if (this.parentColumns.length > 0) {
      this.childColumns = [];
    } else {
      this.childColumns = regularColumns;
    }

    this.saveSelectionsToService(regularColumns);
    this.updateSelectionState();
  }

  createChildColumnsWithMetadata(parent: any): ChildColumn[] {
    if (!parent?.children?.length) return [];

    return parent.children.map((child: any) => ({
      ...child,
      parentName: parent.headerName,
      uniqueField: `${parent.headerName}_${child.field}`,
      displayName: `${child.headerName} (${parent.headerName})`,
      originalField: child.field
    }));
  }

  saveSelectionsToService(regularColumns: any[]): void {
    this.graphStateService.initializeSelections(this.reportType, this.parentColumns, regularColumns);
    this.graphStateService.updateParentSelection(this.reportType, this.selectedParent, this.childColumns);
    this.graphStateService.updateChildrenSelection(this.reportType, this.selectedChildren);
  }

  updateChartWithDelay(): void {
    this.isChartReady = false;
    setTimeout(() => this.initializeChart(), 300);
  }

  validateSelections(): boolean {
    if (this.parentColumns.length > 0) {
      return this.selectedParent?.length > 0 && this.selectedChildren?.length > 0;
    } else {
      return this.selectedChildren?.length > 0;
    }
  }

  updateSelectionState(): void {
    const hasValidSelections = this.validateSelections();
    this.showSelectionMessage = !hasValidSelections;

    if (!hasValidSelections) {
      this.isChartReady = false;
      this.noDataAvailable = false;
      this.destroyExistingChart();
    }
  }

  findColumnByField(field: string): any {
    if (!field) return null;

    const foundInCache = this.searchInColumns(this.filteredColumnDefsCache, field);
    if (foundInCache) return foundInCache;

    if (this.gridOptions?.columnDefs) {
      return this.searchInColumns(this.gridOptions.columnDefs, field);
    }

    return null;
  }

  searchInColumns(columns: any[], field: string): any {
    for (const col of columns) {
      if (col.field === field) return col;

      if (col.children?.length > 0) {
        const childCol = col.children.find((child: any) => child.field === field);
        if (childCol) return childCol;
      }
    }
    return null;
  }

  filterTotalRows(rows: any[]): any[] {
    if (!rows?.length) return [];

    const totalFields = [
      'userName', 'projectTitle', 'name', 'firstName',
      'source', 'subSource', 'agencyName', 'city',
      'status', 'subStatus', 'location'
    ];

    return rows.filter(row =>
      row && !totalFields.some(field => row[field] === 'Total')
    );
  }

  getXAxisLabel(row: any): string {
    if (!this.xAxisData || !row) return '';

    if (this.type === 'DateFormat' && row[this.xAxisData]) {
      return getTimeZoneDate(row[this.xAxisData], this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear');
    }

    if (this.xAxisData.includes(' ')) {
      return this.xAxisData.split(' ')
        .map((field: string) => row[field] || '')
        .filter(Boolean)
        .join(' ');
    }

    if (this.xAxisData.includes('|')) {
      const fields = this.xAxisData.split('|');
      for (const field of fields) {
        const value = this.getNestedValue(row, field.trim());
        if (value) return value;
      }
      return '';
    }

    if (this.xAxisData.includes('.')) {
      return this.getNestedValue(row, this.xAxisData) || '';
    }

    return row[this.xAxisData] || '';
  }

  getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, prop) => current?.[prop], obj);
  }

  initializeChart(): void {
    this.updateSelectionState();

    if (this.showSelectionMessage) {
      this.destroyExistingChart();
      return;
    }

    const ctx = document.getElementById('activityChart') as HTMLCanvasElement;
    if (!ctx) {
      this.isChartReady = false;
      return;
    }

    this.destroyExistingChart();

    const filteredRowData = this.filterTotalRows(this.rowData);
    if (!filteredRowData?.length) {
      this.showNoDataState();
      return;
    }

    this.noDataAvailable = false;
    this.showSelectionMessage = false;
    const datasets = this.createChartDatasets(filteredRowData);
    const labels = this.createChartLabels(filteredRowData);

    this.chart = new Chart(ctx, {
      type: this.selectedChartType,
      data: { labels, datasets },
      options: this.getChartOptions()
    });

    this.isChartReady = true;
  }

  destroyExistingChart(): void {
    if (this.chart) {
      this.chart.destroy();
      this.chart = null;
    }

    const ctx = document.getElementById('activityChart') as HTMLCanvasElement;
    if (ctx) {
      const context = ctx.getContext('2d');
      if (context) {
        context.clearRect(0, 0, ctx.width, ctx.height);
      }
    }
  }

  showNoDataState(): void {
    this.noDataAvailable = true;
    this.isChartReady = false;
    this.showSelectionMessage = false;
  }

  createChartLabels(filteredRowData: any[]): string[] {
    return filteredRowData.map(row => {
      const label = this.getXAxisLabel(row);
      return label.length > 15 ? label.substring(0, 15) + "..." : label;
    });
  }

  createChartDatasets(filteredRowData: any[]): any[] {
    return this.selectedChildren.map((child, index) => {
      const fieldToUse = child.originalField || child.field;
      const col = this.findColumnByField(fieldToUse);

      return {
        label: child.displayName || col?.headerName || col?.field || 'Unknown',
        data: this.extractDataFromRows(filteredRowData, col),
        backgroundColor: this.getChartColor(index, 0.5),
        borderColor: this.getChartColor(index, 1),
        borderWidth: 1,
      };
    });
  }

  extractDataFromRows(rows: any[], col: any): number[] {
    return rows.map(row => {
      let value;
      if (col?.valueGetter && typeof col.valueGetter === 'function') {
        value = col.valueGetter({ data: row });
        value = Array.isArray(value) ? value[0] : value;
      } else {
        value = row[col?.field];
      }
      // Convert undefined, null, or NaN to 0
      return value === undefined || value === null || isNaN(value) ? 0 : value;
    });
  }

  getChartOptions(): any {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        tooltip: { mode: 'index', intersect: false },
        legend: this.getLegendConfig()
      },
      scales: {
        x: { beginAtZero: true, ticks: { font: { size: 11 } } },
        y: { beginAtZero: true, ticks: { font: { size: 11 } } },
      },
    };
  }

  getLegendConfig(): any {
    return {
      display: true,
      position: 'top',
      align: 'center',
      labels: {
        usePointStyle: true,
        pointStyle: 'circle',
        font: { size: 13, weight: 'normal' },
        color: '#333333',
        padding: 16,
        boxWidth: 10,
        boxHeight: 10,
        generateLabels: (chart: any) => {
          return chart.data.datasets.map((dataset: any, index: number) => ({
            text: dataset.label,
            fillStyle: dataset.backgroundColor,
            strokeStyle: dataset.borderColor,
            lineWidth: 0,
            pointStyle: 'circle',
            hidden: false,
            index: index
          }));
        }
      }
    };
  }

  onParentChange(events: any[]) {
    if (events && events.length > 0) {
      this.selectedParent = events;
      this.childColumns = [];
      events.forEach((parent) => {
        if (parent?.children?.length > 0) {
          const childrenWithParentInfo = parent.children.map((child: any) => ({
            ...child,
            parentName: parent.headerName,
            uniqueField: `${parent.headerName}_${child.field}`,
            displayName: `${child.headerName} (${parent.headerName})`,
            originalField: child.field
          }));
          this.childColumns = [...this.childColumns, ...childrenWithParentInfo];
        }
      });

      this.selectedChildren = this.selectedChildren.filter(selected =>
        this.childColumns.some(available =>
          available.uniqueField === selected.uniqueField ||
          (available.field === selected.field && available.parentName === selected.parentName)
        )
      );

      this.graphStateService.updateParentSelection(this.reportType, events, this.childColumns);
      this.initializeChart();
    } else {
      this.childColumns = [];
      this.selectedChildren = [];
      this.destroyExistingChart();
      this.updateSelectionState();
    }
  }

  onChildrenChange(events: any[]) {
    this.selectedChildren = events || [];
    this.graphStateService.updateChildrenSelection(this.reportType, this.selectedChildren);

    if (!this.selectedChildren.length) {
      this.destroyExistingChart();
    }

    this.initializeChart();
  }

  onChartTypeChange(event: any): void {
    this.selectedChartType = event.value;
    this.graphStateService.updateChartType(this.reportType, event.value);
    this.updateChartWithDelay();
  }

  getChartColor(index: number, opacity: number): string {
    const hue = (index * 137.508) % 360;
    const saturation = 65 + (index % 4) * 8;
    const lightness = 45 + (index % 5) * 6;

    const h = hue / 360;
    const s = Math.min(saturation, 90) / 100;
    const l = Math.min(lightness, 70) / 100;

    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    };

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    const r = Math.round(hue2rgb(p, q, h + 1 / 3) * 255);
    const g = Math.round(hue2rgb(p, q, h) * 255);
    const b = Math.round(hue2rgb(p, q, h - 1 / 3) * 255);

    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  compareByField(item1: any, item2: any): boolean {
    return item1?.field === item2?.field;
  }

  compareByUniqueField(item1: any, item2: any): boolean {
    return item1?.uniqueField === item2?.uniqueField;
  }

  getBindLabel(): string {
    return this.parentColumns.length > 0 ? 'displayName' : 'headerName';
  }

  getBindValue(): string {
    return this.parentColumns.length > 0 ? 'uniqueField' : 'field';
  }

  getCompareFunction(): (item1: any, item2: any) => boolean {
    return this.parentColumns.length > 0 ? this.compareByUniqueField : this.compareByField;
  }

  getDisplayLabel(item: any): string {
    return item.displayName || item.headerName;
  }

  exportGraph() {
    this.exportTriggered.emit();
    const canvas = document.getElementById('activityChart') as HTMLCanvasElement;
    if (canvas) {
      const scaledCanvas = document.createElement('canvas');
      const ctx = scaledCanvas.getContext('2d');
      scaledCanvas.width = canvas.width;
      scaledCanvas.height = canvas.height;

      if (ctx) {
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(0, 0, scaledCanvas.width, scaledCanvas.height);
        ctx.drawImage(canvas, 0, 0);

        const imgData = scaledCanvas.toDataURL('image/png');
        const pdf = new jsPDF('landscape', 'mm', 'a4');
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();

        // Use the current header title as the graph name
        const graphName = this.headerTitle.pageTitle.getValue();
        pdf.setFontSize(18);
        pdf.text(graphName, pdfWidth / 2, 20, { align: 'center' });

        const ratio = Math.min(pdfWidth / scaledCanvas.width, (pdfHeight - 30) / scaledCanvas.height);
        const imgX = (pdfWidth - scaledCanvas.width * ratio) / 2;
        const imgY = 30; // Start image lower to make space for title

        pdf.addImage(imgData, 'PNG', imgX, imgY, scaledCanvas.width * ratio, scaledCanvas.height * ratio);
        this.setTime = setTimeZoneDateWithTime(this.currentDate, this.userData?.timeZoneInfo?.baseUTcOffset);
        this.getTime = getTimeZoneDate(new Date(this.setTime), this.userData?.timeZoneInfo?.baseUTcOffset, 'fullDateTime')
        const fileName = `${this.reportType}_chart_${this.getTime}.pdf`;
        pdf.save(fileName);
      }
    }
  }
}

