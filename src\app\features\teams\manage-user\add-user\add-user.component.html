<div routerLink="/teams/manage-user" *ngIf="canViewUsers" [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
  class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
</div>
<div *ngIf="canViewComponent" class="p-30 pt-0 h-100-115 scrollbar scroll-hide">
  <form [formGroup]="userBasicInfoForm" class="pt-10" autocomplete="off">
    <div class="d-flex tb-flex-col">
      <div class="d-flex tb-w-100 ip-flex-col" [ngClass]="canViewRole? ' w-70':' w-100'">
        <div class="w-50 ip-w-100">
          <div class="justify-center">
            <div class="field-label-underline">{{ 'USER_MANAGEMENT.basic-information' | translate }}</div>
          </div>
          <div>
            <div class="field-label-req">{{ 'AUTH.user-name' | translate }} <span type="button" data-bs-toggle="tooltip"
                data-bs-html="true" title="Min length should be 6.
Only Alpha-numeric.
White or empty space is not allowed.">
                <img src="../../../../../assets/images/i-btn.svg" /></span></div>
            <form-errors-wrapper [control]="userBasicInfoForm.controls['userId']"
              label="{{'AUTH.user-name' | translate }}" [ngClass]="{'non-editable' : selectedUserId}">
              <input type="text" autocomplete="off" required
                [formControlName]="selectedUserId ? 'Selecteduser' : 'userId'" id="inpAddUserID"
                data-automate-id="inpAddUserID" placeholder="ex. mounikap" [readOnly]="selectedUserId">
              <div class="error-message" *ngIf="doesUserNameExist&&userBasicInfoForm.controls.userId.status==='VALID'">
                Username already exists</div>
            </form-errors-wrapper>
          </div>
          <div *ngIf="!selectedUserId">
            <div>
              <div class="field-label-req">{{ 'AUTH.password' | translate }}</div>
              <form-errors-wrapper [control]="userBasicInfoForm.controls['password']"
                label="{{'AUTH.password' | translate }}">
                <input [type]="isShowPassword ? 'text' : 'password'" autocomplete="new-password" required
                  formControlName="password" id="inpPassword" data-automate-id="inpPassword"
                  [minlength]="selectedUserId ? 1 : 6" [value]="password ? randomPassword : null" #pass
                  placeholder="ex. 1qaz2wsx....">
                <a class="icon ic-gray cursor-pointer position-absolute top-10 right-10"
                  [ngClass]="isShowPassword ? 'ic-eye-slash' : 'ic-eye-solid'" id="passwordhide"
                  data-automate-id="passwordhide" (click)="isShowPassword = !isShowPassword"></a>
              </form-errors-wrapper>
              <!-- TODO: Once Generate password fucntion is added uncomment the below code -->
              <!--<a class="fw-600 text-sm text-accent-green justify-end mt-4" (click)="generate()">
                            {{ 'LEAD_FORM.generate' | translate }} {{ 'AUTH.password' | translate }}</a>-->
            </div>
            <div class="field-label-req">{{ 'AUTH.confirm-password' | translate }}</div>
            <div class="form-group">
              <form-errors-wrapper [control]="userBasicInfoForm.controls['confirmPassword']"
              label="Confirm Password">
              <input [type]="isShowConfPassword ? 'text' : 'password'" required formControlName="confirmPassword"
                id="inpConfirmPassword" data-automate-id="inpConfirmPassword" [value]="password ? randomPassword : null"
                #pass placeholder="ex. 1qaz2wsx....">
              <a class="icon ic-gray cursor-pointer position-absolute top-10 right-10"
                [ngClass]="isShowConfPassword ? 'ic-eye-slash' : 'ic-eye-solid'" id="confirmPasswordhide"
                data-automate-id="confirmPasswordhide" (click)="isShowConfPassword = !isShowConfPassword"></a>
              </form-errors-wrapper>
            </div>
            <div class="border-bottom mx-30 mt-20 flex-grow-1"></div>
          </div>
          <div class="align-center">
            <div class="w-50">
              <div class="field-label-req">{{ 'AUTH.first-name' | translate }}</div>
              <form-errors-wrapper [control]="userBasicInfoForm.controls['firstName']"
                label="{{'AUTH.first-name' | translate }}">
                <input type="text" required formControlName="firstName" id="inpFirstName"
                  data-automate-id="inpFirstName" placeholder="ex. Mounika">
              </form-errors-wrapper>
            </div>
            <div class="ml-8 w-50">
              <div class="field-label-req">{{ 'AUTH.last-name' | translate }}</div>
              <form-errors-wrapper [control]="userBasicInfoForm.controls['lastName']"
                label="{{'AUTH.last-name' | translate }}">
                <input type="text" required formControlName="lastName" id="inpLastName" data-automate-id="inpLastName"
                  placeholder="ex. Pampana">
              </form-errors-wrapper>
            </div>
          </div>
          <div>
            <div class="field-label-req">{{ 'GLOBAL.work' | translate }} {{'USER.email' | translate}}</div>
            <form-errors-wrapper [control]="userBasicInfoForm.controls['email']"
              label="{{ 'GLOBAL.work' | translate }} {{ 'PROPERTY.OWNER_INFO.email' | translate }}">
              <input type="email" required formControlName="email" id="inpUserEmail" (input)="doesEmailExists()"
                data-automate-id="inpUserEmail" placeholder="ex. <EMAIL>">
              <div class="error-message" *ngIf="doesEmailExist && userBasicInfoForm.controls.email.status==='VALID'">
                Email already exists</div>
            </form-errors-wrapper>
          </div>
          <div>
            <div class="field-label-req">{{ 'GLOBAL.work' | translate }} {{
              'PROPERTY.OWNER_INFO.phone' | translate}}</div>
            <form-errors-wrapper [control]="userBasicInfoForm.controls['contactNo']"
              label="{{ 'GLOBAL.work' | translate }} {{'PROPERTY.OWNER_INFO.phone' | translate}}">
              <ngx-mat-intl-tel-input #contactNoInput *ngIf="hasInternationalSupport"
                [preferredCountries]="preferredCountries" [enablePlaceholder]="true" [enableSearch]="true"
                formControlName="contactNo" class="no-validation contactNoInput" placeholder="9133XXXXXX">
              </ngx-mat-intl-tel-input>
              <ngx-mat-intl-tel-input #contactNoInput *ngIf="!hasInternationalSupport"
                [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                [enablePlaceholder]="true" [enableSearch]="true" formControlName="contactNo"
                class="no-validation contactNoInput" placeholder="9133XXXXXX">
              </ngx-mat-intl-tel-input>
              <div class="error-message"
                *ngIf="doesPhoneNoExist&&userBasicInfoForm.controls.contactNo.status==='VALID'">Phone number already
                exists</div>
            </form-errors-wrapper>
          </div>
          <div>
            <div class="field-label">{{ 'GLOBAL.personal' | translate }}
              {{ 'PROPERTY.OWNER_INFO.phone' | translate }}</div>
            <form-errors-wrapper [control]="userBasicInfoForm.controls['altPhoneNumber']"
              label="{{'GLOBAL.alternate' | translate}} {{'GLOBAL.number' | translate}}">
              <ngx-mat-intl-tel-input #alternateNoInput *ngIf="hasInternationalSupport"
                [preferredCountries]="preferredCountries" [enablePlaceholder]="true" [enableSearch]="true"
                formControlName="altPhoneNumber" class="alternateNoInput no-validation" placeholder="9133XXXXXX">
              </ngx-mat-intl-tel-input>
              <ngx-mat-intl-tel-input #alternateNoInput *ngIf="!hasInternationalSupport"
                [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                [enablePlaceholder]="true" [enableSearch]="true" formControlName="altPhoneNumber"
                class="alternateNoInput no-validation" placeholder="9133XXXXXX">
              </ngx-mat-intl-tel-input>
            </form-errors-wrapper>
          </div>
          <div>
            <div class="field-label">{{'USER_MANAGEMENT.home-location' | translate}}</div>
            <form-errors-wrapper>
              <textarea rows="3" id="txtHomeLocation" data-automate-id="txtHomeLocation" formControlName="homeLocation"
                placeholder="ex.  HSR Layout, Bengaluru"></textarea>
            </form-errors-wrapper>
          </div>
        </div>
        <div class="border-right mx-30 mt-20 flex-grow-1 ip-d-none"></div>
        <div class="w-50 ip-w-100">
          <div class="justify-center">
            <div class="field-label-underline">{{ 'USER_MANAGEMENT.office-information' | translate }}</div>
          </div>
          <div class="remove-form-group">
            <div class="field-label">General Manager</div>
            <form-errors-wrapper label="General Manager">
              <ng-select [virtualScroll]="true" placeholder="ex. Deepak" formControlName="generalManager"
                ResizableDropdown [clearOnBackspace]="true" [ngClass]="{'pe-none blinking': isUserListLoading}">
                <ng-option *ngFor="let user of users" [value]="user?.id" class="flex-center">{{user.firstName}}
                  {{user.lastName}} <span class="error-message-custom top-10 right-33" *ngIf="!user.isActive">( Disabled
                    )</span></ng-option>
              </ng-select>
            </form-errors-wrapper>
          </div>
          <!----<div>
            <div class="field-label">{{ 'USER_MANAGEMENT.employee-number' | translate }}</div>
            <div class="form-group">
              <input type="text" formControlName="empNo" id="inpEmpNo" data-automate-id="inpEmpNo"
                placeholder="ex. GO-1033">
            </div>
          </div>-->
          <div class="remove-form-group">
            <div class="field-label">{{ 'USER_MANAGEMENT.reporting-to' | translate}}</div>
            <form-errors-wrapper label="{{ 'USER_MANAGEMENT.reporting-to' | translate}}">
              <ng-select [virtualScroll]="true" placeholder="ex. Manasa" formControlName="reporting" ResizableDropdown
                [clearOnBackspace]="true" [ngClass]="{'pe-none blinking': isUserListLoading}">
                <ng-option *ngFor="let user of users" [value]="user?.id" class="flex-center">{{user.firstName}}
                  {{user.lastName}} <span class="error-message-custom top-10 right-33" *ngIf="!user.isActive">( Disabled
                    )</span></ng-option>
              </ng-select>
            </form-errors-wrapper>
          </div>
          <div class="remove-form-group">
            <div class="field-label-req">{{ 'USER_MANAGEMENT.department' | translate}}</div>
            <form-errors-wrapper [control]="userBasicInfoForm.controls['department']"
              label="{{ 'USER_MANAGEMENT.department' | translate}}">
              <ng-select [virtualScroll]="true" id="department" [addTag]="addNewDepartment" ResizableDropdown
                placeholder="Select/Create Department" formControlName="department" class="bg-white"
                [ngClass]="{'pe-none blinking': isDepartmentLoading}">
                <ng-option *ngFor="let department of departmentList" [value]="department?.id">
                  {{department.name}}</ng-option>
              </ng-select>
            </form-errors-wrapper>
          </div>
          <div class="remove-form-group">
            <div class="field-label-req">{{ 'USER_MANAGEMENT.designation' | translate}}</div>
            <form-errors-wrapper [control]="userBasicInfoForm.controls['designation']"
              label="{{ 'USER_MANAGEMENT.designation' | translate}}">
              <ng-select [virtualScroll]="true" [addTag]="addNewDesignation" ResizableDropdown
                placeholder="Select/Create Designation" bindValue="id" formControlName="designation" bindLabel="name"
                class="bg-white" [ngClass]="{'pe-none blinking': isDesignationLoading}">
                <ng-option *ngFor="let designation of designationList" [value]="designation?.id">
                  {{designation.name}}</ng-option>
              </ng-select>
            </form-errors-wrapper>
          </div>
          <div class="remove-form-group">
            <div class="justify-between align-end">
              <div class="field-label">Time Zone</div>
              <label class="checkbox-container mb-4" *ngIf="userBasicInfoForm.controls['timeZone'].value">
                <input type="checkbox" formControlName="shouldShowTimeZone">
                <span class="checkmark"></span>Show Time Zone
              </label>
            </div>
            <form-errors-wrapper [control]="userBasicInfoForm.controls['timeZone']" label="Time Zone">
              <ng-select [virtualScroll]="true" ResizableDropdown placeholder="select time zone" class="ph-mx-4"
                formControlName="timeZone">
                <ng-option *ngFor="let time of timeZoneList" [value]="time?.displayName">
                  <div class="flex-between">
                    <div class="text-truncate-1 break-all">{{time?.displayName}}</div>
                    <i class="text-disabled gray">({{time?.ianaZoneId}})</i>
                  </div>
                </ng-option>
              </ng-select>
            </form-errors-wrapper>
          </div>
          <div class="form-group">
            <div class="field-label">Broker Number</div>
            <input type="text" formControlName="licenseNo" id="inplicenseNo" data-automate-id="inplicenseNo"
              placeholder="ex. 98765467">
          </div>
          <div>
            <div class="field-label">{{'PROJECTS.description' | translate}}</div>
            <form-errors-wrapper>
              <textarea rows="5" id="txtUserDesc" data-automate-id="txtUserDesc" formControlName="description"
                placeholder="ex.  I want to say....."></textarea>
            </form-errors-wrapper>
          </div>
        </div>
      </div>
      <div class="border-right mx-30 mt-20 flex-grow-1 ip-d-none" *ngIf="canViewRole"></div>
      <div class="w-30 tb-w-100" *ngIf="canViewRole">
        <ng-container *ngIf="!isRolesLoading; else loader">
          <div class="flex-center">
            <div class="field-label-underline">{{ 'USER_MANAGEMENT.roles-permission' | translate }}</div>
          </div>
          <div *ngIf="canAddRole" class="pt-10 flex-end m-10 mb-16 position-relative">
            <button class="btn-coal w-130" (click)="navigateToRoleModal(saveDataModal)">
              <span class="ic-add icon ic-xxs mr-8"></span>{{ 'USER.add-new-role' | translate }}</button>
            <div class="error-message" *ngIf="noRoleSelected">{{ 'USER.please-select-role' | translate }}</div>
          </div>
          <div class="align-center w-100 px-12 no-validation py-8 br-4 border">
            <span class="search icon ic-search ic-xxs ic-slate-90 mr-6"> </span>
            <input placeholder="Search for role..." (input)="searchRoleName($event.target.value)" name="search"
              class="border-0 outline-0 w-100 bg-light-pearl">
          </div>
          <div class="scrollbar my-10 pr-8" *ngIf="canViewRole" [ngClass]="selectedUserId ? 'h-100-155' : 'h-100vh'">
            <ng-container *ngFor="let role of rolesList">
              <ng-container *ngIf="!searchTerm || role.name.toLowerCase().includes(searchTerm.toLowerCase())">
                <div class="d-flex">
                  <label class="checkbox-container field-label pl-30">
                    <input type="checkbox" id="inp{{role.name}}" data-automate-id="inp{{role.name}}"
                      (change)="assignRole($event.target.checked, role, false)"
                      [checked]="isRoleAssignedToUser(role?.id)">
                    <span class="checkmark"></span>{{role.name}}
                  </label>
                </div>
                <p class="text-gray mt-4 ml-30 text-xs word-break"
                  [innerHTML]="getSanitizedHtml(renamePermission(role.permissionDescription))"></p>
              </ng-container>
            </ng-container>
          </div>
        </ng-container>
      </div>
    </div>
  </form>
</div>
<div *ngIf="canViewComponent" class="px-20 py-16 bg-white w-100"
  [ngClass]="selectedUserId ? 'flex-between ip-flex-col' : 'flex-end'">
  <div class="align-center ph-d-none w-100" *ngIf="selectedUserId">
    <div>
      <div class="text-sm text-mud mr-20">{{'LEADS.created-by' | translate}}:</div>
      <div class="fw-semi-bold">{{ getAssignedToDetails(selectedUserInfo?.createdBy, users, true) || '--'}}</div>
    </div>
    <div class="ml-20">
      <div class="text-sm text-mud">{{'LEADS.created-date' | translate}}:</div>
      <div class="fw-semi-bold">
        {{ selectedUserInfo?.createdOn ? getTimeZoneDate(selectedUserInfo.createdOn,
        userData?.timeZoneInfo?.baseUTcOffset ) : '--'}}
      </div>
    </div>
    <div class="border-left mx-16 h-35px"></div>
    <div>
      <div class="text-sm text-mud mr-20">{{'LEADS.modified-by' | translate}}:</div>
      <div class="fw-semi-bold">{{ getAssignedToDetails(selectedUserInfo?.lastModifiedBy, users, true) || '--'}}</div>
    </div>
    <div class="ml-20">
      <div class="text-sm text-mud">{{'LEADS.modified-date' | translate}}:</div>
      <div class="fw-semi-bold">{{selectedUserInfo?.lastModifiedOn ? getTimeZoneDate(selectedUserInfo.lastModifiedOn,
        userData?.timeZoneInfo?.baseUTcOffset ) : '--'}}</div>
    </div>
  </div>
  <div *ngIf="canViewComponent" class="align-center">
    <button class="btn-gray" (click)="goToManageUser()">{{ 'BUTTONS.cancel' | translate }}</button>
    <button class="btn-coal ml-20" (click)="postData(licenseBoughtModal)">{{ 'BUTTONS.save' | translate }}</button>
  </div>
</div>

<ng-template #saveDataModal>
  <save-changes (SaveChanges)="saveInModal(licenseBoughtModal)" (DiscardChanges)="discardInModal()"
    (Hide)="closeModal()"></save-changes>
</ng-template>

<ng-template #licenseBoughtModal>
  <div class="p-20">
    <h4 class="fw-semi-bold text-balck-200">License Limit Reached Connect with your admin or {{getAppName()}} Support
      **********
      to Buy More </h4>
    <div class="flex-end mt-30">
      <button class="btn-green" (click)="checkLicenseBought()">
        ok</button>
    </div>
  </div>
</ng-template>
<ng-template #loader>
  <div class="flex-center h-100">
    <application-loader></application-loader>
  </div>
</ng-template>