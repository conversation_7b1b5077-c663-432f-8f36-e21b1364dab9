import {
  Component,
  EventEmitter,
  OnD<PERSON>roy,
  OnInit,
  TemplateRef,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { Subject, switchMap, takeUntil } from 'rxjs';
import {
  PAGE_SIZE,
  REPORTS_DATE_TYPE,
  REPORT_FILTERS_KEY_LABEL,
  SHOW_ENTRIES,
  USER_VISIBILITY,
} from 'src/app/app.constants';
import {
  IntegrationSource,
  LeadSource,
  ReportDateType,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { ReportsFilter } from 'src/app/core/interfaces/reports.interface';
import {
  assignToSort,
  changeCalendar,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import {
  FetchLeadCities,
  FetchLeadCountries,
  FetchLeadStates,
  FetchProjectList,
  FetchSubSourceList,
} from 'src/app/reducers/lead/lead.actions';
import {
  getLeadCities,
  getLeadCitiesIsLoading,
  getLeadCountries,
  getLeadCountriesIsLoading,
  getLeadStates,
  getLeadStatesIsLoading,
  getProjectList,
  getProjectListIsLoading,
  getSubSourceList,
  getSubSourceListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchReportsUserSubSource,
  FetchUserSubSourceExportSuccess,
  UpdateUserSubSourceFilterPayload,
} from 'src/app/reducers/reports/reports.actions';
import {
  getReportsUserSubSourceList,
  getReportsUsersSubSourceListIsLoading,
  getUserSubSourceFiltersPayload,
} from 'src/app/reducers/reports/reports.reducer';
import {
  FetchOnlyReporteesWithInactive,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getOnlyReporteesWithInactive,
  getOnlyReporteesWithInactiveIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'user-subsource-report',
  templateUrl: './user-subsource-report.component.html',
})
export class UserSubsourceReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  leadSources: Array<any> = [];
  columnDropDown: { field: string; hide: boolean }[] = [];
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  projectList: any;
  rowData: Array<any> = [];
  filtersPayload: ReportsFilter;
  appliedFilter: any;
  canExportAllUsers: boolean = false;
  canViewAllUsers: boolean = false;
  canViewReportees: boolean = false;
  canExportReportees: boolean = false;
  userTotalCount: number;
  getPages = getPages;
  keySeparator: string = ',/_,-,/_,';
  dateTypeList: Array<string> = REPORTS_DATE_TYPE.slice(0, 4);
  visibilityList: Array<Object> = USER_VISIBILITY.slice(0, 3);
  subSourceList: any;
  allSubSourceList: any;
  allUsers: Array<any> = [];
  onlyReportees: Array<any> = [];
  users: Array<any> = [];
  reportees: Array<any> = [];
  mappingSource: Array<any> = [];
  allReportsData: Array<any> = [];
  showLeftNav: boolean = true;
  isSubSourceReportLoading: boolean = true;
  isAllUsersLoading: boolean = true;
  isOnlyReporteesLoading: boolean = true;
  allSubSourceListIsLoading: boolean = true;
  isProjectListLoading: boolean = true;
  showFilters: boolean = false;
  moment = moment;
  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;
  cities: string[];
  citiesIsLoading: boolean = true;
  states: string[];
  statesIsLoading: boolean = true;
  countryList: any[];
  countryIsLoading: boolean = true;
  isSourcesLoading: boolean = true;


  userData: any;
  currentDate: Date = new Date();
  toDate: any = new Date();
  fromDate: any = new Date();
  onPickerOpened = onPickerOpened;
  s3BucketUrl: string = environment.s3ImageBucketURL;

  constructor(
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    private router: Router,
    private modalService: BsModalService,
    private shareDataService: ShareDataService
  ) { }
  ngOnInit() {
    this.selectedPageSize = 50;
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });
    this.headerTitle.setLangTitle('Leads - User vs Sub-Source Report');
    this.metaTitle.setTitle('CRM | Reports');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);

    this._store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        if (leadSource) {
          const enabledSources = leadSource
            .filter((source: any) => source.isEnabled)
            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));
          this.leadSources = [...enabledSources];
        } else {
          this.leadSources = [];
        }
      });

    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });

    this._store.dispatch(new FetchSubSourceList());
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this._store
      .select(getUserSubSourceFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = { ...data, isNavigatedFromReports: true };
        this.pageSize = this.filtersPayload?.pageSize;
        const userStatus =
          this.filtersPayload?.userStatus === undefined
            ? 1
            : this.filtersPayload?.userStatus;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          userStatus: userStatus,
          visibility: this.filtersPayload?.userStatus,
          dateType: ReportDateType[Number(this.filtersPayload?.dateType)],
          date: [
            patchTimeZoneDate(
              this.filtersPayload?.fromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              this.filtersPayload?.toDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ],
          withTeam: this.filtersPayload?.IsWithTeam,
          users: this.filtersPayload?.UserIds,
          search: this.filtersPayload?.SearchText,
          sources: this.filtersPayload?.Sources,
          subSources: this.filtersPayload?.SubSources,
          projects: this.filtersPayload?.Projects,
          cities: this.filtersPayload?.Cities,
          states: this.filtersPayload?.States,
        };
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.users = usersData;
          this.allUsers = usersData;
          this.allUsers = assignToSort(this.allUsers, '');
          return this._store
            .select(getUsersListForReassignmentIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
        if (!isLoading) {
          this.currentVisibility(1, false);
        }
      });

    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.reportees = usersData;
          this.onlyReportees = usersData;
          this.onlyReportees = assignToSort(this.onlyReportees, '');
          return this._store
            .select(getOnlyReporteesWithInactiveIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isOnlyReporteesLoading = isLoading;
        if (!isLoading) {
          this.currentVisibility(1, false);
        }
      });

    this._store
      .select(getLeadCities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.cities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getLeadCitiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.citiesIsLoading = data;
      });

    this._store
      .select(getLeadStates)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.states = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getLeadStatesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.statesIsLoading = data;
      });

    this._store
      .select(getLeadCountries)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryList = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getLeadCountriesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.countryIsLoading = data;
      });

    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProjectListLoading = isLoading;
      });

    this._store
      .select(getSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allSubSourceList = data;
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.allSubSourceListIsLoading = isLoading;
      });

    this._store
      .select(getReportsUserSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.items?.length) {
          this.allReportsData = data?.items;
          this.mappingSource = data?.items?.map(
            (item: any) => item?.sourceWithSubSourceCount
          );

          let totalRow: any = {
            projectTitle: 'Total',
            firstName: 'Total',
            lastName: '',
            userName: 'Total',
            subSource: 'Total',
            agencyName: 'Total',
            source: 'Total',
          };
          totalRow.sourceWithSubSourceCount = {
            ...totalRow,
            ...this.sumObjectValues(
              data?.items?.map((item: any) => {
                return item.sourceWithSubSourceCount;
              })
            ),
          };
          this.rowData = [...data?.items, totalRow].map((row: any) => {
            const sourceTotalCounts: any = {};
            this.allReportsData?.forEach((user: any) => {
              user.source.forEach((source: any) => {
                if (!sourceTotalCounts[source.displayName]) {
                  sourceTotalCounts[source.displayName] = 0;
                }
                sourceTotalCounts[source.displayName] += source.count;
              });
            });

            return {
              ...row,
              ...sourceTotalCounts,
            };
          });
          this.userTotalCount = data?.totalCount;
          this.initializeGridSettings();
        } else {
          this.rowData = [];
        }
      });

    this._store
      .select(getReportsUsersSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isSubSourceReportLoading = isLoading;
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExportAllUsers = permissionsSet.has(
          'Permissions.Reports.ExportAllUsers'
        );
        this.canViewAllUsers = permissionsSet.has(
          'Permissions.Reports.ViewAllUsers'
        );
        this.canExportReportees = permissionsSet.has(
          'Permissions.Reports.ExportReportees'
        );
        this.canViewReportees = permissionsSet.has(
          'Permissions.Reports.ViewReportees'
        );
        if (this.canViewAllUsers) {
          this._store.dispatch(new FetchUsersListForReassignment());
        }
        if (this.canViewReportees) {
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
      });
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
    this.filterFunction();
    this.initializeGridSettings();
  }

  flattenObject(obj: any, parentKey = '') {
    let result: any = {};
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        let newKey = parentKey ? `${parentKey}${this.keySeparator}${key}` : key;

        if (typeof obj[key] === 'object' && obj[key] !== null) {
          Object.assign(result, this.flattenObject(obj[key], newKey));
        } else {
          result[newKey] = obj[key];
        }
      }
    }
    return result;
  }

  sumObjectValues(objects: any) {
    const result: any = {};
    objects.forEach((obj: any) => {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (
            typeof obj[key] === 'object' &&
            Object.keys(obj[key]).length > 0
          ) {
            result[key] = result[key] || {};
            result[key] = this.sumObjectValues([result[key], obj[key]]);
          } else {
            result[key] = (result[key] || 0) + obj[key];
          }
        }
      }
    });
    return result;
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);

    this.gridOptions.columnDefs = [
      {
        headerName: 'User Name',
        field: 'User Name',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [
          params.data?.userName,
          params?.data?.projectTitle,
        ],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<div class="py-16 align-center text-truncate"><p>${params.value[0]}
                    </p></div>`;
        },
      },
    ];
    const columnMap = new Map();
    const customSort = (key: string): number => {
      if (key.includes('(WithOutSubSourceCount)')) {
        return 1;
      } else {
        return 0;
      }
    };
    for (const item of this.mappingSource) {
      for (let [source, subSources] of Object.entries(item)) {
        let tempSubSources: any = subSources;
        const sortedObj: any = {};
        const itemKeys: string[] = Object.keys(tempSubSources).sort(
          (a, b) => customSort(a) - customSort(b)
        );
        itemKeys.forEach((key) => {
          sortedObj[key] = tempSubSources[key];
        });
        let col = columnMap.get(source);

        subSources = sortedObj;

        if (!col) {
          col = {
            headerName: source.toString(),
            children: [],
          };
          columnMap.set(source, col);
          this.gridOptions.columnDefs.push(col);
        }
        const subsourceColumns = [];
        for (const subSource of Object.keys(subSources)) {
          const existingSubSource = col.children.find(
            (child: any) => child.field === this.getKey(subSource?.toString())
          );

          if (!existingSubSource) {
            subsourceColumns.push({
              headerName: subSource
                ?.toString()
                ?.replace('(WithOutSubSourceCount)', '')
                .trim(),
              field: this.getKey(subSource?.toString()),
              filter: false,
              valueGetter: (params: any) => {
                return [
                  params.data?.sourceWithSubSourceCount?.[source]?.[
                  subSource
                  ] || '--',
                  source,
                  subSource,
                  params.data?.projectTitle,
                  params?.data?.userId,
                ];
              },
              minWidth: 160,
              cellRenderer: (params: any) => {
                const filters = {
                  ...this.filtersPayload,
                  UserIds: [params?.value?.[4]],
                  Sources: [source],
                };

                if (filters?.IsWithTeam) filters.IsWithTeam = false;
                return params.value[3] === 'Total' || params.value[0] === '--'
                  ? `<p>${params.value[0] || params.value[3] || '--'}</p>`
                  : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
              },
              cellStyle: { cursor: 'pointer' },
              onCellClicked: (event: any) => {
                const isCtrlClick = event?.event?.ctrlKey;
                const params = { value: event?.value, data: event?.data };
                const source = event?.value[1];
                const subSourceC = event?.value[2];
                if (
                  params?.data?.projectTitle === 'Total' ||
                  params.value[0] === '--' ||
                  subSourceC?.includes('(WithOutSubSourceCount)')
                ) {
                  return;
                } else if (event.value[0]) {
                  if (isCtrlClick) {
                    this.getDataInNewTab(source, subSourceC, params);
                    return;
                  }
                  this.getDataFromCell(source, subSourceC, params);
                }
              },
            });
          }
        }

        if (!col.children.find((child: any) => child.headerName === 'Total')) {
          col.children.push({
            headerName: 'Total',
            field: this.getKey(source) + 'Total',
            filter: false,
            valueGetter: (params: any) => {
              const subSourceData = params.data?.source;
              const count = Array.isArray(subSourceData)
                ? subSourceData
                  .filter(
                    (data) =>
                      this.getKey(data?.displayName) === this.getKey(source)
                  )
                  .map((data) => data?.count)[0] || 0
                : 0;
              return [
                count,
                params?.data?.userId,
                params?.data?.projectTitle,
                params?.data?.[source],
              ];
            },
            minWidth: 160,
            cellRenderer: (params: any) => {
              const filters = {
                ...this.filtersPayload,
                UserIds: [params?.value?.[1]],
                Sources: [source],
              };

              if (filters?.IsWithTeam) filters.IsWithTeam = false;
              return params?.value?.[2] === 'Total'
                ? `<p>${params.value[3] ? params.value[3] : '--'}</p>`
                : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
            },
            cellStyle: { cursor: 'pointer' },
            onCellClicked: (event: any) => {
              const isCtrlClick = event?.event?.ctrlKey;
              const params = { value: event?.value, data: event?.data };
              if (event.data.projectTitle === 'Total') {
                return;
              } else if (event.value[0]) {
                if (isCtrlClick) {
                  const filters = {
                    ...this.filtersPayload,
                    UserIds: [params?.value?.[1]],
                    Sources: [source],
                  };
                  if (filters?.IsWithTeam) filters.IsWithTeam = false;
                  window?.open(
                    `leads/manage-leads?leadReportGetData=true&assignTo=${params?.value[1]
                    }&data=${encodeURIComponent(
                      JSON.stringify({ source })
                    )}&operation=${'All Leads'}&filtersPayload=${encodeURIComponent(
                      JSON.stringify({ ...filters })
                    )}`
                  );
                  return;
                }
                this.getData(source, event);
              }
            },
          });
        }
        col.children.unshift(...subsourceColumns);
      }
    }

    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index !== 0 && index !== this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.rowData = this.rowData;
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  getData(operation: string, event: any, subSourceList?: string) {
    this.router.navigate(['leads/manage-leads']);
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.dateType = this.appliedFilter.dateType;
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.status = operation;
    this.gridOptionsService.subStatus = subSourceList;
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) filters.IsWithTeam = false;
    this.gridOptionsService.payload = {
      ...filters,
      AssignTo: [event?.data?.userName],
      Sources: [operation],
    };
  }

  getKey(displayName: string): string {
    let keyWithoutSpace = displayName?.replace(/[\s]+/g, '');
    return keyWithoutSpace?.toLowerCase();
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  getDataFromCell(source: string, subSource: string, event: any) {
    if (subSource?.includes('(WithOutSubSourceCount)')) {
      return;
    }
    this.router.navigate(['leads/manage-leads']);
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.dateType = this.appliedFilter.dateType;
    this.gridOptionsService.data = event?.data;
    this.gridOptionsService.status = 'All Leads';
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) filters.IsWithTeam = false;
    this.gridOptionsService.payload = {
      ...filters,
      Sources: [source],
      SubSources: !subSource?.includes('(WithOutSubSourceCount)')
        ? [subSource]
        : null,
    };
  }

  getDataInNewTab(source: string, subSource: string, params: any) {
    if (subSource?.includes('(WithOutSubSourceCount)')) {
      return;
    }
    const filters = {
      ...this.filtersPayload,
      UserIds: [params?.value[4]],
      Sources: [source],
      SubSources: !subSource?.includes('(WithOutSubSourceCount)')
        ? [subSource]
        : null,
    };

    if (filters?.IsWithTeam) {
      filters.IsWithTeam = false;
    }
    window?.open(
      `leads/manage-leads?leadReportGetData=true&assignTo=
       ${params?.value[4]}&data=${encodeURIComponent(
        JSON.stringify({
          subSource: !subSource?.includes('(WithOutSubSourceCount)')
            ? [subSource]
            : null,
        })
      )}&operation=${'All Leads'}&filtersPayload=${encodeURIComponent(
        JSON.stringify({ ...filters })
      )}`,
      '_blank'
    );
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this._store.dispatch(
      new UpdateUserSubSourceFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchReportsUserSubSource());
  }

  onResetDateFilter() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateType: null,
      date: '',
    };
    this.filterFunction();
  }

  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = [
      'subSources',
      'projects',
      'agencyNames',
      'cities',
      'states',
    ];

    if (
      [
        'pageSize',
        'pageNumber',
        'visibility',
        'withTeam',
        'userStatus',
        'search',
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'date' && values.length === 2) {
      if (key === 'date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDate = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDate = getTimeZoneDate(
          this.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDate = getTimeZoneDate(
          this.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    } else if (allowedKeys.includes(key)) {
      return values;
    }
    return values?.toString()?.split(',');
  }

  applyAdvancedFilter() {
    this.filterFunction();
    this.modalService.hide();
  }

  onRemoveFilter(key: string, value: string) {
    if (['dateType', 'date'].includes(key)) {
      delete this.appliedFilter[key];
      const dependentKey = key === 'date' ? 'dateType' : 'date';
      if (this.appliedFilter[dependentKey]) {
        delete this.appliedFilter[dependentKey];
      }
    } else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter(
        (item: any) => item !== value
      );
    }
    this.filterFunction();
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchSubSourceList());
    this._store.dispatch(new FetchLeadCities());
    this._store.dispatch(new FetchLeadStates());
    // this._store.dispatch(new FetchLeadCountries());
    this._store.dispatch(new FetchAllSources());
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.fullName}`;
    });
    return userName;
  }

  currentVisibility(visibility: any, isTopLevelFilter: any) {
    this.appliedFilter.userStatus = visibility;
    this.appliedFilter.pageNumber = 1;
    if (isTopLevelFilter) {
      this.appliedFilter.users = null;
    }
    this.filterFunction();

    if (this.canViewAllUsers) {
      switch (visibility) {
        case 1:
          this.allUsers = this.users?.filter((user: any) => user.isActive);
          break;
        case 2:
          this.allUsers = this.users?.filter((user: any) => !user.isActive);
          break;
        case null:
          this.allUsers = this.users;
          break;
      }
      this.allUsers = assignToSort(this.allUsers, '');
    } else {
      switch (visibility) {
        case 1:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => user.isActive
          );
          break;
        case 2:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => !user.isActive
          );
          break;
        case null:
          this.onlyReportees = this.reportees;
          break;
      }
      this.onlyReportees = assignToSort(this.onlyReportees, '');
    }
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(
      new UpdateUserSubSourceFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchReportsUserSubSource());
    this.currOffset = 0;
  }

  filterFunction() {
    this.appliedFilter.pageNumber = 1;
    if (
      this.appliedFilter?.dateType?.length ||
      this.appliedFilter?.date?.[0]?.length ||
      this.appliedFilter.users?.length ||
      this.appliedFilter.projects?.length ||
      this.appliedFilter.subSources?.length ||
      this.appliedFilter.sources?.length ||
      this.appliedFilter.cities?.length ||
      this.appliedFilter.Countries?.length ||
      this.appliedFilter.states?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      userStatus: this.appliedFilter.userStatus,
      dateType: ReportDateType[this.appliedFilter.dateType],
      fromDate: setTimeZoneDate(
        this.appliedFilter?.date?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      toDate: setTimeZoneDate(
        this.appliedFilter.date?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      IsWithTeam: this.appliedFilter.withTeam,
      UserIds: this.appliedFilter.users,
      SearchText: this.searchTerm,
      Sources: this.appliedFilter.sources,
      SubSources: this.appliedFilter.subSources,
      Projects: this.appliedFilter.projects,
      ReportPermission: this.canViewAllUsers ? 0 : 1,
      ExportPermission: this.canExportAllUsers ? 0 : 1,
      Cities: this.appliedFilter?.cities,
      States: this.appliedFilter?.states,
      Countries: this.appliedFilter?.Countries,
    };
    this._store.dispatch(
      new UpdateUserSubSourceFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchReportsUserSubSource());
    this.currOffset = 0;
  }

  reset() {
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.pageSize,
    };
    this.filterFunction();
  }

  updateSubSource() {
    if (this.appliedFilter?.sources?.length) {
      this.subSourceList = [];
      this.appliedFilter?.sources.forEach((i: any) => {
        const source: any = LeadSource[i];
        const leadSource = IntegrationSource[source];
        this.subSourceList.push.apply(
          this.subSourceList,
          this.allSubSourceList[leadSource] || []
        );
      });
    } else {
      this.subSourceList = Object.values(this.allSubSourceList).flat();
    }
  }

  exportLeadReport() {
    this._store.dispatch(new FetchUserSubSourceExportSuccess(''));
    this.filterFunction();

    let initialState: any = {
      payload: {
        ...this.filtersPayload,
        timeZoneId:
          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset:
          this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
