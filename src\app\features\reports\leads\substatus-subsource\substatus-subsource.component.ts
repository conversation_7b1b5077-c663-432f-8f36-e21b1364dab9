import { Component, EventEmitter, OnD<PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, map, skipWhile, switchMap, take, takeUntil } from 'rxjs';
import { DATE_TYPE, PAGE_SIZE, REPORT_FILTERS_KEY_LABEL, SHOW_ENTRIES } from 'src/app/app.constants';
import { LeadDateType } from 'src/app/app.enum';

import * as moment from 'moment';
import { AppState } from 'src/app/app.reducer';
import { ReportsFilter } from 'src/app/core/interfaces/reports.interface';
import {
  assignToSort,
  changeCalendar,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading, getGlobalAnonymousIsLoading } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchLeadCities, FetchLeadStates, FetchProjectList } from 'src/app/reducers/lead/lead.actions';
import {
  getIsLeadCustomStatusEnabled,
  getLeadCities,
  getLeadCitiesIsLoading,
  getLeadCountries,
  getLeadCountriesIsLoading,
  getLeadStates,
  getLeadStatesIsLoading,
  getProjectList,
  getProjectListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchSubReport,
  FetchSubReportExportSuccess,
  UpdateSubReportFilterPayload,
} from 'src/app/reducers/reports/reports.actions';
import {
  getSubReportFiltersPayload,
  getSubReportList,
} from 'src/app/reducers/reports/reports.reducer';
import { CustomStatus, getCustomStatusList, getCustomStatusListIsLoading } from 'src/app/reducers/status/status.reducer';
import {
  FetchOnlyReporteesWithInactive,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getOnlyReporteesWithInactive,
  getOnlyReporteesWithInactiveIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';

@Component({
  selector: 'substatus-subsource',
  templateUrl: './substatus-subsource.component.html',
})
export class SubstatusSubsourceComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  leadSources: Array<any> = [];
  columnDropDown: { field: string; hide: boolean }[] = [];
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  currOffset: number = 0;
  selectedPageSize: number;
  projectList: any;
  rowData: Array<any> = [];
  filtersPayload: ReportsFilter;
  getPages = getPages;
  appliedFilter: any;
  searchTerm: string;
  currentView: 'table' | 'graph' = 'table';
  canExportAllUsers: boolean = false;
  canViewAllUsers: boolean = false;
  canViewReportees: boolean = false;
  canExportReportees: boolean = false;
  SubReportTotalCount: number;
  dateTypeList: Array<string> = DATE_TYPE.slice(0, 4);
  allUsers: Array<any> = [];
  onlyReportees: Array<any> = [];
  users: Array<any> = [];
  reportees: Array<any> = [];
  allStatusList: Array<any> = [];
  subStatusParentMap: any = {};
  isAllUsersLoading: boolean = true;
  isOnlyReporteesLoading: boolean = true;
  isProjectListLoading: boolean = true;
  cities: string[];
  citiesIsLoading: boolean = true;
  states: string[];
  statesIsLoading: boolean = true;
  keySeparator: string = ",/_,-,/_,";

  params2: any = '';
  customStatusList: CustomStatus[] = [];
  isCustomStatusListLoading: boolean = true;
  isSubStatusVsSubSourceReportLoading: boolean = false;
  showFilters: boolean = false;
  moment = moment;
  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;
  showLeftNav: boolean = true;
  isCustomStatusEnabled: boolean = false;
  isGlobalSettingsLoading: boolean = true;
  countryList: any[];
  countryIsLoading: boolean = true;
  isSourcesLoading: boolean = true;
  userData: any;
  currentDate: Date = new Date();
  toDate: any = new Date();
  fromDate: any = new Date();
  onPickerOpened = onPickerOpened;
  filteredColumnDefsCache: any[] = [];
  @ViewChild('reportsGraph') reportsGraph: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    public router: Router,
    private modalService: BsModalService,
    private shareDataService: ShareDataService,
    private modalRef: BsModalRef

  ) { }

  async ngOnInit() {
    await this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    this.isCustomStatusEnabled = await this._store
      .select(getIsLeadCustomStatusEnabled)
      .pipe(
        map((data: any) => data),
        take(1)
      ).toPromise();

    if (this.isCustomStatusEnabled)
      this.fetchCustomStatuses();
    this.selectedPageSize = 50;
    if (!this.isCustomStatusEnabled) {
      this.allStatusList = JSON.parse(localStorage.getItem('masterleadstatus'));
    }
    this.headerTitle.setTitle('Leads - Sub-Status vs Sub-Source Report');
    this.metaTitle.setTitle('CRM | Reports');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this._store
      .select(getUsersListForReassignment)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          this.users = data;
          this.allUsers = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.allUsers = assignToSort(this.allUsers, '');
          return this._store.select(getUsersListForReassignmentIsLoading).pipe(
            takeUntil(this.stopper)
          );
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
      });

    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          this.reportees = data;
          this.onlyReportees = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.onlyReportees = assignToSort(this.onlyReportees, '');
          return this._store.select(getOnlyReporteesWithInactiveIsLoading).pipe(
            takeUntil(this.stopper)
          );
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isOnlyReporteesLoading = isLoading;
      });
    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProjectListLoading = isLoading;
      });
    this._store
      .select(getSubReportList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isSubStatusVsSubSourceReportLoading = data?.isLoading;
        this.initializeGridSettings();
        this.graphData();
        (this.isCustomStatusEnabled ? this.customStatusList : this.allStatusList).forEach((status: any) => {
          if (status?.childTypes?.length) {
            status.childTypes.forEach((subStatus: any) => {
              this.subStatusParentMap[this.getKey(subStatus?.displayName)] = status?.status;
            });
          }
        });
        let totalRow: any = {
          projectTitle: 'Total',
          firstName: 'Total',
          lastName: '',
          userName: 'Total',
          subSource: 'Total',
          agencyName: 'Total',
          source: 'Total'
        };
        totalRow.baseStatusWithSubStatusCount = {
          ...totalRow, ...this.sumObjectValues(data?.items?.map((item: any) => {
            return item.baseStatusWithSubStatusCount;
          }))
        };
        this.rowData = ([...data?.items, totalRow]).map((row: any) => {
          let totalCounts: any = {};
          let flattenedRow = this.flattenObject(row.baseStatusWithSubStatusCount);
          Object.entries(flattenedRow).forEach((key: any) => {
            let totalStatusKeyAndCount = [key[0].split(this.keySeparator)[0] + "Total", key[1]];
            if (totalCounts.hasOwnProperty(totalStatusKeyAndCount[0])) {
              totalCounts[totalStatusKeyAndCount[0]] += totalStatusKeyAndCount[1];
            } else {
              totalCounts[totalStatusKeyAndCount[0]] = totalStatusKeyAndCount[1];
            }
          });
          return {
            ...row,
            ...totalCounts,
            all: row?.All,
            active: row?.Active,
          };
        });
        this.SubReportTotalCount = data.totalCount;
      });
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });
    this._store
      .select(getSubReportFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = { ...data, isNavigatedFromReports: true };
        this.pageSize = this.filtersPayload?.pageSize;
        this.selectedPageSize = this.filtersPayload?.pageSize;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          dateType: LeadDateType[Number(this.filtersPayload?.dateType)],
          date: [patchTimeZoneDate(this.filtersPayload?.fromDate, this.userData?.timeZoneInfo?.baseUTcOffset), patchTimeZoneDate(this.filtersPayload?.toDate, this.userData?.timeZoneInfo?.baseUTcOffset)],
          withTeam: this.filtersPayload?.IsWithTeam,
          users: this.filtersPayload?.UserIds,
          search: this.filtersPayload?.SearchText,
          sources: this.filtersPayload?.Sources,
          projects: this.filtersPayload?.Projects,
          cities: this.filtersPayload?.Cities,
          states: this.filtersPayload?.States
        };
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExportAllUsers = permissionsSet.has('Permissions.Reports.ExportAllUsers');
        this.canViewAllUsers = permissionsSet.has('Permissions.Reports.ViewAllUsers');
        this.canExportReportees = permissionsSet.has('Permissions.Reports.ExportReportees');
        this.canViewReportees = permissionsSet.has('Permissions.Reports.ViewReportees');
        if (this.canViewAllUsers) {
          this._store.dispatch(new FetchUsersListForReassignment());
        }
        if (this.canViewReportees) {
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
      });

    this._store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        if (leadSource) {
          const enabledSources = leadSource
            .filter((source: any) => source.isEnabled)
            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));
          this.leadSources = [...enabledSources];
        } else {
          this.leadSources = [];
        }
      });

    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });

    this.shareDataService.showLeftNav$.subscribe(show => {
      this.showLeftNav = show;
    });

    this.filterFunction();

    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });


    this._store
      .select(getLeadCities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.cities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadCitiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.citiesIsLoading = data;
      });

    this._store
      .select(getLeadStates)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.states = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadStatesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.statesIsLoading = data;
      });

    this._store
      .select(getLeadCountries)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryList = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadCountriesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.countryIsLoading = data;
      });

  }
  fetchCustomStatuses() {
    this._store
      .select(getCustomStatusListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isCustomStatusListLoading = isLoading;
        if (!isLoading) this.initializeGridSettings();
        this.graphData();
      });

    this._store
      .select(getCustomStatusList)
      .pipe(skipWhile(() => this.isCustomStatusListLoading), take(1))
      .subscribe((customStatus: any) => {
        this.customStatusList = customStatus;
      });
  }
  flattenObject(obj: any, parentKey = '') {
    let result: any = {};
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        let newKey = parentKey ? `${parentKey}${this.keySeparator}${key}` : key;

        if (typeof obj[key] === 'object' && obj[key] !== null) {
          Object.assign(result, this.flattenObject(obj[key], newKey));
        } else {
          result[newKey] = obj[key];
        }
      }
    }
    return result;
  }
  sumObjectValues(objects: any) {
    const result: any = {};
    objects.forEach((obj: any) => {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (typeof obj[key] === 'object' && Object.keys(obj[key]).length > 0) {
            result[key] = result[key] || {};
            result[key] = this.sumObjectValues([result[key], obj[key]]);
          } else {
            result[key] = (result[key] || 0) + obj[key];
          }
        }
      }
    });
    return result;
  }

  graphData(){
    this.filteredColumnDefsCache = this.gridOptions?.columnDefs?.filter(
      (col: any) => col.field !== 'Sub-Source'
    ).map((col: any) => {
      // For all columns, make them parents
      const column = {
        ...col,
        isParent: true
      };

      // If it has children, keep them
      if (col.children && col.children.length > 0) {
        column.children = col.children.filter((child: any) => child.headerName !== 'Total');
      } else {
        // If no children, add itself as a child
        column.children = [{
          ...col,
          parentName: col.headerName
        }];
      }

      return column;
    });
  }
  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Sub-Source',
        field: 'Sub-Source',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [params.data?.subSource],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<div class="py-16 align-center text-truncate"><p>${params.value[0]}
            </p></div>`;
        },
      },
      {
        headerName: 'All Leads',
        field: 'All Leads',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.baseStatusWithSubStatusCount?.All,
          params.data?.baseStatusWithSubStatusCount?.Active,
          // params?.data?.userId,
          // params?.data?.projectTitle,
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `${params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`
            }
          <p class="text-truncate"><span class="text-dark-gray">active: </span>
          <span class="fw-600">${params.value[1] && params?.value?.[3] != 'Total'
              ? `<a>${params.value[1]}</a>`
              : params.value[1]
                ? params.value[1]
                : '--'
            }<span>
          </p>`;
        },
        cellStyle: { cursor: 'pointer' },
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.event?.target?.innerText == event.value[0]) {
            if (isCtrlClick) {
              this.getDataInNewTab('All Leads', 'leadReportGetData', params);
              return;
            }
            this.getDataFromCell('All Leads', event);
          } else if (event.event.target.innerText == event.value[1]) {
            if (isCtrlClick) {
              this.getDataInNewTab('Active Leads', 'leadReportGetData', params);
              return;
            }
            this.getDataFromCell('Active Leads', event);
          }
        },
      },
      {
        headerName: 'Overdue',
        field: 'Overdue',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.baseStatusWithSubStatusCount?.Overdue,
          // params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 80,
        cellRenderer: (params: any) => {
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
        },
        cellStyle: { cursor: 'pointer' },
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Overdue', 'leadSubStatusGetData', params);
              return;
            }
            this.getDataFromCell('Overdue', event);
          }
        },
      },
      {
        headerName: 'Meeting Done',
        field: 'Meeting Done',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.baseStatusWithSubStatusCount?.MeetingDoneCount,
          params.data?.baseStatusWithSubStatusCount?.MeetingDoneUniqueCount,
          // params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }</span></p></a>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountInNewTab('All Leads', params, 'Meeting Done');
              return;
            }
            this.getMeetingCountFromCell('All Leads', event, 'Meeting Done');
          }
        },
      },
      {
        headerName: 'Meeting Not Done',
        field: 'Meeting Not Done',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.baseStatusWithSubStatusCount?.MeetingNotDoneCount,
          params.data?.baseStatusWithSubStatusCount?.MeetingNotDoneUniqueCount,
          // params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 150,
        cellRenderer: (params: any) => {
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }</span></p></a>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountInNewTab(
                'All Leads',
                params,
                'Meeting Not Done'
              );
              return;
            }
            this.getMeetingCountFromCell(
              'All Leads',
              event,
              'Meeting Not Done'
            );
          }
        },
      },
      {
        headerName: 'Site Visit Done',
        field: 'Site Visit Done',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.baseStatusWithSubStatusCount?.siteVisitDoneCount,
          params.data?.baseStatusWithSubStatusCount?.SiteVisitDoneUniqueCount,
          // params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p>
          </a>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountInNewTab(
                'All Leads',
                params,
                'Site Visit Done'
              );
              return;
            }
            this.getMeetingCountFromCell('All Leads', event, 'Site Visit Done');
          }
        },
      },
      {
        headerName: 'Site Visit Not Done',
        field: 'Site Visit Not Done',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.baseStatusWithSubStatusCount?.SiteVisitNotDoneCount,
          params.data?.baseStatusWithSubStatusCount?.SiteVisitNotDoneUniqueCount,
          // params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 150,
        cellRenderer: (params: any) => {
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountInNewTab(
                'All Leads',
                params,
                'Site Visit Not Done'
              );
              return;
            }
            this.getMeetingCountFromCell(
              'All Leads',
              event,
              'Site Visit Not Done'
            );
          }
        },
      },
    ];
    for (const status of (this.isCustomStatusEnabled ? this.customStatusList : this.allStatusList)) {
      let col: any;
      if (!status?.childTypes?.length) {
        col = {
          headerName: this.getKey(status.displayName),
          field: this.getKey(status.displayName),
          filter: false,
          valueGetter: (params: any) => [
            params.data?.baseStatusWithSubStatusCount?.[status?.status],
            params?.data?.subSource,
          ],
          minWidth: 90,
          cellRenderer: (params: any) => {
            return params?.value?.[2] == 'Total' || !params.value[0]
              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
              : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
          },
          cellStyle: { cursor: 'pointer' },
          onCellClicked: (event: any) => {
            const isCtrlClick = event?.event?.ctrlKey;
            const params = { value: event?.value, data: event?.data };
            if (event.data.projectTitle == 'Total') {
              return;
            } else if (event.value[0]) {
              if (isCtrlClick) {
                this.getDataInNewTab(
                  status.displayName,
                  'leadSubStatusGetData',
                  params
                );
                return;
              }
              this.getDataFromCell(status.displayName, event);
            }
          },
        };
      } else {
        col = {
          headerName: status.displayName,
          children: [],
        };
        for (const subStatus of status.childTypes) {
          col.children.push({
            headerName: subStatus.displayName,
            field: this.getKey(subStatus?.displayName),
            filter: false,
            valueGetter: (params: any) => {
              return [params.data?.baseStatusWithSubStatusCount?.[this.splitAndJoin(status?.displayName)]?.[this.convertToLowerCaseWithoutSpaces(subStatus?.displayName)], params?.data?.projectTitle];
            }
            ,
            minWidth: 160,
            cellRenderer: (params: any) => {
              return params?.value?.[2] == 'Total' || !params.value[0]
                ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
                : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
            },
            cellStyle: { cursor: 'pointer' },
            onCellClicked: (event: any) => {
              const isCtrlClick = event?.event?.ctrlKey;
              const params = { value: event?.value, data: event?.data };
              if (event.data.projectTitle == 'Total') {
                return;
              } else if (event.value[0]) {
                if (isCtrlClick) {
                  window?.open(
                    `leads/manage-leads?leadSubStatusGetData=true&assignTo=${params?.value?.[1]
                    }&data=${encodeURIComponent(
                      JSON.stringify(params?.data)
                    )}&operation=All Leads&subStatus=${subStatus.displayName
                    }&filtersPayload=${encodeURIComponent(
                      JSON.stringify(this.filtersPayload)
                    )}`,
                    '_blank'
                  );
                  return;
                }
                this.getDataFromCell('All Leads', event, subStatus.displayName);
              }
            },
          });
        }
        col.children.push({
          headerName: 'Total',
          field: this.getKey(status.status) + 'Total',
          filter: false,
          valueGetter: (params: any) => [
            params.data?.[this.getKey(status?.status) + 'Total'],
            params?.data?.projectTitle,
          ],
          minWidth: 160,
          cellRenderer: (params: any) => {
            return params?.value?.[2] == 'Total' || !params.value[0]
              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
              : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
          },
          cellStyle: { cursor: 'pointer' },
          onCellClicked: (event: any) => {
            const isCtrlClick = event?.event?.ctrlKey;
            const params = { value: event?.value, data: event?.data };
            if (event.data.projectTitle == 'Total') {
              return;
            } else if (event.value[0]) {
              if (isCtrlClick) {
                this.getDataInNewTab(
                  status.displayName,
                  'leadSubStatusGetData',
                  params
                );
                return;
              }
              this.getDataFromCell(status.displayName, event);
            }
          },
        });
      }

      this.gridOptions.columnDefs.push(col);
    }
    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  getKey(displayName: string): string {
    let keyWithoutSpace = displayName?.replace(/\s+/g, '');
    return keyWithoutSpace?.charAt(0)?.toLowerCase() + keyWithoutSpace?.slice(1);
  }

  convertToLowerCaseWithoutSpaces(value: any) {
    let result = value.replace(/[\s]/g, '').toLowerCase();
    return result;
  }

  splitAndJoin(str: any) {
    var words = str.split(' ');
    var result = words.map(function (word: any) {
      return word.toLowerCase();
    }).join('_');
    return result;
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  getDataFromCell(operation: string, event: any, subStatus?: string) {
    this.router.navigate(['leads/manage-leads']);
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.dateType = this.appliedFilter.dateType;
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.status = operation;
    this.gridOptionsService.subStatus = subStatus;
    this.gridOptionsService.payload = this.filtersPayload;
  }


  getDataInNewTab(operation: string, leadDataAndCount: any, params: any) {
    if (this.isCustomStatusEnabled) {
      return;
    }
    window?.open(
      `leads/manage-leads?${leadDataAndCount}=true&data=${encodeURIComponent(
        JSON.stringify(params?.data)
      )}&operation=${operation}&filtersPayload=${encodeURIComponent(
        JSON.stringify(this.filtersPayload)
      )}`,
      '_blank'
    );
  }

  getMeetingCountFromCell(
    operation: string,
    event: any,
    meetingStatus: string
  ) {
    this.router.navigate(['leads/manage-leads']);
    let visitMeeting = [];
    visitMeeting.push(meetingStatus);
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.dateType = this.appliedFilter.dateType;
    this.gridOptionsService.status = operation;
    this.gridOptionsService.payload = this.filtersPayload;
    this.gridOptionsService.meetingStatus = visitMeeting;
  }


  getMeetingCountInNewTab(
    operation: string,
    params: any,
    meetingStatus: string
  ) {
    window?.open(
      `leads/manage-leads?leadReportGetMeetingCount=true&data=${encodeURIComponent(
        JSON.stringify(params?.data)
      )}&operation=${operation}&meetingStatus=${meetingStatus}&filtersPayload=${encodeURIComponent(
        JSON.stringify({ ...this.filtersPayload, UserIds: null })
      )}`,
      '_blank'
    );
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this._store.dispatch(new UpdateSubReportFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchSubReport());
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(new UpdateSubReportFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchSubReport());
    this.currOffset = 0;
  }


  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = ['subSources', 'projects', 'agencyNames', 'cities', 'states'];

    if (
      [
        'pageSize',
        'pageNumber',
        'visibility',
        'withTeam',
        'userStatus',
        'search'
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'date' && values.length === 2) {
      if (key === 'date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(new Date(values[0]), this.userData?.timeZoneInfo?.baseUTcOffset);
        this.fromDate = setTimeZoneDate(new Date(values[1]), this.userData?.timeZoneInfo?.baseUTcOffset);
        const formattedToDate = getTimeZoneDate(this.toDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear');;
        const formattedFromDate = getTimeZoneDate(this.fromDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear');;
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    } else if (allowedKeys.includes(key)) {
      return values;
    }
    return values?.toString()?.split(',');
  }

  applyAdvancedFilter() {
    this.filterFunction();
    this.modalService.hide();
  }

  onRemoveFilter(key: string, value: string) {
    if (['dateType', 'date'].includes(key)) {
      delete this.appliedFilter[key];
      const dependentKey = key === 'date' ? 'dateType' : 'date';
      if (this.appliedFilter[dependentKey]) {
        delete this.appliedFilter[dependentKey];
      }
    } else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter(
        (item: any) => item !== value
      );
    }
    this.filterFunction();
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    this._store.dispatch(new FetchLeadCities());
    this._store.dispatch(new FetchLeadStates());
    this._store.dispatch(new FetchProjectList());
    // this._store.dispatch(new FetchLeadCountries());
    this._store.dispatch(new FetchAllSources());
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.fullName}`;
    });
    return userName;

  }

  filterFunction() {
    this.appliedFilter.pageNumber = 1;
    if (this.appliedFilter?.dateType?.length ||
      this.appliedFilter?.date?.[0]?.length ||
      this.appliedFilter.users?.length ||
      this.appliedFilter.projects?.length ||
      this.appliedFilter.sources?.length ||
      this.appliedFilter.cities?.length ||
      this.appliedFilter.Countries?.length ||
      this.appliedFilter.states?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      dateType: LeadDateType[this.appliedFilter.dateType],
      fromDate: setTimeZoneDate(this.appliedFilter?.date?.[0], this.userData?.timeZoneInfo?.baseUTcOffset),
      toDate: setTimeZoneDate(this.appliedFilter.date?.[1], this.userData?.timeZoneInfo?.baseUTcOffset),
      IsWithTeam: this.appliedFilter.withTeam,
      UserIds: this.appliedFilter.users,
      SearchText: this.searchTerm,
      Sources: this.appliedFilter.sources,
      Projects: this.appliedFilter.projects,
      ReportPermission: this.canViewAllUsers ? 0 : 1,
      ExportPermission: this.canExportAllUsers ? 0 : 1,
      Cities: this.appliedFilter?.cities,
      States: this.appliedFilter?.states,
      Countries: this.appliedFilter?.Countries,
    };
    this._store.dispatch(new UpdateSubReportFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchSubReport());
    this.currOffset = 0;
  }

  reset() {
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.pageSize,
    };
    this.filterFunction();
  }

  onResetDateFilter() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateType: null,
      date: '',
    };
    this.filterFunction();
  }

  exportSubReport() {
    this._store.dispatch(new FetchSubReportExportSuccess(''));
    this.filterFunction();
    let initialState: any = {
      payload: {
        ...this.filtersPayload,
        timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset()
      }, class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  toggleView() {
    this.currentView = this.currentView === 'graph' ? 'table' : 'graph';
  }

  exportGraphAsPDF() {
    if (this.reportsGraph && this.isGraphExportEnabled()) {
      this.reportsGraph.exportGraph();
    }
  }

  isGraphExportEnabled(): boolean {
    return this.currentView === 'graph' && 
           this.reportsGraph?.isChartReady && 
           !this.reportsGraph?.showSelectionMessage;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
